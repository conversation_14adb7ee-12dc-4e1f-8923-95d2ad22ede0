#!/usr/bin/env python3
"""
Test script to verify enhanced bearish signal functionality in memecoin service.
"""

import sys
import os
import json

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_notification_templates():
    """Test that the notification templates are valid and contain bearish enhancements."""
    print("=" * 60)
    print("TESTING ENHANCED BEARISH SIGNAL TEMPLATES")
    print("=" * 60)
    
    try:
        # Load templates
        with open('src/notification/templates.json', 'r', encoding='utf-8') as f:
            templates = json.load(f)
        
        print("✓ Templates loaded successfully")
        
        # Check for required bearish templates
        required_templates = [
            'strategy_execution_bearish',
            'mtpi_signal_change_bearish',
            'daily_summary_bearish'
        ]
        
        for template_name in required_templates:
            if template_name in templates:
                print(f"✓ Found template: {template_name}")
                
                # Check for bearish indicators in template
                template_content = templates[template_name]
                bearish_indicators = ['❌', '🚨', '🔴', 'BEARISH', 'CAUTION', 'AVOID']
                found_indicators = [indicator for indicator in bearish_indicators if indicator in template_content]
                
                if found_indicators:
                    print(f"  ✓ Contains bearish indicators: {', '.join(found_indicators)}")
                else:
                    print(f"  ⚠️ Missing bearish indicators")
            else:
                print(f"✗ Missing template: {template_name}")
        
        # Test template formatting
        print("\n" + "=" * 40)
        print("TESTING TEMPLATE FORMATTING")
        print("=" * 40)
        
        # Test bearish strategy execution template
        test_data = {
            'status': 'completed',
            'best_asset': 'PEPE (highest score)',
            'mtpi_signal': 'BEARISH ❌❌❌ (signal changed) ❌❌❌',
            'allocation_approach': 'Single asset selection',
            'assets_traded_str': '',
            'asset_scores_str': '1. PEPE: 25\n2. DOGE: 23\n3. SHIB: 21\n',
            'time': '2025-05-31 12:00:00 UTC'
        }
        
        if 'strategy_execution_bearish' in templates:
            formatted_message = templates['strategy_execution_bearish'].format(**test_data)
            print("BEARISH STRATEGY EXECUTION MESSAGE:")
            print("-" * 40)
            print(formatted_message)
            print("-" * 40)
        
        # Test bearish signal change template
        signal_data = {
            'signal': 'BEARISH ❌❌❌',
            'previous_signal': 'BULLISH',
            'time': '2025-05-31 12:00:00 UTC'
        }
        
        if 'mtpi_signal_change_bearish' in templates:
            formatted_signal = templates['mtpi_signal_change_bearish'].format(**signal_data)
            print("\nBEARISH SIGNAL CHANGE MESSAGE:")
            print("-" * 40)
            print(formatted_signal)
            print("-" * 40)
        
        print("\n✅ Template testing completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing templates: {e}")
        return False

def test_bearish_signal_formatting():
    """Test the bearish signal formatting logic."""
    print("\n" + "=" * 60)
    print("TESTING BEARISH SIGNAL FORMATTING LOGIC")
    print("=" * 60)
    
    # Test MTPI signal formatting
    test_cases = [
        (1, False, "bullish"),
        (1, True, "bullish (signal changed)"),
        (-1, False, "BEARISH ❌❌❌"),
        (-1, True, "BEARISH ❌❌❌ (signal changed) ❌❌❌"),
        (0, False, "neutral"),
        (0, True, "neutral (signal changed)")
    ]
    
    print("Testing MTPI signal formatting:")
    for mtpi_signal, signal_changed, expected_base in test_cases:
        # Simulate the formatting logic from the service
        if mtpi_signal == -1:
            mtpi_signal_text = 'BEARISH ❌❌❌'
            if signal_changed:
                mtpi_display = f"{mtpi_signal_text} (signal changed) ❌❌❌"
            else:
                mtpi_display = mtpi_signal_text
        else:
            mtpi_signal_text = 'bullish' if mtpi_signal == 1 else 'neutral'
            if signal_changed:
                mtpi_display = f"{mtpi_signal_text} (signal changed)"
            else:
                mtpi_display = mtpi_signal_text
        
        print(f"  Signal: {mtpi_signal}, Changed: {signal_changed} → '{mtpi_display}'")
        
        # Verify bearish signals have proper indicators
        if mtpi_signal == -1:
            if '❌' in mtpi_display and 'BEARISH' in mtpi_display:
                print(f"    ✓ Bearish formatting correct")
            else:
                print(f"    ✗ Bearish formatting missing indicators")
        else:
            print(f"    ✓ Non-bearish formatting correct")
    
    print("\n✅ Signal formatting testing completed!")
    return True

def main():
    """Main test function."""
    print("ENHANCED BEARISH SIGNAL FUNCTIONALITY TEST")
    print("=" * 80)
    
    success = True
    
    # Test notification templates
    if not test_notification_templates():
        success = False
    
    # Test signal formatting logic
    if not test_bearish_signal_formatting():
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED! Enhanced bearish signals are ready!")
        print("\nKey enhancements implemented:")
        print("• ❌❌❌ Red X indicators for bearish signals")
        print("• 🚨 Warning emojis and caution messages")
        print("• 🔴 Recommendations to avoid memecoin investments")
        print("• Special bearish templates for all notification types")
        print("• Enhanced console output with prominent warnings")
        print("• Asset rankings still shown for informational purposes")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    
    return success

if __name__ == "__main__":
    main()
