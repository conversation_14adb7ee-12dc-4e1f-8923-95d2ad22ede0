#!/usr/bin/env python3
"""
Debug script to check DWMA signal logic and verify if bearish/bullish signals are correct.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.dwma_score import calculate_dwma_score

def debug_dwma_signals():
    """Debug DWMA signals to check if they make sense."""
    print("=" * 60)
    print("DWMA Signal Logic Debug")
    print("=" * 60)

    # Load configuration to see what parameters are actually being used
    from src.config_manager import load_config
    config = load_config()
    mtpi_config = config.get('settings', {}).get('mtpi_indicators', {})
    dwma_config = mtpi_config.get('dwma_score', {})

    print("YAML Configuration for DWMA:")
    for key, value in dwma_config.items():
        print(f"  {key}: {value}")
    print()

    # Fetch recent BTC data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-05-01'  # Recent data for easier analysis
    )

    btc_df = data_dict['BTC/USDT']
    print(f"Analyzing {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")

    # Test 1: Calculate DWMA with exact YAML parameters
    print("\n" + "=" * 60)
    print("TEST 1: Using YAML Configuration Parameters")
    print("=" * 60)

    yaml_params = {
        'smoothing_style': dwma_config.get('smoothing_style', 'Weighted SD'),
        'src_col': dwma_config.get('src_col', 'close'),
        'length': dwma_config.get('length', 17),
        'ma_type': dwma_config.get('ma_type', 'EMA'),
        'ma_smooth_length': dwma_config.get('ma_smooth_length', 12),
        'sd_length': dwma_config.get('sd_length', 33),
        'upper_sd_weight': dwma_config.get('upper_sd_weight', 1.031),
        'lower_sd_weight': dwma_config.get('lower_sd_weight', 0.996),
        'atr_period': dwma_config.get('atr_period', 12),
        'atr_multiplier': dwma_config.get('atr_multiplier', 1.0),
        'loop_start': dwma_config.get('loop_start', 1),
        'loop_end': dwma_config.get('loop_end', 60),
        'long_threshold': dwma_config.get('long_threshold', 30),
        'short_threshold': dwma_config.get('short_threshold', 0)
    }

    print("Parameters being passed to calculate_dwma_score:")
    for key, value in yaml_params.items():
        print(f"  {key}: {value}")
    print()

    signal_yaml, dwma_yaml, dwmas_yaml = calculate_dwma_score(df=btc_df, **yaml_params)

    # Test 2: Calculate DWMA using the MTPI signal handler method
    print("\n" + "=" * 60)
    print("TEST 2: Using MTPI Signal Handler Method")
    print("=" * 60)

    from src.indicators.dwma_score import generate_dwma_signal

    # Simulate exactly what the MTPI signal handler does
    mtpi_signal = generate_dwma_signal(
        df=btc_df,
        smoothing_style=dwma_config.get('smoothing_style', 'Weighted SD'),
        src_col=dwma_config.get('src_col', 'close'),
        length=dwma_config.get('length', 17),
        ma_type=dwma_config.get('ma_type', 'EMA'),
        ma_smooth_length=dwma_config.get('ma_smooth_length', 12),
        # Weighted SD parameters
        sd_length=dwma_config.get('sd_length', 33),
        upper_sd_weight=dwma_config.get('upper_sd_weight', 1.031),
        lower_sd_weight=dwma_config.get('lower_sd_weight', 0.996),
        # ATR parameters
        atr_period=dwma_config.get('atr_period', 12),
        atr_multiplier=dwma_config.get('atr_multiplier', 1.0),
        # For loop parameters
        loop_start=dwma_config.get('loop_start', 1),
        loop_end=dwma_config.get('loop_end', 60),
        long_threshold=dwma_config.get('long_threshold', 30),
        short_threshold=dwma_config.get('short_threshold', 0)
    )
    
    # Calculate the bands for analysis
    sd = dwmas.rolling(window=tv_params['sd_length']).std()
    sd_upper = (dwmas + sd) * tv_params['upper_sd_weight']
    sd_lower = (dwmas - sd) * tv_params['lower_sd_weight']
    
    # Find signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"\nFound {len(change_indices)} signal changes")
    print("\nSignal Change Analysis:")
    print("=" * 80)
    
    for i, change_date in enumerate(change_indices[-10:]):  # Last 10 changes
        idx = btc_df.index.get_loc(change_date)
        
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            
            close_price = btc_df['close'].iloc[idx]
            prev_close = btc_df['close'].iloc[idx-1]
            
            upper_band = sd_upper.iloc[idx]
            lower_band = sd_lower.iloc[idx]
            dwma_value = dwmas.iloc[idx]
            
            price_change = ((close_price - prev_close) / prev_close) * 100
            
            print(f"\nDate: {change_date.strftime('%Y-%m-%d')}")
            print(f"Signal: {prev_signal} → {curr_signal}")
            print(f"Price: ${prev_close:.2f} → ${close_price:.2f} ({price_change:+.2f}%)")
            print(f"DWMA: ${dwma_value:.2f}")
            print(f"Upper Band: ${upper_band:.2f}")
            print(f"Lower Band: ${lower_band:.2f}")
            
            # Check crossover logic
            if curr_signal == 1:  # Long signal
                print(f"LONG: Price crossed above upper band? {close_price > upper_band}")
                if close_price > upper_band and price_change > 0:
                    print("✅ CORRECT: Bullish signal on upward price movement")
                else:
                    print("❌ SUSPICIOUS: Bullish signal but price didn't cross above or move up")
            elif curr_signal == -1:  # Short signal
                print(f"SHORT: Price crossed below lower band? {close_price < lower_band}")
                if close_price < lower_band and price_change < 0:
                    print("✅ CORRECT: Bearish signal on downward price movement")
                else:
                    print("❌ SUSPICIOUS: Bearish signal but price didn't cross below or move down")
    
    # Recent signals analysis
    print(f"\n" + "=" * 80)
    print("Recent Signals (Last 20 days):")
    print("=" * 80)
    
    recent_data = pd.DataFrame({
        'Date': btc_df.index[-20:].strftime('%Y-%m-%d'),
        'Close': btc_df['close'].iloc[-20:].round(2),
        'Signal': signal.iloc[-20:],
        'DWMA': dwmas.iloc[-20:].round(2),
        'Upper': sd_upper.iloc[-20:].round(2),
        'Lower': sd_lower.iloc[-20:].round(2)
    })
    
    print(recent_data.to_string(index=False))
    
    # Check current market condition
    current_price = btc_df['close'].iloc[-1]
    current_signal = signal.iloc[-1]
    current_upper = sd_upper.iloc[-1]
    current_lower = sd_lower.iloc[-1]
    
    print(f"\n" + "=" * 80)
    print("Current Market Analysis:")
    print("=" * 80)
    print(f"Current Price: ${current_price:.2f}")
    print(f"Current Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Upper Band: ${current_upper:.2f}")
    print(f"Lower Band: ${current_lower:.2f}")
    print(f"Price vs Upper: {((current_price - current_upper) / current_upper * 100):+.2f}%")
    print(f"Price vs Lower: {((current_price - current_lower) / current_lower * 100):+.2f}%")
    
    # Logic check
    if current_signal == 1 and current_price > current_upper:
        print("✅ LOGIC CHECK: Bullish signal with price above upper band - CORRECT")
    elif current_signal == -1 and current_price < current_lower:
        print("✅ LOGIC CHECK: Bearish signal with price below lower band - CORRECT")
    elif current_signal == 1 and current_price < current_lower:
        print("❌ LOGIC ERROR: Bullish signal but price below lower band - INCORRECT")
    elif current_signal == -1 and current_price > current_upper:
        print("❌ LOGIC ERROR: Bearish signal but price above upper band - INCORRECT")
    else:
        print("⚠️  NEUTRAL ZONE: Signal may be from previous crossover")

if __name__ == "__main__":
    debug_dwma_signals()
