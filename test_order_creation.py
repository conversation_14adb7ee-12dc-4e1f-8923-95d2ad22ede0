#!/usr/bin/env python3
"""
Test script to verify order creation works with the validation fix.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.order import OrderManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_order_creation():
    """Test order creation with the validation fix."""
    
    print("Testing order creation with validation fix...")
    
    try:
        # Initialize order manager for Kraken in paper mode
        order_manager = OrderManager(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        # Override to paper mode for testing
        order_manager.trading_config['mode'] = 'paper'
        
        # Test the exact amounts that were failing
        test_cases = [
            {
                'symbol': 'BTC/EUR',
                'amount': 12.52023425,  # EUR amount from the logs
                'description': 'BTC/EUR order that was failing'
            },
            {
                'symbol': 'TRX/EUR', 
                'amount': 3.01200000,  # EUR amount from the logs
                'description': 'TRX/EUR order that was failing'
            }
        ]
        
        for test_case in test_cases:
            print(f"\n=== Testing {test_case['description']} ===")
            print(f"Symbol: {test_case['symbol']}")
            print(f"Amount: {test_case['amount']} EUR")
            
            try:
                # This should now work without the NoneType comparison error
                order = order_manager.create_market_buy_order(
                    symbol=test_case['symbol'],
                    amount=test_case['amount']
                )
                
                if order:
                    print(f"✅ Order created successfully!")
                    print(f"Order ID: {order.get('id', 'N/A')}")
                    print(f"Status: {order.get('status', 'N/A')}")
                    print(f"Amount: {order.get('amount', 'N/A')}")
                    print(f"Price: {order.get('price', 'N/A')}")
                else:
                    print(f"❌ Order creation returned empty result")
                    
            except Exception as e:
                print(f"❌ Error creating order: {e}")
                import traceback
                traceback.print_exc()
                
                # Check if it's still the NoneType comparison error
                if "'>' not supported between instances of 'NoneType' and" in str(e):
                    print("🔍 Still getting NoneType comparison error - need further investigation")
                else:
                    print("🔍 Different error - this might be expected for paper trading")
    
    except Exception as e:
        print(f"Error in test setup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_order_creation()
    print("\nTest completed!")
