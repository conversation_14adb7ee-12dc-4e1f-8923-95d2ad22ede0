#!/usr/bin/env python3
"""
Test script to verify notification configurations are set up correctly.
"""

import sys
import json

# Add the src directory to the path
sys.path.append('src')

def test_notification_config(config_path: str, exchange_name: str):
    """Test a specific notification configuration."""
    print(f"\n{'='*50}")
    print(f"TESTING {exchange_name.upper()} NOTIFICATION CONFIG")
    print(f"{'='*50}")
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        telegram = config.get('telegram', {})
        settings = config.get('notification_settings', {})
        
        print(f"✅ Config loaded successfully")
        print(f"   Telegram enabled: {telegram.get('enabled', False)}")
        print(f"   Bot token: {telegram.get('token', 'NOT SET')[:20]}...")
        print(f"   Chat ID: {telegram.get('chat_id', 'NOT SET')}")
        print(f"   Level: {telegram.get('level', 'NOT SET')}")
        print(f"   Exchange identifier: {settings.get('exchange_identifier', 'NOT SET')}")
        
        # Check if all required fields are present
        required_fields = ['enabled', 'token', 'chat_id']
        missing_fields = [field for field in required_fields if not telegram.get(field)]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        else:
            print(f"✅ All required fields present")
            return True
            
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def main():
    """Test all notification configurations."""
    print("NOTIFICATION CONFIGURATION TEST")
    print("=" * 60)
    
    configs = [
        ('config/notifications_kraken.json', 'Kraken'),
        ('config/notifications_bitvavo.json', 'Bitvavo'),
        ('config/notifications.json', 'Binance')
    ]
    
    all_good = True
    
    for config_path, exchange_name in configs:
        try:
            result = test_notification_config(config_path, exchange_name)
            all_good = all_good and result
        except FileNotFoundError:
            print(f"\n❌ {exchange_name} notification config not found: {config_path}")
            all_good = False
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    if all_good:
        print("✅ ALL NOTIFICATION CONFIGS ARE READY!")
        print("\nYou can now start both services without bot conflicts:")
        print("")
        print("# Terminal 1: Kraken")
        print("python background_service.py --config config/settings_kraken_eur.yaml --notifications config/notifications_kraken.json")
        print("")
        print("# Terminal 2: Bitvavo") 
        print("python background_service.py --config config/settings_bitvavo_eur.yaml --notifications config/notifications_bitvavo.json")
        print("")
        print("Each exchange will use its own bot - no more conflicts! 🎉")
    else:
        print("❌ Some notification configs have issues. Please fix them before starting.")

if __name__ == "__main__":
    main()
