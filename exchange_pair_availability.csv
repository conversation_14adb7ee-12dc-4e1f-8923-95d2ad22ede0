Token,Quote,Pair,OKX_available,OKX_has_enough_data,OKX_days_available,BYBIT_available,BYBIT_has_enough_data,BYBIT_days_available,KRAKEN_available,KRAKEN_has_enough_data,KRAKEN_days_available,BITVAVO_available,BITVAVO_has_enough_data,BITVAVO_days_available,Total_Exchanges_Available,Total_Exchanges_With_Enough_Data,Available_On,Available_With_Enough_Data_On
1000CAT,USDC,1000CAT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1000CAT,USDT,1000CAT/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1000CHEEMS,USDC,1000CHEEMS/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1000CHEEMS,USDT,1000CHEEMS/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1000SATS,USDC,1000SATS/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1000SATS,USDT,1000SATS/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1MBABYDOGE,USDC,1MBABYDOGE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
1MBABYDOGE,USDT,1MBABYDOGE/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
AAVE,USDC,AAVE/USDC,True,True,35,True,False,14,False,False,0,False,False,0,51,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
AAVE,USDT,AAVE/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ACT,USDC,ACT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ACT,USDT,ACT/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
ACX,USDC,ACX/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ACX,USDT,ACX/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ADA,USDC,ADA/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
ADA,USDT,ADA/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
AIXBT,USDC,AIXBT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
AIXBT,USDT,AIXBT/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ALGO,USDC,ALGO/USDC,True,True,35,True,False,14,True,True,35,False,False,0,87,2,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, KRAKEN"
ALGO,USDT,ALGO/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
ALT,USDC,ALT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ALT,USDT,ALT/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
ANIME,USDC,ANIME/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ANIME,USDT,ANIME/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
APE,USDC,APE/USDC,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
APE,USDT,APE/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
API3,USDC,API3/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
API3,USDT,API3/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
APT,USDC,APT/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
APT,USDT,APT/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
AR,USDC,AR/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
AR,USDT,AR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ARB,USDC,ARB/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ARB,USDT,ARB/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ARKM,USDC,ARKM/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
ARKM,USDT,ARKM/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ATOM,USDC,ATOM/USDC,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
ATOM,USDT,ATOM/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
AUCTION,USDC,AUCTION/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
AUCTION,USDT,AUCTION/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
AVAX,USDC,AVAX/USDC,True,True,35,True,True,35,True,True,35,True,False,33,142,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN"
AVAX,USDT,AVAX/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
BABY,USDC,BABY/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BABY,USDT,BABY/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
BANANA,USDC,BANANA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BANANA,USDT,BANANA/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
BANANAS31,USDC,BANANAS31/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BANANAS31,USDT,BANANAS31/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BB,USDC,BB/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BB,USDT,BB/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
BCH,USDC,BCH/USDC,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
BCH,USDT,BCH/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
BEAMX,USDC,BEAMX/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BEAMX,USDT,BEAMX/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BERA,USDC,BERA/USDC,False,False,0,True,False,2,True,True,35,False,False,0,39,1,"BYBIT, BYBIT_days, KRAKEN, KRAKEN_days",KRAKEN
BERA,USDT,BERA/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
BIGTIME,USDC,BIGTIME/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BIGTIME,USDT,BIGTIME/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
BIO,USDC,BIO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BIO,USDT,BIO/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
BLUR,USDC,BLUR/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
BLUR,USDT,BLUR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
BMT,USDC,BMT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BMT,USDT,BMT/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
BNB,USDC,BNB/USDC,False,False,0,True,True,35,True,True,35,False,False,0,72,2,"BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","BYBIT, KRAKEN"
BNB,USDT,BNB/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
BOME,USDC,BOME/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BOME,USDT,BOME/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
BONK,USDC,BONK/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
BONK,USDT,BONK/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
BROCCOLI714,USDC,BROCCOLI714/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BROCCOLI714,USDT,BROCCOLI714/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
BTC,USDC,BTC/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
BTC,USDT,BTC/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
CAKE,USDC,CAKE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CAKE,USDT,CAKE/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
CATI,USDC,CATI/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
CATI,USDT,CATI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
CETUS,USDC,CETUS/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CETUS,USDT,CETUS/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
CFX,USDC,CFX/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CFX,USDT,CFX/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
CGPT,USDC,CGPT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CGPT,USDT,CGPT/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
CHESS,USDC,CHESS/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CHESS,USDT,CHESS/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CHZ,USDC,CHZ/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
CHZ,USDT,CHZ/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
CKB,USDC,CKB/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CKB,USDT,CKB/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
COOKIE,USDC,COOKIE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
COOKIE,USDT,COOKIE/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
COW,USDC,COW/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
COW,USDT,COW/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CRV,USDC,CRV/USDC,True,True,35,True,False,9,False,False,0,False,False,0,46,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
CRV,USDT,CRV/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
CVC,USDC,CVC/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
CVC,USDT,CVC/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
DF,USDC,DF/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
DF,USDT,DF/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
DOGE,USDC,DOGE/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
DOGE,USDT,DOGE/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
DOGS,USDC,DOGS/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
DOGS,USDT,DOGS/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
DOT,USDC,DOT/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
DOT,USDT,DOT/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
DYDX,USDC,DYDX/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
DYDX,USDT,DYDX/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
EGLD,USDC,EGLD/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
EGLD,USDT,EGLD/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
EIGEN,USDC,EIGEN/USDC,True,True,35,True,False,2,False,False,0,False,False,0,39,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
EIGEN,USDT,EIGEN/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ENA,USDC,ENA/USDC,False,False,0,True,False,14,False,False,0,False,False,0,15,0,"BYBIT, BYBIT_days",
ENA,USDT,ENA/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
ENS,USDC,ENS/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
ENS,USDT,ENS/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
EOS,USDC,EOS/USDC,False,False,0,False,False,0,True,True,35,False,False,0,36,1,"KRAKEN, KRAKEN_days",KRAKEN
EOS,USDT,EOS/USDT,False,False,0,False,False,0,True,True,35,False,False,0,36,1,"KRAKEN, KRAKEN_days",KRAKEN
EPIC,USDC,EPIC/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
EPIC,USDT,EPIC/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ETH,USDC,ETH/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
ETH,USDT,ETH/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
ETHFI,USDC,ETHFI/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
ETHFI,USDT,ETHFI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
EUR,USDC,EUR/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
EUR,USDT,EUR/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
FDUSD,USDC,FDUSD/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
FDUSD,USDT,FDUSD/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
FET,USDC,FET/USDC,False,False,0,True,True,35,False,False,0,True,False,27,64,1,"BYBIT, BYBIT_days, BITVAVO, BITVAVO_days",BYBIT
FET,USDT,FET/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
FIL,USDC,FIL/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
FIL,USDT,FIL/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
FLOKI,USDC,FLOKI/USDC,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
FLOKI,USDT,FLOKI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
FORM,USDC,FORM/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
FORM,USDT,FORM/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
GALA,USDC,GALA/USDC,True,True,35,True,False,2,False,False,0,False,False,0,39,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
GALA,USDT,GALA/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
GMX,USDC,GMX/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
GMX,USDT,GMX/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
GPS,USDC,GPS/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
GPS,USDT,GPS/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
GUN,USDC,GUN/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
GUN,USDT,GUN/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
HBAR,USDC,HBAR/USDC,True,True,35,True,False,14,False,False,0,False,False,0,51,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
HBAR,USDT,HBAR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
HEI,USDC,HEI/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
HEI,USDT,HEI/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
HIVE,USDC,HIVE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
HIVE,USDT,HIVE/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
HMSTR,USDC,HMSTR/USDC,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
HMSTR,USDT,HMSTR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ICP,USDC,ICP/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ICP,USDT,ICP/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
IDEX,USDC,IDEX/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
IDEX,USDT,IDEX/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
INJ,USDC,INJ/USDC,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
INJ,USDT,INJ/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
IO,USDC,IO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
IO,USDT,IO/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
IOTA,USDC,IOTA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
IOTA,USDT,IOTA/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
JTO,USDC,JTO/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
JTO,USDT,JTO/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
JUP,USDC,JUP/USDC,True,True,35,True,False,9,False,False,0,False,False,0,46,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
JUP,USDT,JUP/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
JUV,USDC,JUV/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
JUV,USDT,JUV/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
KAIA,USDC,KAIA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
KAIA,USDT,KAIA/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
KAITO,USDC,KAITO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
KAITO,USDT,KAITO/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
KERNEL,USDC,KERNEL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
KERNEL,USDT,KERNEL/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
LAYER,USDC,LAYER/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
LAYER,USDT,LAYER/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
LDO,USDC,LDO/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
LDO,USDT,LDO/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
LINK,USDC,LINK/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
LINK,USDT,LINK/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
LTC,USDC,LTC/USDC,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
LTC,USDT,LTC/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
MANTA,USDC,MANTA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
MANTA,USDT,MANTA/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
MEME,USDC,MEME/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
MEME,USDT,MEME/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
MKR,USDC,MKR/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
MKR,USDT,MKR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
MOVE,USDC,MOVE/USDC,True,True,35,True,False,9,False,False,0,False,False,0,46,1,"OKX, OKX_days, BYBIT, BYBIT_days",OKX
MOVE,USDT,MOVE/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
MUBARAK,USDC,MUBARAK/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
MUBARAK,USDT,MUBARAK/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
NEAR,USDC,NEAR/USDC,True,True,35,True,True,35,False,False,0,True,False,25,98,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
NEAR,USDT,NEAR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
NEIRO,USDC,NEIRO/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
NEIRO,USDT,NEIRO/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
NEO,USDC,NEO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
NEO,USDT,NEO/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
NIL,USDC,NIL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
NIL,USDT,NIL/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
NOT,USDC,NOT/USDC,True,True,35,True,True,35,False,False,0,True,False,5,78,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
NOT,USDT,NOT/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
OM,USDC,OM/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
OM,USDT,OM/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
OMNI,USDC,OMNI/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
OMNI,USDT,OMNI/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
ONDO,USDC,ONDO/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ONDO,USDT,ONDO/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ONT,USDC,ONT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ONT,USDT,ONT/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
OP,USDC,OP/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
OP,USDT,OP/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ORCA,USDC,ORCA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ORCA,USDT,ORCA/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ORDI,USDC,ORDI/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ORDI,USDT,ORDI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
OSMO,USDC,OSMO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
OSMO,USDT,OSMO/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PARTI,USDC,PARTI/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PARTI,USDT,PARTI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
PENDLE,USDC,PENDLE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PENDLE,USDT,PENDLE/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
PENGU,USDC,PENGU/USDC,False,False,0,False,False,0,True,True,35,False,False,0,36,1,"KRAKEN, KRAKEN_days",KRAKEN
PENGU,USDT,PENGU/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
PEOPLE,USDC,PEOPLE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PEOPLE,USDT,PEOPLE/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
PEPE,USDC,PEPE/USDC,True,True,35,True,True,35,False,False,0,True,False,32,105,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
PEPE,USDT,PEPE/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
PHA,USDC,PHA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PHA,USDT,PHA/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
PIXEL,USDC,PIXEL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
PIXEL,USDT,PIXEL/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
PNUT,USDC,PNUT/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
PNUT,USDT,PNUT/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
POL,USDC,POL/USDC,False,False,0,True,False,2,False,False,0,False,False,0,3,0,"BYBIT, BYBIT_days",
POL,USDT,POL/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
PYTH,USDC,PYTH/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
PYTH,USDT,PYTH/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
QNT,USDC,QNT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
QNT,USDT,QNT/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
RARE,USDC,RARE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RARE,USDT,RARE/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RAY,USDC,RAY/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RAY,USDT,RAY/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
RED,USDC,RED/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RED,USDT,RED/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
RENDER,USDC,RENDER/USDC,False,False,0,True,False,9,False,False,0,False,False,0,10,0,"BYBIT, BYBIT_days",
RENDER,USDT,RENDER/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
REZ,USDC,REZ/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
REZ,USDT,REZ/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RPL,USDC,RPL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RPL,USDT,RPL/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
RSR,USDC,RSR/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RSR,USDT,RSR/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
RUNE,USDC,RUNE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
RUNE,USDT,RUNE/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
S,USDC,S/USDC,False,False,0,False,False,0,True,True,35,True,False,18,55,1,"KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days",KRAKEN
S,USDT,S/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
SAGA,USDC,SAGA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SAGA,USDT,SAGA/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SAND,USDC,SAND/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
SAND,USDT,SAND/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
SEI,USDC,SEI/USDC,False,False,0,True,True,35,False,False,0,True,False,14,51,1,"BYBIT, BYBIT_days, BITVAVO, BITVAVO_days",BYBIT
SEI,USDT,SEI/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
SHELL,USDC,SHELL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SHELL,USDT,SHELL/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SHIB,USDC,SHIB/USDC,True,True,35,True,True,35,True,True,35,True,False,33,142,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN"
SHIB,USDT,SHIB/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
SLF,USDC,SLF/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SLF,USDT,SLF/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SOL,USDC,SOL/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
SOL,USDT,SOL/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
STEEM,USDC,STEEM/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
STEEM,USDT,STEEM/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
STRK,USDC,STRK/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
STRK,USDT,STRK/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
STX,USDC,STX/USDC,True,True,35,False,False,0,False,False,0,True,False,9,46,1,"OKX, OKX_days, BITVAVO, BITVAVO_days",OKX
STX,USDT,STX/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
SUI,USDC,SUI/USDC,True,True,35,True,True,35,False,False,0,True,True,35,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT, BITVAVO"
SUI,USDT,SUI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
SYN,USDC,SYN/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
SYN,USDT,SYN/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
T,USDC,T/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
T,USDT,T/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
TAO,USDC,TAO/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TAO,USDT,TAO/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
THE,USDC,THE/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
THE,USDT,THE/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
THETA,USDC,THETA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
THETA,USDT,THETA/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
TIA,USDC,TIA/USDC,True,True,35,True,True,35,False,False,0,True,True,35,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT, BITVAVO"
TIA,USDT,TIA/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
TLM,USDC,TLM/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TLM,USDT,TLM/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TNSR,USDC,TNSR/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TNSR,USDT,TNSR/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
TON,USDC,TON/USDC,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
TON,USDT,TON/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
TRB,USDC,TRB/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TRB,USDT,TRB/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
TRUMP,USDC,TRUMP/USDC,False,False,0,True,True,35,True,True,35,False,False,0,72,2,"BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","BYBIT, KRAKEN"
TRUMP,USDT,TRUMP/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
TRX,USDC,TRX/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
TRX,USDT,TRX/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
TST,USDC,TST/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TST,USDT,TST/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
TURBO,USDC,TURBO/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
TURBO,USDT,TURBO/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
TUT,USDC,TUT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
TUT,USDT,TUT/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
UNI,USDC,UNI/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
UNI,USDT,UNI/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
USUAL,USDC,USUAL/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
USUAL,USDT,USUAL/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
UTK,USDC,UTK/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
UTK,USDT,UTK/USDT,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
VANA,USDC,VANA/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
VANA,USDT,VANA/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
VANRY,USDC,VANRY/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
VANRY,USDT,VANRY/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
VELODROME,USDC,VELODROME/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
VELODROME,USDT,VELODROME/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
VET,USDC,VET/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
VET,USDT,VET/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
VIRTUAL,USDC,VIRTUAL/USDC,False,False,0,False,False,0,True,True,35,False,False,0,36,1,"KRAKEN, KRAKEN_days",KRAKEN
VIRTUAL,USDT,VIRTUAL/USDT,False,False,0,True,True,35,True,True,35,False,False,0,72,2,"BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","BYBIT, KRAKEN"
W,USDC,W/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
W,USDT,W/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
WCT,USDC,WCT/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
WCT,USDT,WCT/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
WIF,USDC,WIF/USDC,True,True,35,True,True,35,False,False,0,True,False,32,105,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
WIF,USDT,WIF/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
WLD,USDC,WLD/USDC,True,True,35,True,True,35,False,False,0,True,False,13,86,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
WLD,USDT,WLD/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
XLM,USDC,XLM/USDC,True,True,35,True,True,35,False,False,0,True,False,34,107,2,"OKX, OKX_days, BYBIT, BYBIT_days, BITVAVO, BITVAVO_days","OKX, BYBIT"
XLM,USDT,XLM/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
XRP,USDC,XRP/USDC,True,True,35,True,True,35,True,True,35,True,True,35,144,4,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days, BITVAVO, BITVAVO_days","OKX, BYBIT, KRAKEN, BITVAVO"
XRP,USDT,XRP/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
XTZ,USDC,XTZ/USDC,False,False,0,False,False,0,True,True,35,False,False,0,36,1,"KRAKEN, KRAKEN_days",KRAKEN
XTZ,USDT,XTZ/USDT,True,True,35,True,True,35,True,True,35,False,False,0,108,3,"OKX, OKX_days, BYBIT, BYBIT_days, KRAKEN, KRAKEN_days","OKX, BYBIT, KRAKEN"
YGG,USDC,YGG/USDC,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
YGG,USDT,YGG/USDT,True,True,35,False,False,0,False,False,0,False,False,0,36,1,"OKX, OKX_days",OKX
ZEN,USDC,ZEN/USDC,False,False,0,False,False,0,False,False,0,False,False,0,0,0,,
ZEN,USDT,ZEN/USDT,False,False,0,True,True,35,False,False,0,False,False,0,36,1,"BYBIT, BYBIT_days",BYBIT
ZK,USDC,ZK/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ZK,USDT,ZK/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ZRO,USDC,ZRO/USDC,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
ZRO,USDT,ZRO/USDT,True,True,35,True,True,35,False,False,0,False,False,0,72,2,"OKX, OKX_days, BYBIT, BYBIT_days","OKX, BYBIT"
