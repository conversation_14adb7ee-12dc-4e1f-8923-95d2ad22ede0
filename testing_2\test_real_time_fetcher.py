#!/usr/bin/env python
"""
Test script for the new real-time data fetching implementation.

This script tests the fetch_real_time_data function to ensure it properly
fetches only the missing data since the last cached timestamp.
"""

import os
import sys
import logging
import pandas as pd
import time
from datetime import datetime, timed<PERSON><PERSON>

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.incremental_fetcher import fetch_real_time_data
from src.utils import setup_logging

def test_real_time_fetcher():
    """Test the real-time data fetcher implementation."""
    setup_logging(level=logging.INFO)
    
    print("\n" + "=" * 70)
    print("TESTING REAL-TIME DATA FETCHER IMPLEMENTATION")
    print("=" * 70)
    
    # Test parameters
    exchange_id = 'binance'
    symbols = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC']
    timeframe = '1m'
    
    # First run - should fetch data from cache or fetch all if not in cache
    print("\nFIRST RUN - FETCHING DATA FROM CACHE OR FULL FETCH IF NOT AVAILABLE")
    data_dict = fetch_real_time_data(
        exchange_id=exchange_id,
        symbols=symbols,
        timeframe=timeframe,
        use_cache=True
    )
    
    # Print results
    if data_dict:
        print(f"\nSuccessfully fetched data for {len(data_dict)} assets:")
        for symbol, df in data_dict.items():
            if not df.empty:
                print(f"  - {symbol}: {len(df)} candles from {df.index.min()} to {df.index.max()}")
                print(f"    Last few candles:")
                print(df.tail(3))
            else:
                print(f"  - {symbol}: No data fetched")
    else:
        print("No data was fetched")
    
    # Wait a few minutes to ensure new candles are available
    wait_time = 2  # minutes
    print(f"\nWaiting {wait_time} minutes for new candles to be available...")
    time.sleep(wait_time * 60)
    
    # Second run - should only fetch new candles since the last run
    print("\n" + "=" * 70)
    print("SECOND RUN - SHOULD ONLY FETCH NEW CANDLES")
    print("=" * 70)
    
    data_dict_2 = fetch_real_time_data(
        exchange_id=exchange_id,
        symbols=symbols,
        timeframe=timeframe,
        use_cache=True
    )
    
    # Print results
    if data_dict_2:
        print(f"\nSecond run results for {len(data_dict_2)} assets:")
        for symbol, df in data_dict_2.items():
            if not df.empty:
                # Compare with first run
                if symbol in data_dict and not data_dict[symbol].empty:
                    first_run_last = data_dict[symbol].index.max()
                    second_run_last = df.index.max()
                    new_candles = len(df) - len(data_dict[symbol])
                    
                    print(f"  - {symbol}: {len(df)} total candles (added {new_candles} new candles)")
                    print(f"    First run last timestamp: {first_run_last}")
                    print(f"    Second run last timestamp: {second_run_last}")
                    
                    if new_candles > 0:
                        print(f"    New candles:")
                        # Show only the new candles
                        new_data = df[df.index > first_run_last]
                        print(new_data)
                else:
                    print(f"  - {symbol}: {len(df)} candles from {df.index.min()} to {df.index.max()}")
            else:
                print(f"  - {symbol}: No data fetched")
    else:
        print("No data was fetched in the second run")
    
    print("\nTest completed successfully")

if __name__ == "__main__":
    test_real_time_fetcher()
