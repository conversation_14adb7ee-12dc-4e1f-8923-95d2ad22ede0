#!/usr/bin/env python3
"""
Clean incomplete candles from cache files.

This script removes today's incomplete candle from all cached daily data files.
It's useful when you want to clean up cache files that contain incomplete candles
that were saved before the filtering fix was implemented.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime, timezone
import argparse
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.data_cache import (
    get_cache_path,
    get_cache_metadata_path,
    DEFAULT_CACHE_DIR
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def filter_incomplete_candles_from_file(
    cache_path: str,
    timeframe: str = '1d'
) -> bool:
    """
    Remove incomplete candles from a cache file.
    
    Args:
        cache_path: Path to the cache file
        timeframe: The timeframe (e.g., '1d', '1h')
    
    Returns:
        True if successful, False otherwise
    """
    try:
        if not os.path.exists(cache_path):
            logging.warning(f"Cache file does not exist: {cache_path}")
            return False
        
        # Read the cache file
        df = pd.read_csv(cache_path, index_col=0, parse_dates=True)
        
        if df.empty:
            logging.info(f"Cache file is empty: {cache_path}")
            return True
        
        original_count = len(df)
        
        # Get current UTC time
        current_time = pd.Timestamp.now(tz=timezone.utc)
        
        # Filter based on timeframe
        if timeframe == '1d':
            # For daily candles, exclude today's incomplete candle
            current_date = current_time.date()
            filtered_df = df[df.index.date < current_date]
        elif timeframe == '1h':
            # For hourly candles, exclude the current hour's candle
            current_hour_start = current_time.replace(minute=0, second=0, microsecond=0)
            filtered_df = df[df.index < current_hour_start]
        elif timeframe == '4h':
            # For 4-hour candles, exclude the current 4-hour block
            hour = (current_time.hour // 4) * 4
            current_4h_start = current_time.replace(hour=hour, minute=0, second=0, microsecond=0)
            filtered_df = df[df.index < current_4h_start]
        elif timeframe == '12h':
            # For 12-hour candles, exclude the current 12-hour block
            hour = (current_time.hour // 12) * 12
            current_12h_start = current_time.replace(hour=hour, minute=0, second=0, microsecond=0)
            filtered_df = df[df.index < current_12h_start]
        else:
            # For other timeframes, use a conservative approach and exclude the current day
            current_date = current_time.date()
            filtered_df = df[df.index.date < current_date]
        
        filtered_count = original_count - len(filtered_df)
        
        if filtered_count > 0:
            # Create backup
            backup_path = cache_path + '.backup'
            df.to_csv(backup_path)
            
            # Save filtered data
            filtered_df.to_csv(cache_path)
            
            # Remove backup if successful
            os.remove(backup_path)
            
            logging.info(f"Removed {filtered_count} incomplete candles from {cache_path}")
            return True
        else:
            logging.info(f"No incomplete candles found in {cache_path}")
            return True
            
    except Exception as e:
        logging.error(f"Error filtering incomplete candles from {cache_path}: {e}")
        return False

def clean_cache_directory(
    cache_dir: str = DEFAULT_CACHE_DIR,
    exchange_id: str = 'binance',
    timeframe: str = '1d',
    dry_run: bool = False
) -> None:
    """
    Clean incomplete candles from all cache files in a directory.
    
    Args:
        cache_dir: The cache directory
        exchange_id: The exchange ID (e.g., 'binance')
        timeframe: The timeframe (e.g., '1d', '1h')
        dry_run: If True, only show what would be cleaned without actually doing it
    """
    timeframe_dir = os.path.join(cache_dir, exchange_id, timeframe)
    
    if not os.path.exists(timeframe_dir):
        logging.warning(f"Timeframe directory does not exist: {timeframe_dir}")
        return
    
    # Find all CSV files in the timeframe directory
    csv_files = []
    for file in os.listdir(timeframe_dir):
        if file.endswith('.csv') and not file.endswith('_metadata.json'):
            csv_files.append(os.path.join(timeframe_dir, file))
    
    if not csv_files:
        logging.info(f"No cache files found in {timeframe_dir}")
        return
    
    logging.info(f"Found {len(csv_files)} cache files to process")
    
    success_count = 0
    failure_count = 0
    
    for csv_file in csv_files:
        symbol = os.path.basename(csv_file).replace('.csv', '').replace('_', '/')
        
        if dry_run:
            logging.info(f"[DRY RUN] Would clean incomplete candles from {symbol}")
            success_count += 1
        else:
            logging.info(f"Cleaning incomplete candles from {symbol}...")
            success = filter_incomplete_candles_from_file(csv_file, timeframe)
            
            if success:
                success_count += 1
            else:
                failure_count += 1
    
    # Summary
    if dry_run:
        logging.info(f"[DRY RUN] Would process {success_count} files")
    else:
        logging.info(f"Successfully processed {success_count} files")
        if failure_count > 0:
            logging.warning(f"Failed to process {failure_count} files")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Clean incomplete candles from cache files')
    parser.add_argument('--cache-dir', type=str, default=DEFAULT_CACHE_DIR,
                       help='Cache directory path')
    parser.add_argument('--exchange', type=str, default='binance',
                       help='Exchange ID (default: binance)')
    parser.add_argument('--timeframe', type=str, default='1d',
                       help='Timeframe (default: 1d)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be cleaned without actually doing it')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("CLEAN INCOMPLETE CANDLES FROM CACHE")
    print("=" * 60)
    print(f"Cache directory: {args.cache_dir}")
    print(f"Exchange: {args.exchange}")
    print(f"Timeframe: {args.timeframe}")
    print(f"Dry run: {args.dry_run}")
    print("=" * 60)
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No files will be modified")
    else:
        print("⚠️  LIVE MODE - Files will be modified")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Cancelled.")
            return
    
    clean_cache_directory(
        cache_dir=args.cache_dir,
        exchange_id=args.exchange,
        timeframe=args.timeframe,
        dry_run=args.dry_run
    )
    
    print("\n" + "=" * 60)
    print("CLEANUP COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
