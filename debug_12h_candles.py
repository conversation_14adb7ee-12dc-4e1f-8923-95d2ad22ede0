#!/usr/bin/env python3
"""
Debug the 12h candle structure to understand why midnight candles aren't being found.
"""

import sys
import os
import pandas as pd

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data

def debug_12h_candles():
    """Debug the 12h candle structure."""
    
    print("=" * 80)
    print("DEBUGGING 12H CANDLE STRUCTURE")
    print("=" * 80)
    
    # Fetch 12h data for BTC
    print("Fetching 12h data for BTC/USDT...")
    twelve_h_data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='12h',
        since='2024-04-16',
        use_cache=True
    )
    twelve_h_data = twelve_h_data_dict['BTC/USDT']
    
    print(f"Total 12h candles: {len(twelve_h_data)}")
    
    # Filter midnight candles (hour = 0)
    midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0]
    print(f"Midnight candles (hour=0): {len(midnight_candles)}")
    
    # Filter 12PM candles (hour = 12)
    twelve_pm_candles = twelve_h_data[twelve_h_data.index.hour == 12]
    print(f"12PM candles (hour=12): {len(twelve_pm_candles)}")
    
    print("\nFirst 10 12h candles:")
    print(f"{'Timestamp':<25} {'Hour':<5} {'Open':<10} {'Close':<10}")
    print("-" * 55)
    for i in range(min(10, len(twelve_h_data))):
        row = twelve_h_data.iloc[i]
        timestamp = twelve_h_data.index[i]
        print(f"{timestamp} {timestamp.hour:<5} {row['open']:<10.2f} {row['close']:<10.2f}")
    
    print("\nFirst 5 midnight candles:")
    print(f"{'Timestamp':<25} {'Hour':<5} {'Open':<10} {'Close':<10}")
    print("-" * 55)
    for i in range(min(5, len(midnight_candles))):
        row = midnight_candles.iloc[i]
        timestamp = midnight_candles.index[i]
        print(f"{timestamp} {timestamp.hour:<5} {row['open']:<10.2f} {row['close']:<10.2f}")
    
    # Test the date matching logic
    print("\nTesting date matching logic:")
    test_dates = ['2024-04-17', '2024-04-18', '2024-04-19', '2024-04-20', '2024-04-21']
    
    for date_str in test_dates:
        test_date = pd.Timestamp(date_str, tz='UTC')
        midnight_date = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        found_in_midnight = midnight_date in midnight_candles.index
        
        print(f"  {date_str}: Looking for {midnight_date}")
        print(f"    Found in midnight candles: {found_in_midnight}")
        
        if found_in_midnight:
            close_price = midnight_candles.loc[midnight_date, 'close']
            print(f"    12PM close price: ${close_price:.4f}")
        else:
            print(f"    Available dates around this time:")
            # Find dates close to this one
            nearby_dates = []
            for idx_date in midnight_candles.index:
                if abs((idx_date - midnight_date).days) <= 2:
                    nearby_dates.append(idx_date)
            
            for nearby_date in sorted(nearby_dates)[:5]:
                print(f"      {nearby_date}")
    
    return twelve_h_data, midnight_candles

if __name__ == "__main__":
    debug_12h_candles()
