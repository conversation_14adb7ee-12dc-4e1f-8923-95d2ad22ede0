#!/usr/bin/env python3
"""
Test script for execution timing comparison.

This script demonstrates how to run the same strategy with different execution timings:
1. Automatic execution at candle close (00:00 UTC)
2. Manual execution at 12 PM UTC

Usage:
    python test_execution_timing.py
"""

import logging
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_program import AllocationTester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_timing_comparison():
    """Run the same strategy with both execution timings and compare results."""
    
    # Common parameters for both tests
    common_params = {
        'timeframe': '1d',
        'mtpi_timeframe': '1d', 
        'analysis_start_date': '2024-01-01',
        'n_assets': 2,
        'transaction_fee_rate': 0.001,
        'selected_assets': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
        'use_cache': True,
        'initial_capital': 10000,
        'wait_for_confirmed_signals': True,
        'use_mtpi': True,
        'use_weighted_allocation': False,
        'weights': None,
        'enable_rebalancing': False,
        'rebalance_threshold': 0.05
    }
    
    print("=" * 80)
    print("EXECUTION TIMING COMPARISON STUDY")
    print("=" * 80)
    print()
    
    # Test 1: Automatic execution at candle close
    print("🤖 Running AUTOMATIC execution (candle close at 00:00 UTC)...")
    print("-" * 60)
    
    tester_auto = AllocationTester(
        **common_params,
        execution_timing='candle_close'
    )
    
    try:
        tester_auto.fetch_data()
        tester_auto.fetch_mtpi_signals()
        tester_auto.calculate_scores()
        tester_auto.run_strategy_with_detailed_logging()
        tester_auto.analyze_allocation()
        
        auto_results = {
            'total_return': tester_auto.strategy_metrics.get('total_increase', 0),
            'sharpe_ratio': tester_auto.strategy_metrics.get('sharpe_ratio', 0),
            'max_drawdown': tester_auto.strategy_metrics.get('max_drawdown', 0),
            'num_trades': tester_auto.strategy_metrics.get('num_trades', 0),
            'final_equity': tester_auto.strategy_equity_curve.iloc[-1] if not tester_auto.strategy_equity_curve.empty else 0
        }
        
        print(f"✅ Automatic execution completed successfully!")
        print(f"   Total Return: {auto_results['total_return']:.2f}%")
        print(f"   Sharpe Ratio: {auto_results['sharpe_ratio']:.2f}")
        print(f"   Max Drawdown: {auto_results['max_drawdown']:.2f}%")
        print(f"   Number of Trades: {auto_results['num_trades']}")
        print(f"   Final Equity: ${auto_results['final_equity']:,.2f}")
        
    except Exception as e:
        print(f"❌ Error in automatic execution: {e}")
        return
    
    print()
    
    # Test 2: Manual execution at 12 PM UTC
    print("👤 Running MANUAL execution (12 PM UTC)...")
    print("-" * 60)
    
    tester_manual = AllocationTester(
        **common_params,
        execution_timing='manual_12pm'
    )
    
    try:
        tester_manual.fetch_data()
        tester_manual.fetch_mtpi_signals()
        tester_manual.calculate_scores()
        tester_manual.run_strategy_with_detailed_logging()
        tester_manual.analyze_allocation()
        
        manual_results = {
            'total_return': tester_manual.strategy_metrics.get('total_increase', 0),
            'sharpe_ratio': tester_manual.strategy_metrics.get('sharpe_ratio', 0),
            'max_drawdown': tester_manual.strategy_metrics.get('max_drawdown', 0),
            'num_trades': tester_manual.strategy_metrics.get('num_trades', 0),
            'final_equity': tester_manual.strategy_equity_curve.iloc[-1] if not tester_manual.strategy_equity_curve.empty else 0
        }
        
        print(f"✅ Manual execution completed successfully!")
        print(f"   Total Return: {manual_results['total_return']:.2f}%")
        print(f"   Sharpe Ratio: {manual_results['sharpe_ratio']:.2f}")
        print(f"   Max Drawdown: {manual_results['max_drawdown']:.2f}%")
        print(f"   Number of Trades: {manual_results['num_trades']}")
        print(f"   Final Equity: ${manual_results['final_equity']:,.2f}")
        
    except Exception as e:
        print(f"❌ Error in manual execution: {e}")
        return
    
    print()
    print("=" * 80)
    print("COMPARISON RESULTS")
    print("=" * 80)
    
    # Calculate differences
    return_diff = manual_results['total_return'] - auto_results['total_return']
    sharpe_diff = manual_results['sharpe_ratio'] - auto_results['sharpe_ratio']
    drawdown_diff = manual_results['max_drawdown'] - auto_results['max_drawdown']
    equity_diff = manual_results['final_equity'] - auto_results['final_equity']
    equity_diff_pct = (equity_diff / auto_results['final_equity']) * 100 if auto_results['final_equity'] != 0 else 0
    
    print(f"📊 Performance Impact of Manual vs Automatic Execution:")
    print(f"   Total Return Difference: {return_diff:+.2f}% ({'+' if return_diff >= 0 else ''}{'better' if return_diff >= 0 else 'worse'})")
    print(f"   Sharpe Ratio Difference: {sharpe_diff:+.2f} ({'+' if sharpe_diff >= 0 else ''}{'better' if sharpe_diff >= 0 else 'worse'})")
    print(f"   Max Drawdown Difference: {drawdown_diff:+.2f}% ({'+' if drawdown_diff <= 0 else ''}{'better' if drawdown_diff <= 0 else 'worse'})")
    print(f"   Final Equity Difference: ${equity_diff:+,.2f} ({equity_diff_pct:+.2f}%)")
    print()
    
    if return_diff < 0:
        print(f"💡 INSIGHT: Manual execution underperforms by {abs(return_diff):.2f}%")
        print(f"   This represents the cost of the 12-hour execution delay.")
        print(f"   Automation provides ${abs(equity_diff):,.2f} additional value.")
    elif return_diff > 0:
        print(f"💡 INSIGHT: Manual execution outperforms by {return_diff:.2f}%")
        print(f"   This suggests timing luck or market conditions favor delayed execution.")
        print(f"   Manual execution provides ${equity_diff:,.2f} additional value.")
    else:
        print(f"💡 INSIGHT: Both execution timings perform equally.")
    
    print()
    print("🎯 CONCLUSION:")
    if abs(return_diff) < 1.0:
        print("   Execution timing has minimal impact on performance (<1% difference).")
    elif abs(return_diff) < 5.0:
        print("   Execution timing has moderate impact on performance (1-5% difference).")
    else:
        print("   Execution timing has significant impact on performance (>5% difference).")
    
    print()
    print("✨ You can now run your regular main_program.py with --execution-timing parameter:")
    print("   python main_program.py --execution-timing candle_close    # Automatic")
    print("   python main_program.py --execution-timing manual_12pm     # Manual")

if __name__ == "__main__":
    run_timing_comparison()
