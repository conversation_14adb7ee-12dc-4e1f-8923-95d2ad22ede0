#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debug AAVE/TRX PGO Values

This script prints the exact PGO values for the AAVE/TRX pair to understand
why AAVE shows a long signal visually but loses in the scoring system.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.indicators.ratio_indicators import calculate_ratio_pgo_with_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_aave_trx_pgo():
    """Debug the exact PGO values for AAVE/TRX pair."""
    
    print("AAVE/TRX PGO Debug Analysis")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    settings = config.get('settings', {})
    
    # Get PGO parameters from config
    pgo_length = settings.get('pgo_length', 35)
    upper_threshold = settings.get('pgo_upper_threshold', 1.1)
    lower_threshold = settings.get('pgo_lower_threshold', -0.58)
    
    print(f"PGO Parameters: length={pgo_length}, upper={upper_threshold}, lower={lower_threshold}")
    
    # Fetch data for AAVE and TRX
    exchange_id = config.get('exchange', 'binance')
    timeframe = settings.get('timeframe', '1d')
    
    # Use recent data (last 120 days for warmup + recent period)
    data_start_date = '2025-02-10'  # Same as debugger
    
    assets = ['AAVE/USDT', 'TRX/USDT']
    print(f"Fetching data for {assets} since {data_start_date}")
    
    data_dict = fetch_ohlcv_data(
        exchange_id=exchange_id,
        symbols=assets,
        timeframe=timeframe,
        since=data_start_date,
        use_cache=True
    )
    
    if not data_dict or 'AAVE/USDT' not in data_dict or 'TRX/USDT' not in data_dict:
        print("ERROR: Failed to fetch data for AAVE and TRX")
        return
    
    # Get price series
    aave_price = data_dict['AAVE/USDT']['close']
    trx_price = data_dict['TRX/USDT']['close']
    
    print(f"AAVE data: {len(aave_price)} candles from {aave_price.index[0]} to {aave_price.index[-1]}")
    print(f"TRX data: {len(trx_price)} candles from {trx_price.index[0]} to {trx_price.index[-1]}")
    
    # Calculate AAVE/TRX ratio PGO
    print(f"\nCalculating AAVE/TRX ratio PGO...")
    ratio_values, pgo_values, signal_values = calculate_ratio_pgo_with_signal(
        aave_price, trx_price, pgo_length, upper_threshold, lower_threshold
    )
    
    print(f"Calculated {len(pgo_values)} PGO values")
    
    # Print the last 10 values to see the trend
    print(f"\nLast 10 PGO values for AAVE/TRX:")
    print("=" * 80)
    print(f"{'Date':<12} {'Ratio':<12} {'PGO':<12} {'Signal':<8} {'Status'}")
    print("-" * 80)
    
    for i in range(-10, 0):
        if i < -len(pgo_values):
            continue
            
        date = pgo_values.index[i]
        ratio = ratio_values.iloc[i] if i < len(ratio_values) else np.nan
        pgo = pgo_values.iloc[i]
        signal = signal_values.iloc[i]
        
        # Determine status based on thresholds
        if pgo >= upper_threshold:
            status = "LONG"
        elif pgo <= lower_threshold:
            status = "SHORT"
        else:
            status = "NEUTRAL"
            
        print(f"{date.strftime('%Y-%m-%d'):<12} {ratio:<12.6f} {pgo:<12.6f} {signal:<8.0f} {status}")
    
    # Focus on the latest value
    latest_date = pgo_values.index[-1]
    latest_ratio = ratio_values.iloc[-1] if len(ratio_values) > 0 else np.nan
    latest_pgo = pgo_values.iloc[-1]
    latest_signal = signal_values.iloc[-1]

    print(f"\n" + "=" * 80)
    print("LATEST VALUES (Most Recent Day)")
    print("=" * 80)
    print(f"Date: {latest_date.strftime('%Y-%m-%d')}")
    print(f"AAVE/TRX Ratio: {latest_ratio:.6f}")
    print(f"PGO Value: {latest_pgo:.6f}")
    print(f"Signal: {latest_signal:.0f}")
    print(f"Upper Threshold: {upper_threshold}")
    print(f"Lower Threshold: {lower_threshold}")

    # Get the actual OHLC values for the latest day
    aave_latest = data_dict['AAVE/USDT'].iloc[-1]
    trx_latest = data_dict['TRX/USDT'].iloc[-1]

    print(f"\nRAW OHLC DATA FOR LATEST DAY ({latest_date.strftime('%Y-%m-%d')}):")
    print("-" * 60)
    print(f"AAVE/USDT - Open: {aave_latest['open']:.6f}, High: {aave_latest['high']:.6f}")
    print(f"           Low: {aave_latest['low']:.6f}, Close: {aave_latest['close']:.6f}")
    print(f"TRX/USDT  - Open: {trx_latest['open']:.6f}, High: {trx_latest['high']:.6f}")
    print(f"           Low: {trx_latest['low']:.6f}, Close: {trx_latest['close']:.6f}")

    # Calculate the ratio manually
    manual_ratio = aave_latest['close'] / trx_latest['close']
    print(f"\nManual Ratio Calculation: {aave_latest['close']:.6f} / {trx_latest['close']:.6f} = {manual_ratio:.6f}")
    print(f"System Ratio Calculation: {latest_ratio:.6f}")
    print(f"Ratio Difference: {abs(manual_ratio - latest_ratio):.8f}")

    # Check if we're using the right date
    print(f"\nDATE VERIFICATION:")
    print(f"Latest AAVE data date: {data_dict['AAVE/USDT'].index[-1]}")
    print(f"Latest TRX data date: {data_dict['TRX/USDT'].index[-1]}")
    print(f"PGO calculation date: {latest_date}")

    # Compare with TradingView expectation
    tradingview_pgo = -0.52  # From your screenshot
    pgo_difference = latest_pgo - tradingview_pgo
    print(f"\nTRADINGVIEW COMPARISON:")
    print(f"Your System PGO: {latest_pgo:.6f}")
    print(f"TradingView PGO: {tradingview_pgo:.6f}")
    print(f"Difference: {pgo_difference:.6f}")

    if abs(pgo_difference) > 0.1:
        print("⚠️  SIGNIFICANT DISCREPANCY DETECTED!")
        print("   This could be due to:")
        print("   1. Different data sources")
        print("   2. Different calculation periods")
        print("   3. Different PGO calculation methods")
        print("   4. Data synchronization issues")
    else:
        print("✅ Values are reasonably close")
    
    # Determine signal status
    if latest_pgo >= upper_threshold:
        signal_status = "LONG (AAVE outperforming TRX)"
        distance_to_threshold = latest_pgo - upper_threshold
        print(f"Status: {signal_status}")
        print(f"Distance above upper threshold: +{distance_to_threshold:.6f}")
    elif latest_pgo <= lower_threshold:
        signal_status = "SHORT (TRX outperforming AAVE)"
        distance_to_threshold = lower_threshold - latest_pgo
        print(f"Status: {signal_status}")
        print(f"Distance below lower threshold: -{distance_to_threshold:.6f}")
    else:
        signal_status = "NEUTRAL (No clear trend)"
        distance_to_upper = upper_threshold - latest_pgo
        distance_to_lower = latest_pgo - lower_threshold
        print(f"Status: {signal_status}")
        print(f"Distance to upper threshold: {distance_to_upper:.6f}")
        print(f"Distance to lower threshold: {distance_to_lower:.6f}")
    
    # Now calculate TRX/AAVE (inverse) for comparison
    print(f"\n" + "=" * 80)
    print("INVERSE COMPARISON: TRX/AAVE")
    print("=" * 80)
    
    # Calculate TRX/AAVE ratio PGO
    trx_aave_ratio, trx_aave_pgo, trx_aave_signal = calculate_ratio_pgo_with_signal(
        trx_price, aave_price, pgo_length, upper_threshold, lower_threshold
    )
    
    latest_trx_aave_pgo = trx_aave_pgo.iloc[-1]
    latest_trx_aave_signal = trx_aave_signal.iloc[-1]
    
    print(f"TRX/AAVE PGO Value: {latest_trx_aave_pgo:.6f}")
    print(f"TRX/AAVE Signal: {latest_trx_aave_signal:.0f}")
    
    # Compare with the 1 - signal inversion used in scoring
    inverted_signal = 1 - latest_signal
    print(f"AAVE/TRX Signal: {latest_signal:.0f}")
    print(f"Inverted Signal (1 - AAVE/TRX): {inverted_signal:.0f}")
    print(f"Direct TRX/AAVE Signal: {latest_trx_aave_signal:.0f}")
    
    if inverted_signal == latest_trx_aave_signal:
        print("✅ Signal inversion is working correctly")
    else:
        print("❌ Signal inversion mismatch!")
    
    # Explain the scoring result
    print(f"\n" + "=" * 80)
    print("SCORING EXPLANATION")
    print("=" * 80)
    
    if latest_signal == 1:
        print("AAVE/TRX signal = 1 → AAVE is outperforming TRX")
        print("In scoring matrix: AAVE beats TRX (AAVE gets +1 point)")
        print("But TRX/AAVE signal = 0 → TRX is NOT outperforming AAVE")
    else:
        print("AAVE/TRX signal = 0 → AAVE is NOT outperforming TRX")
        print("In scoring matrix: TRX beats AAVE (TRX gets +1 point)")
        print("TRX/AAVE signal = 1 → TRX is outperforming AAVE")
    
    print(f"\nThis explains why the scoring debugger shows:")
    if latest_signal == 1:
        print("- AAVE beats TRX in the comparison matrix")
        print("- AAVE should have higher score than TRX")
    else:
        print("- TRX beats AAVE in the comparison matrix")
        print("- TRX has higher score than AAVE")

    # Additional debugging: Check PGO calculation details
    print(f"\n" + "=" * 80)
    print("PGO CALCULATION DEBUGGING")
    print("=" * 80)

    # Import the PGO calculation function to examine it
    from src.indicators.pgo_score import calculate_pgo

    # Calculate PGO step by step
    print(f"Calculating PGO with length={pgo_length} on AAVE/TRX ratio...")

    # Create a DataFrame with the ratio for PGO calculation
    ratio_df = pd.DataFrame({
        'open': ratio_values,
        'high': ratio_values,  # Simplified - using close as high
        'low': ratio_values,   # Simplified - using close as low
        'close': ratio_values,
        'volume': pd.Series([1000] * len(ratio_values), index=ratio_values.index)
    })

    # Calculate PGO directly
    direct_pgo = calculate_pgo(ratio_df, length=pgo_length)

    print(f"Direct PGO calculation - Latest value: {direct_pgo.iloc[-1]:.6f}")
    print(f"Ratio-based PGO calculation - Latest value: {latest_pgo:.6f}")
    print(f"Difference: {abs(direct_pgo.iloc[-1] - latest_pgo):.8f}")

    # Show the last few values for comparison
    print(f"\nLast 5 direct PGO values:")
    for i in range(-5, 0):
        if i < -len(direct_pgo):
            continue
        date = direct_pgo.index[i]
        value = direct_pgo.iloc[i]
        print(f"  {date.strftime('%Y-%m-%d')}: {value:.6f}")

    # Check if there are any NaN values that might affect calculation
    nan_count = pgo_values.isna().sum()
    if nan_count > 0:
        print(f"\n⚠️  Found {nan_count} NaN values in PGO calculation")

    # Check the data range used for PGO calculation
    pgo_start_date = pgo_values.index[0]
    pgo_end_date = pgo_values.index[-1]
    print(f"\nPGO calculation period: {pgo_start_date.strftime('%Y-%m-%d')} to {pgo_end_date.strftime('%Y-%m-%d')}")
    print(f"Total days in calculation: {len(pgo_values)}")
    print(f"PGO length parameter: {pgo_length}")

    if len(pgo_values) < pgo_length + 10:
        print(f"⚠️  WARNING: Limited data for PGO calculation!")
        print(f"   Available: {len(pgo_values)} days, Recommended: {pgo_length + 10}+ days")

if __name__ == "__main__":
    debug_aave_trx_pgo()
