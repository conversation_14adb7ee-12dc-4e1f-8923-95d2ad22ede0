# Portfolio Drift Rebalancing Implementation

## Overview

We have successfully implemented automatic portfolio rebalancing based on portfolio drift for the Asset Rotation Strategy. This addresses the critical gap where the strategy previously only rebalanced when asset rankings changed, but not when portfolio weights drifted from target allocations due to market movements.

## Problem Solved

### Before Implementation
- **Issue**: If ETH was allocated 80% and BTC 20%, and ETH gained significantly (e.g., ETH becomes 90%, BTC becomes 10%), the strategy would NOT rebalance back to the target 80%/20% allocation.
- **Result**: Portfolio could drift significantly from target weights, reducing the effectiveness of the weighted allocation strategy.

### After Implementation
- **Solution**: The strategy now automatically detects portfolio drift and rebalances when the drift exceeds a configurable threshold.
- **Result**: Portfolio maintains target weights more consistently, improving strategy performance and risk management.

## Key Features Implemented

### 1. Portfolio Drift Detection
```python
# Calculate current portfolio weights after market movements
current_portfolio_value = equity_curve.iloc[i-1] if i > start_idx else self.initial_capital

# Apply today's returns to get current market values
current_market_values = {}
total_current_value = 0.0

for asset, weight in current_holdings.items():
    if asset in asset_returns and today in asset_returns[asset].index:
        asset_return = asset_returns[asset].loc[today]
        current_value = (current_portfolio_value * weight) * (1 + asset_return)
        current_market_values[asset] = current_value
        total_current_value += current_value

# Calculate current weights based on market values
current_weights = {}
if total_current_value > 0:
    for asset, value in current_market_values.items():
        current_weights[asset] = value / total_current_value
```

### 2. Drift Threshold Checking
```python
# Check for weight drift
max_drift = 0.0
for asset in new_holdings.keys():
    if asset in current_weights:
        target_weight = new_holdings[asset]
        current_weight = current_weights[asset]
        drift = abs(current_weight - target_weight)
        max_drift = max(max_drift, drift)

if max_drift > self.rebalance_threshold:
    portfolio_needs_rebalancing = True
    rebalancing_reason = f"Portfolio drift ({max_drift:.1%}) exceeds threshold ({self.rebalance_threshold:.1%})"
```

### 3. Enable/Disable Rebalancing
- **Command Line Parameter**: `--enable-rebalancing` (disabled by default)
- **Configuration File**: `enable_rebalancing: false` in `config/settings.yaml`
- **Background Service**: Automatically uses the configured setting

### 4. Configurable Rebalance Threshold
- **Command Line Parameter**: `--rebalance-threshold 0.05` (default: 5%)
- **Configuration File**: `rebalance_threshold: 0.05` in `config/settings.yaml`
- **Background Service**: Automatically uses the configured threshold

### 5. Enhanced Transaction Cost Calculation
```python
# For portfolio drift rebalancing, force rebalancing even if assets haven't changed
if portfolio_needs_rebalancing and not assets_to_sell and not assets_to_buy:
    # All assets are the same but weights need adjustment
    assets_with_changed_weights = set(new_holdings.keys())

# Calculate transaction costs including weight changes
if assets_to_sell or assets_to_buy or assets_with_changed_weights:
    transaction_cost = (len(assets_to_sell) + len(assets_to_buy) + len(assets_with_changed_weights)) * self.transaction_fee_rate
```

### 6. Detailed Logging and Tracking
```python
allocation_entry = {
    'date': today,
    'mtpi_signal': mtpi_signal_value,
    'top_assets': top_assets,
    'current_holdings': list(current_holdings.keys()) if current_holdings else [],
    'portfolio_return': portfolio_return,
    'transaction_cost': transaction_cost,
    'equity_value': equity_curve.iloc[i],
    'rebalanced': portfolio_needs_rebalancing,
    'rebalancing_reason': rebalancing_reason if portfolio_needs_rebalancing else ""
}
```

## Usage Examples

### Command Line Usage
```bash
# Traditional mode (no automatic rebalancing) - DEFAULT BEHAVIOR
py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --timeframe 1d --n-assets 2 --weighted --weights 0.8 0.2

# Enable rebalancing with conservative threshold (10%)
py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --timeframe 1d --n-assets 2 --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.10

# Enable rebalancing with standard threshold (5%)
py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --timeframe 1d --n-assets 2 --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.05

# Enable rebalancing with aggressive threshold (2%)
py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --timeframe 1d --n-assets 2 --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.02
```

### Configuration File
```yaml
settings:
  use_weighted_allocation: true
  weights: [0.8, 0.2]
  enable_rebalancing: false  # Set to true to enable automatic rebalancing
  rebalance_threshold: 0.05  # 5% drift threshold
  n_assets: 2
  timeframe: 1d
```

## Rebalancing Scenarios

### Scenario 1: Portfolio Drift Rebalancing
**Initial State**: ETH=80% ($8,000), BTC=20% ($2,000)
**After Market Movement**: ETH gains 12.5%, BTC loses 50%
- ETH: $8,000 → $9,000 (90% of portfolio)
- BTC: $2,000 → $1,000 (10% of portfolio)
- **Drift**: ETH drift = 10%, BTC drift = 10%
- **Action**: If threshold is 5%, rebalance back to 80%/20%

### Scenario 2: Asset Selection Change
**Current Holdings**: ETH=80%, BTC=20%
**New Top Assets**: SOL=80%, ETH=20% (based on scores)
- **Action**: Sell BTC, buy SOL, adjust ETH allocation

### Scenario 3: No Rebalancing Needed
**Current Holdings**: ETH=80%, BTC=20%
**After Small Movement**: ETH=82%, BTC=18%
- **Drift**: 2% for both assets
- **Action**: No rebalancing if threshold is 5%

## Benefits

1. **Improved Risk Management**: Maintains target risk exposure by keeping weights close to intended allocations
2. **Better Performance**: Prevents portfolio from becoming overly concentrated in outperforming assets
3. **Configurable Control**: Users can adjust rebalancing frequency via threshold setting
4. **Cost Awareness**: Tracks transaction costs associated with rebalancing
5. **Transparency**: Detailed logging shows when and why rebalancing occurs

## Integration Points

### Files Modified
1. **main_program.py**: Core rebalancing logic, command line parameter, AllocationTester class
2. **background_service.py**: Integration with real-time strategy execution
3. **config/settings.yaml**: Default configuration setting
4. **test_rebalancing.py**: Test script demonstrating functionality

### Backward Compatibility
- Default threshold of 5% provides reasonable rebalancing frequency
- Existing strategies without weights continue to work unchanged
- New parameter is optional with sensible defaults

## Testing

Run the test script to see the rebalancing functionality in action:
```bash
py test_rebalancing.py
```

This will demonstrate different rebalancing thresholds and show how they affect strategy behavior and transaction costs.
