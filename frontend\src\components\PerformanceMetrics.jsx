import React from 'react'
import {
  Box,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Flex,
  Text,
  Divider,
  Tooltip,
  Icon,
} from '@chakra-ui/react'
import { InfoIcon } from '@chakra-ui/icons'

const MetricCard = ({ title, value, helpText, isPercentage, isPositive, tooltip }) => {
  const formattedValue = isPercentage ? `${value.toFixed(2)}%` : value.toFixed(2)

  return (
    <Stat
      px={4}
      py={2}
      shadow="sm"
      border="1px"
      borderColor="gray.200"
      rounded="lg"
      bg="white"
    >
      <Flex justify="space-between">
        <StatLabel fontWeight="medium" isTruncated>
          {title}
        </StatLabel>
        {tooltip && (
          <Tooltip label={tooltip} placement="top">
            <Icon as={InfoIcon} color="gray.400" w={3} h={3} />
          </Tooltip>
        )}
      </Flex>
      <StatNumber fontSize="xl" fontWeight="semibold">
        {formattedValue}
      </StatNumber>
      {helpText && (
        <StatHelpText>
          {isPositive !== undefined && (
            <StatArrow type={isPositive ? 'increase' : 'decrease'} />
          )}
          {helpText}
        </StatHelpText>
      )}
    </Stat>
  )
}

const PerformanceMetrics = ({ metrics }) => {
  if (!metrics) {
    return (
      <Box p={4} bg="white" borderRadius="lg" boxShadow="sm">
        <Text>No performance metrics available</Text>
      </Box>
    )
  }

  // Check if metrics is in the new format (with strategy and buy_hold_metrics)
  const isNewFormat = metrics.strategy && typeof metrics.strategy === 'object';

  // Use the appropriate metrics object based on the format
  const strategyMetrics = isNewFormat ? metrics.strategy : metrics;

  // Log the metrics structure for debugging
  console.log('Performance metrics structure:', metrics);
  if (isNewFormat) {
    console.log('Using new format metrics with strategy object');
    console.log('Strategy metrics:', strategyMetrics);
    console.log('Buy and hold metrics:', metrics.buy_hold_metrics);
  } else {
    console.log('Using legacy format metrics');
  }

  return (
    <Box p={4} bg="white" borderRadius="lg" boxShadow="sm">
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Performance Metrics
      </Text>

      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={6}>
        <MetricCard
          title="Sharpe Ratio"
          value={strategyMetrics.sharpe_ratio}
          tooltip="Risk-adjusted return (higher is better)"
          isPositive={strategyMetrics.sharpe_ratio > 1}
          helpText={strategyMetrics.sharpe_ratio > 1 ? "Good" : "Poor"}
        />
        <MetricCard
          title="Sortino Ratio"
          value={strategyMetrics.sortino_ratio}
          tooltip="Downside risk-adjusted return (higher is better)"
          isPositive={strategyMetrics.sortino_ratio > 1}
          helpText={strategyMetrics.sortino_ratio > 1 ? "Good" : "Poor"}
        />
        <MetricCard
          title="Omega Ratio"
          value={strategyMetrics.omega_ratio}
          tooltip="Probability-weighted ratio of gains vs. losses"
          isPositive={strategyMetrics.omega_ratio > 1}
          helpText={strategyMetrics.omega_ratio > 1 ? "Good" : "Poor"}
        />
        <MetricCard
          title="Max Drawdown"
          value={strategyMetrics.max_drawdown}
          isPercentage={true}
          tooltip="Maximum observed loss from a peak to a trough"
          isPositive={false}
        />
      </SimpleGrid>

      <Divider my={4} />

      <Text fontSize="md" fontWeight="semibold" mb={3}>
        Return Metrics
      </Text>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
        <MetricCard
          title="Mean Return"
          value={strategyMetrics.annualized_mean_return}
          isPercentage={true}
          tooltip="Annualized mean of all returns"
          isPositive={strategyMetrics.annualized_mean_return > 0}
        />
        <MetricCard
          title="Mean Positive Return"
          value={strategyMetrics.mean_positive_return}
          isPercentage={true}
          tooltip="Mean of positive returns only"
          isPositive={true}
        />
        <MetricCard
          title="Mean Negative Return"
          value={strategyMetrics.mean_negative_return}
          isPercentage={true}
          tooltip="Mean of negative returns only"
          isPositive={false}
        />
      </SimpleGrid>

      <Divider my={4} />

      <Text fontSize="md" fontWeight="semibold" mb={3}>
        Volatility Metrics
      </Text>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
        <MetricCard
          title="Standard Deviation"
          value={strategyMetrics.annualized_std_dev}
          isPercentage={true}
          tooltip="Annualized standard deviation of returns"
        />
        <MetricCard
          title="Positive Volatility"
          value={strategyMetrics.std_dev_positive}
          isPercentage={true}
          tooltip="Standard deviation of positive returns"
        />
        <MetricCard
          title="Negative Volatility"
          value={strategyMetrics.std_dev_negative}
          isPercentage={true}
          tooltip="Standard deviation of negative returns"
        />
      </SimpleGrid>

      {/* Buy and Hold Metrics Section */}
      {isNewFormat && metrics.buy_hold_metrics && Object.keys(metrics.buy_hold_metrics).length > 0 && (
        <>
          <Divider my={4} />

          <Text fontSize="md" fontWeight="semibold" mb={3}>
            Buy and Hold Metrics
          </Text>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
            {Object.entries(metrics.buy_hold_metrics).map(([asset, assetMetrics]) => (
              assetMetrics && (
                <Box
                  key={asset}
                  p={3}
                  border="1px"
                  borderColor="gray.200"
                  borderRadius="md"
                  boxShadow="sm"
                >
                  <Text fontWeight="bold" mb={2}>{asset}</Text>
                  <Text fontSize="sm">
                    Sharpe: {assetMetrics.sharpe_ratio?.toFixed(2) || 'N/A'}
                  </Text>
                  <Text fontSize="sm">
                    Max Drawdown: {assetMetrics.max_drawdown ? `${assetMetrics.max_drawdown.toFixed(2)}%` : 'N/A'}
                  </Text>
                  <Text fontSize="sm">
                    Total Return: {assetMetrics.total_increase ? `${assetMetrics.total_increase.toFixed(2)}%` : 'N/A'}
                  </Text>
                </Box>
              )
            ))}
          </SimpleGrid>
        </>
      )}
    </Box>
  )
}

export default PerformanceMetrics
