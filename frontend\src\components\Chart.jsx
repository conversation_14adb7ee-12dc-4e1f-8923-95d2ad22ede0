import React, { useEffect, useRef } from 'react'
import { Box, useColorModeValue } from '@chakra-ui/react'
import { createChart } from 'lightweight-charts'

const Chart = ({ data, asset, height = 400 }) => {
  const chartContainerRef = useRef(null)
  const chartRef = useRef(null)
  const seriesRef = useRef(null)

  const bgColor = useColorModeValue('white', 'gray.800')
  const textColor = useColorModeValue('black', 'white')
  const gridColor = useColorModeValue('rgba(0, 0, 0, 0.06)', 'rgba(255, 255, 255, 0.06)')

  useEffect(() => {
    if (!chartContainerRef.current || !data || data.length === 0) return

    // Clean up previous chart if it exists
    if (chartRef.current) {
      chartRef.current.remove()
      chartRef.current = null
      seriesRef.current = null
    }

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: height,
      layout: {
        background: { color: bgColor },
        textColor: textColor,
      },
      grid: {
        vertLines: { color: gridColor },
        horzLines: { visible: false },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
      },
      crosshair: {
        mode: 0,
      },
    })

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    })

    // Format data for the chart
    const formattedData = data.map(item => ({
      time: new Date(item.timestamp).getTime() / 1000,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
    }))

    candlestickSeries.setData(formattedData)

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    })

    const volumeData = data.map(item => ({
      time: new Date(item.timestamp).getTime() / 1000,
      value: item.volume,
      color: item.close >= item.open ? '#26a69a' : '#ef5350',
    }))

    volumeSeries.setData(volumeData)

    // Fit content
    chart.timeScale().fitContent()

    // Handle window resize
    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    // Save references
    chartRef.current = chart
    seriesRef.current = { candlestickSeries, volumeSeries }

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
        seriesRef.current = null
      }
    }
  }, [data, bgColor, textColor, gridColor, height])

  return (
    <Box
      ref={chartContainerRef}
      w="100%"
      h={`${height}px`}
      borderRadius="md"
      overflow="hidden"
      boxShadow="sm"
      bg={bgColor}
    />
  )
}

export default Chart
