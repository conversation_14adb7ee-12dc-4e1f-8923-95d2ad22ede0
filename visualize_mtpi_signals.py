#!/usr/bin/env python3
"""
MTPI Signal Visualization Script

This script creates visual plots showing:
1. BTC/USDT price chart with bullish/bearish periods clearly marked
2. Binary aggregated MTPI signal plot
3. Individual indicator signals for analysis

The visualization helps validate MTPI signal generation and shows clear
bullish/bearish period indicators as requested.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import logging

from src.MTPI_signal_handler import (
    load_mtpi_multi_indicator_config,
    fetch_mtpi_signal_from_config
)
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.pgo_score import generate_pgo_score_signal
from src.indicators.bb_score import generate_bb_score_signal
from src.indicators.dwma_score import generate_dwma_signal
from src.indicators.median_score import generate_median_score_signal
from src.indicators.aad_score import generate_aad_score_signal
from src.indicators.dynamic_ema_score import generate_dynamic_ema_score_signal
from src.indicators.quantile_dema_score import generate_quantile_dema_score_signal
from src.indicators.advanced_indicators import generate_dema_super_score_signal, generate_dpsd_score_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_mtpi_signals_series(df, config):
    """
    Calculate MTPI signals and continuous scores over the entire time series.

    Args:
        df: DataFrame with OHLCV data
        config: MTPI configuration

    Returns:
        tuple: (signals_series, scores_series) - Binary signals and continuous MTPI scores
    """
    try:
        from src.MTPI_signal_handler import combine_multi_signals

        enabled_indicators = config.get('enabled_indicators', [])
        combination_method = config.get('combination_method', 'consensus')
        long_threshold = config.get('long_threshold', 0.1)
        short_threshold = config.get('short_threshold', -0.1)

        logging.info(f"Calculating signals for {len(df)} candles with {len(enabled_indicators)} indicators")

        # Calculate individual indicator signals for the entire series
        all_signals = {}

        if 'pgo' in enabled_indicators:
            pgo_config = config.get('pgo', {})
            pgo_signals = generate_pgo_score_signal(
                df=df,
                length=pgo_config.get('length', 35),
                upper_threshold=pgo_config.get('upper_threshold', 1.35),
                lower_threshold=pgo_config.get('lower_threshold', -0.58),
                skip_warmup=False
            )
            all_signals['pgo'] = pgo_signals

        if 'bollinger_bands' in enabled_indicators:
            bb_config = config.get('bollinger_bands', {})
            bb_signals = generate_bb_score_signal(
                df=df,
                length=bb_config.get('length', 33),
                multiplier=bb_config.get('multiplier', 2.0),
                long_threshold=bb_config.get('long_threshold', 76.0),
                short_threshold=bb_config.get('short_threshold', 31.0),
                src_col=bb_config.get('src_col', 'close'),
                use_heikin_ashi=bb_config.get('use_heikin_ashi', True),
                heikin_src=bb_config.get('heikin_src', 'close')
            )
            all_signals['bollinger_bands'] = bb_signals

        if 'dwma_score' in enabled_indicators:
            dwma_config = config.get('dwma_score', {})
            dwma_signals = generate_dwma_signal(
                df=df,
                smoothing_style=dwma_config.get('smoothing_style', 'Weighted SD'),
                src_col=dwma_config.get('src_col', 'close'),
                length=dwma_config.get('length', 17),
                ma_type=dwma_config.get('ma_type', 'EMA'),
                ma_smooth_length=dwma_config.get('ma_smooth_length', 12),
                # Weighted SD parameters (matching TradingView)
                sd_length=dwma_config.get('sd_length', 33),
                upper_sd_weight=dwma_config.get('upper_sd_weight', 1.031),
                lower_sd_weight=dwma_config.get('lower_sd_weight', 0.996),
                # ATR parameters (matching TradingView)
                atr_period=dwma_config.get('atr_period', 12),
                atr_multiplier=dwma_config.get('atr_multiplier', 1.0),
                # For loop parameters (matching TradingView)
                loop_start=dwma_config.get('loop_start', 1),
                loop_end=dwma_config.get('loop_end', 60),
                long_threshold=dwma_config.get('long_threshold', 30),
                short_threshold=dwma_config.get('short_threshold', 0)
            )
            all_signals['dwma_score'] = dwma_signals

        if 'median_score' in enabled_indicators:
            median_config = config.get('median_score', {})
            median_signals = generate_median_score_signal(
                df=df,
                atr_period=median_config.get('atr_period', 10),
                multiplier=median_config.get('multiplier', 1.55),
                median_length=median_config.get('median_length', 7),
                src_col=median_config.get('src_col', 'close')
            )
            all_signals['median_score'] = median_signals

        if 'dema_super_score' in enabled_indicators:
            dema_config = config.get('dema_super_score', {})
            dema_signals = generate_dema_super_score_signal(
                df=df,
                atr_period=dema_config.get('atr_period', 19),
                multiplier=dema_config.get('multiplier', 2.8),
                dema_length=dema_config.get('length', 17),
                src_col=dema_config.get('src_col', 'close')
            )
            all_signals['dema_super_score'] = dema_signals

        if 'dpsd_score' in enabled_indicators:
            dpsd_config = config.get('dpsd_score', {})
            dpsd_signals = generate_dpsd_score_signal(
                df=df,
                dema_length=dpsd_config.get('dema_length', 9),
                percentile_length=dpsd_config.get('percentile_length', 58),
                sd_length=dpsd_config.get('sd_length', 27),
                ema_length=dpsd_config.get('ema_length', 14),
                percentile_upper=dpsd_config.get('percentile_upper', 60.0),
                percentile_lower=dpsd_config.get('percentile_lower', 45.0),
                src_col=dpsd_config.get('src_col', 'close')
            )
            all_signals['dpsd_score'] = dpsd_signals

        if 'aad_score' in enabled_indicators:
            aad_config = config.get('aad_score', {})
            aad_signals = generate_aad_score_signal(
                df=df,
                src_col=aad_config.get('src_col', 'close'),
                length=aad_config.get('length', 22),
                aad_mult=aad_config.get('aad_mult', 1.2),
                avg_type=aad_config.get('avg_type', 'SMA')
            )
            all_signals['aad_score'] = aad_signals

        if 'dynamic_ema_score' in enabled_indicators:
            dynamic_config = config.get('dynamic_ema_score', {})
            dynamic_signals = generate_dynamic_ema_score_signal(
                df=df,
                median_length=dynamic_config.get('median_length', 9),
                median_src=dynamic_config.get('median_src', 'close'),
                ema_length=dynamic_config.get('ema_length', 12),
                smoothing_style=dynamic_config.get('smoothing_style', 'Weighted SD'),
                sd_length=dynamic_config.get('sd_length', 33),
                upper_sd_weight=dynamic_config.get('upper_sd_weight', 1.017),
                lower_sd_weight=dynamic_config.get('lower_sd_weight', 0.996),
                atr_period=dynamic_config.get('atr_period', 14),
                atr_multiplier=dynamic_config.get('atr_multiplier', 1.2)
            )
            all_signals['dynamic_ema_score'] = dynamic_signals

        if 'quantile_dema_score' in enabled_indicators:
            quantile_config = config.get('quantile_dema_score', {})
            quantile_signals = generate_quantile_dema_score_signal(
                df=df,
                dema_length=quantile_config.get('dema_length', 30),
                percentile_filter=quantile_config.get('percentile_filter', 10),
                atr_length=quantile_config.get('atr_length', 14),
                mult_up=quantile_config.get('mult_up', 1.2),
                mult_dn=quantile_config.get('mult_dn', 1.2),
                dema_st_length=quantile_config.get('dema_st_length', 30),
                percentile_length=quantile_config.get('percentile_length', 20),
                sd_length=quantile_config.get('sd_length', 30),
                src_col=quantile_config.get('src_col', 'close')
            )
            all_signals['quantile_dema_score'] = quantile_signals

        # Calculate both continuous scores and binary signals for each time point
        combined_signals = pd.Series(0, index=df.index, dtype=int)
        continuous_scores = pd.Series(0.0, index=df.index, dtype=float)

        for i in range(len(df)):
            # Get signals for this time point
            point_signals = {}
            for indicator, signal_series in all_signals.items():
                if i < len(signal_series):
                    point_signals[indicator] = signal_series.iloc[i]

            # Calculate continuous score (finaltpi = scoreSum / componentCount)
            if point_signals:
                signal_values = list(point_signals.values())
                score_sum = sum(signal_values)
                component_count = len(signal_values)
                finaltpi = score_sum / component_count if component_count > 0 else 0.0
                continuous_scores.iloc[i] = finaltpi

                # Convert to binary signal using thresholds
                if finaltpi >= long_threshold:
                    combined_signals.iloc[i] = 1
                elif finaltpi <= short_threshold:
                    combined_signals.iloc[i] = -1
                else:
                    combined_signals.iloc[i] = 0

        logging.info(f"Generated {len(combined_signals)} combined signals and continuous scores")
        return combined_signals, continuous_scores

    except Exception as e:
        logging.error(f"Error calculating MTPI signals series: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def fetch_btc_data_with_signals(timeframe='1d', limit=100, start_date=None):
    """
    Fetch BTC data and calculate all MTPI signals for visualization.

    Args:
        timeframe: Timeframe for data
        limit: Number of candles to fetch
        start_date: Start date for visualization (e.g., '2024-01-01')

    Returns:
        tuple: (btc_df, signals_series, combined_signal, config)
    """
    try:
        # Load configuration
        config = load_mtpi_multi_indicator_config()
        enabled_indicators = config.get('enabled_indicators', [])
        
        logging.info(f"Fetching BTC data for visualization with indicators: {enabled_indicators}")
        
        # Fetch BTC data with proper start date handling
        fetch_params = {
            'exchange_id': 'binance',
            'symbols': ['BTC/USDT'],
            'timeframe': timeframe,
            'use_cache': True
        }

        if start_date:
            # Use since parameter to fetch from start_date
            fetch_params['since'] = start_date
            fetch_params['ensure_data_since'] = True
            fetch_params['limit'] = None  # Don't limit when using since
            logging.info(f"Fetching data from {start_date} onwards")
        else:
            # Use limit for recent data
            fetch_params['limit'] = limit
            logging.info(f"Fetching latest {limit} candles")

        # Force fresh data to match TradingView
        fetch_params['force_refresh'] = True
        fetch_params['max_cache_age_days'] = 0

        data_dict = fetch_ohlcv_data(**fetch_params)
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            raise Exception("Failed to fetch BTC data")
        
        btc_df = data_dict['BTC/USDT']
        logging.info(f"Loaded {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")

        # Calculate MTPI signals and continuous scores over time
        signals_series, scores_series = calculate_mtpi_signals_series(btc_df, config)

        # Get the latest combined signal and score for summary
        combined_signal = signals_series.iloc[-1] if len(signals_series) > 0 else 0
        current_score = scores_series.iloc[-1] if len(scores_series) > 0 else 0.0

        logging.info(f"Processed {len(btc_df)} candles, latest signal: {combined_signal}, current score: {current_score:.4f}")

        return btc_df, (signals_series, scores_series), combined_signal, config
        
    except Exception as e:
        logging.error(f"Error fetching data with signals: {e}")
        return None, None, None, None

def create_signal_periods(df, signal_series):
    """
    Create periods for bullish/bearish visualization with state persistence.

    When signal is neutral (0), maintain the last non-neutral state.

    Args:
        df: DataFrame with price data
        signal_series: Series with binary signals (1, -1, 0)

    Returns:
        list: List of (start_idx, end_idx, signal_type) tuples
    """
    periods = []
    current_state = 0  # Track the persistent state
    start_idx = 0

    # Create persistent signal series (maintain last state when neutral)
    persistent_signals = signal_series.copy()
    last_non_neutral = 0

    for i, signal in enumerate(signal_series):
        if signal != 0:
            # Non-neutral signal - update last known state
            last_non_neutral = signal
            persistent_signals.iloc[i] = signal
        else:
            # Neutral signal - maintain last non-neutral state
            persistent_signals.iloc[i] = last_non_neutral

    # Now create periods based on persistent signals
    for i, signal in enumerate(persistent_signals):
        if signal != current_state:
            # State changed
            if current_state != 0:
                # End previous period
                periods.append((start_idx, i-1, current_state))

            # Start new period
            current_state = signal
            start_idx = i

    # Close final period
    if current_state != 0:
        periods.append((start_idx, len(persistent_signals)-1, current_state))

    return periods

def plot_btc_with_mtpi_signals(btc_df, signals_data, combined_signal, config):
    """
    Create comprehensive MTPI signal visualization with continuous scores.

    Args:
        btc_df: DataFrame with BTC OHLCV data
        signals_data: Tuple of (signals_series, scores_series)
        combined_signal: Latest combined MTPI signal value
        config: MTPI configuration
    """
    try:
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), 
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # Unpack signals data
        signals_series, scores_series = signals_data

        # Prepare data
        dates = btc_df.index
        prices = btc_df['close']

        # Get configuration values
        long_threshold = config.get('long_threshold', 0.1)
        short_threshold = config.get('short_threshold', -0.1)
        combination_method = config.get('combination_method', 'consensus')

        # Ensure series match the data length
        if len(signals_series) != len(btc_df):
            logging.warning(f"Signal series length ({len(signals_series)}) doesn't match data length ({len(btc_df)})")
            signals_series = signals_series.reindex(btc_df.index, fill_value=0)
            scores_series = scores_series.reindex(btc_df.index, fill_value=0.0)
        
        # Create signal periods for background coloring
        periods = create_signal_periods(btc_df, signals_series)

        # Plot 1: BTC Price with Bullish/Bearish Periods
        ax1.plot(dates, prices, color='black', linewidth=1.5, label='BTC/USDT', zorder=3)

        # Add bullish/bearish background periods
        for start_idx, end_idx, signal_type in periods:
            start_date = dates[start_idx]
            end_date = dates[end_idx]

            if signal_type == 1:  # Bullish
                ax1.axvspan(start_date, end_date, alpha=0.2, color='green',
                           label='Bullish Period' if start_idx == periods[0][0] else "")
            elif signal_type == -1:  # Bearish
                ax1.axvspan(start_date, end_date, alpha=0.2, color='red',
                           label='Bearish Period' if start_idx == periods[0][0] else "")
        
        # Customize price chart
        ax1.set_title(f'BTC/USDT Price with MTPI Signal Periods\n'
                     f'Enabled Indicators: {", ".join(config.get("enabled_indicators", []))}', 
                     fontsize=14, fontweight='bold')
        ax1.set_ylabel('Price (USDT)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper left')
        
        # Format x-axis
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(btc_df)//10)))
        
        # Plot 2: Continuous MTPI Score (like TradingView)
        # Color the line based on persistent signal state (no neutral/orange)
        line_colors = []
        last_state_color = 'blue'  # Default color

        for score in scores_series:
            if score >= long_threshold:
                last_state_color = 'green'
                line_colors.append('green')
            elif score <= short_threshold:
                last_state_color = 'red'
                line_colors.append('red')
            else:
                # Neutral - maintain last state color
                line_colors.append(last_state_color)

        # Plot the continuous MTPI line with state-based coloring
        # Plot segments with different colors based on persistent state
        for i in range(len(dates) - 1):
            color = line_colors[i]
            ax2.plot(dates[i:i+2], scores_series.iloc[i:i+2],
                    color=color, linewidth=2, alpha=0.8)

        # Add a single legend entry for the MTPI line
        ax2.plot([], [], color='blue', linewidth=2, label='MTPI Score (finaltpi)', alpha=0.8)

        # Add threshold lines
        ax2.axhline(y=long_threshold, color='green', linestyle='--', alpha=0.7, linewidth=2,
                   label=f'Long Threshold ({long_threshold})')
        ax2.axhline(y=short_threshold, color='red', linestyle='--', alpha=0.7, linewidth=2,
                   label=f'Short Threshold ({short_threshold})')
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5, linewidth=1)

        # Fill areas above/below thresholds
        ax2.fill_between(dates, long_threshold, 1, alpha=0.1, color='green', label='Long Zone')
        ax2.fill_between(dates, short_threshold, -1, alpha=0.1, color='red', label='Short Zone')

        # Add crossover/crossunder markers
        for i in range(1, len(scores_series)):
            prev_score = scores_series.iloc[i-1]
            curr_score = scores_series.iloc[i]

            # Crossover (bullish)
            if prev_score < long_threshold and curr_score >= long_threshold:
                ax2.scatter(dates[i], curr_score, color='green', s=100, marker='^',
                           zorder=5, label='Long Entry' if i == 1 else "")

            # Crossunder (bearish)
            elif prev_score > short_threshold and curr_score <= short_threshold:
                ax2.scatter(dates[i], curr_score, color='red', s=100, marker='v',
                           zorder=5, label='Short Entry' if i == 1 else "")

        # Customize MTPI chart
        ax2.set_title('Continuous MTPI Score (TradingView Style)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('MTPI Score', fontsize=10)
        ax2.set_ylim(-1.2, 1.2)
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper right', fontsize=8)
        
        # Format x-axis for signal plot
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(btc_df)//10)))
        
        # Rotate x-axis labels
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        # Add configuration info including current score
        current_score = scores_series.iloc[-1] if len(scores_series) > 0 else 0.0
        config_text = (f"Method: {config.get('combination_method', 'N/A')}\n"
                      f"Long Threshold: {config.get('long_threshold', 'N/A')}\n"
                      f"Short Threshold: {config.get('short_threshold', 'N/A')}\n"
                      f"Current Signal: {combined_signal}\n"
                      f"Current Score: {current_score:.4f}")

        ax1.text(0.02, 0.98, config_text, transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=9)
        
        # Adjust layout and save
        plt.tight_layout()
        
        # Save plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mtpi_signals_visualization_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        
        # Extract current day's MTPI score
        current_score = scores_series.iloc[-1] if len(scores_series) > 0 else 0.0
        current_date = btc_df.index[-1] if len(btc_df) > 0 else "N/A"

        print(f"\n📊 Visualization saved as: {filename}")
        print(f"🎯 Current MTPI Signal: {combined_signal}")
        print(f"📊 Current MTPI Score: {current_score:.4f}")
        print(f"📅 Current Date: {current_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(current_date, 'strftime') else current_date}")
        print(f"📈 Enabled Indicators: {', '.join(config.get('enabled_indicators', []))}")

        # Show plot
        plt.show()
        
    except Exception as e:
        logging.error(f"Error creating visualization: {e}")

def print_current_mtpi_details(btc_df, signals_data, config):
    """
    Extract and print detailed current day MTPI information.

    Args:
        btc_df: DataFrame with BTC OHLCV data
        signals_data: Tuple of (signals_series, scores_series)
        config: MTPI configuration
    """
    try:
        signals_series, scores_series = signals_data

        if len(btc_df) == 0 or len(scores_series) == 0:
            print("❌ No data available for current MTPI analysis")
            return

        # Get current (latest) values
        current_date = btc_df.index[-1]
        current_price = btc_df['close'].iloc[-1]
        current_score = scores_series.iloc[-1]
        current_signal = signals_series.iloc[-1]

        # Get thresholds
        long_threshold = config.get('long_threshold', 0.1)
        short_threshold = config.get('short_threshold', -0.1)

        # Determine signal interpretation
        if current_signal == 1:
            signal_text = "🟢 BULLISH"
        elif current_signal == -1:
            signal_text = "🔴 BEARISH"
        else:
            signal_text = "🟡 NEUTRAL"

        # Calculate distance to thresholds
        distance_to_long = long_threshold - current_score
        distance_to_short = current_score - short_threshold

        print("\n" + "="*60)
        print("📊 CURRENT DAY MTPI ANALYSIS")
        print("="*60)
        print(f"📅 Date: {current_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 BTC Price: ${current_price:,.2f}")
        print(f"📈 MTPI Score: {current_score:.6f}")
        print(f"🎯 Signal: {signal_text} ({current_signal})")
        print(f"📊 Long Threshold: {long_threshold}")
        print(f"📊 Short Threshold: {short_threshold}")

        if current_signal == 0:  # Neutral
            print(f"📏 Distance to Long Signal: {distance_to_long:.6f}")
            print(f"📏 Distance to Short Signal: {distance_to_short:.6f}")

            if abs(distance_to_long) < abs(distance_to_short):
                print(f"🔄 Closer to BULLISH signal (needs +{distance_to_long:.6f})")
            else:
                print(f"🔄 Closer to BEARISH signal (needs -{distance_to_short:.6f})")

        # Show enabled indicators
        enabled_indicators = config.get('enabled_indicators', [])
        print(f"🔧 Active Indicators ({len(enabled_indicators)}): {', '.join(enabled_indicators)}")

        # Show recent trend (last 5 scores)
        if len(scores_series) >= 5:
            recent_scores = scores_series.tail(5)
            print(f"📈 Recent Trend (last 5): {[f'{score:.4f}' for score in recent_scores]}")

            # Calculate trend direction
            if recent_scores.iloc[-1] > recent_scores.iloc[0]:
                trend = "📈 IMPROVING"
            elif recent_scores.iloc[-1] < recent_scores.iloc[0]:
                trend = "📉 DECLINING"
            else:
                trend = "➡️ STABLE"
            print(f"🔄 Trend Direction: {trend}")

        print("="*60)

    except Exception as e:
        logging.error(f"Error printing current MTPI details: {e}")

def main(start_date='2024-01-01', timeframe='1d', limit=500):
    """
    Main visualization function.

    Args:
        start_date: Start date for visualization (e.g., '2024-01-01')
        timeframe: Data timeframe
        limit: Number of candles to fetch
    """
    print("🎨 MTPI SIGNAL VISUALIZATION")
    print("=" * 50)
    print(f"📅 Start Date: {start_date}")
    print(f"⏰ Timeframe: {timeframe}")
    print(f"📊 Limit: {limit} candles")

    # Fetch data and signals
    btc_df, signals_data, combined_signal, config = fetch_btc_data_with_signals(
        timeframe=timeframe,
        limit=limit,
        start_date=start_date
    )

    if btc_df is None:
        print("❌ Failed to fetch data. Cannot create visualization.")
        return

    # Print detailed current day MTPI analysis
    print_current_mtpi_details(btc_df, signals_data, config)

    # Create visualization
    plot_btc_with_mtpi_signals(btc_df, signals_data, combined_signal, config)

    print("\n✅ Visualization complete!")

if __name__ == "__main__":
    import sys

    # Parse command line arguments
    start_date = '2024-01-01'  # Default start date
    timeframe = '1d'
    limit = 500

    if len(sys.argv) > 1:
        start_date = sys.argv[1]
    if len(sys.argv) > 2:
        timeframe = sys.argv[2]
    if len(sys.argv) > 3:
        limit = int(sys.argv[3])

    main(start_date=start_date, timeframe=timeframe, limit=limit)
