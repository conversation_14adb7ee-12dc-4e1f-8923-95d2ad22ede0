# src/indicators/bb_score.py
"""
Bollinger Bands Score Indicator for MTPI System

This module implements the Bollinger Bands indicator specifically for the MTPI 8-indicator system.
The BB Score calculates position between bands as percentage and generates binary signals
based on threshold crossovers with persistence logic.

This implementation matches the TradingView PineScript implementation with exact logic.
"""

import pandas as pd
import numpy as np
import logging
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Default configuration for BB Score
DEFAULT_BB_SCORE_CONFIG = {
    'length': 33,
    'multiplier': 2.0,
    'long_threshold': 76.0,
    'short_threshold': 31.0,
    'use_heikin_ashi': True,
    'heikin_src': 'close',
    'src_col': 'close'
}

def calculate_heikin_ashi_candles(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate Heikin-Ashi candles from regular OHLCV data.
    
    Args:
        df: DataFrame with OHLCV data
        
    Returns:
        DataFrame with Heikin-Ashi OHLCV data
    """
    try:
        ha_df = df.copy()
        
        # Initialize first Heikin-Ashi candle
        ha_df.loc[ha_df.index[0], 'ha_close'] = (df['open'].iloc[0] + df['high'].iloc[0] + 
                                                 df['low'].iloc[0] + df['close'].iloc[0]) / 4
        ha_df.loc[ha_df.index[0], 'ha_open'] = (df['open'].iloc[0] + df['close'].iloc[0]) / 2
        
        # Calculate subsequent Heikin-Ashi candles
        for i in range(1, len(df)):
            # HA Close = (O + H + L + C) / 4
            ha_df.loc[ha_df.index[i], 'ha_close'] = (df['open'].iloc[i] + df['high'].iloc[i] + 
                                                     df['low'].iloc[i] + df['close'].iloc[i]) / 4
            
            # HA Open = (previous HA Open + previous HA Close) / 2
            ha_df.loc[ha_df.index[i], 'ha_open'] = (ha_df['ha_open'].iloc[i-1] + 
                                                    ha_df['ha_close'].iloc[i-1]) / 2
            
            # HA High = max(H, HA Open, HA Close)
            ha_df.loc[ha_df.index[i], 'ha_high'] = max(df['high'].iloc[i], 
                                                       ha_df['ha_open'].iloc[i], 
                                                       ha_df['ha_close'].iloc[i])
            
            # HA Low = min(L, HA Open, HA Close)
            ha_df.loc[ha_df.index[i], 'ha_low'] = min(df['low'].iloc[i], 
                                                      ha_df['ha_open'].iloc[i], 
                                                      ha_df['ha_close'].iloc[i])
        
        return ha_df
        
    except Exception as e:
        logging.error(f"Error calculating Heikin-Ashi candles: {e}")
        return df

def get_heikin_ashi_source(df: pd.DataFrame, heikin_src: str) -> pd.Series:
    """
    Get the specified Heikin-Ashi source series.
    
    Args:
        df: DataFrame with Heikin-Ashi data
        heikin_src: Source type ('close', 'open', 'high', 'hlc3')
        
    Returns:
        Series with the requested source data
    """
    try:
        if heikin_src == 'close':
            return df['ha_close']
        elif heikin_src == 'open':
            return df['ha_open']
        elif heikin_src == 'high':
            return df['ha_high']
        elif heikin_src == 'hlc3':
            return (df['ha_high'] + df['ha_low'] + df['ha_close']) / 3
        else:
            logging.warning(f"Unknown Heikin-Ashi source: {heikin_src}, using 'close'")
            return df['ha_close']
            
    except Exception as e:
        logging.error(f"Error getting Heikin-Ashi source: {e}")
        return df['close']

def generate_bb_score_signal(df: pd.DataFrame,
                            length: int = 33,
                            multiplier: float = 2.0,
                            long_threshold: float = 76.0,
                            short_threshold: float = 31.0,
                            src_col: str = 'close',
                            use_heikin_ashi: bool = True,
                            heikin_src: str = 'close') -> pd.Series:
    """
    Generate Bollinger Bands Score signals for MTPI integration.
    
    This implementation matches the TradingView PineScript logic with persistence.
    
    Args:
        df: DataFrame with OHLCV data
        length: Bollinger Bands period length
        multiplier: Standard deviation multiplier
        long_threshold: Upper threshold for long signals
        short_threshold: Lower threshold for short signals
        src_col: Source column when not using Heikin-Ashi
        use_heikin_ashi: Whether to use Heikin-Ashi candles
        heikin_src: Heikin-Ashi source type
        
    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        logging.info(f"Generating BB Score signals with length={length}, multiplier={multiplier}, "
                    f"long_threshold={long_threshold}, short_threshold={short_threshold}, "
                    f"use_heikin_ashi={use_heikin_ashi}")
        
        # Determine source data
        if use_heikin_ashi:
            # Calculate Heikin-Ashi candles
            ha_df = calculate_heikin_ashi_candles(df)
            src = get_heikin_ashi_source(ha_df, heikin_src)
        else:
            src = df[src_col]
        
        # Calculate Bollinger Bands
        basis = src.rolling(window=length).mean()  # Simple Moving Average
        std_dev = src.rolling(window=length).std()  # Standard Deviation
        
        upper_band = basis + (multiplier * std_dev)
        lower_band = basis - (multiplier * std_dev)
        
        # Calculate position between bands as percentage
        band_width = upper_band - lower_band
        position_between_bands = pd.Series(index=df.index, dtype=float)
        
        # Only calculate where band_width > 0 to avoid division by zero
        valid_mask = band_width > 0
        position_between_bands[valid_mask] = 100 * (src[valid_mask] - lower_band[valid_mask]) / band_width[valid_mask]
        
        # Initialize signal series
        signal = pd.Series(0, index=df.index, dtype=int)
        
        # Track the last active trend (persistent signal logic)
        last_trend = 0  # Start neutral
        
        # Process signals with persistence logic
        for i in range(len(df)):
            if pd.isna(position_between_bands.iloc[i]):
                signal.iloc[i] = last_trend  # Maintain last trend if no valid calculation
                continue
                
            pos = position_between_bands.iloc[i]
            
            # Generate initial signal based on thresholds
            if pos > long_threshold:
                current_sig = 1  # Long
            elif pos < short_threshold:
                current_sig = -1  # Short
            else:
                current_sig = 0  # Neutral
            
            # Update last_trend only when signal is not neutral
            if current_sig != 0:
                last_trend = current_sig
            
            # Use current signal if not neutral, otherwise inherit last trend
            signal.iloc[i] = current_sig if current_sig != 0 else last_trend
        
        # Log signal statistics
        signal_counts = signal.value_counts().sort_index()
        logging.info(f"Generated BB Score signals: {dict(signal_counts)}")
        
        return signal
        
    except Exception as e:
        logging.error(f"Error generating BB Score signals: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def calculate_bb_score(df: pd.DataFrame, **kwargs) -> tuple:
    """
    Calculate BB Score with all components for analysis.
    
    Args:
        df: DataFrame with OHLCV data
        **kwargs: BB parameters
        
    Returns:
        Tuple of (signal, position_pct, upper_band, lower_band, basis)
    """
    try:
        # Extract parameters
        length = kwargs.get('length', 33)
        multiplier = kwargs.get('multiplier', 2.0)
        long_threshold = kwargs.get('long_threshold', 76.0)
        short_threshold = kwargs.get('short_threshold', 31.0)
        src_col = kwargs.get('src_col', 'close')
        use_heikin_ashi = kwargs.get('use_heikin_ashi', True)
        heikin_src = kwargs.get('heikin_src', 'close')
        
        # Determine source data
        if use_heikin_ashi:
            ha_df = calculate_heikin_ashi_candles(df)
            src = get_heikin_ashi_source(ha_df, heikin_src)
        else:
            src = df[src_col]
        
        # Calculate Bollinger Bands components
        basis = src.rolling(window=length).mean()
        std_dev = src.rolling(window=length).std()
        upper_band = basis + (multiplier * std_dev)
        lower_band = basis - (multiplier * std_dev)
        
        # Calculate position percentage
        band_width = upper_band - lower_band
        position_pct = pd.Series(index=df.index, dtype=float)
        valid_mask = band_width > 0
        position_pct[valid_mask] = 100 * (src[valid_mask] - lower_band[valid_mask]) / band_width[valid_mask]
        
        # Generate signals
        signal = generate_bb_score_signal(
            df, length, multiplier, long_threshold, short_threshold, 
            src_col, use_heikin_ashi, heikin_src
        )
        
        return signal, position_pct, upper_band, lower_band, basis
        
    except Exception as e:
        logging.error(f"Error calculating BB Score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float))

# Aliases for backward compatibility
generate_bollinger_band_signal = generate_bb_score_signal
calculate_bollinger_band_signal = generate_bb_score_signal
