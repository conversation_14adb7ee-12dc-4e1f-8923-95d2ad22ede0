# Key Differences Between main.py and allocation_report.py

After analyzing both implementations, we've identified several key differences that are causing the discrepancy in returns (6.8% vs 4.12%) when running the same configuration. This document outlines these differences and provides recommendations for fixing them.

## 1. MTPI Signal Handling

### allocation_report.py (Correct Implementation)
- Uses strict signal confirmation with `wait_for_confirmed_signals=True` by default
- For daily timeframes in strict mode, always uses the previous day's completed candle:
  ```python
  # In strict mode, always use the previous day's completed candle
  signal_time = today.replace(hour=0, minute=0) - pd.Timedelta(days=1)
  ```
- Ensures proper timeframe alignment between asset data and MTPI signals

### main.py (Current Implementation)
- Has a different approach to signal confirmation:
  ```python
  # For daily timeframes, the signal from day X becomes effective at 00:00 of day X+1
  if today.hour == 0 and today.minute == 0:
      # If we're exactly at midnight, use yesterday's signal
      signal_time = today - pd.Timedelta(days=mtpi_number)
  else:
      # Otherwise, use today's signal (from 00:00)
      signal_time = today.replace(hour=0, minute=0)
  ```
- This can lead to using signals from partially formed candles in some cases
- Does not have the same strict waiting period for signal confirmation

## 2. Signal Confirmation and Market Entry Logic

### allocation_report.py (Correct Implementation)
- Waits for confirmation before entering the market after a bullish signal:
  ```python
  # Calculate returns if we're staying in the market OR entering the market
  if mtpi_allows_trade:
      if current_holdings:
          # Calculate equal-weighted return from current holdings
          # ...
      elif new_holdings:
          # We're entering the market today - calculate returns for new holdings
          # This ensures we experience market movement on entry days
          # ...
  ```

### main.py (Current Implementation)
- Has a different approach to waiting for confirmation:
  ```python
  # Signal just turned bullish - wait for confirmation (next day) before entering
  if mtpi_signal_just_changed and mtpi_signal_value == 1 and prev_mtpi_signal_value != 1:
      new_holdings = set()  # Stay out of market for one more day for confirmation
  ```
- The implementation of `mtpi_signal_just_changed` might not be consistent with allocation_report.py

## 3. Transaction Fee Application

### allocation_report.py (Correct Implementation)
- Applies transaction fees for each asset sold and bought:
  ```python
  # Apply transaction fee for each asset sold and bought
  transaction_cost = (len(assets_to_sell) + len(assets_to_buy)) * self.transaction_fee_rate
  ```
- Applies fees at the beginning of the day after signal confirmation
- Handles market exits due to bearish MTPI signals by applying fees and not experiencing market movement:
  ```python
  # If MTPI signal is not bullish, exit positions immediately at the beginning of the day
  # and stay in cash - we don't experience any market movement for this day
  if current_holdings:  # Only apply transaction fee if we have holdings to sell
      # Since we exit at the beginning of the day, we don't apply portfolio return
      # We only apply transaction fees for selling all current holdings
      transaction_cost = len(current_holdings) * self.transaction_fee_rate
      equity_value = equity_curve.iloc[i-1] * (1 - transaction_cost)
      equity_curve.iloc[i] = equity_value
  ```

### main.py (Current Implementation)
- Has a similar approach but might apply fees differently in some edge cases:
  ```python
  # Apply transaction fee for each asset sold and bought (after confirmation for buys)
  transaction_cost = (len(assets_to_sell) + len(assets_to_buy)) * transaction_fee_rate
  ```
- The timing of fee application might differ, especially for market entries and exits

## 4. Return Calculation

### allocation_report.py (Correct Implementation)
- Uses `pct_change()` to calculate returns, which matches TradingView's approach:
  ```python
  # Calculate daily returns
  df_returns = df['close'].pct_change()
  asset_returns[symbol] = df_returns
  ```
- Calculates returns as percentage change from previous candle's close to current candle's close
- Handles market exits due to bearish MTPI signals by not experiencing market movement:
  ```python
  # If MTPI signal is bearish, we've already exited positions at the beginning of the day
  # so we don't experience any market movement
  portfolio_return = 0.0
  ```

### main.py (Current Implementation)
- Also uses `pct_change()` but might handle market exits differently:
  ```python
  # Calculate daily returns
  df_returns = df['close'].pct_change()
  asset_returns[symbol] = df_returns
  ```
- The key difference is likely in how returns are applied during market exits

## 5. Equity Curve Calculation

### allocation_report.py (Correct Implementation)
- First day: Applies transaction fee for initial purchases and market movement:
  ```python
  # Apply both transaction fee and market movement for the day
  equity_curve.iloc[i] = equity_curve.iloc[i-1] * fee_factor * (1 + portfolio_return)
  ```
- Subsequent days: Applies portfolio return and transaction costs:
  ```python
  # Apply portfolio return and transaction costs
  equity_value = equity_curve.iloc[i-1] * (1 + portfolio_return)
  # Apply transaction costs if there are changes in holdings
  if assets_to_sell or assets_to_buy:
      equity_value *= (1 - transaction_cost)
  equity_curve.iloc[i] = equity_value
  ```

### main.py (Current Implementation)
- Has a similar approach but might calculate equity values differently in some edge cases:
  ```python
  # Apply transaction fee for initial purchases on the current day (after confirmation)
  fee_factor = 1 - (len(new_holdings) * transaction_fee_rate)
  equity_curve.iloc[i] = equity_curve.iloc[i-1] * fee_factor
  ```
- The order of applying returns and transaction costs might differ

## Recommendations for Fixing

1. **Update MTPI Signal Handling**: Modify the `main.py` implementation to use the same strict signal confirmation logic as `allocation_report.py`, especially for daily timeframes.

2. **Align Signal Confirmation Logic**: Ensure that the waiting period for signal confirmation is consistent between both implementations.

3. **Standardize Transaction Fee Application**: Make sure transaction fees are applied at the same time and in the same way in both implementations, especially for market exits due to bearish MTPI signals.

4. **Unify Return Calculation**: Ensure that returns are calculated and applied consistently, particularly during market exits.

5. **Harmonize Equity Curve Calculation**: Make sure the order of applying returns and transaction costs is the same in both implementations.

The most direct approach would be to refactor the strategy execution logic in `main.py` to match the implementation in `allocation_report.py`, which has been identified as the correct implementation.
