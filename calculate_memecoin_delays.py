#!/usr/bin/env python3
"""
Calculate optimal delays for GeckoTerminal memecoin strategy with 60 tokens.

This script calculates the required delays to handle different numbers of memecoin tokens
while respecting GeckoTerminal's rate limit of 30 requests per minute.
"""

def calculate_delays(num_tokens: int, requests_per_minute: int = 30, safety_buffer: float = 1.2):
    """
    Calculate optimal delays for fetching memecoin data.
    
    Args:
        num_tokens: Number of memecoin tokens to fetch
        requests_per_minute: API rate limit (requests per minute)
        safety_buffer: Safety multiplier (1.2 = 20% buffer)
    
    Returns:
        Dictionary with timing calculations
    """
    # Without pool caching (first run)
    requests_per_token_uncached = 2  # pool search + OHLCV data
    total_requests_uncached = num_tokens * requests_per_token_uncached
    
    # With pool caching (subsequent runs)
    requests_per_token_cached = 1  # only OHLCV data
    total_requests_cached = num_tokens * requests_per_token_cached
    
    # Calculate minimum time needed
    min_time_uncached = total_requests_uncached / requests_per_minute * 60  # seconds
    min_time_cached = total_requests_cached / requests_per_minute * 60  # seconds
    
    # Add safety buffer
    safe_time_uncached = min_time_uncached * safety_buffer
    safe_time_cached = min_time_cached * safety_buffer
    
    # Calculate delay per request
    delay_uncached = safe_time_uncached / total_requests_uncached
    delay_cached = safe_time_cached / total_requests_cached
    
    return {
        'num_tokens': num_tokens,
        'requests_per_minute': requests_per_minute,
        'safety_buffer': safety_buffer,
        'uncached': {
            'requests_per_token': requests_per_token_uncached,
            'total_requests': total_requests_uncached,
            'min_time_minutes': min_time_uncached / 60,
            'safe_time_minutes': safe_time_uncached / 60,
            'delay_per_request': delay_uncached
        },
        'cached': {
            'requests_per_token': requests_per_token_cached,
            'total_requests': total_requests_cached,
            'min_time_minutes': min_time_cached / 60,
            'safe_time_minutes': safe_time_cached / 60,
            'delay_per_request': delay_cached
        }
    }

def print_calculation_summary(calc: dict):
    """Print a formatted summary of the calculations."""
    print(f"\n📊 RATE LIMIT CALCULATIONS FOR {calc['num_tokens']} TOKENS")
    print("=" * 60)
    print(f"API Rate Limit: {calc['requests_per_minute']} requests/minute")
    print(f"Safety Buffer: {calc['safety_buffer']}x ({(calc['safety_buffer']-1)*100:.0f}% extra time)")
    
    print(f"\n🔍 FIRST RUN (No Pool Cache):")
    uncached = calc['uncached']
    print(f"  • Requests per token: {uncached['requests_per_token']} (pool search + OHLCV)")
    print(f"  • Total requests needed: {uncached['total_requests']}")
    print(f"  • Minimum time: {uncached['min_time_minutes']:.1f} minutes")
    print(f"  • Safe time (with buffer): {uncached['safe_time_minutes']:.1f} minutes")
    print(f"  • Recommended delay: {uncached['delay_per_request']:.1f} seconds per request")
    
    print(f"\n⚡ SUBSEQUENT RUNS (With Pool Cache):")
    cached = calc['cached']
    print(f"  • Requests per token: {cached['requests_per_token']} (OHLCV only)")
    print(f"  • Total requests needed: {cached['total_requests']}")
    print(f"  • Minimum time: {cached['min_time_minutes']:.1f} minutes")
    print(f"  • Safe time (with buffer): {cached['safe_time_minutes']:.1f} minutes")
    print(f"  • Recommended delay: {cached['delay_per_request']:.1f} seconds per request")
    
    # Time savings with caching
    time_saved = uncached['safe_time_minutes'] - cached['safe_time_minutes']
    percent_saved = (time_saved / uncached['safe_time_minutes']) * 100
    print(f"\n💡 CACHING BENEFITS:")
    print(f"  • Time saved: {time_saved:.1f} minutes ({percent_saved:.0f}% faster)")
    print(f"  • Requests saved: {uncached['total_requests'] - cached['total_requests']}")

def main():
    """Main function to calculate delays for different token counts."""
    print("🧮 GeckoTerminal Memecoin Strategy - Rate Limit Calculator")
    print("=" * 60)
    
    # Test different token counts
    token_counts = [29, 60, 100]  # Current, target, and stretch goal
    
    for num_tokens in token_counts:
        calc = calculate_delays(num_tokens)
        print_calculation_summary(calc)
        print()
    
    # Specific recommendation for 60 tokens
    print("\n🎯 RECOMMENDATION FOR 60 TOKENS:")
    print("=" * 60)
    calc_60 = calculate_delays(60)
    
    print(f"For the memecoin strategy with 60 tokens:")
    print(f"• Use {calc_60['uncached']['delay_per_request']:.1f}s delay for first run (building pool cache)")
    print(f"• Use {calc_60['cached']['delay_per_request']:.1f}s delay for subsequent runs (using pool cache)")
    print(f"• Total strategy execution time:")
    print(f"  - First run: ~{calc_60['uncached']['safe_time_minutes']:.0f} minutes")
    print(f"  - Subsequent runs: ~{calc_60['cached']['safe_time_minutes']:.0f} minutes")
    
    print(f"\n⚙️  IMPLEMENTATION:")
    print(f"• Set page_delay_seconds: {calc_60['cached']['delay_per_request']:.1f} in GeckoTerminal config")
    print(f"• Implement pool address caching to halve API usage")
    print(f"• Consider running strategy every hour (plenty of time between runs)")
    
    print(f"\n📈 SCALING POTENTIAL:")
    print(f"• With current 30 req/min limit and caching:")
    print(f"  - 60 tokens: {calc_60['cached']['safe_time_minutes']:.0f} minutes")
    calc_100 = calculate_delays(100)
    print(f"  - 100 tokens: {calc_100['cached']['safe_time_minutes']:.0f} minutes")
    print(f"• Strategy can easily handle 60+ tokens with proper delays")

if __name__ == "__main__":
    main()
