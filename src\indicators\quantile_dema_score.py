#!/usr/bin/env python3
"""
Quantile DEMA Trend Score Indicator - MTPI Component

This module implements the Quantile DEMA Trend indicator which combines:
- DEMA (Double Exponential Moving Average) function
- SuperTrend with percentile filters
- ATR-based volatility calculations
- Standard deviation filtering

Parameters from PineScript specification:
- DEMA Length = 30
- Percentile Filter = 10/20
- ATR Length = 14
- Multipliers = 1.2 (both long and short)
- SD Length = 30

Signal Logic:
- Long (1): When SuperTrend direction crosses under 0 (bullish)
- Short (-1): When SuperTrend direction crosses over 0 (bearish)
- Maintains state until opposite crossover occurs
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_dema(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate DEMA (Double Exponential Moving Average).
    
    DEMA = 2 * EMA1 - EMA2
    where EMA1 = EMA(src, length) and EMA2 = EMA(EMA1, length)
    
    Args:
        src: Source price series
        length: Period for EMA calculation
        
    Returns:
        DEMA series
    """
    try:
        # Calculate first EMA
        ema1 = src.ewm(span=length, adjust=False).mean()
        
        # Calculate second EMA (EMA of EMA1)
        ema2 = ema1.ewm(span=length, adjust=False).mean()
        
        # Calculate DEMA
        dema = 2 * ema1 - ema2
        
        return dema
        
    except Exception as e:
        logging.error(f"Error calculating DEMA: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_percentile_nearest_rank(src: pd.Series, length: int, percentile: float) -> pd.Series:
    """
    Calculate percentile using nearest rank method (matching TradingView's ta.percentile_nearest_rank).
    
    Args:
        src: Source price series
        length: Rolling window length
        percentile: Percentile value (0-100)
        
    Returns:
        Percentile series
    """
    try:
        result = pd.Series(index=src.index, dtype=float)
        
        for i in range(len(src)):
            if i < length - 1:
                # Use available data for initial values
                window_data = src.iloc[:i+1].values
            else:
                # Use full window
                window_data = src.iloc[i-length+1:i+1].values
            
            # Sort the window data
            sorted_data = np.sort(window_data)
            n = len(sorted_data)
            
            # Calculate nearest-rank percentile
            # Formula: rank = ceil(P/100 * N)
            rank = int(np.ceil(percentile/100.0 * n))
            
            # Convert to 0-indexed and get value
            percentile_value = sorted_data[rank - 1] if rank > 0 else sorted_data[0]
            result.iloc[i] = percentile_value
            
        return result
        
    except Exception as e:
        logging.error(f"Error calculating percentile nearest rank: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_atr(df: pd.DataFrame, length: int) -> pd.Series:
    """
    Calculate Average True Range (ATR).
    
    Args:
        df: DataFrame with OHLC data
        length: Period for ATR calculation
        
    Returns:
        ATR series
    """
    try:
        high = df['high']
        low = df['low']
        close = df['close']
        
        # Calculate True Range components
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        # True Range is the maximum of the three
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Calculate ATR as RMA (Running Moving Average) of True Range
        atr = true_range.ewm(alpha=1.0/length, adjust=False).mean()
        
        return atr
        
    except Exception as e:
        logging.error(f"Error calculating ATR: {e}")
        return pd.Series(index=df.index, dtype=float)

def calculate_supertrend_with_percentile(df: pd.DataFrame, 
                                       dema: pd.Series,
                                       percentile_length: int,
                                       atr_length: int,
                                       mult_up: float,
                                       mult_dn: float,
                                       sd_filter: pd.Series) -> Tuple[pd.Series, pd.Series]:
    """
    Calculate SuperTrend with percentile filters (matching PineScript F_SupTrend function).
    
    Args:
        df: DataFrame with OHLC data
        dema: DEMA series for SuperTrend calculation
        percentile_length: Length for percentile calculation
        atr_length: Length for ATR calculation
        mult_up: Multiplier for upper band
        mult_dn: Multiplier for lower band
        sd_filter: Standard deviation filter series
        
    Returns:
        Tuple of (SuperTrend line, Direction)
    """
    try:
        # Calculate ATR
        atr = calculate_atr(df, atr_length)
        
        # Calculate percentile bands
        lower_percentile = calculate_percentile_nearest_rank(dema, percentile_length, 25)
        upper_percentile = calculate_percentile_nearest_rank(dema, percentile_length, 75)
        
        # Calculate initial bands
        lower_atr = lower_percentile - atr * mult_up
        upper_atr = upper_percentile + atr * mult_dn
        
        # Initialize result series
        final_lower = pd.Series(index=df.index, dtype=float)
        final_upper = pd.Series(index=df.index, dtype=float)
        direction = pd.Series(index=df.index, dtype=int)
        supertrend = pd.Series(index=df.index, dtype=float)
        
        close = df['close']
        
        # Process each bar (matching PineScript logic)
        for i in range(len(df)):
            if i == 0:
                # Initialize first values
                final_lower.iloc[i] = lower_atr.iloc[i]
                final_upper.iloc[i] = upper_atr.iloc[i]
                direction.iloc[i] = 1  # Start with uptrend
            else:
                # Update lower band
                if lower_atr.iloc[i] > final_lower.iloc[i-1] or close.iloc[i-1] < final_lower.iloc[i-1]:
                    final_lower.iloc[i] = lower_atr.iloc[i]
                else:
                    final_lower.iloc[i] = final_lower.iloc[i-1]
                
                # Update upper band
                if upper_atr.iloc[i] < final_upper.iloc[i-1] or close.iloc[i-1] > final_upper.iloc[i-1]:
                    final_upper.iloc[i] = upper_atr.iloc[i]
                else:
                    final_upper.iloc[i] = final_upper.iloc[i-1]
                
                # Determine direction
                if pd.isna(sd_filter.iloc[i-1]):
                    direction.iloc[i] = 1
                elif supertrend.iloc[i-1] == final_upper.iloc[i-1]:
                    direction.iloc[i] = -1 if close.iloc[i] > final_upper.iloc[i] else 1
                else:
                    direction.iloc[i] = 1 if close.iloc[i] < final_lower.iloc[i] else -1
            
            # Set SuperTrend line
            supertrend.iloc[i] = final_lower.iloc[i] if direction.iloc[i] == -1 else final_upper.iloc[i]
        
        return supertrend, direction
        
    except Exception as e:
        logging.error(f"Error calculating SuperTrend with percentile: {e}")
        return pd.Series(index=df.index, dtype=float), pd.Series(index=df.index, dtype=int)

def calculate_quantile_dema_score(df: pd.DataFrame,
                                dema_length: int = 30,
                                percentile_filter: int = 10,
                                atr_length: int = 14,
                                mult_up: float = 1.2,
                                mult_dn: float = 1.2,
                                dema_st_length: int = 30,
                                percentile_length: int = 20,
                                sd_length: int = 30,
                                src_col: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate Quantile DEMA Trend Score - Exact PineScript Logic.
    
    This implementation follows the exact PineScript logic with:
    - DEMA function calculation
    - Percentile filtering
    - SuperTrend with percentile bands
    - Standard deviation filtering
    - Direction-based signal generation
    
    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        percentile_filter: Initial percentile filter length
        atr_length: ATR calculation length
        mult_up: Long multiplier
        mult_dn: Short multiplier
        dema_st_length: DEMA SuperTrend length
        percentile_length: Percentile filter length for SuperTrend
        sd_length: Standard deviation length
        src_col: Source column for calculations
        
    Returns:
        Tuple of (signal, supertrend_line, direction)
    """
    try:
        # Step 1: Apply initial percentile filter
        filtered_src = calculate_percentile_nearest_rank(df[src_col], percentile_filter, 50)
        
        # Step 2: Calculate base DEMA
        dema_base = calculate_dema(filtered_src, dema_length)
        
        # Step 3: Calculate final DEMA for SuperTrend
        dema_final = calculate_dema(dema_base, dema_st_length)
        
        # Step 4: Calculate standard deviation filter
        sd_filter = filtered_src.rolling(window=sd_length).std()
        
        # Step 5: Calculate SuperTrend with percentile
        supertrend_line, st_direction = calculate_supertrend_with_percentile(
            df, dema_final, percentile_length, atr_length, mult_up, mult_dn, sd_filter
        )
        
        # Step 6: Generate signals based on direction crossovers
        signal = pd.Series(0, index=df.index, dtype=int)
        
        # Initialize QB state variable (matching PineScript)
        qb_state = 0
        
        for i in range(1, len(df)):
            # Detect crossovers
            long_condition = st_direction.iloc[i-1] >= 0 and st_direction.iloc[i] < 0  # crossunder(ST_Direction, 0)
            short_condition = st_direction.iloc[i-1] <= 0 and st_direction.iloc[i] > 0  # crossover(ST_Direction, 0)
            
            # Update QB state (matching PineScript logic)
            if long_condition and not short_condition:
                qb_state = 1
            elif short_condition:
                qb_state = -1
            
            signal.iloc[i] = qb_state
        
        # Set first signal value
        signal.iloc[0] = 0
        
        return signal, supertrend_line, st_direction
        
    except Exception as e:
        logging.error(f"Error calculating Quantile DEMA score: {e}")
        return (pd.Series(index=df.index, dtype=int), 
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=int))

def generate_quantile_dema_score_signal(df: pd.DataFrame,
                                      dema_length: int = 30,
                                      percentile_filter: int = 10,
                                      atr_length: int = 14,
                                      mult_up: float = 1.2,
                                      mult_dn: float = 1.2,
                                      dema_st_length: int = 30,
                                      percentile_length: int = 20,
                                      sd_length: int = 30,
                                      src_col: str = 'close') -> pd.Series:
    """
    Generate Quantile DEMA Trend binary signal for MTPI integration.
    
    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        percentile_filter: Initial percentile filter length
        atr_length: ATR calculation length
        mult_up: Long multiplier
        mult_dn: Short multiplier
        dema_st_length: DEMA SuperTrend length
        percentile_length: Percentile filter length for SuperTrend
        sd_length: Standard deviation length
        src_col: Source column for calculations
        
    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        signal, _, _ = calculate_quantile_dema_score(
            df, dema_length, percentile_filter, atr_length, mult_up, mult_dn,
            dema_st_length, percentile_length, sd_length, src_col
        )
        
        return signal
        
    except Exception as e:
        logging.error(f"Error generating Quantile DEMA score signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

# Default configuration for MTPI integration
DEFAULT_QUANTILE_DEMA_SCORE_CONFIG = {
    'dema_length': 30,
    'percentile_filter': 10,
    'atr_length': 14,
    'mult_up': 1.2,
    'mult_dn': 1.2,
    'dema_st_length': 30,
    'percentile_length': 20,
    'sd_length': 30,
    'src_col': 'close'
}

# Export the main function for MTPI integration
__all__ = ['generate_quantile_dema_score_signal', 'calculate_quantile_dema_score', 'DEFAULT_QUANTILE_DEMA_SCORE_CONFIG']
