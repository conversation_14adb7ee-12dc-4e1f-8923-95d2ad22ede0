2025-07-11 10:04:50,340 - root - INFO - Loaded environment variables from .env file
2025-07-11 10:04:51,705 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-07-11 10:04:53,395 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:53,404 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:53,405 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:53,420 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:53,421 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:53,429 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:53,429 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-07-11 10:04:53,430 - root - INFO - Notification configuration loaded successfully.
2025-07-11 10:04:54,597 - root - INFO - Telegram command handlers registered
2025-07-11 10:04:54,598 - root - INFO - Telegram bot polling started
2025-07-11 10:04:54,598 - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-11 10:04:54,598 - root - INFO - Telegram notification channel initialized
2025-07-11 10:04:54,599 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-11 10:04:54,599 - root - INFO - Loaded 24 templates from file
2025-07-11 10:04:54,599 - root - INFO - Notification manager initialized with 1 channels
2025-07-11 10:04:54,599 - root - INFO - Notification manager initialized
2025-07-11 10:04:54,600 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-11 10:04:54,600 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-11 10:04:54,600 - root - INFO - Set up critical time windows for 1d timeframe
2025-07-11 10:04:54,600 - root - INFO - Network watchdog initialized with 10s check interval
2025-07-11 10:04:54,602 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-07-11 10:04:54,603 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250627_214259.json.json
2025-07-11 10:04:54,603 - root - INFO - Recovery manager initialized
2025-07-11 10:04:54,603 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-11 10:04:54,603 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 1, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.0025}
2025-07-11 10:04:54,608 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:54,617 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:54,618 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 10:04:54,618 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 10:04:54,618 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 10:04:54,619 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 10:04:54,619 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 10:04:54,619 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 10:04:54,619 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 10:04:54,619 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 10:04:54,619 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:54,628 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:54,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-07-11 10:04:54,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-07-11 10:04:54,849 - telegram.ext.Application - INFO - Application started
2025-07-11 10:04:54,970 - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 10:04:54,971 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 10:04:54,971 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 10:04:54,971 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 10:04:54,971 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 10:04:54,971 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:54,979 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:54,984 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:54,993 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:54,993 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:04:55,005 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:55,005 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-07-11 10:04:55,006 - root - INFO - Paper trading initialized with EUR as quote currency
2025-07-11 10:04:55,006 - root - INFO - Trading executor initialized for bitvavo
2025-07-11 10:04:55,006 - root - INFO - Trading mode: paper
2025-07-11 10:04:55,006 - root - INFO - Trading enabled: True
2025-07-11 10:04:55,006 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 10:04:55,006 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 10:04:55,006 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 10:04:55,006 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 10:04:55,006 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:04:55,013 - root - INFO - Configuration loaded successfully.
2025-07-11 10:04:55,319 - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 10:04:55,320 - root - INFO - Trading enabled in paper mode
2025-07-11 10:04:55,321 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-11 10:04:55,321 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-07-11 10:04:55,321 - root - INFO - Reset paper trading account to initial balance
2025-07-11 10:04:55,321 - root - INFO - Generated run ID: ********_100455
2025-07-11 10:04:55,322 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-11 10:04:55,322 - root - INFO - Background service initialized
2025-07-11 10:04:55,322 - root - INFO - Network watchdog started
2025-07-11 10:04:55,322 - root - INFO - Network watchdog started
2025-07-11 10:04:55,324 - root - INFO - Schedule set up for 1d timeframe
2025-07-11 10:04:55,324 - root - INFO - Background service started
2025-07-11 10:04:55,325 - root - INFO - Executing strategy (run #1)...
2025-07-11 10:04:55,326 - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-11 10:04:55,327 - root - INFO - No trades recorded today (Max: 5)
2025-07-11 10:04:55,327 - root - INFO - Initialized daily trades counter for 2025-07-11
2025-07-11 10:04:55,328 - root - INFO - Creating snapshot for candle timestamp: ********
2025-07-11 10:04:55,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 10:05:01,343 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-07-11 10:05:01,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 10:05:01,488 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-11 10:05:01,488 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-11 10:05:01,489 - root - INFO - Using recent date for performance tracking: 2025-07-04
2025-07-11 10:05:01,491 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-11 10:05:01,544 - root - INFO - Loaded 2152 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,545 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,545 - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,546 - root - INFO - Data is up to date for ETH/USDT
2025-07-11 10:05:01,547 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,568 - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,569 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,570 - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,571 - root - INFO - Data is up to date for BTC/USDT
2025-07-11 10:05:01,572 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,591 - root - INFO - Loaded 1795 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,593 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,593 - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,594 - root - INFO - Data is up to date for SOL/USDT
2025-07-11 10:05:01,595 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,607 - root - INFO - Loaded 800 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,611 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,612 - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,612 - root - INFO - Data is up to date for SUI/USDT
2025-07-11 10:05:01,613 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,640 - root - INFO - Loaded 2152 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,642 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,643 - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,643 - root - INFO - Data is up to date for XRP/USDT
2025-07-11 10:05:01,644 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,662 - root - INFO - Loaded 1730 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,663 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,663 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,664 - root - INFO - Data is up to date for AAVE/USDT
2025-07-11 10:05:01,665 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,685 - root - INFO - Loaded 1753 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,688 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,692 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,700 - root - INFO - Data is up to date for AVAX/USDT
2025-07-11 10:05:01,708 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,756 - root - INFO - Loaded 2152 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,758 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,759 - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,759 - root - INFO - Data is up to date for ADA/USDT
2025-07-11 10:05:01,760 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,789 - root - INFO - Loaded 2152 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,790 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,791 - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,791 - root - INFO - Data is up to date for LINK/USDT
2025-07-11 10:05:01,792 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,819 - root - INFO - Loaded 2152 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,820 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,820 - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,821 - root - INFO - Data is up to date for TRX/USDT
2025-07-11 10:05:01,822 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,836 - root - INFO - Loaded 798 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,837 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,838 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,838 - root - INFO - Data is up to date for PEPE/USDT
2025-07-11 10:05:01,839 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,864 - root - INFO - Loaded 2152 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,866 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,867 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,869 - root - INFO - Data is up to date for DOGE/USDT
2025-07-11 10:05:01,870 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,897 - root - INFO - Loaded 2152 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,898 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,899 - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,899 - root - INFO - Data is up to date for BNB/USDT
2025-07-11 10:05:01,901 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,922 - root - INFO - Loaded 1788 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:01,925 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,926 - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 10:05:01,926 - root - INFO - Data is up to date for DOT/USDT
2025-07-11 10:05:01,927 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:01,959 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-11 10:05:01,959 - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-11 10:05:01,961 - root - INFO -   - Number of indicators: 8
2025-07-11 10:05:01,961 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 10:05:01,961 - root - INFO -   - Combination method: consensus
2025-07-11 10:05:01,962 - root - INFO -   - Long threshold: 0.1
2025-07-11 10:05:01,962 - root - INFO -   - Short threshold: -0.1
2025-07-11 10:05:01,975 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 10:05:01,976 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:01,976 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-11 10:05:01,979 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-11 10:05:01,979 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-11 10:05:01,979 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-07-11 10:05:01,979 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-11 10:05:01,980 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-11 10:05:01,980 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-11 10:05:01,980 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-11 10:05:01,980 - root - INFO - Using provided trend method: PGO For Loop
2025-07-11 10:05:01,980 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:05:01,997 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:02,000 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-11 10:05:02,012 - root - INFO - Configuration saved successfully.
2025-07-11 10:05:02,012 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 10:05:02,012 - root - INFO - Number of trend detection assets: 14
2025-07-11 10:05:02,012 - root - INFO - Selected assets type: <class 'list'>
2025-07-11 10:05:02,012 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:02,012 - root - INFO - Number of trading assets: 14
2025-07-11 10:05:02,013 - root - INFO - Trading assets type: <class 'list'>
2025-07-11 10:05:02,948 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:05:02,955 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:02,972 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 10:05:02,982 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:02,983 - root - INFO - Execution context: backtesting
2025-07-11 10:05:02,983 - root - INFO - Execution timing: candle_close
2025-07-11 10:05:02,983 - root - INFO - Ratio calculation method: independent
2025-07-11 10:05:02,984 - root - INFO - Tie-breaking strategy: imcumbent
2025-07-11 10:05:02,984 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-11 10:05:02,984 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 10:05:02,984 - root - INFO - MTPI combination method override: consensus
2025-07-11 10:05:02,984 - root - INFO - MTPI long threshold override: 0.1
2025-07-11 10:05:02,985 - root - INFO - MTPI short threshold override: -0.1
2025-07-11 10:05:02,985 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-11 10:05:02,985 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 10:05:02,988 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,988 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,988 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,989 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,989 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,990 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,990 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,990 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,991 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,991 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,991 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,992 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,992 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,992 - root - INFO - Loaded metadata for 48 assets
2025-07-11 10:05:02,993 - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-11 10:05:03,019 - root - INFO - Loaded 211 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,020 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,021 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,021 - root - INFO - Loaded 211 rows of ETH/USDT data from cache (after filtering).
2025-07-11 10:05:03,041 - root - INFO - Loaded 211 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,042 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,043 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,043 - root - INFO - Loaded 211 rows of BTC/USDT data from cache (after filtering).
2025-07-11 10:05:03,059 - root - INFO - Loaded 211 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,060 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,061 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,061 - root - INFO - Loaded 211 rows of SOL/USDT data from cache (after filtering).
2025-07-11 10:05:03,072 - root - INFO - Loaded 211 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,074 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,074 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,075 - root - INFO - Loaded 211 rows of SUI/USDT data from cache (after filtering).
2025-07-11 10:05:03,092 - root - INFO - Loaded 211 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,093 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,094 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,094 - root - INFO - Loaded 211 rows of XRP/USDT data from cache (after filtering).
2025-07-11 10:05:03,111 - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,112 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,113 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,113 - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (after filtering).
2025-07-11 10:05:03,127 - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,128 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,128 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,129 - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (after filtering).
2025-07-11 10:05:03,152 - root - INFO - Loaded 211 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,153 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,153 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,154 - root - INFO - Loaded 211 rows of ADA/USDT data from cache (after filtering).
2025-07-11 10:05:03,173 - root - INFO - Loaded 211 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,174 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,175 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,175 - root - INFO - Loaded 211 rows of LINK/USDT data from cache (after filtering).
2025-07-11 10:05:03,195 - root - INFO - Loaded 211 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,196 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,197 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,197 - root - INFO - Loaded 211 rows of TRX/USDT data from cache (after filtering).
2025-07-11 10:05:03,210 - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,211 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,212 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,212 - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (after filtering).
2025-07-11 10:05:03,229 - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,232 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,233 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,234 - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (after filtering).
2025-07-11 10:05:03,252 - root - INFO - Loaded 211 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,254 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,254 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,254 - root - INFO - Loaded 211 rows of BNB/USDT data from cache (after filtering).
2025-07-11 10:05:03,272 - root - INFO - Loaded 211 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,273 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 10:05:03,273 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,274 - root - INFO - Loaded 211 rows of DOT/USDT data from cache (after filtering).
2025-07-11 10:05:03,274 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 10:05:03,274 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,274 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,275 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,276 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 10:05:03,297 - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-11 10:05:03,297 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 10:05:03,297 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-11 10:05:03,297 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 10:05:03,297 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:05:03,307 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:03,307 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-11 10:05:03,307 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 10:05:03,308 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 10:05:03,308 - root - INFO - Override: combination_method = consensus
2025-07-11 10:05:03,308 - root - INFO - Override: long_threshold = 0.1
2025-07-11 10:05:03,308 - root - INFO - Override: short_threshold = -0.1
2025-07-11 10:05:03,308 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-11 10:05:03,309 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 10:05:03,309 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-11 10:05:03,309 - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 10:05:03,329 - root - INFO - Loaded 271 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 10:05:03,329 - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 10:05:03,329 - root - INFO - Loaded 271 rows of BTC/USDT data from cache (after filtering).
2025-07-11 10:05:03,330 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 10:05:03,330 - root - INFO - Fetched BTC data: 271 candles from 2024-10-13 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:03,330 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 10:05:03,367 - root - INFO - Generated PGO Score signals: {-1: 111, 0: 34, 1: 126}
2025-07-11 10:05:03,368 - root - INFO - Generated pgo signals: 271 values
2025-07-11 10:05:03,368 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-11 10:05:03,368 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 10:05:03,379 - root - INFO - Generated BB Score signals: {-1: 109, 0: 32, 1: 130}
2025-07-11 10:05:03,380 - root - INFO - Generated Bollinger Band signals: 271 values
2025-07-11 10:05:03,380 - root - INFO - Generated bollinger_bands signals: 271 values
2025-07-11 10:05:03,817 - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 10:05:03,818 - root - INFO - Generated dwma_score signals: 271 values
2025-07-11 10:05:03,878 - root - INFO - Generated DEMA Supertrend signals
2025-07-11 10:05:03,878 - root - INFO - Signal distribution: {-1: 156, 0: 1, 1: 114}
2025-07-11 10:05:03,878 - root - INFO - Generated DEMA Super Score signals
2025-07-11 10:05:03,878 - root - INFO - Generated dema_super_score signals: 271 values
2025-07-11 10:05:04,007 - root - INFO - Generated DPSD signals
2025-07-11 10:05:04,007 - root - INFO - Signal distribution: {-1: 105, 0: 87, 1: 79}
2025-07-11 10:05:04,008 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 10:05:04,008 - root - INFO - Generated dpsd_score signals: 271 values
2025-07-11 10:05:04,021 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 10:05:04,027 - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 10:05:04,031 - root - INFO - Generated aad_score signals: 271 values
2025-07-11 10:05:04,126 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 10:05:04,127 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 10:05:04,127 - root - INFO - Generated dynamic_ema_score signals: 271 values
2025-07-11 10:05:04,277 - root - INFO - Generated quantile_dema_score signals: 271 values
2025-07-11 10:05:04,285 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-11 10:05:04,286 - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 10:05:04,286 - root - INFO - Generated combined MTPI signals: 271 values using consensus method
2025-07-11 10:05:04,287 - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 10:05:04,288 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-11 10:05:04,291 - root - INFO - Saving configuration to config/settings.yaml...
2025-07-11 10:05:04,300 - root - INFO - Configuration saved successfully.
2025-07-11 10:05:04,300 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-11 10:05:04,301 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:05:04,314 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:04,314 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-11 10:05:04,315 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-11 10:05:04,315 - root - INFO - Using ratio calculation method: independent
2025-07-11 10:05:04,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,369 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:04,397 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:04,397 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,424 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:04,432 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:04,453 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 10:05:04,454 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,474 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 10:05:04,480 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:04,506 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:04,506 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,526 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:04,535 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:04,560 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:04,561 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,583 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:04,590 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:04,607 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 10:05:04,607 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,624 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 10:05:04,630 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:04,657 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:04,657 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,674 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:04,680 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:04,703 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:04,703 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,720 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:04,727 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:04,748 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:04,748 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,770 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:04,779 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:04,802 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:04,802 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,819 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:04,825 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:04,845 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:04,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,863 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:04,871 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:04,892 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,915 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:04,937 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 10:05:04,937 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:04,962 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 10:05:04,969 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:04,993 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:04,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,020 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:05,025 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:05,048 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:05:05,077 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:05,096 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:05,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,113 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:05,120 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:05,139 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:05,139 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,156 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:05,162 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:05,181 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:05,181 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,201 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:05,208 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:05,235 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 10:05:05,236 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,255 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 10:05:05,261 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:05,280 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:05,280 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,298 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:05,305 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:05,325 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:05,325 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,342 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:05,348 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:05,370 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:05,370 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,388 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:05,394 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:05,414 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:05,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,432 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:05,439 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:05,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,486 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:05,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,532 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:05,553 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 10:05:05,553 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,570 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 10:05:05,577 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:05,598 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 10:05:05,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,618 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 10:05:05,626 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:05,645 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,669 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:05,690 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:05,691 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,709 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:05,716 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:05,741 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:05,741 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,763 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:05,771 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:05,795 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 10:05:05,795 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,820 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 10:05:05,825 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:05,845 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:05,846 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,863 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:05,870 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:05,891 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:05,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,909 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:05,915 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:05,936 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:05,937 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,955 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:05,960 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:05,979 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 10:05:05,979 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:05,999 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 10:05:06,007 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:06,032 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:06,034 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,069 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 10:05:06,078 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:06,105 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,105 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,129 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,139 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:06,162 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,162 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,181 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,188 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:06,210 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:06,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,230 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:06,237 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:06,259 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,259 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,280 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:06,288 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:06,313 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,339 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:06,360 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,393 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:06,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,464 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:06,501 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 10:05:06,502 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,524 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 10:05:06,530 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:06,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,587 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:06,609 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,634 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:06,656 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:06,656 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,676 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:06,682 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:06,702 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,726 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:06,745 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,775 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:06,797 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:06,798 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,817 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:06,823 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:06,843 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,870 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:06,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,925 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:06,954 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 10:05:06,954 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:06,981 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 10:05:06,994 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:07,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,054 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:07,088 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,127 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:07,149 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:07,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,170 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:07,177 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:07,199 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,199 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,221 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,230 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:07,253 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 10:05:07,253 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,270 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 10:05:07,277 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:07,295 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:07,295 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,312 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:07,319 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:07,338 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,338 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,357 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,364 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:07,387 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:07,387 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,408 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:07,414 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:07,435 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,435 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,451 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 10:05:07,459 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:07,477 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,500 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:07,523 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,551 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:07,571 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:07,571 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,588 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:07,596 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:07,615 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,642 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:07,659 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,681 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:07,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,727 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:07,744 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,770 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:07,789 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,810 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:07,828 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:07,829 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,845 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:07,852 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:07,871 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:07,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,888 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:07,895 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:07,915 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:07,915 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,938 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:07,945 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:07,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:07,995 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:08,020 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,044 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:08,069 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:08,070 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,091 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:08,100 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:08,125 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,153 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:08,171 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:08,171 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,187 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:08,194 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:08,211 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,234 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:08,252 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,275 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:08,292 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,314 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:08,336 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,361 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:08,380 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,405 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:08,424 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 10:05:08,424 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,442 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 10:05:08,449 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:08,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,493 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:08,513 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 10:05:08,514 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,531 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 10:05:08,537 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:08,555 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,576 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:08,593 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,614 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:08,635 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,657 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:08,674 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,694 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:08,711 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,731 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:08,751 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:08,752 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,768 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:08,774 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:08,795 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,831 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:08,857 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,879 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:08,896 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:08,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,913 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:08,919 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:08,936 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,957 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:08,976 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:08,976 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:08,992 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:08,998 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:09,015 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:09,015 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,031 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:09,037 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:09,056 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 10:05:09,057 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,072 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 10:05:09,078 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:09,095 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,115 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:09,133 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:09,134 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,150 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:09,160 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:09,178 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:09,178 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,194 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:09,202 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:09,227 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,258 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:09,285 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,285 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,309 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,318 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:09,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,362 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:09,380 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,402 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:09,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,443 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:09,465 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,487 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:09,504 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,526 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:09,544 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 10:05:09,544 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,561 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 10:05:09,569 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:09,586 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,607 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:09,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,651 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:09,678 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,700 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:09,718 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,718 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,734 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,740 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:09,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,778 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:09,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,822 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:09,842 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,861 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:09,879 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,900 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:09,918 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,937 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:09,954 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,954 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:09,970 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:09,975 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:09,992 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,013 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:10,032 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:10,032 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,047 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:10,053 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:10,071 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:10,071 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,089 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:10,096 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:10,113 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:10,113 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,129 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:10,136 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:10,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,173 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:10,192 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:10,192 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,210 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:10,215 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:10,234 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:10,234 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,252 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:10,257 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:10,275 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,295 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:10,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,334 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:10,352 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,373 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:10,390 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,411 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:10,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,449 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:10,467 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,489 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:10,506 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,527 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:10,544 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,565 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:10,582 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 10:05:10,582 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,599 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 10:05:10,605 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:10,622 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:10,622 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,640 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 10:05:10,645 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:10,662 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:10,662 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,678 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 10:05:10,685 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:10,702 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 10:05:10,702 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,720 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 10:05:10,726 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:10,743 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,764 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:10,784 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,809 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:10,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,848 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:10,866 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,888 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:10,905 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,927 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:10,944 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:10,965 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:10,983 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,005 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:11,022 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:11,022 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,038 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:11,043 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:11,060 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:11,060 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,076 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 10:05:11,081 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:11,102 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,102 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,117 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,123 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:11,140 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 10:05:11,140 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,156 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 10:05:11,161 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:11,178 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 10:05:11,178 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,194 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 10:05:11,199 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:11,219 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:11,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,236 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:11,241 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:11,260 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:11,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,275 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 10:05:11,281 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:11,301 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 10:05:11,302 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,318 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 10:05:11,324 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:11,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,361 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:11,377 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,378 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,393 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,399 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:11,416 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,438 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:11,455 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,476 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:11,494 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,494 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,510 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,515 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:11,532 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,549 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:11,556 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:11,572 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:11,573 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,588 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 10:05:11,594 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:11,612 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:11,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,629 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 10:05:11,635 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:11,652 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 10:05:11,653 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,668 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 10:05:11,673 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:11,690 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,705 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,711 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:11,728 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 10:05:11,728 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,743 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 10:05:11,749 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:11,770 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:11,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,787 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 10:05:11,793 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:11,810 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,827 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:11,833 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:11,850 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,872 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:11,889 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,909 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-11 10:05:11,930 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,954 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-11 10:05:11,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:11,992 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-11 10:05:12,010 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,031 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-11 10:05:12,052 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:12,052 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,068 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 10:05:12,073 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-11 10:05:12,090 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:12,090 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,105 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 10:05:12,111 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-11 10:05:12,128 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:12,128 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,148 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 10:05:12,156 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-11 10:05:12,176 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 10:05:12,177 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,194 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 10:05:12,199 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-11 10:05:12,216 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 10:05:12,216 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,231 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 10:05:12,237 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-11 10:05:12,256 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:12,256 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,272 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 10:05:12,278 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-11 10:05:12,296 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,318 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-11 10:05:12,336 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:12,336 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,353 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 10:05:12,358 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-11 10:05:12,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,395 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-11 10:05:12,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 10:05:12,435 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-11 10:05:14,254 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-11 10:05:14,254 - root - INFO - Finished calculating daily scores. DataFrame shape: (211, 14)
2025-07-11 10:05:14,254 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-07-11 10:05:14,257 - root - INFO - Date ranges for each asset:
2025-07-11 10:05:14,258 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,258 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,258 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,259 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,260 - root - INFO - Common dates range: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 10:05:14,261 - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-10 (151 candles)
2025-07-11 10:05:14,263 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-11 10:05:14,263 - root - INFO -    Execution Method: candle_close
2025-07-11 10:05:14,263 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-11 10:05:14,263 - root - INFO -    Signal generated and executed immediately
2025-07-11 10:05:14,334 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,334 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,334 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,334 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,334 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,335 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,335 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,335 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,335 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,335 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,336 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,336 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,336 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,336 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,340 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 10:05:14,341 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-07-11 10:05:14,341 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-07-11 10:05:14,341 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-07-11 10:05:14,341 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,341 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:14,341 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-07-11 10:05:14,344 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,344 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,345 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,345 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,349 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,353 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,353 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,353 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,353 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,354 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,355 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,355 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,355 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,355 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,360 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,360 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-07-11 10:05:14,360 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-07-11 10:05:14,360 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-07-11 10:05:14,360 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,360 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:14,360 - root - INFO -    Buying: ['BNB/USDT']
2025-07-11 10:05:14,360 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-07-11 10:05:14,363 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,363 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,364 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,364 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,364 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,364 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,364 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,368 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,372 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,372 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,372 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,372 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,372 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,372 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,373 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 10:05:14,377 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,382 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,389 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,394 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,402 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,402 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-07-11 10:05:14,402 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-07-11 10:05:14,402 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-07-11 10:05:14,402 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,402 - root - INFO -    Selling: ['BNB/USDT']
2025-07-11 10:05:14,403 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:14,403 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-07-11 10:05:14,409 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,414 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,422 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,428 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,428 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-07-11 10:05:14,428 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-07-11 10:05:14,429 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-07-11 10:05:14,429 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,429 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:14,429 - root - INFO -    Buying: ['BNB/USDT']
2025-07-11 10:05:14,429 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-07-11 10:05:14,477 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,478 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-07-11 10:05:14,478 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-07-11 10:05:14,478 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-07-11 10:05:14,478 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,478 - root - INFO -    Selling: ['BNB/USDT']
2025-07-11 10:05:14,479 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:14,479 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-07-11 10:05:14,496 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,503 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,509 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,517 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,524 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,529 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,538 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,538 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-07-11 10:05:14,538 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-07-11 10:05:14,538 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-07-11 10:05:14,538 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,538 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:14,538 - root - INFO -    Buying: ['ADA/USDT']
2025-07-11 10:05:14,539 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-07-11 10:05:14,544 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,551 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,557 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,563 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,569 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,575 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,581 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,589 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,590 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-07-11 10:05:14,590 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-07-11 10:05:14,590 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-07-11 10:05:14,590 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,590 - root - INFO -    Selling: ['ADA/USDT']
2025-07-11 10:05:14,590 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:14,590 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-07-11 10:05:14,597 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,604 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,610 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,618 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,618 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-07-11 10:05:14,618 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-07-11 10:05:14,618 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-07-11 10:05:14,619 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,619 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:14,619 - root - INFO -    Buying: ['ADA/USDT']
2025-07-11 10:05:14,619 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-07-11 10:05:14,625 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,631 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,631 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-07-11 10:05:14,631 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-07-11 10:05:14,631 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-07-11 10:05:14,631 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,632 - root - INFO -    Selling: ['ADA/USDT']
2025-07-11 10:05:14,632 - root - INFO -    Buying: ['BNB/USDT']
2025-07-11 10:05:14,632 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-07-11 10:05:14,639 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,645 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,653 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,653 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-07-11 10:05:14,653 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-07-11 10:05:14,653 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-07-11 10:05:14,653 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,653 - root - INFO -    Selling: ['BNB/USDT']
2025-07-11 10:05:14,653 - root - INFO -    Buying: ['XRP/USDT']
2025-07-11 10:05:14,654 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-07-11 10:05:14,662 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,670 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,670 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-07-11 10:05:14,670 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-07-11 10:05:14,670 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-07-11 10:05:14,670 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,670 - root - INFO -    Selling: ['XRP/USDT']
2025-07-11 10:05:14,670 - root - INFO -    Buying: ['BNB/USDT']
2025-07-11 10:05:14,670 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-07-11 10:05:14,677 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,682 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,688 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,694 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,694 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-07-11 10:05:14,694 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-07-11 10:05:14,694 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-07-11 10:05:14,694 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,694 - root - INFO -    Selling: ['BNB/USDT']
2025-07-11 10:05:14,694 - root - INFO -    Buying: ['AVAX/USDT']
2025-07-11 10:05:14,694 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-07-11 10:05:14,701 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,701 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-07-11 10:05:14,701 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-07-11 10:05:14,702 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-07-11 10:05:14,702 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,702 - root - INFO -    Selling: ['AVAX/USDT']
2025-07-11 10:05:14,702 - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 10:05:14,702 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 10:05:14,707 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,713 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,721 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,727 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,727 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-07-11 10:05:14,727 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-07-11 10:05:14,727 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-07-11 10:05:14,727 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,727 - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 10:05:14,727 - root - INFO -    Buying: ['BNB/USDT']
2025-07-11 10:05:14,728 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-07-11 10:05:14,734 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,734 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-07-11 10:05:14,734 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-07-11 10:05:14,734 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-07-11 10:05:14,734 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,735 - root - INFO -    Selling: ['BNB/USDT']
2025-07-11 10:05:14,735 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:14,735 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-07-11 10:05:14,741 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,746 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,753 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,759 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,764 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,771 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,777 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,785 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,791 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,797 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,804 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,810 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,816 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,822 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,828 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,836 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,842 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,849 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,849 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-07-11 10:05:14,849 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-07-11 10:05:14,849 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-07-11 10:05:14,849 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,850 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:14,850 - root - INFO -    Buying: ['SOL/USDT']
2025-07-11 10:05:14,850 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-07-11 10:05:14,858 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,864 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,873 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,879 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,887 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,887 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-11 10:05:14,887 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-11 10:05:14,887 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-11 10:05:14,887 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:14,888 - root - INFO -    Selling: ['SOL/USDT']
2025-07-11 10:05:14,888 - root - INFO -    Buying: ['SUI/USDT']
2025-07-11 10:05:14,888 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-11 10:05:14,943 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,951 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,976 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,987 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:14,996 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,006 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,012 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,019 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,025 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,030 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,036 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,041 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,046 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,052 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,057 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,062 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,063 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-11 10:05:15,063 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-11 10:05:15,063 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-11 10:05:15,063 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,063 - root - INFO -    Selling: ['SUI/USDT']
2025-07-11 10:05:15,063 - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 10:05:15,063 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 10:05:15,069 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,074 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,079 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,086 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,091 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,096 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,102 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,107 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,112 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:05:15,120 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,126 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,126 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-11 10:05:15,126 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-11 10:05:15,126 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-11 10:05:15,126 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,126 - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 10:05:15,127 - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 10:05:15,127 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-11 10:05:15,132 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,139 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,139 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-11 10:05:15,139 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-11 10:05:15,139 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-11 10:05:15,139 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,140 - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 10:05:15,140 - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 10:05:15,140 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 10:05:15,145 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,152 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,153 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-07-11 10:05:15,153 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-07-11 10:05:15,153 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-07-11 10:05:15,153 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,153 - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 10:05:15,153 - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 10:05:15,153 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-07-11 10:05:15,158 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,163 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,170 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,175 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,180 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,186 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,192 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,197 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,203 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,208 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,213 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,220 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,225 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,229 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,236 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,241 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,245 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,252 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,257 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,262 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,269 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,274 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,279 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,285 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,290 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,295 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,302 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,302 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-11 10:05:15,302 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-11 10:05:15,302 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-11 10:05:15,302 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,302 - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 10:05:15,303 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:15,303 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-07-11 10:05:15,308 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,313 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,320 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,325 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,330 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,330 - root - INFO - ASSET CHANGE DETECTED on 2025-06-26:
2025-07-11 10:05:15,330 - root - INFO -    Signal Date: 2025-06-25 (generated at 00:00 UTC)
2025-07-11 10:05:15,331 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-26 00:00 UTC (immediate)
2025-07-11 10:05:15,331 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,331 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:15,331 - root - INFO -    Buying: ['BTC/USDT']
2025-07-11 10:05:15,331 - root - INFO -    BTC/USDT buy price: $106947.0600 (close price)
2025-07-11 10:05:15,337 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,343 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,347 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,354 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,359 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,364 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,371 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,376 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,381 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,389 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,395 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,402 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,402 - root - INFO - ASSET CHANGE DETECTED on 2025-07-08:
2025-07-11 10:05:15,402 - root - INFO -    Signal Date: 2025-07-07 (generated at 00:00 UTC)
2025-07-11 10:05:15,403 - root - INFO -    AUTOMATIC EXECUTION: 2025-07-08 00:00 UTC (immediate)
2025-07-11 10:05:15,403 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,403 - root - INFO -    Selling: ['BTC/USDT']
2025-07-11 10:05:15,403 - root - INFO -    Buying: ['TRX/USDT']
2025-07-11 10:05:15,403 - root - INFO -    TRX/USDT buy price: $0.2874 (close price)
2025-07-11 10:05:15,408 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,408 - root - INFO - ASSET CHANGE DETECTED on 2025-07-09:
2025-07-11 10:05:15,408 - root - INFO -    Signal Date: 2025-07-08 (generated at 00:00 UTC)
2025-07-11 10:05:15,409 - root - INFO -    AUTOMATIC EXECUTION: 2025-07-09 00:00 UTC (immediate)
2025-07-11 10:05:15,409 - root - INFO -    Execution Delay: 0 hours
2025-07-11 10:05:15,409 - root - INFO -    Selling: ['TRX/USDT']
2025-07-11 10:05:15,409 - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 10:05:15,409 - root - INFO -    AAVE/USDT buy price: $301.6800 (close price)
2025-07-11 10:05:15,414 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 10:05:15,444 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-07-11 10:05:15,445 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-07-11 10:05:15,445 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-07-11 10:05:15,445 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-07-11 10:05:15,445 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-07-11 10:05:15,445 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-07-11 10:05:15,446 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-11 10:05:15,447 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 10:05:15,448 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-07-11 10:05:15,448 - root - INFO - Swap trade at 2025-06-26 00:00:00+00:00: TRX/USDT -> BTC/USDT
2025-07-11 10:05:15,448 - root - INFO - Swap trade at 2025-07-08 00:00:00+00:00: BTC/USDT -> TRX/USDT
2025-07-11 10:05:15,448 - root - INFO - Swap trade at 2025-07-09 00:00:00+00:00: TRX/USDT -> AAVE/USDT
2025-07-11 10:05:15,448 - root - INFO - Total trades: 25 (Entries: 1, Exits: 0, Swaps: 24)
2025-07-11 10:05:15,451 - root - INFO - Strategy execution completed in 1s
2025-07-11 10:05:15,451 - root - INFO - DEBUG: self.elapsed_time = 1.1959788799285889 seconds
2025-07-11 10:05:15,459 - root - INFO - Saved allocation history to allocation_history_1d_1d_no_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-11 10:05:15,459 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-11 10:05:15,460 - root - INFO - Assets included in buy-and-hold comparison:
2025-07-11 10:05:15,460 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-11 10:05:15,460 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-11 10:05:15,461 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-11 10:05:15,462 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-11 10:05:15,462 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-11 10:05:15,462 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-11 10:05:15,462 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-11 10:05:15,462 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-11 10:05:15,463 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-11 10:05:15,464 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,465 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,468 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,469 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,470 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,471 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,473 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,474 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,475 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,476 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,478 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,479 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,480 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,481 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 10:05:15,484 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 211 points
2025-07-11 10:05:15,484 - root - INFO - ETH/USDT B&H total return: 10.90%
2025-07-11 10:05:15,486 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 211 points
2025-07-11 10:05:15,486 - root - INFO - BTC/USDT B&H total return: 19.07%
2025-07-11 10:05:15,488 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 211 points
2025-07-11 10:05:15,489 - root - INFO - SOL/USDT B&H total return: -18.02%
2025-07-11 10:05:15,490 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 211 points
2025-07-11 10:05:15,490 - root - INFO - SUI/USDT B&H total return: 8.55%
2025-07-11 10:05:15,492 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 211 points
2025-07-11 10:05:15,492 - root - INFO - XRP/USDT B&H total return: 5.09%
2025-07-11 10:05:15,494 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 211 points
2025-07-11 10:05:15,494 - root - INFO - AAVE/USDT B&H total return: 22.23%
2025-07-11 10:05:15,495 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 211 points
2025-07-11 10:05:15,495 - root - INFO - AVAX/USDT B&H total return: -19.36%
2025-07-11 10:05:15,497 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 211 points
2025-07-11 10:05:15,497 - root - INFO - ADA/USDT B&H total return: -4.86%
2025-07-11 10:05:15,499 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 211 points
2025-07-11 10:05:15,499 - root - INFO - LINK/USDT B&H total return: -19.03%
2025-07-11 10:05:15,501 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 211 points
2025-07-11 10:05:15,502 - root - INFO - TRX/USDT B&H total return: 19.37%
2025-07-11 10:05:15,503 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 211 points
2025-07-11 10:05:15,503 - root - INFO - PEPE/USDT B&H total return: 29.41%
2025-07-11 10:05:15,505 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 211 points
2025-07-11 10:05:15,505 - root - INFO - DOGE/USDT B&H total return: -23.63%
2025-07-11 10:05:15,507 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 211 points
2025-07-11 10:05:15,507 - root - INFO - BNB/USDT B&H total return: 10.85%
2025-07-11 10:05:15,508 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 10:05:15,509 - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 10:05:15,509 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:05:15,517 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:15,525 - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 10:05:15,621 - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 10:05:15,627 - root - INFO - Configuration loaded successfully.
2025-07-11 10:05:16,763 - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,763 - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,763 - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,763 - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,763 - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,764 - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,765 - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,765 - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,765 - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 10:05:16,765 - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 10:05:16,766 - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,766 - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,766 - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,766 - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,766 - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,766 - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,767 - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 10:05:16,779 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 10:05:16,780 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 10:05:16,782 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 10:05:16,782 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-07-11 10:05:16,784 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 10:05:16,784 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 10:05:16,784 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 10:05:16,785 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 10:05:16,786 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:16,786 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 10:05:16,791 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250704_run_********_100455.csv
2025-07-11 10:05:16,791 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250704_run_********_100455.csv
2025-07-11 10:05:16,791 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 10:05:16,791 - root - INFO - Results type: <class 'dict'>
2025-07-11 10:05:16,791 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 10:05:16,791 - root - INFO - Success flag set to: True
2025-07-11 10:05:16,792 - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 10:05:16,792 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 10:05:16,792 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 10:05:16,792 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 10:05:16,792 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-07-11 10:05:16,792 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-07-11 10:05:16,792 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 10:05:16,792 - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 10:05:16,792 - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 10:05:16,792 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 10:05:16,792 - root - INFO -   - success: <class 'bool'>
2025-07-11 10:05:16,793 - root - INFO -   - message: <class 'str'>
2025-07-11 10:05:16,793 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-07-11 10:05:16,793 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 10:05:16,793 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: AAVE/EUR
2025-07-11 10:05:16,793 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00     BTC/EUR
2025-07-07 00:00:00+00:00     BTC/EUR
2025-07-08 00:00:00+00:00     TRX/EUR
2025-07-09 00:00:00+00:00    AAVE/EUR
2025-07-10 00:00:00+00:00    AAVE/EUR
dtype: object
2025-07-11 10:05:16,793 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:16,794 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 10:05:16,794 - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: AAVE/EUR -> SUI/EUR
2025-07-11 10:05:16,794 - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 10:05:16,810 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 10:05:16,810 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 10:05:16,810 - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 10:05:16,810 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 10:05:16,810 - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 10:05:16,810 - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 10:05:16,810 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 10:05:16,810 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-07-11 10:05:16,812 - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 10:05:16,812 - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=paper
2025-07-11 10:05:16,812 - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 10:05:16,812 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 10:05:16,812 - root - INFO - Attempting to enter position for SUI/EUR in paper mode
2025-07-11 10:05:16,812 - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 10:05:16,812 - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: paper
2025-07-11 10:05:16,812 - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 10:05:16,812 - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 10:05:16,812 - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 10:05:16,812 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange bitvavo
2025-07-11 10:05:16,815 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-11 10:05:16,815 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-11 10:05:17,109 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-11 10:05:17,110 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 10:05:17,182 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 10:05:17,183 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752217512252, 'datetime': '2025-07-11T07:05:12.252Z', 'high': 3.0319, 'low': 2.7271, 'bid': 2.9322, 'bidVolume': 2310.72, 'ask': 2.9343, 'askVolume': 1553.42035686, 'vwap': 2.87246630859547, 'open': 2.7337, 'close': 2.9358, 'last': 2.9358, 'previousClose': None, 'change': 0.2021, 'percentage': 7.392910707100267, 'average': 2.83475, 'baseVolume': 2943606.44132995, 'quoteVolume': 8455410.32848489, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752131112252', 'timestamp': '1752217512252', 'open': '2.7337', 'openTimestamp': '1752131118882', 'high': '3.0319', 'low': '2.7271', 'last': '2.9358', 'closeTimestamp': '1752217474352', 'bid': '2.932200', 'bidSize': '2310.72000000', 'ask': '2.934300', 'askSize': '1553.42035686', 'volume': '2943606.44132995', 'volumeQuote': '8455410.328484888386'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 10:05:17,183 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9358
2025-07-11 10:05:17,183 - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9358
2025-07-11 10:05:17,184 - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 10:05:17,184 - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 10:05:17,184 - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 10:05:17,184 - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 10:05:17,184 - root - INFO - Available balance for EUR: 100.********
2025-07-11 10:05:17,187 - root - INFO - Loaded market info for 176 trading pairs
2025-07-11 10:05:17,187 - root - WARNING - Order value 0.******** is below minimum order value 5.0 for SUI/EUR
2025-07-11 10:05:17,188 - root - INFO - Using minimum amount for cost 1.******** for SUI/EUR
2025-07-11 10:05:17,188 - root - INFO - Calculated position size for SUI/EUR: 1.******** (using 1% of 100, accounting for fees)
2025-07-11 10:05:17,188 - root - INFO - Calculated position size: 1.******** SUI
2025-07-11 10:05:17,188 - root - INFO - Entering position for SUI/EUR: 1.******** units at 2.******** (value: 5.******** EUR)
2025-07-11 10:05:17,188 - root - INFO - Executing paper market buy order for SUI/EUR
2025-07-11 10:05:17,189 - root - WARNING - Order value 4.******** is below minimum order value 5.0 for SUI/EUR
2025-07-11 10:05:17,189 - root - INFO - Using minimum amount for cost 1.******** for SUI/EUR
2025-07-11 10:05:17,189 - root - INFO - Paper trading buy for SUI/EUR: original=1.********, adjusted=1.********, after_fee=1.********
2025-07-11 10:05:17,190 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-11 10:05:17,190 - root - INFO - Created paper market buy order: SUI/EUR, amount: 1.****************, price: 2.9358
2025-07-11 10:05:17,190 - root - INFO - Filled amount: 1.******** SUI
2025-07-11 10:05:17,190 - root - INFO - Order fee: 0.00500000 EUR
2025-07-11 10:05:17,190 - root - INFO - Successfully entered position: SUI/EUR, amount: 1.********, price: 2.********
2025-07-11 10:05:17,192 - root - INFO - Trade executed: BUY SUI/EUR, amount=1.********, price=2.********, filled=1.********
2025-07-11 10:05:17,192 - root - INFO -   Fee: 0.00500000 EUR
2025-07-11 10:05:17,192 - root - INFO - TRADE SUCCESS - SUI/EUR: Successfully updated current asset
2025-07-11 10:05:17,193 - root - INFO - Trade executed: BUY SUI/EUR, amount=1.********, price=2.********, filled=1.********
2025-07-11 10:05:17,193 - root - INFO -   Fee: 0.00500000 EUR
2025-07-11 10:05:17,194 - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 10:05:17,194 - root - INFO - Trade executed: {'success': True, 'symbol': 'SUI/EUR', 'side': 'buy', 'amount': 1.********10961237, 'price': 2.9358, 'order': {'id': 'paper-1752217517-SUI/EUR-buy-1.****************', 'symbol': 'SUI/EUR', 'side': 'buy', 'type': 'market', 'amount': 1.****************, 'price': 2.9358, 'cost': 5.0, 'fee': {'cost': 0.005, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 1.****************, 'remaining': 0, 'timestamp': 1752217517189, 'datetime': '2025-07-11T10:05:17.189670', 'trades': [], 'average': 2.9358, 'average_price': 2.9358}, 'filled_amount': 1.****************, 'fee': {'cost': 0.005, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'SUI', 'timestamp': '2025-07-11T10:05:17.190920'}
2025-07-11 10:05:17,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 10:05:17,346 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 10:05:17,347 - root - INFO - Asset scores (sorted by score):
2025-07-11 10:05:17,347 - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 10:05:17,347 - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,347 - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,347 - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,347 - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,347 - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,347 - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,348 - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 10:05:17,349 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 10:05:17,349 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-07-11 10:05:17,349 - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 10:05:17,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 10:05:17,430 - root - INFO - Strategy execution completed successfully in 22.10 seconds
2025-07-11 10:05:17,433 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-07-11 10:05:25,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:05:35,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:05:45,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:05:55,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:05,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:15,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:25,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:35,443 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:45,770 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:06:55,904 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:06,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:16,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:26,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:36,293 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:46,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:07:56,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:06,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:16,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:26,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:36,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:46,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:08:56,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:06,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:16,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:26,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:36,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:46,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:09:56,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:06,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:16,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:27,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:37,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:47,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:10:57,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:07,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:17,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:27,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:37,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:47,326 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:11:57,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:07,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:17,461 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:27,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:37,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:47,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:12:57,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:07,656 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:17,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:27,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:37,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:47,822 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:13:57,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:07,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:17,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:27,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:38,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:48,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:14:58,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:08,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:18,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:28,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:38,254 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:48,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:15:58,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:08,397 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:18,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:28,476 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:38,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:48,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:16:58,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:08,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:18,662 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:28,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:38,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:48,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:17:58,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:08,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:18,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:28,937 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:38,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:49,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:18:59,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:09,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:19,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:29,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:39,218 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:49,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:19:59,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:09,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:19,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:29,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:39,472 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:49,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:20:59,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:21:09,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:21:19,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:21:29,671 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:21:39,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:21:50,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:00,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:10,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:20,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:30,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:40,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:22:50,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:01,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:11,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:21,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:31,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:41,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:23:51,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:01,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:11,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:21,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:31,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:41,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:24:51,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:01,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:11,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:21,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:31,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:41,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:25:51,711 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:01,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:11,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:21,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:31,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:41,908 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:26:51,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:01,993 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:12,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:22,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:32,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:42,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:27:52,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:02,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:12,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:22,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:32,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:42,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:28:52,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:02,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:12,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:22,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:32,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:42,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:29:52,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:02,784 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:12,824 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:22,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:32,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:42,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:30:52,987 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:03,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:13,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:23,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:33,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:43,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:31:53,213 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:03,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:13,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:23,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:33,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:43,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:32:53,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:03,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:13,519 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:23,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:33,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:43,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:33:53,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:03,713 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:13,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:23,800 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:33,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:43,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:34:53,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:03,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:14,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:24,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:34,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:44,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:35:54,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:04,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:14,277 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:24,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:34,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:44,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:36:54,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:04,729 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:14,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:24,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:34,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:44,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:37:55,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:05,078 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:15,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:25,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:35,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:45,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:38:55,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:05,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:15,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:25,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:35,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:45,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:39:55,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:05,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:15,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:25,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:35,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:45,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:40:55,842 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:05,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:15,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:25,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:36,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:46,041 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:41:56,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:06,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:16,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:26,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:36,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:46,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:42:56,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:06,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:16,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:26,482 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:36,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:46,610 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:43:56,646 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:06,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:16,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:26,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:36,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:46,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:44:56,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:06,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:16,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:27,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:37,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:47,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:45:57,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:07,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:17,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:27,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:37,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:47,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:46:57,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:07,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:17,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:27,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:37,506 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:47,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:47:57,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:07,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:17,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:27,699 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:37,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:47,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:48:57,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:07,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:17,947 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:27,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:38,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:48,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:49:58,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:08,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:18,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:28,225 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:38,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:48,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:50:58,348 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:08,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:18,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:28,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:38,551 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:49,039 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:51:59,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:52:10,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:52:20,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:52:30,108 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:52:40,146 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:52:50,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:00,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:10,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:20,301 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:30,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:40,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:53:50,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:00,449 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:10,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:20,526 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:30,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:40,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:54:50,641 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:00,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:10,729 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:20,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:30,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:40,981 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:55:51,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:01,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:11,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:21,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:31,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:41,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:56:51,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:01,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:11,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:21,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:31,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:41,482 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:57:51,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:01,573 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:11,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:21,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:31,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:41,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:58:51,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:01,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:11,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:21,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:32,034 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:42,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 10:59:52,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:02,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:12,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:22,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:32,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:42,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:00:52,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 11:01:02,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 12:05:35,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 12:05:35,892 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 12:05:35,903 - root - ERROR - Error getting balance: bitvavo GET https://api.bitvavo.com/v2/balance
2025-07-11 12:05:39,217 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 12:05:39,466 - root - ERROR - Error getting all balances: bitvavo GET https://api.bitvavo.com/v2/balance
2025-07-11 12:05:40,522 - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 12:05:40,664 - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 12:05:40,805 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-11 12:05:40,880 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 12:05:41,457 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-11 12:05:42,263 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 15:08:55,473 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exception occurred: bitvavo GET https://api.bitvavo.com/v2/ticker/24h?market=SUI-EUR
2025-07-11 15:08:55,578 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exception type: <class 'ccxt.base.errors.NetworkError'>
2025-07-11 15:08:55,600 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Error message: bitvavo GET https://api.bitvavo.com/v2/ticker/24h?market=SUI-EUR
2025-07-11 15:08:55,632 - root - ERROR - Error getting price for SUI/EUR: bitvavo GET https://api.bitvavo.com/v2/ticker/24h?market=SUI-EUR
2025-07-11 15:08:55,651 - root - ERROR - [DEBUG] PRICE - SUI/EUR: Returning 0.0 (other error)
2025-07-11 15:08:55,844 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-07-11 15:08:55,850 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:08:59,071 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:08:59,101 - root - WARNING - Network error sending notification (attempt 1/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:08:59,104 - root - INFO - Retrying in 5 seconds...
2025-07-11 15:08:59,364 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:00,131 - root - WARNING - Network connection lost at 2025-07-11 15:09:00
2025-07-11 15:09:00,266 - root - INFO - Saving state due to network issues
2025-07-11 15:09:00,292 - root - INFO - Saved state to C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\background_service_********_100455.json
2025-07-11 15:09:07,175 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:08,103 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:08,176 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:09,247 - root - WARNING - Network error sending notification (attempt 1/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:09,497 - root - WARNING - Network error sending notification (attempt 2/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:09,512 - root - INFO - Retrying in 5 seconds...
2025-07-11 15:09:09,627 - root - INFO - Retrying in 10 seconds...
2025-07-11 15:09:14,805 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:15,415 - root - WARNING - Network error sending notification (attempt 2/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:15,415 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 4610, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:15,515 - root - INFO - Retrying in 10 seconds...
2025-07-11 15:09:20,370 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:20,577 - root - ERROR - Failed to send notification: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 15:09:20,614 - root - INFO - Status update sent
2025-07-11 15:09:29,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 15:09:39,067 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:09:57,835 - root - INFO - Successfully saved state after connection loss
2025-07-11 15:10:07,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:10:17,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:10:26,827 - root - WARNING - Network is down, skipping scheduled tasks
2025-07-11 15:10:55,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:10:58,753 - root - INFO - Network connection restored at 2025-07-11 15:10:58
2025-07-11 15:10:59,173 - root - INFO - Connection was down for approximately 118.6 seconds
2025-07-11 15:10:59,176 - root - INFO - Network recovery callback triggered after 118.6 seconds downtime
2025-07-11 15:11:02,756 - root - INFO - Attempting to send network recovery notification via Telegram
2025-07-11 15:11:03,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 15:11:03,134 - root - INFO - Network recovery notification sent successfully
2025-07-11 15:11:03,147 - root - INFO - Successfully executed recovery callback
2025-07-11 15:11:07,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:11:17,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-07-11 15:11:26,829 - root - INFO - Network is back up, resuming scheduled tasks
2025-07-11 15:11:28,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
