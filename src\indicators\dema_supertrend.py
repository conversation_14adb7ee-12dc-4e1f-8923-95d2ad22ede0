#!/usr/bin/env python3
"""
DEMA Supertrend Indicator - MTPI Component

Based on TradingView PineScript implementation:
- Uses DEMA (Double Exponential Moving Average) as source for Supertrend calculation
- Parameters: Supertrend Length=19, Multiplier=2.8, DEMA Length=17, Source=high
- Generates crossover signals for long/short positions

Signal Logic:
- Long (1): Supertrend direction crossover above 0 (bullish)
- Short (-1): Supertrend direction crossunder below 0 (bearish)
- Maintains signal state until opposite signal occurs

PineScript Reference:
subject = input.int(19,"Supertrend Len", minval = 2)
mul = input.float(2.8, "Supertrend Multiple", step=0.05)
demalen = input.int(17, "Dema len")
srcDEMA = input.source(high, "Dema source")
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_dema(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate DEMA (Double Exponential Moving Average).
    
    DEMA formula:
    EMA1 = EMA(src, length)
    EMA2 = EMA(EMA1, length)
    DEMA = 2 * EMA1 - EMA2
    
    Args:
        src: Source price series
        length: Period length for DEMA calculation
        
    Returns:
        DEMA series
    """
    try:
        # Calculate first EMA
        ema1 = src.ewm(span=length, adjust=False).mean()
        
        # Calculate second EMA (EMA of EMA1)
        ema2 = ema1.ewm(span=length, adjust=False).mean()
        
        # Calculate DEMA: 2 * EMA1 - EMA2
        dema = 2 * ema1 - ema2
        
        return dema
        
    except Exception as e:
        logging.error(f"Error calculating DEMA: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range.
    
    Args:
        df: DataFrame with OHLCV data
        period: Period for ATR calculation
        
    Returns:
        ATR series
    """
    try:
        high = df['high']
        low = df['low'] 
        close = df['close']
        
        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # ATR is the moving average of True Range
        atr = true_range.rolling(window=period, min_periods=1).mean()
        
        return atr
        
    except Exception as e:
        logging.error(f"Error calculating ATR: {e}")
        return pd.Series(index=df.index, dtype=float)

def calculate_dema_supertrend(df: pd.DataFrame,
                             atr_period: int = 19,
                             multiplier: float = 2.8,
                             dema_length: int = 17,
                             src_col: str = 'close') -> Tuple[pd.Series, pd.Series]:
    """
    Calculate DEMA Supertrend indicator based on the provided PineScript.

    PineScript Logic:
    src = input.source(hlc3, "Dema source")  # Configurable source (hlc3, close, etc.)
    dema = ta.dema(src, demalen)  # DEMA of the configured source

    Args:
        df: DataFrame with OHLCV data
        atr_period: Period for ATR calculation (subject parameter)
        multiplier: ATR multiplier (mul parameter)
        dema_length: Period for DEMA calculation (demalen parameter)
        src_col: Source for DEMA calculation (supports: close, high, low, hlc3, hl2, ohlc4)

    Returns:
        Tuple of (supertrend_line, direction)
    """
    try:
        # Step 1: Calculate source price series (supports calculated fields)
        # In PineScript: src = input.source(hlc3, "Dema source")
        # dema = ta.dema(src, demalen)
        if src_col == 'hlc3':
            src = (df['high'] + df['low'] + df['close']) / 3
        elif src_col == 'hl2':
            src = (df['high'] + df['low']) / 2
        elif src_col == 'ohlc4':
            src = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        elif src_col in df.columns:
            src = df[src_col]
        else:
            logging.warning(f"Unknown source column '{src_col}', defaulting to 'close'")
            src = df['close']

        # Step 2: Calculate DEMA from the source
        dema = calculate_dema(src, dema_length)
        
        # Step 3: Calculate ATR
        atr = calculate_atr(df, atr_period)
        
        # Step 4: Calculate basic upper and lower bands using DEMA as source
        upper_band = dema + (multiplier * atr)
        lower_band = dema - (multiplier * atr)

        # Step 5: Calculate final supertrend bands with trend logic
        close = df['close']
        
        # Initialize arrays
        final_upper = pd.Series(index=df.index, dtype=float)
        final_lower = pd.Series(index=df.index, dtype=float)
        supertrend = pd.Series(index=df.index, dtype=float)
        direction = pd.Series(index=df.index, dtype=int)
        
        # First value initialization
        final_upper.iloc[0] = upper_band.iloc[0]
        final_lower.iloc[0] = lower_band.iloc[0]
        direction.iloc[0] = 1  # Start with uptrend
        supertrend.iloc[0] = final_lower.iloc[0]
        
        # Calculate supertrend values following PineScript logic
        for i in range(1, len(df)):
            # Upper band logic: u := u < pu or close[1] > pu ? u : pu
            if upper_band.iloc[i] < final_upper.iloc[i-1] or close.iloc[i-1] > final_upper.iloc[i-1]:
                final_upper.iloc[i] = upper_band.iloc[i]
            else:
                final_upper.iloc[i] = final_upper.iloc[i-1]
            
            # Lower band logic: l := l > pl or close[1] < pl ? l : pl
            if lower_band.iloc[i] > final_lower.iloc[i-1] or close.iloc[i-1] < final_lower.iloc[i-1]:
                final_lower.iloc[i] = lower_band.iloc[i]
            else:
                final_lower.iloc[i] = final_lower.iloc[i-1]
            
            # Direction logic from PineScript
            # int d = na
            # float st = na
            # pt = st[1]
            # if na(atr[1])
            #     d := 1
            # else if pt == pu
            #     d := close > u ? -1 : 1
            # else
            #     d := close < l ? 1 : -1

            prev_supertrend = supertrend.iloc[i-1]
            prev_upper = final_upper.iloc[i-1]

            if pd.isna(atr.iloc[i-1]):
                direction.iloc[i] = 1
            elif prev_supertrend == prev_upper:
                # Previous supertrend was upper band (pt == pu)
                # d := close > u ? -1 : 1
                direction.iloc[i] = -1 if close.iloc[i] > final_upper.iloc[i] else 1
            else:
                # Previous supertrend was lower band (pt != pu, so pt == pl)
                # d := close < l ? 1 : -1
                direction.iloc[i] = 1 if close.iloc[i] < final_lower.iloc[i] else -1
            
            # Supertrend line: st := d == -1 ? l : u
            supertrend.iloc[i] = final_lower.iloc[i] if direction.iloc[i] == -1 else final_upper.iloc[i]
        
        return supertrend, direction

    except Exception as e:
        logging.error(f"Error calculating DEMA supertrend: {e}")
        return pd.Series(index=df.index, dtype=float), pd.Series(index=df.index, dtype=int)

def generate_dema_supertrend_signal(df: pd.DataFrame,
                                   atr_period: int = 19,
                                   multiplier: float = 2.8,
                                   dema_length: int = 17,
                                   src_col: str = 'close') -> pd.Series:
    """
    Generate DEMA Supertrend signals based on direction crossovers.

    Follows the PineScript logic:
    stl1 = ta.crossunder(d, 0)  # Long signal
    sts1 = ta.crossover(d, 0)   # Short signal

    Args:
        df: DataFrame with OHLCV data
        atr_period: Supertrend ATR period
        multiplier: Supertrend multiplier
        dema_length: DEMA calculation period
        src_col: Source column for DEMA calculation

    Returns:
        Series with signals: 1 (Long), -1 (Short), 0 (Neutral)
    """
    try:
        # Calculate DEMA supertrend
        supertrend_line, direction = calculate_dema_supertrend(
            df, atr_period, multiplier, dema_length, src_col
        )

        # Detect crossovers following PineScript logic
        direction_prev = direction.shift(1)

        # PineScript crossover detection:
        # stl1 = ta.crossunder(d, 0) -> direction crosses under 0 (from 1 to -1)
        # sts1 = ta.crossover(d, 0) -> direction crosses over 0 (from -1 to 1)
        #
        # NOTE: In the PineScript logic:
        # L = stl1 (Long when direction crosses UNDER 0, i.e., 1 to -1)
        # S = sts1 (Short when direction crosses OVER 0, i.e., -1 to 1)
        # This seems counterintuitive but matches the original PineScript

        crossunder_signal = (direction == -1) & (direction_prev == 1)  # crossunder(d, 0)
        crossover_signal = (direction == 1) & (direction_prev == -1)   # crossover(d, 0)

        # Apply PineScript signal logic exactly as written:
        # L = stl1 (crossunder triggers LONG)
        # S = sts1 (crossover triggers SHORT)
        long_signal = crossunder_signal   # L = stl1
        short_signal = crossover_signal   # S = sts1

        # Initialize signal series
        signal = pd.Series(0, index=df.index, dtype=int)

        # Apply signal logic with state persistence following PineScript:
        # var demaSuperScore = 0
        # if L and not S
        #     demaSuperScore := 1
        # if S
        #     demaSuperScore := -1
        current_signal = 0
        for i in range(len(df)):
            L = long_signal.iloc[i]
            S = short_signal.iloc[i]

            if L and not S:
                current_signal = 1
            elif S:
                current_signal = -1
            # Keep previous signal if no crossover

            signal.iloc[i] = current_signal

        # Log signal statistics
        signal_counts = signal.value_counts().sort_index()
        logging.info(f"Generated DEMA Supertrend signals")
        logging.info(f"Signal distribution: {dict(signal_counts)}")

        return signal

    except Exception as e:
        logging.error(f"Error generating DEMA supertrend signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def calculate_dema_supertrend_score(df: pd.DataFrame, **kwargs) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate complete DEMA Supertrend indicator with components.

    Args:
        df: DataFrame with OHLCV data
        **kwargs: Additional parameters

    Returns:
        Tuple of (signal, supertrend_line, direction)
    """
    try:
        # Extract parameters with defaults matching PineScript
        atr_period = kwargs.get('atr_period', 19)      # subject
        multiplier = kwargs.get('multiplier', 2.8)     # mul
        dema_length = kwargs.get('dema_length', 17)    # demalen
        src_col = kwargs.get('src_col', 'close')       # src (main price series)

        # Calculate components
        supertrend_line, direction = calculate_dema_supertrend(
            df, atr_period, multiplier, dema_length, src_col
        )

        # Generate signals
        signal = generate_dema_supertrend_signal(
            df, atr_period, multiplier, dema_length, src_col
        )

        return signal, supertrend_line, direction

    except Exception as e:
        logging.error(f"Error calculating DEMA supertrend score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=int))

# Default configuration matching PineScript parameters
DEFAULT_DEMA_SUPERTREND_CONFIG = {
    'atr_period': 19,        # subject - Supertrend Len
    'multiplier': 2.8,       # mul - Supertrend Multiple
    'dema_length': 17,       # demalen - Dema len
    'src_col': 'close'       # src - source for DEMA calculation (supports: close, high, low, hlc3, hl2, ohlc4)
}
