#!/usr/bin/env python
"""
Test script to verify the test Telegram bot setup.
"""

import os
import sys
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import notification modules
try:
    from src.notification.telegram_bot import TelegramNotifier
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    print("ERROR: Telegram modules not available")
    sys.exit(1)

def test_live_bot():
    """Test the live Telegram bot."""
    print("\n" + "=" * 50)
    print("TESTING LIVE TELEGRAM BOT")
    print("=" * 50)
    
    # Get live bot credentials
    live_token = os.getenv('TELEGRAM_BOT_TOKEN')
    live_chat_id = os.getenv('TELEGRAM_CHAT_ID')
    
    if not live_token or not live_chat_id:
        print("❌ Live bot credentials not found in environment")
        return False
    
    print(f"Live bot token: {live_token[:10]}...")
    print(f"Live chat ID: {live_chat_id}")
    
    try:
        # Initialize live bot
        live_bot = TelegramNotifier(
            token=live_token,
            chat_id=live_chat_id,
            notification_level='standard'
        )
        
        # Send test message
        message = f"🧪 **Live Bot Test**\n\nThis is a test message from the LIVE bot.\nTime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        success = live_bot.send_notification(message, level='standard')
        
        if success:
            print("✅ Live bot test message sent successfully!")
            return True
        else:
            print("❌ Failed to send live bot test message")
            return False
            
    except Exception as e:
        print(f"❌ Error testing live bot: {e}")
        return False

def test_test_bot():
    """Test the test Telegram bot."""
    print("\n" + "=" * 50)
    print("TESTING TEST TELEGRAM BOT")
    print("=" * 50)
    
    # Get test bot credentials
    test_token = os.getenv('TELEGRAM_BOT_TOKEN_TEST')
    test_chat_id = os.getenv('TELEGRAM_CHAT_ID_TEST')
    
    if not test_token or not test_chat_id:
        print("❌ Test bot credentials not found in environment")
        print("Please set TELEGRAM_BOT_TOKEN_TEST and TELEGRAM_CHAT_ID_TEST in your .env file")
        return False
    
    print(f"Test bot token: {test_token[:10]}...")
    print(f"Test chat ID: {test_chat_id}")
    
    try:
        # Initialize test bot
        test_bot = TelegramNotifier(
            token=test_token,
            chat_id=test_chat_id,
            notification_level='standard'
        )
        
        # Send test message
        message = f"🧪 **Test Bot Test**\n\nThis is a test message from the TEST bot.\nTime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        success = test_bot.send_notification(message, level='standard')
        
        if success:
            print("✅ Test bot test message sent successfully!")
            return True
        else:
            print("❌ Failed to send test bot test message")
            return False
            
    except Exception as e:
        print(f"❌ Error testing test bot: {e}")
        return False

def main():
    """Main function to test both bots."""
    print("TELEGRAM BOT SETUP VERIFICATION")
    print("This script will test both your live and test Telegram bots")
    print("You should receive messages from both bots if setup is correct")
    
    # Test live bot
    live_success = test_live_bot()
    
    # Test test bot
    test_success = test_test_bot()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Live bot: {'✅ Working' if live_success else '❌ Failed'}")
    print(f"Test bot: {'✅ Working' if test_success else '❌ Failed'}")
    
    if live_success and test_success:
        print("\n🎉 Both bots are working correctly!")
        print("You can now use:")
        print("  - python background_service.py (for live bot)")
        print("  - python background_service.py --test (for test bot)")
    elif test_success:
        print("\n⚠️  Test bot is working, but live bot failed")
        print("Check your live bot credentials in .env file")
    elif live_success:
        print("\n⚠️  Live bot is working, but test bot failed")
        print("Follow the setup instructions in TELEGRAM_TEST_BOT_SETUP.md")
    else:
        print("\n❌ Both bots failed")
        print("Check your .env file and bot credentials")

if __name__ == "__main__":
    main()
