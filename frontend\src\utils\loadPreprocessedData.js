// Utility to load preprocessed data for direct visualization
import axios from 'axios';

// Function to load preprocessed data
export async function loadPreprocessedData() {
  try {
    // First try to load from the API
    const response = await axios.get('/api/equity');
    const data = response.data;

    // Check if the data already has color information
    if (data.curves?.strategy?.length > 0 && data.curves.strategy[0].color) {
      console.log('API data already has color information');
      return data;
    }

    // Process the data on the fly
    console.log('Processing data on the fly...');

    // Define asset colors
    const assetColors = {
      'BTC/USDT': '#f7931a', // Bitcoin orange
      'ETH/USDT': '#627eea', // Ethereum blue
      'SOL/USDT': '#00ffbd', // Solana green
      'out-of-market': '#999999' // Gray for out-of-market periods
    };

    // Create a map of time to asset for quick lookup
    const timeToAssetMap = {};

    if (data.assetChanges && data.assetChanges.length > 0) {
      // Sort asset changes by date to ensure chronological order
      const sortedAssetChanges = [...data.assetChanges].sort((a, b) => a.date - b.date);

      // Process all data points to assign the correct asset
      if (data.curves && data.curves.strategy) {
        for (let i = 0; i < data.curves.strategy.length; i++) {
          const point = data.curves.strategy[i];
          const pointTime = point.time;

          // Find the most recent asset change before this point
          let currentAsset = 'out-of-market'; // Default if no asset change is found

          for (let j = sortedAssetChanges.length - 1; j >= 0; j--) {
            if (pointTime >= sortedAssetChanges[j].date) {
              currentAsset = sortedAssetChanges[j].asset || 'out-of-market';
              break; // Found the most recent asset change
            }
          }

          // Store the asset for this time
          timeToAssetMap[pointTime] = currentAsset;
        }
      }
    }

    // Find the most recent MTPI signal before each point
    const mtpiSignals = data.metadata?.rawMtpiSignals || [];

    // Now map the strategy data points with the correct colors
    if (data.curves && data.curves.strategy) {
      const coloredData = data.curves.strategy.map(point => {
        // Get the asset for this point
        const asset = timeToAssetMap[point.time] || 'out-of-market';

        // Find the most recent MTPI signal before this point
        let mtpiValue = null;
        for (let j = mtpiSignals.length - 1; j >= 0; j--) {
          if (point.time >= mtpiSignals[j].time) {
            mtpiValue = mtpiSignals[j].value;
            break;
          }
        }

        // Determine if MTPI allows trading
        const mtpiAllowsTrade = mtpiValue === null || mtpiValue === 1;

        // If MTPI doesn't allow trading, use out-of-market color
        const assetToUse = mtpiAllowsTrade ? asset : 'out-of-market';

        // Get the color for this asset
        let color = assetColors[assetToUse] || '#0d6efd';

        // If the asset is empty string, it means we're out of market
        if (assetToUse === '') {
          color = assetColors['out-of-market'];
        }

        return {
          time: point.time,
          value: point.value,
          color: color,
          asset: assetToUse,
          mtpi: mtpiValue
        };
      });

      // Replace the strategy data with the colored data
      data.curves.strategy = coloredData;
    }

    return data;
    }
  } catch (error) {
    console.error('Error loading data:', error);
    throw error;
  }
}
