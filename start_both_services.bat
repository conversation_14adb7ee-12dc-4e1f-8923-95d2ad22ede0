@echo off
echo ========================================
echo Starting Multiple Background Services
echo ========================================

echo.
echo Starting YOUR service (using .env) in new window...
start "Asset Rotation - Your Account" cmd /k "python start_service_main.py"

echo.
echo Waiting 5 seconds before starting Person 2's service...
timeout /t 5 /nobreak > nul

echo.
echo Starting PERSON 2's service (using .env.person2) in new window...
start "Asset Rotation - Person 2" cmd /k "python start_service_person2.py"

echo.
echo ========================================
echo Both services started in separate windows
echo ========================================
echo.
echo Your service uses: .env
echo Person 2's service uses: .env.person2
echo.
echo To stop the services:
echo 1. Close the respective command windows, or
echo 2. Press Ctrl+C in each window
echo.
pause
