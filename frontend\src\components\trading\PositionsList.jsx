import React from 'react';
import {
  Box,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  Spin<PERSON>,
  useColorModeValue,
  Flex,
  Button,
} from '@chakra-ui/react';

const PositionsList = ({ positions, isLoading, onSelectAsset, selectedAsset }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const hoverBgColor = useColorModeValue('gray.50', 'gray.600');
  
  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  // Format percentage
  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };
  
  // Get color based on value
  const getColorScheme = (value) => {
    if (value > 0) return 'green';
    if (value < 0) return 'red';
    return 'gray';
  };
  
  // Handle row click
  const handleRowClick = (symbol) => {
    if (onSelectAsset) {
      onSelectAsset(symbol);
    }
  };
  
  if (isLoading) {
    return (
      <Box 
        bg={bgColor} 
        p={4} 
        borderRadius="lg" 
        boxShadow="sm"
        height="100%"
      >
        <Text fontSize="lg" fontWeight="bold" mb={4}>
          Current Positions
        </Text>
        <Flex justify="center" align="center" height="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
    >
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Current Positions
      </Text>
      
      {positions && positions.length > 0 ? (
        <Box overflowX="auto">
          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>Asset</Th>
                <Th isNumeric>Amount</Th>
                <Th isNumeric>Entry Price</Th>
                <Th isNumeric>Current Price</Th>
                <Th isNumeric>Value</Th>
                <Th isNumeric>P&L</Th>
              </Tr>
            </Thead>
            <Tbody>
              {positions.map((position) => (
                <Tr 
                  key={position.symbol}
                  cursor="pointer"
                  onClick={() => handleRowClick(position.symbol)}
                  bg={selectedAsset === position.symbol ? hoverBgColor : 'transparent'}
                  _hover={{ bg: hoverBgColor }}
                >
                  <Td fontWeight={selectedAsset === position.symbol ? 'bold' : 'normal'}>
                    {position.symbol}
                  </Td>
                  <Td isNumeric>{position.amount.toFixed(6)}</Td>
                  <Td isNumeric>{formatCurrency(position.entry_price)}</Td>
                  <Td isNumeric>{formatCurrency(position.current_price)}</Td>
                  <Td isNumeric>{formatCurrency(position.current_value)}</Td>
                  <Td isNumeric>
                    <Badge colorScheme={getColorScheme(position.pnl_pct)}>
                      {formatPercentage(position.pnl_pct)}
                    </Badge>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      ) : (
        <Flex 
          direction="column" 
          justify="center" 
          align="center" 
          height="200px"
          border="1px dashed"
          borderColor="gray.200"
          borderRadius="md"
          p={4}
        >
          <Text color="gray.500" mb={4}>No open positions</Text>
          <Button 
            size="sm" 
            colorScheme="blue" 
            variant="outline"
            onClick={() => onSelectAsset && onSelectAsset('BTC/USDT')}
          >
            View Market Data
          </Button>
        </Flex>
      )}
    </Box>
  );
};

export default PositionsList;
