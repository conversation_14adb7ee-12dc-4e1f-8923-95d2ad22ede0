#!/usr/bin/env python3
"""
Test script to verify sandbox/testnet support for Kraken and Bitvavo.
This script tests if setSandboxMode(true) works for both exchanges.
"""

import os
import sys
import ccxt
import logging
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_sandbox_mode(exchange_id: str) -> dict:
    """Test if sandbox mode works for a specific exchange."""
    results = {
        'exchange': exchange_id,
        'sandbox_supported': False,
        'sandbox_connection': False,
        'sandbox_balance_access': False,
        'live_connection': False,
        'live_balance_access': False,
        'errors': []
    }
    
    print(f"\n{'='*60}")
    print(f"TESTING SANDBOX SUPPORT FOR {exchange_id.upper()}")
    print(f"{'='*60}")
    
    try:
        # Get credentials (we'll use dummy ones for sandbox if needed)
        credentials = get_exchange_credentials(exchange_id)
        api_key = credentials.get('api_key', 'dummy_key')
        api_secret = credentials.get('api_secret', 'dummy_secret')
        
        # Get exchange class
        exchange_class = getattr(ccxt, exchange_id, None)
        if not exchange_class:
            results['errors'].append(f"Exchange '{exchange_id}' not found in ccxt")
            return results
        
        # Test 1: Check if setSandboxMode method exists
        exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': True,
        })
        
        if hasattr(exchange, 'setSandboxMode'):
            print(f"✅ setSandboxMode method exists")
            results['sandbox_supported'] = True
            
            # Test 2: Try to enable sandbox mode
            try:
                exchange.setSandboxMode(True)
                print(f"✅ Sandbox mode enabled successfully")
                print(f"   Sandbox flag: {exchange.sandbox}")
                
                # Test 3: Try to connect in sandbox mode
                try:
                    markets = exchange.load_markets()
                    print(f"✅ Sandbox connection successful ({len(markets)} markets)")
                    results['sandbox_connection'] = True
                    
                    # Test 4: Try to access balance in sandbox (might fail without proper sandbox credentials)
                    try:
                        balance = exchange.fetch_balance()
                        print(f"✅ Sandbox balance access successful")
                        results['sandbox_balance_access'] = True
                        
                        # Show sandbox balances
                        non_zero = {k: v for k, v in balance['free'].items() if float(v) > 0}
                        if non_zero:
                            print(f"   Sandbox balances: {non_zero}")
                        else:
                            print(f"   No balances in sandbox (expected for new sandbox account)")
                            
                    except Exception as e:
                        error_msg = f"Sandbox balance access failed: {e}"
                        results['errors'].append(error_msg)
                        print(f"❌ {error_msg}")
                        
                        # This might be expected if we don't have sandbox-specific credentials
                        if 'invalid' in str(e).lower() or 'unauthorized' in str(e).lower():
                            print(f"   ℹ️ This might be expected - sandbox may need specific credentials")
                        
                except Exception as e:
                    error_msg = f"Sandbox connection failed: {e}"
                    results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Failed to enable sandbox mode: {e}"
                results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
                
        else:
            print(f"❌ setSandboxMode method not available")
            results['errors'].append("setSandboxMode method not available")
        
        # Test 5: Test live mode for comparison (only if we have real credentials)
        if credentials.get('api_key') and credentials.get('api_secret'):
            print(f"\n--- Testing LIVE mode for comparison ---")
            
            live_exchange = exchange_class({
                'apiKey': credentials.get('api_key'),
                'secret': credentials.get('api_secret'),
                'enableRateLimit': True,
                'sandbox': False,  # Explicitly set to false
            })
            
            try:
                markets = live_exchange.load_markets()
                print(f"✅ Live connection successful ({len(markets)} markets)")
                results['live_connection'] = True
                
                try:
                    balance = live_exchange.fetch_balance()
                    print(f"✅ Live balance access successful")
                    results['live_balance_access'] = True
                except Exception as e:
                    error_msg = f"Live balance access failed: {e}"
                    results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Live connection failed: {e}"
                results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        else:
            print(f"⚠️ No live credentials available for comparison")
            
    except Exception as e:
        error_msg = f"Unexpected error testing {exchange_id}: {e}"
        results['errors'].append(error_msg)
        print(f"❌ {error_msg}")
    
    return results

def test_ccxt_sandbox_info():
    """Test what CCXT says about sandbox support."""
    print(f"\n{'='*60}")
    print(f"CCXT SANDBOX INFORMATION")
    print(f"{'='*60}")
    
    exchanges_to_test = ['kraken', 'bitvavo']
    
    for exchange_id in exchanges_to_test:
        try:
            exchange_class = getattr(ccxt, exchange_id, None)
            if exchange_class:
                exchange = exchange_class()
                
                print(f"\n{exchange_id.upper()}:")
                print(f"  Has setSandboxMode: {hasattr(exchange, 'setSandboxMode')}")
                print(f"  Default sandbox: {getattr(exchange, 'sandbox', 'Not set')}")
                print(f"  Exchange name: {exchange.name}")
                print(f"  Exchange version: {getattr(exchange, 'version', 'Unknown')}")
                
                # Check if there are any sandbox-related properties
                sandbox_props = [attr for attr in dir(exchange) if 'sandbox' in attr.lower()]
                if sandbox_props:
                    print(f"  Sandbox-related properties: {sandbox_props}")
                else:
                    print(f"  No sandbox-related properties found")
                    
        except Exception as e:
            print(f"Error checking {exchange_id}: {e}")

def print_recommendations(results: dict):
    """Print recommendations based on test results."""
    print(f"\n{'='*70}")
    print(f"RECOMMENDATIONS")
    print(f"{'='*70}")
    
    kraken_results = results.get('kraken', {})
    bitvavo_results = results.get('bitvavo', {})
    
    if kraken_results.get('sandbox_supported') or bitvavo_results.get('sandbox_supported'):
        print(f"✅ GOOD NEWS: At least one exchange supports sandbox mode!")
        
        if kraken_results.get('sandbox_supported'):
            print(f"   - Kraken: Sandbox mode available")
        if bitvavo_results.get('sandbox_supported'):
            print(f"   - Bitvavo: Sandbox mode available")
            
        print(f"\n📝 NEXT STEPS:")
        print(f"1. Use sandbox mode for initial testing")
        print(f"2. Get sandbox-specific API credentials if needed")
        print(f"3. Test paper trading functionality")
        print(f"4. Switch to live mode when ready")
        
    else:
        print(f"⚠️ LIMITATION: Neither exchange appears to support sandbox mode via CCXT")
        print(f"\n📝 ALTERNATIVE APPROACHES:")
        print(f"1. Use our built-in paper trading simulator")
        print(f"2. Start with very small amounts in live mode")
        print(f"3. Test thoroughly with minimal capital first")
        print(f"4. Use stop-losses and position limits for safety")
        
    print(f"\n🔧 PAPER TRADING OPTIONS:")
    print(f"1. Built-in simulator: Set trading.mode = 'paper' in config")
    print(f"2. Exchange sandbox: Use setSandboxMode(true) if supported")
    print(f"3. Minimal live trading: Start with €10-50 per exchange")

def main():
    """Main function to test sandbox support."""
    print(f"\n{'='*70}")
    print(f"SANDBOX SUPPORT TEST")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*70}")
    
    # Test CCXT sandbox information
    test_ccxt_sandbox_info()
    
    # Test both exchanges
    exchanges = ['kraken', 'bitvavo']
    results = {}
    
    for exchange_id in exchanges:
        results[exchange_id] = test_sandbox_mode(exchange_id)
    
    # Print summary
    print(f"\n{'='*70}")
    print(f"SUMMARY")
    print(f"{'='*70}")
    
    for exchange_id, result in results.items():
        print(f"\n{exchange_id.upper()}:")
        print(f"  Sandbox supported: {'✅' if result['sandbox_supported'] else '❌'}")
        print(f"  Sandbox connection: {'✅' if result['sandbox_connection'] else '❌'}")
        print(f"  Live connection: {'✅' if result['live_connection'] else '❌'}")
        
        if result['errors']:
            print(f"  Errors: {len(result['errors'])}")
    
    # Print recommendations
    print_recommendations(results)

if __name__ == "__main__":
    main()
