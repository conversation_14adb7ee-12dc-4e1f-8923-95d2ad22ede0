#!/usr/bin/env python
"""
Token address verification script for memecoin strategy.
This script checks if the token addresses in the config file are correct.
"""

import yaml
import sys
import logging
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Known correct addresses (verified from official sources)
VERIFIED_ADDRESSES = {
    'FWOG': {
        'network': 'solana',
        'address': 'A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump',
        'source': 'Official FWOG contract'
    },
    'BONK': {
        'network': 'solana', 
        'address': 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        'source': 'Coinbase verified'
    },
    'WIF': {
        'network': 'solana',
        'address': 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
        'source': 'Solscan verified'
    },
    'PEPE': {
        'network': 'eth',
        'address': '******************************************',
        'source': 'Official PEPE contract'
    },
    'GIGA': {
        'network': 'solana',
        'address': '63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9',
        'source': 'Coinbase verified'
    },
    'POPCAT': {
        'network': 'solana',
        'address': '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr',
        'source': 'Solscan verified'
    }
}

# Known problematic addresses to flag
PROBLEMATIC_ADDRESSES = {
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC token address',
    'So11111111111111111111111111111111111111112': 'SOL (wrapped SOL) token address',
    'CKaKtYvz6dKPyMvYq9Rh3UBrnNqYZAyd7iF4hJtjUvks': 'Unknown/incorrect GIGA address'
}

def parse_asset_string(asset_string: str) -> Tuple[str, str, str, str]:
    """
    Parse asset string format: gt:network:address:symbol
    Returns: (prefix, network, address, symbol)
    """
    try:
        parts = asset_string.split(':')
        if len(parts) != 4:
            raise ValueError(f"Invalid asset format: {asset_string}")
        return parts[0], parts[1], parts[2], parts[3]
    except Exception as e:
        logging.error(f"Error parsing asset string '{asset_string}': {e}")
        return "", "", "", ""

def verify_token_addresses(config_path: str) -> Dict:
    """
    Verify all token addresses in the config file.
    """
    print(f"\n{'='*80}")
    print(f"TOKEN ADDRESS VERIFICATION")
    print(f"{'='*80}")
    
    # Load config
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        logging.error(f"Error loading config file: {e}")
        return {}
    
    assets = config.get('settings', {}).get('assets', [])
    
    if not assets:
        print("❌ No assets found in config file")
        return {}
    
    print(f"Found {len(assets)} assets to verify\n")
    
    verification_results = {
        'correct': [],
        'incorrect': [],
        'unknown': [],
        'problematic': []
    }
    
    for i, asset_string in enumerate(assets):
        prefix, network, address, symbol = parse_asset_string(asset_string)
        
        if not all([prefix, network, address, symbol]):
            verification_results['problematic'].append({
                'index': i,
                'asset_string': asset_string,
                'issue': 'Invalid format'
            })
            continue
        
        print(f"{i+1:2d}. {symbol:<12} | {network:<8} | {address[:20]}...")
        
        # Check if address is in problematic list
        if address in PROBLEMATIC_ADDRESSES:
            issue = PROBLEMATIC_ADDRESSES[address]
            print(f"    ❌ PROBLEMATIC: {issue}")
            verification_results['problematic'].append({
                'index': i,
                'symbol': symbol,
                'network': network,
                'address': address,
                'issue': issue,
                'asset_string': asset_string
            })
            continue
        
        # Check against verified addresses
        if symbol in VERIFIED_ADDRESSES:
            verified = VERIFIED_ADDRESSES[symbol]
            if (verified['network'] == network and 
                verified['address'].lower() == address.lower()):
                print(f"    ✅ CORRECT: {verified['source']}")
                verification_results['correct'].append({
                    'index': i,
                    'symbol': symbol,
                    'network': network,
                    'address': address
                })
            else:
                print(f"    ❌ INCORRECT: Expected {verified['address']}")
                verification_results['incorrect'].append({
                    'index': i,
                    'symbol': symbol,
                    'network': network,
                    'current_address': address,
                    'correct_address': verified['address'],
                    'asset_string': asset_string
                })
        else:
            print(f"    ⚠️  UNKNOWN: Not in verified list")
            verification_results['unknown'].append({
                'index': i,
                'symbol': symbol,
                'network': network,
                'address': address,
                'asset_string': asset_string
            })
    
    # Summary
    print(f"\n{'='*80}")
    print(f"VERIFICATION SUMMARY")
    print(f"{'='*80}")
    print(f"✅ Correct addresses:     {len(verification_results['correct'])}")
    print(f"❌ Incorrect addresses:   {len(verification_results['incorrect'])}")
    print(f"⚠️  Unknown addresses:     {len(verification_results['unknown'])}")
    print(f"🚨 Problematic addresses: {len(verification_results['problematic'])}")
    
    # Detailed issues
    if verification_results['incorrect']:
        print(f"\n🔧 INCORRECT ADDRESSES TO FIX:")
        for item in verification_results['incorrect']:
            print(f"   {item['symbol']}: {item['current_address'][:20]}... → {item['correct_address'][:20]}...")
    
    if verification_results['problematic']:
        print(f"\n🚨 PROBLEMATIC ADDRESSES:")
        for item in verification_results['problematic']:
            print(f"   {item['symbol']}: {item['issue']}")
    
    if verification_results['unknown']:
        print(f"\n⚠️  UNKNOWN ADDRESSES (need manual verification):")
        for item in verification_results['unknown']:
            print(f"   {item['symbol']} ({item['network']}): {item['address'][:20]}...")
    
    return verification_results

def generate_fixes(verification_results: Dict) -> List[str]:
    """
    Generate suggested fixes for incorrect addresses.
    """
    fixes = []
    
    for item in verification_results['incorrect']:
        old_string = item['asset_string']
        new_string = old_string.replace(item['current_address'], item['correct_address'])
        fixes.append(f"Replace: {old_string}")
        fixes.append(f"With:    {new_string}")
        fixes.append("")
    
    return fixes

def main():
    """Main function."""
    config_path = "config/settings_memecoins.yaml"
    
    results = verify_token_addresses(config_path)
    
    if results['incorrect'] or results['problematic']:
        print(f"\n{'='*80}")
        print(f"SUGGESTED FIXES")
        print(f"{'='*80}")
        
        fixes = generate_fixes(results)
        for fix in fixes:
            print(fix)
    
    print(f"\n{'='*80}")
    print(f"VERIFICATION COMPLETE")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
