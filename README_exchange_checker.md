# Exchange Pair Availability Checker

This tool checks the availability of USDC/USDT trading pairs across multiple cryptocurrency exchanges to help you find alternatives to Binance.

## Supported Exchanges
- **OKX** - 0.08%/0.10% fees, EU-regulated, 350+ cryptos
- **Bybit** - 0.10%/0.10% fees, EU-regulated, 450+ cryptos  
- **Kraken** - 0.25%/0.40% fees, Fully EU-regulated, 230+ cryptos
- **Bitvavo** - 0.15%/0.25% fees, DNB-registered (NL), 200+ cryptos

## Installation

1. Install required dependencies:
```bash
pip install -r requirements_exchange_checker.txt
```

## Usage

### Basic Usage (Check all tokens on all exchanges)
```bash
python exchange_pair_checker.py
```

### Check specific tokens only
```bash
python exchange_pair_checker.py --tokens BTC ETH SOL AVAX
```

### Check specific exchanges only
```bash
python exchange_pair_checker.py --exchanges okx bybit
```

### Custom output file
```bash
python exchange_pair_checker.py --output my_results.csv
```

### Set minimum trading history requirement (default is 35 days)
```bash
python exchange_pair_checker.py --min-age-days 60
```

### Combined options
```bash
python exchange_pair_checker.py --tokens BTC ETH XRP --exchanges okx kraken --min-age-days 35 --output top_coins.csv
```

## Output

The script generates a CSV file with the following columns:
- `Token`: The base cryptocurrency (e.g., BTC, ETH)
- `Quote`: The quote currency (USDC or USDT)
- `Pair`: The trading pair (e.g., BTC/USDC)
- `OKX_available`: Boolean indicating if pair is available on OKX
- `OKX_has_enough_data`: Boolean indicating if pair has 35+ days of trading data on OKX
- `OKX_days_available`: Number of days of trading data available on OKX
- `BYBIT_available`: Boolean indicating if pair is available on Bybit
- `BYBIT_has_enough_data`: Boolean indicating if pair has 35+ days of trading data on Bybit
- `BYBIT_days_available`: Number of days of trading data available on Bybit
- (Similar columns for KRAKEN and BITVAVO)
- `Total_Exchanges_Available`: Number of exchanges where pair is listed
- `Total_Exchanges_With_Enough_Data`: Number of exchanges with 35+ days of data
- `Available_On`: List of exchanges where pair is listed
- `Available_With_Enough_Data_On`: List of exchanges with sufficient trading history

## Example Output

| Token | Quote | Pair     | OKX_available | OKX_has_enough_data | OKX_days_available | Total_Exchanges_With_Enough_Data | Available_With_Enough_Data_On |
|-------|-------|----------|---------------|---------------------|--------------------|---------------------------------|-------------------------------|
| BTC   | USDC  | BTC/USDC | True          | True                | 180                | 4                               | OKX, BYBIT, KRAKEN, BITVAVO   |
| ETH   | USDT  | ETH/USDT | True          | True                | 165                | 3                               | OKX, BYBIT, KRAKEN            |

## Features

- **Trading History Verification**: Checks if pairs have sufficient trading history (35+ days by default)
- **Combined Availability + Age Check**: Verifies both pair existence and trading maturity in one step
- **Exchange-Specific Data**: Uses each exchange's own historical data for accurate verification
- **Rate Limiting**: Built-in rate limiting to respect exchange API limits
- **Error Handling**: Robust error handling for network issues and API failures
- **Progress Tracking**: Shows progress during the checking process
- **Detailed Statistics**: Provides comprehensive statistics about pair availability and data sufficiency
- **Flexible Filtering**: Check specific tokens or exchanges as needed
- **Multiple Symbol Formats**: Handles different symbol formats across exchanges

## Notes

- The script uses public API endpoints and doesn't require API keys
- **Trading History Verification**: Checks actual trading data availability on each exchange (35+ days by default)
- Some exchanges may have rate limits, so the script includes delays between requests
- Results are saved automatically to CSV for easy analysis
- The tool checks both USDC and USDT pairs for maximum coverage
- More accurate than external age APIs since it uses exchange-specific trading data
- Helps identify not just listed pairs, but pairs with sufficient liquidity/trading history

## Troubleshooting

If you encounter issues:
1. Check your internet connection
2. Ensure you have the latest version of ccxt installed
3. Some exchanges may temporarily block requests - try again later
4. Use the `--exchanges` flag to test individual exchanges if one is causing issues
