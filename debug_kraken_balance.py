#!/usr/bin/env python3
"""
Debug script to investigate Kraken balance calculation issues.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.account import AccountManager
from src.config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_kraken_balance():
    """Debug Kraken balance calculation."""
    
    print("=== KRAKEN BALANCE DEBUGGING ===")
    
    try:
        # Initialize account manager for Kraken
        account_manager = AccountManager(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        print("\n1. RAW BALANCE DATA:")
        print("-" * 50)
        
        # Get raw balance data
        if account_manager.exchange:
            raw_balance = account_manager.exchange.fetch_balance()
            
            print("Raw balance structure:")
            for key in ['free', 'used', 'total']:
                if key in raw_balance:
                    print(f"\n{key.upper()} balances:")
                    for currency, amount in raw_balance[key].items():
                        if float(amount) > 0:
                            print(f"  {currency}: {amount}")
            
            print(f"\nInfo section: {raw_balance.get('info', 'N/A')}")
            
        print("\n2. PROCESSED BALANCE DATA:")
        print("-" * 50)
        
        # Get EUR balance using our method
        eur_balance = account_manager.get_balance('EUR')
        print(f"EUR balance (our method): {eur_balance}")
        
        # Get all balances
        all_balances = account_manager.get_all_balances()
        print(f"All non-zero balances: {all_balances}")
        
        # Get open positions
        positions = account_manager.get_open_positions()
        print(f"\nOpen positions: {len(positions)}")
        
        total_position_value = 0
        for symbol, position in positions.items():
            value = position.get('value_usdt', 0)
            amount = position.get('amount', 0)
            price = position.get('current_price', 0)
            print(f"  {symbol}: {amount:.8f} units @ {price:.8f} = {value:.8f} EUR")
            total_position_value += value
        
        print(f"\nTotal position value: {total_position_value:.8f} EUR")
        print(f"Free EUR balance: {eur_balance:.8f} EUR")
        print(f"Estimated total portfolio: {total_position_value + eur_balance:.8f} EUR")
        
        print("\n3. BALANCE BREAKDOWN ANALYSIS:")
        print("-" * 50)
        
        if account_manager.exchange and 'EUR' in raw_balance.get('total', {}):
            total_eur = float(raw_balance['total']['EUR'])
            free_eur = float(raw_balance['free']['EUR'])
            used_eur = float(raw_balance['used']['EUR'])
            
            print(f"Total EUR: {total_eur:.8f}")
            print(f"Free EUR:  {free_eur:.8f}")
            print(f"Used EUR:  {used_eur:.8f}")
            print(f"Difference (Total - Free): {total_eur - free_eur:.8f}")
            
            if used_eur > 0:
                print(f"\n⚠️ You have {used_eur:.8f} EUR in 'used' balance!")
                print("This could be:")
                print("- Pending orders")
                print("- Reserved funds")
                print("- Margin/collateral")
        
        print("\n4. EXCHANGE-SPECIFIC ANALYSIS:")
        print("-" * 50)
        
        # Check if there are any exchange-specific balance issues
        if account_manager.exchange:
            exchange_name = account_manager.exchange.id
            print(f"Exchange ID: {exchange_name}")
            
            # Check for any pending orders
            try:
                open_orders = account_manager.exchange.fetch_open_orders()
                print(f"Open orders: {len(open_orders)}")
                for order in open_orders:
                    print(f"  Order {order['id']}: {order['symbol']} {order['side']} {order['amount']} @ {order['price']}")
            except Exception as e:
                print(f"Could not fetch open orders: {e}")
        
    except Exception as e:
        print(f"Error in balance debugging: {e}")
        import traceback
        traceback.print_exc()

def compare_balance_methods():
    """Compare different balance calculation methods."""
    
    print("\n=== BALANCE METHOD COMPARISON ===")
    
    try:
        import ccxt
        
        # Initialize Kraken exchange directly
        credentials = get_exchange_credentials('kraken', 'config/settings_kraken_eur.yaml')
        
        exchange = ccxt.kraken({
            'apiKey': credentials.get('api_key', ''),
            'secret': credentials.get('api_secret', ''),
            'enableRateLimit': True,
        })
        
        # Method 1: Direct CCXT call
        balance1 = exchange.fetch_balance()
        print(f"Method 1 (Direct CCXT):")
        print(f"  Free EUR: {balance1['free'].get('EUR', 0)}")
        print(f"  Used EUR: {balance1['used'].get('EUR', 0)}")
        print(f"  Total EUR: {balance1['total'].get('EUR', 0)}")
        
        # Method 2: Our account manager
        account_manager = AccountManager('kraken', config_path='config/settings_kraken_eur.yaml')
        balance2 = account_manager.get_balance('EUR')
        print(f"\nMethod 2 (Our AccountManager): {balance2}")
        
        # Method 3: Check what our system thinks is available
        from src.trading.executor import TradingExecutor
        executor = TradingExecutor('kraken', config_path='config/settings_kraken_eur.yaml')
        
        if executor.trading_config.get('mode') == 'paper':
            balance3 = executor.paper_trading.get_balance().get('EUR', 0.0)
            print(f"Method 3 (Paper Trading): {balance3}")
        else:
            balance3 = executor.account_manager.get_balance('EUR')
            print(f"Method 3 (Trading Executor): {balance3}")
        
    except Exception as e:
        print(f"Error in balance method comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_kraken_balance()
    compare_balance_methods()
    print("\nDebugging completed!")
