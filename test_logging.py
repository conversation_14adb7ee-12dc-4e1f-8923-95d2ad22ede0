#!/usr/bin/env python3
"""
Test script to verify exchange-specific logging works correctly
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_logging_setup():
    """Test the logging setup for different exchanges"""
    
    print("🔍 Testing Exchange-Specific Logging")
    
    # Test Kraken config
    print("\n=== Testing Kraken Configuration ===")
    try:
        from background_service import BackgroundService
        
        # Test with Kraken config
        service = BackgroundService(
            config_path='config/settings_kraken_eur.yaml',
            notification_config_path='config/notifications_kraken_liepa.json',
            test_mode=True
        )
        
        # Log some test messages
        logging.info("Test message for Kraken service")
        logging.warning("Test warning for Kraken service")
        logging.error("Test error for Kraken service")
        
        print("✅ Kraken logging test completed")
        
        # Clean up
        service.stop()
        
    except Exception as e:
        print(f"❌ Error testing Kraken logging: {e}")
    
    # Check what log files were created
    print("\n=== Log Files Created ===")
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.startswith('background_service_')]
        for log_file in sorted(log_files):
            file_path = os.path.join(log_dir, log_file)
            file_size = os.path.getsize(file_path)
            print(f"📄 {log_file} ({file_size} bytes)")
            
            # Show first few lines of the log
            if file_size > 0:
                try:
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[:5]  # First 5 lines
                        for i, line in enumerate(lines, 1):
                            print(f"   {i}: {line.strip()}")
                        if len(lines) >= 5:
                            print("   ...")
                except Exception as e:
                    print(f"   Error reading file: {e}")
            else:
                print("   (empty file)")
    else:
        print("❌ No logs directory found")

if __name__ == "__main__":
    test_logging_setup()
