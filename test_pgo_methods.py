#!/usr/bin/env python3
"""
PGO Methods Comparison Test
===========================

This script examines the actual PGO calculation methods in both implementations
to identify the exact differences in the calculation logic.
"""

import os
import sys
import pandas as pd
import numpy as np
import inspect
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import both implementations
from test_allocation import AllocationTester
from allocation_report import AllocationReporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pgo_methods_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PGOMethodsTester:
    """Test and compare PGO calculation methods"""
    
    def __init__(self):
        self.tester = None
        self.reporter = None
        
    def setup_instances(self):
        """Create instances of both implementations"""
        logger.info("Creating instances for method inspection...")
        
        # Minimal parameters for instance creation
        params = {
            'timeframe': '1d',
            'mtpi_timeframe': '1d', 
            'analysis_start_date': '2023-08-26',
            'n_assets': 1,
            'transaction_fee_rate': 0.001,
            'selected_assets': ['BTC/USDT', 'ETH/USDT'],
            'use_cache': True,
            'initial_capital': 1.0,
            'wait_for_confirmed_signals': True,
            'use_mtpi': False,
            'use_weighted_allocation': False,
            'weights': None
        }
        
        self.tester = AllocationTester(**params)
        self.reporter = AllocationReporter(**params)
        
    def inspect_methods(self):
        """Inspect all methods in both classes"""
        logger.info("=== INSPECTING METHODS IN BOTH CLASSES ===")
        
        # Get all methods from both classes
        tester_methods = [method for method in dir(self.tester) if not method.startswith('_')]
        reporter_methods = [method for method in dir(self.reporter) if not method.startswith('_')]
        
        logger.info(f"AllocationTester methods: {len(tester_methods)}")
        for method in sorted(tester_methods):
            if callable(getattr(self.tester, method)):
                logger.info(f"  - {method}")
                
        logger.info(f"\nAllocationReporter methods: {len(reporter_methods)}")
        for method in sorted(reporter_methods):
            if callable(getattr(self.reporter, method)):
                logger.info(f"  - {method}")
                
        # Find PGO-related methods
        self.find_pgo_methods()
        
    def find_pgo_methods(self):
        """Find PGO-related methods in both classes"""
        logger.info("=== FINDING PGO-RELATED METHODS ===")
        
        pgo_keywords = ['pgo', 'trend', 'signal', 'score', 'calculate']
        
        tester_pgo_methods = []
        reporter_pgo_methods = []
        
        # Search in tester
        for attr_name in dir(self.tester):
            if any(keyword in attr_name.lower() for keyword in pgo_keywords):
                attr = getattr(self.tester, attr_name)
                if callable(attr):
                    tester_pgo_methods.append(attr_name)
                    
        # Search in reporter
        for attr_name in dir(self.reporter):
            if any(keyword in attr_name.lower() for keyword in pgo_keywords):
                attr = getattr(self.reporter, attr_name)
                if callable(attr):
                    reporter_pgo_methods.append(attr_name)
                    
        logger.info(f"Tester PGO-related methods: {tester_pgo_methods}")
        logger.info(f"Reporter PGO-related methods: {reporter_pgo_methods}")
        
        # Compare method signatures
        self.compare_method_signatures(tester_pgo_methods, reporter_pgo_methods)
        
    def compare_method_signatures(self, tester_methods, reporter_methods):
        """Compare method signatures between implementations"""
        logger.info("=== COMPARING METHOD SIGNATURES ===")
        
        common_methods = set(tester_methods) & set(reporter_methods)
        
        for method_name in common_methods:
            logger.info(f"\nComparing method: {method_name}")
            
            try:
                tester_method = getattr(self.tester, method_name)
                reporter_method = getattr(self.reporter, method_name)
                
                tester_sig = inspect.signature(tester_method)
                reporter_sig = inspect.signature(reporter_method)
                
                logger.info(f"  Tester signature:  {method_name}{tester_sig}")
                logger.info(f"  Reporter signature: {method_name}{reporter_sig}")
                
                if str(tester_sig) != str(reporter_sig):
                    logger.warning(f"  ⚠️  Signatures differ!")
                else:
                    logger.info(f"  ✓ Signatures match")
                    
                # Try to get source code
                self.compare_method_source(method_name, tester_method, reporter_method)
                
            except Exception as e:
                logger.error(f"Error comparing {method_name}: {e}")
                
    def compare_method_source(self, method_name, tester_method, reporter_method):
        """Compare source code of methods"""
        try:
            tester_source = inspect.getsource(tester_method)
            reporter_source = inspect.getsource(reporter_method)
            
            if tester_source == reporter_source:
                logger.info(f"  ✓ Source code identical")
            else:
                logger.warning(f"  ⚠️  Source code differs!")
                
                # Save source code for detailed comparison
                with open(f'tester_{method_name}_source.py', 'w') as f:
                    f.write(f"# AllocationTester.{method_name}\n")
                    f.write(tester_source)
                    
                with open(f'reporter_{method_name}_source.py', 'w') as f:
                    f.write(f"# AllocationReporter.{method_name}\n")
                    f.write(reporter_source)
                    
                logger.info(f"  Source code saved to tester_{method_name}_source.py and reporter_{method_name}_source.py")
                
        except Exception as e:
            logger.warning(f"  Could not get source code: {e}")
            
    def test_calculate_scores_method(self):
        """Specifically test the calculate_scores method"""
        logger.info("=== TESTING CALCULATE_SCORES METHOD ===")
        
        # Check if both have calculate_scores method
        has_tester_calc = hasattr(self.tester, 'calculate_scores')
        has_reporter_calc = hasattr(self.reporter, 'calculate_scores')
        
        logger.info(f"Tester has calculate_scores: {has_tester_calc}")
        logger.info(f"Reporter has calculate_scores: {has_reporter_calc}")
        
        if has_tester_calc and has_reporter_calc:
            # Get method signatures
            tester_calc = getattr(self.tester, 'calculate_scores')
            reporter_calc = getattr(self.reporter, 'calculate_scores')
            
            tester_sig = inspect.signature(tester_calc)
            reporter_sig = inspect.signature(reporter_calc)
            
            logger.info(f"Tester calculate_scores signature: {tester_sig}")
            logger.info(f"Reporter calculate_scores signature: {reporter_sig}")
            
            # Try to get and compare source
            try:
                tester_source = inspect.getsource(tester_calc)
                reporter_source = inspect.getsource(reporter_calc)
                
                # Save for detailed comparison
                with open('tester_calculate_scores.py', 'w') as f:
                    f.write("# AllocationTester.calculate_scores\n")
                    f.write(tester_source)
                    
                with open('reporter_calculate_scores.py', 'w') as f:
                    f.write("# AllocationReporter.calculate_scores\n")
                    f.write(reporter_source)
                    
                logger.info("calculate_scores source code saved for comparison")
                
                # Quick comparison
                if tester_source == reporter_source:
                    logger.info("✓ calculate_scores source code is identical")
                else:
                    logger.warning("⚠️  calculate_scores source code differs!")
                    
            except Exception as e:
                logger.error(f"Could not extract calculate_scores source: {e}")
                
    def test_trend_method_handling(self):
        """Test how trend methods are handled"""
        logger.info("=== TESTING TREND METHOD HANDLING ===")
        
        # Check for trend method attributes
        tester_trend_attrs = [attr for attr in dir(self.tester) if 'trend' in attr.lower()]
        reporter_trend_attrs = [attr for attr in dir(self.reporter) if 'trend' in attr.lower()]
        
        logger.info(f"Tester trend-related attributes: {tester_trend_attrs}")
        logger.info(f"Reporter trend-related attributes: {reporter_trend_attrs}")
        
        # Check current trend method values
        for attr in tester_trend_attrs:
            if not callable(getattr(self.tester, attr)):
                value = getattr(self.tester, attr)
                logger.info(f"Tester.{attr} = {value}")
                
        for attr in reporter_trend_attrs:
            if not callable(getattr(self.reporter, attr)):
                value = getattr(self.reporter, attr)
                logger.info(f"Reporter.{attr} = {value}")
                
    def create_minimal_test_data(self):
        """Create minimal test data for PGO calculation testing"""
        logger.info("=== CREATING MINIMAL TEST DATA ===")
        
        # Create simple test data
        dates = pd.date_range('2023-08-20', '2023-08-30', freq='D')
        
        # Simple price data that should give predictable PGO results
        np.random.seed(42)  # For reproducible results
        
        test_data = pd.DataFrame({
            'open': 100 + np.random.randn(len(dates)) * 2,
            'high': 102 + np.random.randn(len(dates)) * 2,
            'low': 98 + np.random.randn(len(dates)) * 2,
            'close': 100 + np.random.randn(len(dates)) * 2,
            'volume': 1000 + np.random.randn(len(dates)) * 100
        }, index=dates)
        
        # Ensure high >= close >= low
        test_data['high'] = np.maximum(test_data['high'], test_data['close'])
        test_data['low'] = np.minimum(test_data['low'], test_data['close'])
        
        logger.info(f"Created test data with {len(test_data)} rows")
        logger.info(f"Date range: {test_data.index[0]} to {test_data.index[-1]}")
        
        # Save test data
        test_data.to_csv('minimal_test_data.csv')
        logger.info("Test data saved to minimal_test_data.csv")
        
        return test_data
        
    def test_pgo_on_minimal_data(self, test_data):
        """Test PGO calculation on minimal data"""
        logger.info("=== TESTING PGO ON MINIMAL DATA ===")
        
        # Try to call PGO calculation methods directly
        pgo_length = 5  # Short length for minimal data
        
        # Test if we can access PGO calculation methods
        pgo_methods_to_test = [
            'calculate_pgo',
            '_calculate_pgo', 
            'pgo_signal',
            '_pgo_signal'
        ]
        
        for method_name in pgo_methods_to_test:
            logger.info(f"\nTesting method: {method_name}")
            
            # Test tester
            if hasattr(self.tester, method_name):
                try:
                    tester_method = getattr(self.tester, method_name)
                    tester_result = tester_method(test_data, pgo_length)
                    logger.info(f"  Tester {method_name}: {type(tester_result)}, length: {len(tester_result) if hasattr(tester_result, '__len__') else 'N/A'}")
                except Exception as e:
                    logger.warning(f"  Tester {method_name} failed: {e}")
            else:
                logger.info(f"  Tester does not have {method_name}")
                
            # Test reporter
            if hasattr(self.reporter, method_name):
                try:
                    reporter_method = getattr(self.reporter, method_name)
                    reporter_result = reporter_method(test_data, pgo_length)
                    logger.info(f"  Reporter {method_name}: {type(reporter_result)}, length: {len(reporter_result) if hasattr(reporter_result, '__len__') else 'N/A'}")
                except Exception as e:
                    logger.warning(f"  Reporter {method_name} failed: {e}")
            else:
                logger.info(f"  Reporter does not have {method_name}")

def main():
    """Main test execution"""
    logger.info("=== STARTING PGO METHODS TEST ===")
    
    tester = PGOMethodsTester()
    
    try:
        # Setup instances
        tester.setup_instances()
        
        # Inspect methods
        tester.inspect_methods()
        
        # Test specific methods
        tester.test_calculate_scores_method()
        
        # Test trend method handling
        tester.test_trend_method_handling()
        
        # Create and test minimal data
        test_data = tester.create_minimal_test_data()
        tester.test_pgo_on_minimal_data(test_data)
        
        logger.info("=== PGO METHODS TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
