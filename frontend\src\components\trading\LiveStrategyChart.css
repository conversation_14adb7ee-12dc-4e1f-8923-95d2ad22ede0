.chart-container {
  width: 100%;
  height: 500px;
  position: relative;
  background-color: #1A202C;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #1A202C;
  color: white;
  border-bottom: 1px solid #2D3748;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.scale-toggle-btn {
  background-color: #2D3748;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.scale-toggle-btn:hover {
  background-color: #4A5568;
}

.status-badge {
  margin-left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.running {
  background-color: #48BB78;
  color: white;
}

.status-badge.stopped {
  background-color: #F56565;
  color: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
  border-radius: 4px;
}

.asset-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 8px 16px;
  background-color: #1A202C;
  color: white;
  border-top: 1px solid #2D3748;
}

.asset-legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.asset-color-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.strategy-line {
  width: 20px;
  height: 3px;
  border-radius: 1px;
}

.live-data-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #48BB78;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
