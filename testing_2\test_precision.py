"""
Test script for precision utilities.
"""
import logging
import sys
from src.utils.precision import adjust_amount_for_precision
from src.utils.market_info import load_market_info, get_market_info_for_symbol

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_precision_for_symbol(symbol, amount, price):
    """Test precision adjustment for a specific symbol."""
    logging.info(f"Testing precision for {symbol} with amount {amount} and price {price}")
    
    # Get market info
    market_info = get_market_info_for_symbol(symbol)
    if market_info:
        logging.info(f"Market info for {symbol}: {market_info}")
    else:
        logging.warning(f"No market info found for {symbol}")
    
    # Test adjustment without market info
    adjusted_amount_without_info = adjust_amount_for_precision(
        amount, symbol, market_info=None, price=price, is_buy=True
    )
    logging.info(f"Adjusted amount without market info: {adjusted_amount_without_info}")
    
    # Test adjustment with market info
    adjusted_amount_with_info = adjust_amount_for_precision(
        amount, symbol, market_info=market_info, price=price, is_buy=True
    )
    logging.info(f"Adjusted amount with market info: {adjusted_amount_with_info}")
    
    return adjusted_amount_with_info

def main():
    """Main entry point for the test script."""
    # Load market info
    market_info = load_market_info()
    logging.info(f"Loaded market info for {len(market_info)} trading pairs")
    
    # Test AAVE/USDC with 10% allocation of 100 USDC
    symbol = "AAVE/USDC"
    price = 261.95  # Price from binance_usdc_pairs.json
    amount = 10.0 / price  # 10% of 100 USDC
    
    adjusted_amount = test_precision_for_symbol(symbol, amount, price)
    
    if adjusted_amount > 0:
        logging.info(f"SUCCESS: Adjusted amount for {symbol} is {adjusted_amount}")
    else:
        logging.warning(f"FAILURE: Adjusted amount for {symbol} is 0.0")
        
        # Get the minimum amount required
        market_info = get_market_info_for_symbol(symbol)
        if market_info and 'limits' in market_info and 'amount' in market_info['limits']:
            min_amount = market_info['limits']['amount'].get('min', 0.0001)
            min_cost = market_info['limits']['cost'].get('min', 5.0)
            logging.info(f"Minimum amount for {symbol}: {min_amount}")
            logging.info(f"Minimum cost for {symbol}: {min_cost}")
            logging.info(f"Required USDC for minimum amount: {min_amount * price}")
            
            # Calculate how much USDC would be needed for the minimum amount
            required_usdc = min_amount * price
            logging.info(f"To buy the minimum amount of {min_amount} {symbol}, you need {required_usdc:.2f} USDC")
            
            # Calculate what percentage of 100 USDC this would be
            percentage = (required_usdc / 100.0) * 100
            logging.info(f"This is {percentage:.2f}% of 100 USDC")
    
    # Test a few other symbols for comparison
    test_symbols = ["BTC/USDC", "ETH/USDC", "SOL/USDC", "SHIB/USDC"]
    for sym in test_symbols:
        market_info = get_market_info_for_symbol(sym)
        if market_info:
            price = market_info.get('last_price', 0)
            if price > 0:
                amount = 10.0 / price  # 10% of 100 USDC
                test_precision_for_symbol(sym, amount, price)

if __name__ == "__main__":
    main()
