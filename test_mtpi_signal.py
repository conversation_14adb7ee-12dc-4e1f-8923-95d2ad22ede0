#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test MTPI signal calculation and identify why the initial period is showing as bearish
when it should be bullish according to TradingView.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the necessary functions
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal, fetch_historical_mtpi_signals
from src.data_fetcher import fetch_ohlcv_data

def test_mtpi_signal_calculation(
    timeframe='1d',
    length=35,
    upper_threshold=1.1,
    lower_threshold=-0.58,
    start_date='2023-08-01',
    end_date='2023-10-30',
    use_cache=True
):
    """
    Test MTPI signal calculation for a specific period and visualize the results.

    Args:
        timeframe: The timeframe for the candles (e.g., '1d')
        length: The period length for PGO calculation
        upper_threshold: The threshold for bullish signals
        lower_threshold: The threshold for bearish signals
        start_date: The start date for analysis (format: 'YYYY-MM-DD')
        end_date: The end date for analysis (format: 'YYYY-MM-DD')
        use_cache: Whether to use cached data if available
    """
    logging.info(f"Testing MTPI signal calculation with parameters:")
    logging.info(f"  Timeframe: {timeframe}")
    logging.info(f"  PGO Length: {length}")
    logging.info(f"  Upper Threshold: {upper_threshold}")
    logging.info(f"  Lower Threshold: {lower_threshold}")
    logging.info(f"  Start Date: {start_date}")
    logging.info(f"  End Date: {end_date}")

    # Fetch BTC data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDC'],
        timeframe=timeframe,
        since=start_date,
        use_cache=use_cache
    )

    if not data_dict or 'BTC/USDC' not in data_dict:
        logging.error("Failed to fetch BTC data. Cannot calculate MTPI signals.")
        return

    # Get the BTC DataFrame
    btc_df = data_dict['BTC/USDC']

    # Filter data between start and end dates
    start_ts = pd.Timestamp(start_date).tz_localize('UTC')
    end_ts = pd.Timestamp(end_date).tz_localize('UTC')
    btc_df = btc_df[(btc_df.index >= start_ts) & (btc_df.index <= end_ts)]

    logging.info(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")

    # Calculate PGO values
    pgo_values = calculate_pgo(btc_df, length=length)

    # Calculate PGO signals with different skip_warmup settings
    signals_with_skip = generate_pgo_signal(
        btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=True
    )

    signals_without_skip = generate_pgo_signal(
        btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False
    )

    # Create a DataFrame for analysis
    analysis_df = pd.DataFrame({
        'close': btc_df['close'],
        'pgo': pgo_values,
        'signal_with_skip': signals_with_skip,
        'signal_without_skip': signals_without_skip
    })

    # Print the first few rows to see the initial signals
    print("\nFirst 10 rows of analysis data:")
    print(analysis_df.head(10))

    # Print the signal distribution
    print("\nSignal distribution with skip_warmup=True:")
    print(signals_with_skip.value_counts())

    print("\nSignal distribution with skip_warmup=False:")
    print(signals_without_skip.value_counts())

    # Analyze signal transitions
    transitions_with_skip = (signals_with_skip != signals_with_skip.shift(1)).sum()
    transitions_without_skip = (signals_without_skip != signals_without_skip.shift(1)).sum()

    print(f"\nSignal transitions with skip_warmup=True: {transitions_with_skip}")
    print(f"Signal transitions with skip_warmup=False: {transitions_without_skip}")

    # Find periods where signals differ
    diff_mask = signals_with_skip != signals_without_skip
    diff_count = diff_mask.sum()

    print(f"\nPeriods where signals differ: {diff_count} out of {len(signals_with_skip)} ({diff_count/len(signals_with_skip)*100:.1f}%)")

    if diff_count > 0:
        print("\nFirst 5 periods where signals differ:")
        diff_periods = analysis_df[diff_mask].head(5)
        print(diff_periods)

    # Visualize the results
    plt.figure(figsize=(14, 10))

    # Plot 1: Price
    plt.subplot(3, 1, 1)
    plt.plot(btc_df.index, btc_df['close'], label='BTC/USDC Close')
    plt.title('BTC/USDC Price')
    plt.grid(True, alpha=0.3)
    plt.legend()

    # Plot 2: PGO Values with Thresholds
    plt.subplot(3, 1, 2)
    plt.plot(btc_df.index, pgo_values, label='PGO')
    plt.axhline(y=upper_threshold, color='g', linestyle='--', label=f'Upper Threshold ({upper_threshold})')
    plt.axhline(y=lower_threshold, color='r', linestyle='--', label=f'Lower Threshold ({lower_threshold})')
    plt.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    plt.title('PGO Values')
    plt.grid(True, alpha=0.3)
    plt.legend()

    # Plot 3: Signals
    plt.subplot(3, 1, 3)
    plt.plot(btc_df.index, signals_with_skip, label='Signal (skip_warmup=True)', marker='o', markersize=4)
    plt.plot(btc_df.index, signals_without_skip, label='Signal (skip_warmup=False)', marker='x', markersize=4, alpha=0.7)
    plt.title('MTPI Signals')
    plt.yticks([-1, 0, 1], ['Bearish', 'Neutral', 'Bullish'])
    plt.grid(True, alpha=0.3)
    plt.legend()

    plt.tight_layout()
    plt.savefig('mtpi_signal_analysis.png')
    plt.close()

    logging.info("Analysis complete. Results saved to 'mtpi_signal_analysis.png'")

    # Return the analysis DataFrame for further inspection
    return analysis_df

if __name__ == '__main__':
    print("Starting MTPI signal analysis...")

    # Test with the period shown in the TradingView chart (August to October 2023)
    # The chart shows September 19, 2023 as a key date
    analysis_df = test_mtpi_signal_calculation(
        timeframe='1d',
        length=35,
        upper_threshold=1.1,
        lower_threshold=-0.58,
        start_date='2023-08-01',
        end_date='2023-10-30'
    )

    if analysis_df is None or analysis_df.empty:
        print("Error: No data returned from analysis")
        sys.exit(1)

    # Print the full analysis DataFrame
    print("\nFull analysis DataFrame:")
    print(analysis_df)

    # Focus on the specific period around September 19, 2023
    target_date = pd.Timestamp('2023-09-19').tz_localize('UTC')
    window_days = 5

    start_window = target_date - timedelta(days=window_days)
    end_window = target_date + timedelta(days=window_days)

    window_df = analysis_df[(analysis_df.index >= start_window) & (analysis_df.index <= end_window)]

    print(f"\nDetailed analysis around {target_date.date()}:")
    print(window_df)

    # Check for crossovers around this date
    if not window_df.empty:
        print("\nAnalyzing crossovers around the target date:")
        for i in range(1, len(window_df)):
            date = window_df.index[i]
            prev_date = window_df.index[i-1]
            prev_pgo = window_df['pgo'].iloc[i-1]
            curr_pgo = window_df['pgo'].iloc[i]
            upper = 1.1
            lower = -0.58

            # Check for crossovers
            bullish_crossover = prev_pgo <= upper and curr_pgo > upper
            bearish_crossover = prev_pgo >= lower and curr_pgo < lower

            # Print all PGO values for debugging
            print(f"Date: {date}, PGO: {curr_pgo:.4f}, Previous PGO: {prev_pgo:.4f}")

            if bullish_crossover or bearish_crossover:
                crossover_type = "BULLISH" if bullish_crossover else "BEARISH"
                print(f"\n{crossover_type} CROSSOVER DETECTED:")
                print(f"  Date: {date}")
                print(f"  Previous PGO ({prev_date}): {prev_pgo:.4f}")
                print(f"  Current PGO ({date}): {curr_pgo:.4f}")
                print(f"  Threshold: {upper if bullish_crossover else lower}")

    print("\nAnalysis complete. Check the generated image 'mtpi_signal_analysis.png' for visualization.")
