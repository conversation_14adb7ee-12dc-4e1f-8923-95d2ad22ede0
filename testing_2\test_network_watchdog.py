#!/usr/bin/env python
"""
Test script for the network watchdog.
This script will start the network watchdog with the new settings and wait for you to disconnect and reconnect your WiFi.
It will then check if the run-specific state file was created.
"""

import os
import sys
import time
import logging
from datetime import datetime

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"network_watchdog_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

# Import the network watchdog
from src.network_watchdog import NetworkWatchdog

# Directory for saving state
state_dir = 'data/state'
os.makedirs(state_dir, exist_ok=True)

# Generate a unique run ID for this test
run_id = datetime.now().strftime("%Y%m%d_%H%M%S")

# Mock state to save
test_state = {
    'last_execution_time': datetime.now().isoformat(),
    'test_value': 42,
    'test_string': 'Hello Watchdog',
    'run_id': run_id
}

def network_recovery_callback(downtime_seconds):
    """Called when network connectivity is restored."""
    logging.info(f"RECOVERY CALLBACK: Network recovered after {downtime_seconds:.1f} seconds")
    print(f"\n[RECOVERY] Network connection restored after {downtime_seconds:.1f} seconds downtime")
    return True

def save_state_callback():
    """Called when network issues are detected to save state."""
    logging.info("SAVE STATE CALLBACK: Saving state due to network issues")
    print("\n[SAVE STATE] Network issues detected, saving current state...")
    
    # Save state to run-specific file
    state_file = os.path.join(state_dir, f"test_network_watchdog_{run_id}.json")
    
    # Update timestamp
    test_state['timestamp'] = datetime.now().isoformat()
    
    # Save to file
    import json
    with open(state_file, 'w') as f:
        json.dump(test_state, f, indent=2)
    
    print(f"[SAVE STATE] State saved to {state_file}")
    return True

def main():
    """Main function to run the network watchdog test."""
    print("\n" + "=" * 80)
    print(f"NETWORK WATCHDOG TEST - {datetime.now()}")
    print("=" * 80)
    print(f"Log file: {log_file}")
    print(f"Run ID: {run_id}")
    print("=" * 80 + "\n")
    
    # Create the network watchdog with the new settings
    watchdog = NetworkWatchdog(
        check_interval=10,  # Check every 10 seconds
        recovery_callback=network_recovery_callback,
        state_save_callback=save_state_callback,
        max_failures=1  # Consider network down after 1 consecutive failure
    )
    
    # Start the watchdog
    watchdog.start()
    print("Network watchdog started with check_interval=10, max_failures=1")
    print("\nPlease disconnect your WiFi now and wait for the watchdog to detect it.")
    print("Then reconnect your WiFi and wait for the watchdog to detect the recovery.")
    print("\nThe test will run for 5 minutes and then check if the run-specific state file was created.")
    print("Press Ctrl+C at any time to stop the test.\n")
    
    try:
        # Run for 5 minutes
        for i in range(300):
            # Print status every 30 seconds
            if i % 30 == 0:
                status = watchdog.get_connection_status()
                print(f"\nNetwork Status: {'CONNECTED' if status['is_connected'] else 'DISCONNECTED'}")
                print(f"Consecutive failures: {watchdog.consecutive_failures}")
                
                # Check if the run-specific state file exists
                state_file = os.path.join(state_dir, f"test_network_watchdog_{run_id}.json")
                if os.path.exists(state_file):
                    print(f"Run-specific state file exists: {state_file}")
                else:
                    print(f"Run-specific state file does not exist yet: {state_file}")
            
            time.sleep(1)
        
        # Final check
        print("\n" + "=" * 80)
        print("TEST COMPLETED")
        print("=" * 80)
        
        # Check if the run-specific state file exists
        state_file = os.path.join(state_dir, f"test_network_watchdog_{run_id}.json")
        if os.path.exists(state_file):
            print(f"SUCCESS: Run-specific state file was created: {state_file}")
            
            # Read the file to verify its contents
            import json
            with open(state_file, 'r') as f:
                state = json.load(f)
            
            print("\nState file contents:")
            for key, value in state.items():
                print(f"  {key}: {value}")
        else:
            print(f"FAILURE: Run-specific state file was not created: {state_file}")
            print("This means the NetworkWatchdog did not detect your WiFi disconnection or the state_save_callback was not called.")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        # Stop the watchdog
        watchdog.stop()
        print("Network watchdog stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
