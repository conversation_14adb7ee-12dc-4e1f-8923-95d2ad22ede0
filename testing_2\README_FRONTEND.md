# Asset Rotation Dashboard

This project implements a dashboard for the Asset Rotation Strategy, with a Python backend for data processing and a React frontend for visualization.

## Project Structure

The project is organized into two main parts:

1. **Python Backend**: Handles data fetching, indicator calculation, asset scoring, and strategy simulation.
2. **React Frontend**: Provides a user interface for visualizing the data and interacting with the strategy.

```
Asset_Screener_Python/
├── src/                  # Python backend code
├── frontend/             # React frontend code
├── config/               # Configuration files
├── data/                 # Data storage (optional)
├── notebooks/            # Jupyter notebooks for analysis
├── tests/                # Unit and integration tests
├── run_api_server.py     # Script to run the API server
└── requirements.txt      # Python dependencies
```

## Setup Instructions

### Backend Setup

1. Create and activate a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install the required Python packages:
   ```
   pip install -r requirements.txt
   ```

3. Run the API server:
   ```
   python run_api_server.py
   ```
   This will start the API server on http://localhost:5000.

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install the required Node.js packages:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

3. Start the development server:
   ```
   npm run dev
   ```
   or
   ```
   yarn dev
   ```
   This will start the frontend development server on http://localhost:5173.

4. Open your browser and navigate to http://localhost:5173 to view the dashboard.

## Usage

### Dashboard

The dashboard provides the following features:

- **Asset Chart**: Displays price and volume data for the selected asset.
- **Asset Table**: Shows a comparison of all assets with their scores and performance metrics.
- **Performance Metrics**: Displays various performance metrics for the strategy.
- **Equity Chart**: Shows the equity curve of the strategy compared to a buy and hold approach.

### Settings

The settings page allows you to configure various aspects of the strategy:

- **Exchange & Assets**: Select the exchange and assets to include in the strategy.
- **Indicators**: Configure the technical indicators used for trend classification.
- **Backtest Settings**: Set the backtest period, initial capital, and rebalance frequency.
- **Display Settings**: Configure what information is displayed on the dashboard.

## Development

### Backend Development

The Python backend is organized into several modules:

- **data_fetcher.py**: Fetches OHLCV data from exchanges.
- **indicators/**: Calculates technical indicators.
- **scoring.py**: Calculates asset scores based on pairwise trend analysis.
- **strategy.py**: Simulates the rotation strategy.
- **performance.py**: Calculates performance metrics.
- **api_server.py**: Provides API endpoints for the frontend.

### Frontend Development

The React frontend is organized into several directories:
Use of tradingview libraries for chartvisualisations
- **components/**: Reusable UI components.
- **pages/**: Page components for the dashboard and settings.
- **services/**: API services for communicating with the backend.
- **utils/**: Utility functions.

## License

This project is part of the Asset Rotation Strategy system.
