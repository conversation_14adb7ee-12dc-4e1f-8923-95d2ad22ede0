# Memecoin Strategy Environment Variables Template
# Copy this file to .env.memecoin and fill in your actual values

# Production Memecoin Telegram Bot Credentials
TELEGRAM_BOT_TOKEN_MEMECOINS=your_memecoin_bot_token_here
TELEGRAM_CHAT_ID_MEMECOINS=your_memecoin_chat_id_here

# Test Memecoin Telegram Bot Credentials (optional, for testing)
TELEGRAM_BOT_TOKEN_MEMECOINS_TEST=your_test_memecoin_bot_token_here
TELEGRAM_CHAT_ID_MEMECOINS_TEST=your_test_memecoin_chat_id_here

# Instructions:
# 1. Create a new Telegram bot by messaging @BotFather
# 2. Use /newbot command and follow the instructions
# 3. Copy the bot token and paste it above
# 4. Get your chat ID by messaging @userinfobot
# 5. Copy the chat ID and paste it above
# 6. Rename this file to .env.memecoin
# 7. Load the environment variables before running the service

# Example commands to load environment variables:
# On Windows: 
#   for /f "delims=" %i in (.env.memecoin) do set %i
# On Linux/Mac:
#   export $(cat .env.memecoin | xargs)
