[Unit]
Description=Asset Rotation Strategy Background Service - Kraken EUR (Liepa)
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy
EnvironmentFile=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env.Liepa
ExecStart=/home/<USER>/asset_rotation_screener/venv/bin/python /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py --config config/settings_kraken_eur.yaml --notifications config/notifications_kraken_liepa.json
Restart=always
RestartSec=5s
StandardOutput=append:/var/log/asset-rotation-kraken-liepa.log
StandardError=append:/var/log/asset-rotation-kraken-liepa-error.log

[Install]
WantedBy=multi-user.target
