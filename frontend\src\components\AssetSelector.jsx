import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './AssetSelector.css';
import apiService from '../services/api';

/**
 * AssetSelector component for selecting which assets to include in the dashboard
 *
 * @param {Object} props Component props
 * @param {Array} props.selectedAssets Currently selected assets
 * @param {Function} props.onAssetsChange Callback when selected assets change
 * @param {Array} props.defaultAssets Default assets to select if none are selected
 * @param {number} props.maxAssets Maximum number of assets that can be selected
 */
const AssetSelector = ({ selectedAssets = [], onAssetsChange, defaultAssets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'], maxAssets = 40 }) => {
  const [availableAssets, setAvailableAssets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch available assets from Binance
  useEffect(() => {
    const fetchAvailableAssets = async () => {
      try {
        setLoading(true);
        console.log('Fetching available assets...');

        // Define default assets to use as fallback
        const defaultAssetsList = [
          'BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT', 'ADA/USDT',
          'DOGE/USDT', 'MATIC/USDT', 'DOT/USDT', 'SHIB/USDT', 'AVAX/USDT',
          'LINK/USDT', 'UNI/USDT', 'ATOM/USDT', 'LTC/USDT', 'ETC/USDT',
          'FIL/USDT', 'XLM/USDT', 'NEAR/USDT', 'ALGO/USDT', 'APE/USDT'
        ];

        // Try to fetch using the API service
        try {
          // Use the API service which has proper proxy configuration
          const assets = await apiService.fetchAvailableAssets();

          if (assets && Array.isArray(assets)) {
            setAvailableAssets(assets);
            console.log('Successfully loaded', assets.length, 'assets');
          } else {
            console.warn('API returned non-array data:', assets);
            setAvailableAssets(defaultAssetsList);
          }
        } catch (apiError) {
          console.error('API service request failed, trying direct methods:', apiError);

          // Try direct axios calls as fallback
          try {
            // First try with proxy
            const response = await axios.get('/api/available-assets', {
              timeout: 5000  // 5 second timeout
            });

            if (response.data && Array.isArray(response.data)) {
              setAvailableAssets(response.data);
              console.log('Successfully loaded', response.data.length, 'assets via axios');
            } else {
              // Then try direct URL
              const directResponse = await axios.get('http://localhost:5001/api/available-assets', {
                timeout: 5000
              });

              if (directResponse.data && Array.isArray(directResponse.data)) {
                setAvailableAssets(directResponse.data);
                console.log('Successfully loaded', directResponse.data.length, 'assets via direct URL');
              } else {
                setAvailableAssets(defaultAssetsList);
              }
            }
          } catch (directError) {
            console.error('All API approaches failed:', directError);
            setError('Failed to fetch available assets');
            setAvailableAssets(defaultAssetsList);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error in fetchAvailableAssets:', error);
        setError('Failed to fetch available assets');

        // Fallback to a predefined list of common assets
        setAvailableAssets([
          'BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT', 'ADA/USDT',
          'DOGE/USDT', 'MATIC/USDT', 'DOT/USDT', 'SHIB/USDT', 'AVAX/USDT',
          'LINK/USDT', 'UNI/USDT', 'ATOM/USDT', 'LTC/USDT', 'ETC/USDT',
          'FIL/USDT', 'XLM/USDT', 'NEAR/USDT', 'ALGO/USDT', 'APE/USDT'
        ]);
        setLoading(false);
      }
    };

    fetchAvailableAssets();
  }, []);

  // Initialize with default assets if no assets are selected
  useEffect(() => {
    if (selectedAssets.length === 0 && defaultAssets.length > 0) {
      onAssetsChange(defaultAssets);
    }
  }, [selectedAssets, defaultAssets, onAssetsChange]);

  // Handle asset selection
  const handleAssetToggle = (asset) => {
    if (selectedAssets.includes(asset)) {
      // Remove the asset if it's already selected
      onAssetsChange(selectedAssets.filter(a => a !== asset));
    } else {
      // Add the asset if it's not already selected and we haven't reached the maximum
      if (selectedAssets.length < maxAssets) {
        onAssetsChange([...selectedAssets, asset]);
      } else {
        alert(`You can select a maximum of ${maxAssets} assets.`);
      }
    }
  };

  // Handle "Select All" button
  const handleSelectAll = () => {
    // Select up to maxAssets assets
    const assetsToSelect = availableAssets.slice(0, maxAssets);
    onAssetsChange(assetsToSelect);
  };

  // Handle "Clear All" button
  const handleClearAll = () => {
    onAssetsChange([]);
  };

  // Filter assets based on search term
  const filteredAssets = availableAssets.filter(asset =>
    asset.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="asset-selector">
      <h3>Select Assets (Max {maxAssets})</h3>

      <div className="asset-selector-controls">
        <input
          type="text"
          placeholder="Search assets..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="asset-search-input"
          aria-label="Search assets"
        />

        <div className="asset-selector-buttons">
          <button
            onClick={handleSelectAll}
            disabled={loading}
            className="asset-selector-button"
          >
            Select All
          </button>
          <button
            onClick={handleClearAll}
            disabled={loading}
            className="asset-selector-button"
          >
            Clear All
          </button>
        </div>
      </div>

      {loading ? (
        <div className="asset-selector-loading">Loading assets...</div>
      ) : error ? (
        <div className="asset-selector-error">{error}</div>
      ) : (
        <div className="asset-selector-list">
          {filteredAssets.map(asset => (
            <div key={asset} className="asset-selector-item">
              <label className="asset-checkbox-label">
                <input
                  type="checkbox"
                  checked={selectedAssets.includes(asset)}
                  onChange={() => handleAssetToggle(asset)}
                  disabled={!selectedAssets.includes(asset) && selectedAssets.length >= maxAssets}
                />
                <span className="asset-name">{asset}</span>
              </label>
            </div>
          ))}

          {filteredAssets.length === 0 && (
            <div className="asset-selector-no-results">No assets found matching "{searchTerm}"</div>
          )}
        </div>
      )}

      <div className="asset-selector-selected">
        <div className="asset-selector-selected-count">
          Selected: {selectedAssets.length}/{maxAssets}
        </div>
        <div className="asset-selector-selected-list">
          {selectedAssets.map(asset => (
            <div key={asset} className="asset-selector-selected-item">
              {asset}
              <button
                onClick={() => handleAssetToggle(asset)}
                className="asset-selector-remove-button"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AssetSelector;
