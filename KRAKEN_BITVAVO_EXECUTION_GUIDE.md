# Kraken & Bitvavo Execution Guide

## Overview

This guide provides step-by-step instructions for running the background service with Kraken and Bitvavo exchanges for trading execution while using Binance for data fetching and analysis.

## Prerequisites

### 1. Environment Variables

Set up the required API credentials in your environment:

```bash
# Existing Binance credentials (for data fetching)
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# New Kraken credentials
KRAKEN_API_KEY=your_kraken_api_key
KRAKEN_API_SECRET=your_kraken_api_secret

# New Bitvavo credentials
BITVAVO_API_KEY=your_bitvavo_api_key
BITVAVO_API_SECRET=your_bitvavo_api_secret

# Telegram (same for both instances)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### 2. Configuration Files

The following configuration files are already set up:

- `config/settings_kraken_eur.yaml` - Kraken trading configuration
- `config/settings_bitvavo_eur.yaml` - Bitvavo trading configuration  
- `config/notifications_kraken.json` - Kraken notifications with [KRAKEN-EUR] identifier
- `config/notifications_bitvavo.json` - Bitvavo notifications with [BITVAVO-EUR] identifier

## Pre-Execution Testing

### 1. Test Exchange Connectivity

Before running the live service, test that your API credentials work:

```bash
python test_exchange_connectivity.py
```

This will verify:
- ✅ API credentials are valid
- ✅ Exchange connections work
- ✅ Markets can be loaded
- ✅ Balance access is working
- ✅ Test symbols (BTC/EUR, ETH/EUR, SOL/EUR) are available

### 2. Validate Asset Availability

Verify all 14 assets are available on both exchanges:

```bash
python validate_asset_availability.py
```

This will check:
- ✅ All assets from config files exist on both exchanges
- ✅ Assets are active for trading
- ✅ Minimum trading amounts and costs
- 📊 Detailed availability report

## Execution Commands

### Paper Trading Mode (Recommended First)

Start both instances in paper trading mode to test the setup:

#### Terminal 1: Kraken Instance
```bash
python background_service.py \
  --config config/settings_kraken_eur.yaml \
  --notifications config/notifications_kraken.json
```

#### Terminal 2: Bitvavo Instance  
```bash
python background_service.py \
  --config config/settings_bitvavo_eur.yaml \
  --notifications config/notifications_bitvavo.json
```

### Live Trading Mode

Once paper trading is working correctly, switch to live mode by updating the config files:

1. Edit `config/settings_kraken_eur.yaml`:
   ```yaml
   trading:
     mode: live  # Change from 'paper' to 'live'
   ```

2. Edit `config/settings_bitvavo_eur.yaml`:
   ```yaml
   trading:
     mode: live  # Change from 'paper' to 'live'
   ```

3. Run the same commands as above.

## Key Features

### 1. Unified Notifications

Both instances send notifications to the same Telegram chat with exchange identifiers:

- **Kraken messages**: `[KRAKEN-EUR] Trade Executed: ...`
- **Bitvavo messages**: `[BITVAVO-EUR] Trade Executed: ...`

### 2. Hybrid Data Approach

- **Data Fetching**: Binance/USDT pairs (for analysis and signals)
- **Trading Execution**: Kraken/EUR and Bitvavo/EUR pairs
- **Signal Generation**: Based on Binance data, applied to EUR trading

### 3. Capital Allocation

- **Kraken**: 5000 EUR (50% allocation)
- **Bitvavo**: 5000 EUR (50% allocation)
- **Total**: 10000 EUR across both exchanges

### 4. Risk Management

- Same risk parameters across both instances
- Independent execution but coordinated strategy
- Diversified across two EU-regulated exchanges

## Monitoring

### 1. Log Files

Each instance creates separate log files:
- `logs/background_service_kraken.log`
- `logs/background_service_bitvavo.log`

### 2. Telegram Notifications

All notifications go to the same chat with exchange identifiers:
- Trade executions
- Signal changes  
- Performance updates
- Error alerts
- System status

### 3. Performance Tracking

Each instance tracks performance independently:
- `performance_metrics/kraken/`
- `performance_metrics/bitvavo/`

## Troubleshooting

### Common Issues

1. **API Credentials Error**
   ```
   Solution: Verify environment variables are set correctly
   Test: python test_exchange_connectivity.py
   ```

2. **Asset Not Found**
   ```
   Solution: Check asset availability on the exchange
   Test: python validate_asset_availability.py
   ```

3. **Notification Conflicts**
   ```
   Solution: Ensure different notification config files are used
   Check: --notifications parameter is specified correctly
   ```

4. **Port Conflicts**
   ```
   Solution: Kraken uses port 5001, Bitvavo uses port 5002
   Check: api_port settings in config files
   ```

### Emergency Stop

To stop both instances:
1. Press `Ctrl+C` in each terminal
2. Or kill the processes:
   ```bash
   pkill -f "background_service.py.*kraken"
   pkill -f "background_service.py.*bitvavo"
   ```

## Configuration Details

### Asset Lists (14 assets each)

Both exchanges trade the same assets in EUR:
- BTC/EUR, ETH/EUR, SOL/EUR, SUI/EUR
- XRP/EUR, AVAX/EUR, ADA/EUR, AAVE/EUR  
- LINK/EUR, TRX/EUR, PEPE/EUR, DOGE/EUR
- BNB/EUR, DOT/EUR

### Data Sources

Analysis uses Binance USDT pairs:
- BTC/USDT, ETH/USDT, SOL/USDT, etc.
- Same 14 assets for trend detection and scoring

### Fees

- **Kraken**: 0.25% (0.0025)
- **Bitvavo**: 0.25% (0.0025)  
- Both configured in respective YAML files

## Success Criteria

✅ Both instances start without errors  
✅ Exchange connections established  
✅ Notifications working with correct identifiers  
✅ Paper trading executes successfully  
✅ Performance tracking operational  
✅ Log files created and updating  

## Next Steps

1. **Monitor Performance**: Track both instances for several days
2. **Compare Results**: Analyze performance differences between exchanges
3. **Optimize Allocation**: Adjust 50/50 split based on performance data
4. **Scale Up**: Consider adding more exchanges or increasing capital

---

**Note**: This setup provides regulatory compliance (EU exchanges), risk diversification (dual exchanges), and operational continuity (proven Binance data) while maintaining the same proven strategy logic.
