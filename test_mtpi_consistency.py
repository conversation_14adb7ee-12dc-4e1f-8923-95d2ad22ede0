#!/usr/bin/env python3
"""
Test script to verify MTPI signal consistency between debug script and main program
after fixing the BTC/USDC vs BTC/USDT symbol mismatch.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from MTPI_signal_handler import calculate_pgo, generate_pgo_signal, fetch_historical_mtpi_signals
from data_fetcher import fetch_ohlcv_data
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_mtpi_consistency():
    """
    Test that MTPI signals are consistent between direct calculation and main program method.
    """
    print("=" * 80)
    print("TESTING MTPI SIGNAL CONSISTENCY")
    print("=" * 80)
    
    # Parameters (same as main program)
    timeframe = '1d'
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    # Test period - focus on the problematic flat line period
    test_start = '2022-10-01'  # Start a bit before the flat period
    test_end = '2022-11-30'    # End a bit after
    
    print(f"Parameters:")
    print(f"  Timeframe: {timeframe}")
    print(f"  Length: {length}")
    print(f"  Upper threshold: {upper_threshold}")
    print(f"  Lower threshold: {lower_threshold}")
    print(f"  Test period: {test_start} to {test_end}")
    
    try:
        # Method 1: Direct calculation (like debug script)
        print(f"\nMethod 1: Direct calculation...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe=timeframe,
            since=test_start,
            limit=2000,
            use_cache=False,
            force_refresh=True
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("ERROR: Failed to fetch BTC data for direct calculation")
            return
            
        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Calculate signals directly
        signals_direct_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=True
        )
        
        signals_direct_no_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False
        )
        
        # Method 2: Using main program function
        print(f"\nMethod 2: Using fetch_historical_mtpi_signals (main program method)...")
        signals_main_skip = fetch_historical_mtpi_signals(
            timeframe=timeframe,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=True,
            since=test_start,
            use_cache=False
        )
        
        signals_main_no_skip = fetch_historical_mtpi_signals(
            timeframe=timeframe,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False,
            since=test_start,
            use_cache=False
        )
        
        if signals_main_skip is None or signals_main_no_skip is None:
            print("ERROR: Failed to fetch signals using main program method")
            return
        
        print(f"Main program method fetched {len(signals_main_skip)} signals")
        
        # Compare the signals
        print(f"\nCOMPARING SIGNALS:")
        
        # Find common date range
        common_start = max(signals_direct_skip.index[0], signals_main_skip.index[0])
        common_end = min(signals_direct_skip.index[-1], signals_main_skip.index[-1])
        
        print(f"Common date range: {common_start} to {common_end}")
        
        # Filter to common range
        direct_skip_common = signals_direct_skip[(signals_direct_skip.index >= common_start) & 
                                                (signals_direct_skip.index <= common_end)]
        direct_no_skip_common = signals_direct_no_skip[(signals_direct_no_skip.index >= common_start) & 
                                                      (signals_direct_no_skip.index <= common_end)]
        main_skip_common = signals_main_skip[(signals_main_skip.index >= common_start) & 
                                           (signals_main_skip.index <= common_end)]
        main_no_skip_common = signals_main_no_skip[(signals_main_no_skip.index >= common_start) & 
                                                  (signals_main_no_skip.index <= common_end)]
        
        # Check for exact matches
        skip_match = direct_skip_common.equals(main_skip_common)
        no_skip_match = direct_no_skip_common.equals(main_no_skip_common)
        
        print(f"\nSignal comparison results:")
        print(f"  Skip warmup signals match: {skip_match}")
        print(f"  No skip warmup signals match: {no_skip_match}")
        
        if not skip_match or not no_skip_match:
            print(f"\nDetailed comparison for flat line period (2022-10-26 to 2022-11-08):")
            flat_start = pd.to_datetime('2022-10-26').tz_localize('UTC')
            flat_end = pd.to_datetime('2022-11-08').tz_localize('UTC')
            
            flat_mask = (direct_skip_common.index >= flat_start) & (direct_skip_common.index <= flat_end)
            
            print(f"{'Date':<12} {'Direct_Skip':<12} {'Main_Skip':<10} {'Direct_NoSkip':<14} {'Main_NoSkip':<12} {'Match'}")
            print("-" * 80)
            
            for date in direct_skip_common.index[flat_mask]:
                if date in main_skip_common.index:
                    d_skip = direct_skip_common.loc[date]
                    m_skip = main_skip_common.loc[date]
                    d_no_skip = direct_no_skip_common.loc[date]
                    m_no_skip = main_no_skip_common.loc[date]
                    
                    match = "✓" if (d_skip == m_skip and d_no_skip == m_no_skip) else "✗"
                    
                    print(f"{date.strftime('%Y-%m-%d'):<12} {d_skip:<12} {m_skip:<10} {d_no_skip:<14} {m_no_skip:<12} {match}")
        
        # Summary statistics
        print(f"\nSUMMARY STATISTICS:")
        
        def print_signal_stats(signals, name):
            counts = signals.value_counts()
            total = len(signals)
            print(f"{name}:")
            for signal, count in counts.items():
                signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
                print(f"  {signal_name} ({signal}): {count} ({count/total*100:.1f}%)")
        
        print_signal_stats(direct_skip_common, "Direct calculation (skip warmup)")
        print_signal_stats(main_skip_common, "Main program method (skip warmup)")
        print_signal_stats(direct_no_skip_common, "Direct calculation (no skip warmup)")
        print_signal_stats(main_no_skip_common, "Main program method (no skip warmup)")
        
        if skip_match and no_skip_match:
            print(f"\n✅ SUCCESS: All signals match! The BTC/USDT fix resolved the consistency issue.")
        else:
            print(f"\n❌ ISSUE: Signals still don't match. Further investigation needed.")
            
    except Exception as e:
        print(f"Error in test_mtpi_consistency: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mtpi_consistency()
