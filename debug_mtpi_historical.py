#!/usr/bin/env python3
"""
Debug script to print out all MTPI signals from 2021-02-15 to today
to get comprehensive insights into signal patterns and identify issues.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from data_fetcher import fetch_ohlcv_data
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def print_mtpi_signals_historical():
    """
    Print all MTPI signals from 2021-02-15 to today with detailed analysis.
    """
    print("=" * 100)
    print("COMPREHENSIVE MTPI SIGNAL ANALYSIS: 2021-02-15 to TODAY")
    print("=" * 100)
    
    # Parameters
    timeframe = '1d'
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    # Date range
    start_date = '2021-02-15'
    
    print(f"Parameters:")
    print(f"  Timeframe: {timeframe}")
    print(f"  Length: {length}")
    print(f"  Upper threshold: {upper_threshold}")
    print(f"  Lower threshold: {lower_threshold}")
    print(f"  Start date: {start_date}")
    print(f"  Analysis date: {datetime.now().strftime('%Y-%m-%d')}")
    
    try:
        # Fetch BTC data for the entire period
        print(f"\nFetching BTC data from {start_date}...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe=timeframe,
            since=start_date,
            limit=5000,  # Large limit to get all data
            use_cache=False,
            force_refresh=True
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("ERROR: Failed to fetch BTC data")
            return
            
        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Calculate PGO values
        print(f"\nCalculating PGO values...")
        pgo_values = calculate_pgo(btc_df, length=length)
        
        # Generate signals with and without warmup
        print(f"Generating signals...")
        signals_with_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=True
        )
        
        signals_without_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False
        )
        
        # Overall statistics
        print(f"\nOVERALL SIGNAL STATISTICS:")
        print(f"Total candles: {len(btc_df)}")
        
        # With warmup skip
        signal_counts_skip = signals_with_skip.value_counts()
        print(f"\nWith warmup skip:")
        for signal, count in signal_counts_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(signals_with_skip)*100:.1f}%)")
        
        # Without warmup skip
        signal_counts_no_skip = signals_without_skip.value_counts()
        print(f"\nWithout warmup skip:")
        for signal, count in signal_counts_no_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(signals_without_skip)*100:.1f}%)")
        
        # Find all signal transitions
        print(f"\nSIGNAL TRANSITIONS ANALYSIS:")
        transitions_skip = []
        transitions_no_skip = []
        
        # Analyze transitions with skip
        for i in range(1, len(signals_with_skip)):
            if signals_with_skip.iloc[i] != signals_with_skip.iloc[i-1]:
                transitions_skip.append({
                    'date': signals_with_skip.index[i],
                    'from': signals_with_skip.iloc[i-1],
                    'to': signals_with_skip.iloc[i],
                    'pgo_prev': pgo_values.iloc[i-1],
                    'pgo_curr': pgo_values.iloc[i],
                    'price': btc_df['close'].iloc[i]
                })
        
        # Analyze transitions without skip
        for i in range(1, len(signals_without_skip)):
            if signals_without_skip.iloc[i] != signals_without_skip.iloc[i-1]:
                transitions_no_skip.append({
                    'date': signals_without_skip.index[i],
                    'from': signals_without_skip.iloc[i-1],
                    'to': signals_without_skip.iloc[i],
                    'pgo_prev': pgo_values.iloc[i-1],
                    'pgo_curr': pgo_values.iloc[i],
                    'price': btc_df['close'].iloc[i]
                })
        
        print(f"Signal transitions with warmup skip: {len(transitions_skip)}")
        print(f"Signal transitions without warmup skip: {len(transitions_no_skip)}")
        
        # Print detailed signal history
        print(f"\nDETAILED SIGNAL HISTORY:")
        print(f"{'Date':<12} {'Close':<10} {'PGO':<10} {'Skip':<6} {'NoSkip':<8} {'Transition':<15} {'Note'}")
        print("-" * 85)
        
        # Track previous signals for transition detection
        prev_skip_signal = None
        prev_no_skip_signal = None
        
        for i, (date, row) in enumerate(btc_df.iterrows()):
            close_price = row['close']
            pgo_val = pgo_values.iloc[i] if not pd.isna(pgo_values.iloc[i]) else 0
            skip_signal = signals_with_skip.iloc[i]
            no_skip_signal = signals_without_skip.iloc[i]
            
            # Detect transitions
            transition = ""
            if prev_skip_signal is not None and skip_signal != prev_skip_signal:
                transition = f"{prev_skip_signal}→{skip_signal}"
            
            # Add notes for significant events
            note = ""
            if abs(pgo_val) > 2.0:
                note = "EXTREME PGO"
            elif pgo_val > upper_threshold:
                note = "ABOVE UPPER"
            elif pgo_val < lower_threshold:
                note = "BELOW LOWER"
            elif skip_signal != no_skip_signal:
                note = "SKIP DIFF"
            
            # Highlight specific periods
            if date.strftime('%Y-%m-%d') >= '2022-10-26' and date.strftime('%Y-%m-%d') <= '2022-11-08':
                note += " FLAT_PERIOD"
            
            print(f"{date.strftime('%Y-%m-%d'):<12} ${close_price:<9.2f} {pgo_val:<10.4f} {skip_signal:<6} {no_skip_signal:<8} {transition:<15} {note}")
            
            prev_skip_signal = skip_signal
            prev_no_skip_signal = no_skip_signal
        
        # Analyze the flat line period specifically
        print(f"\n" + "="*100)
        print(f"SPECIFIC ANALYSIS OF FLAT LINE PERIOD (2022-10-26 to 2022-11-08)")
        print(f"="*100)
        
        flat_start = pd.to_datetime('2022-10-26').tz_localize('UTC')
        flat_end = pd.to_datetime('2022-11-08').tz_localize('UTC')
        flat_mask = (btc_df.index >= flat_start) & (btc_df.index <= flat_end)
        
        flat_btc = btc_df[flat_mask]
        flat_pgo = pgo_values[flat_mask]
        flat_signals_skip = signals_with_skip[flat_mask]
        flat_signals_no_skip = signals_without_skip[flat_mask]
        
        print(f"Flat period analysis:")
        print(f"  Period: {flat_start.strftime('%Y-%m-%d')} to {flat_end.strftime('%Y-%m-%d')}")
        print(f"  Candles: {len(flat_btc)}")
        print(f"  Price change: {((flat_btc['close'].iloc[-1] - flat_btc['close'].iloc[0]) / flat_btc['close'].iloc[0] * 100):+.2f}%")
        print(f"  PGO range: {flat_pgo.min():.4f} to {flat_pgo.max():.4f}")
        print(f"  PGO above upper threshold: {(flat_pgo > upper_threshold).sum()} candles")
        print(f"  PGO below lower threshold: {(flat_pgo < lower_threshold).sum()} candles")
        
        # Check what happened before and after the flat period
        print(f"\nCONTEXT AROUND FLAT PERIOD:")
        context_start = flat_start - pd.Timedelta(days=10)
        context_end = flat_end + pd.Timedelta(days=10)
        context_mask = (btc_df.index >= context_start) & (btc_df.index <= context_end)
        
        context_signals = signals_with_skip[context_mask]
        context_dates = btc_df.index[context_mask]
        context_pgo = pgo_values[context_mask]
        
        print(f"10 days before to 10 days after flat period:")
        for i, date in enumerate(context_dates):
            signal = context_signals.iloc[i]
            pgo_val = context_pgo.iloc[i]
            
            period_marker = ""
            if date >= flat_start and date <= flat_end:
                period_marker = " [FLAT]"
            elif date < flat_start:
                period_marker = " [BEFORE]"
            else:
                period_marker = " [AFTER]"
                
            print(f"  {date.strftime('%Y-%m-%d')}: Signal={signal}, PGO={pgo_val:.4f}{period_marker}")
            
    except Exception as e:
        print(f"Error in print_mtpi_signals_historical: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print_mtpi_signals_historical()
