#!/usr/bin/env python3
"""
Test script to verify the Kraken NoneType error fix.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.order import OrderManager

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_kraken_fix():
    """Test the Kraken NoneType error fix with small amounts."""
    
    print("=== TESTING KRAKEN NONETYPE FIX ===")
    
    try:
        # Initialize order manager
        order_manager = OrderManager(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        # Test with very small amounts to avoid insufficient funds
        test_cases = [
            {
                'symbol': 'BTC/EUR',
                'amount': 5.0,  # Small amount
                'description': 'Small BTC/EUR order'
            }
        ]
        
        for test_case in test_cases:
            print(f"\n=== Testing {test_case['description']} ===")
            print(f"Symbol: {test_case['symbol']}")
            print(f"Amount: {test_case['amount']} EUR")
            
            try:
                # This should now handle the NoneType error gracefully
                order = order_manager.create_market_buy_order(
                    symbol=test_case['symbol'],
                    amount=test_case['amount']
                )
                
                if order and order != {}:
                    print(f"✅ Order created successfully!")
                    print(f"Order ID: {order.get('id', 'N/A')}")
                    print(f"Status: {order.get('status', 'N/A')}")
                    print(f"Amount: {order.get('amount', 'N/A')}")
                    print(f"Side: {order.get('side', 'N/A')}")
                    
                    # Check if this was a recovery order
                    if order.get('info', {}).get('kraken_nonetype_error_recovery'):
                        print(f"🔧 This was a recovery order - trade likely succeeded despite CCXT error")
                    else:
                        print(f"✅ Normal successful order")
                        
                else:
                    print(f"❌ Order creation returned empty result")
                    
            except Exception as e:
                print(f"❌ Error creating order: {e}")
                
                # Check if it's still the NoneType comparison error
                if "'>' not supported between instances of 'NoneType' and" in str(e):
                    print("🔍 Still getting NoneType comparison error - fix may need adjustment")
                else:
                    print("🔍 Different error - this might be expected")
    
    except Exception as e:
        print(f"Error in test setup: {e}")

if __name__ == "__main__":
    test_kraken_fix()
    print("\nTest completed!")
