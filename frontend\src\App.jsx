import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Box } from '@chakra-ui/react'

// Import pages
import Settings from './pages/Settings'
import BacktestingDashboard from './pages/AssetBacktest'
import LiveTradingDashboard from './pages/LiveTradingDashboard'

// Import components
import Header from './components/Header'

// Import utility for syncing color mode with CSS
import { useColorModeSync } from './utils/colorModeSync'

function App() {
  // Use the color mode sync utility
  useColorModeSync();

  return (
    <Box minH="100vh">
      <Header />
      <Box as="main" p={4}>
        <Routes>
          <Route path="/" element={<BacktestingDashboard />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/live-trading" element={<LiveTradingDashboard />} />
        </Routes>
      </Box>
    </Box>
  )
}

export default App
