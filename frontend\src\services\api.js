import axios from 'axios'

// Custom JSON parser that can handle NaN, Infinity, and -Infinity
const safeJSONParse = (jsonString) => {
  if (typeof jsonString !== 'string') {
    console.warn('safeJSONParse called with non-string input:', jsonString);
    return jsonString; // Return the input as-is if it's not a string
  }

  try {
    // Replace NaN, Infinity, and -Infinity with string representations
    // that we can later convert back to their JavaScript equivalents
    // Use more robust regex patterns to catch different formats
    const sanitizedString = jsonString
      // Handle NaN in various formats: NaN, "NaN", or as a value
      .replace(/:\s*NaN\s*([,}])/g, ':"__NaN__"$1')
      .replace(/:\s*"NaN"\s*([,}])/g, ':"__NaN__"$1')
      .replace(/:\s*{\s*"value"\s*:\s*NaN\s*}\s*([,}])/g, ':{"value":"__NaN__"}$1')
      // Handle Infinity
      .replace(/:\s*Infinity\s*([,}])/g, ':"__Infinity__"$1')
      .replace(/:\s*"Infinity"\s*([,}])/g, ':"__Infinity__"$1')
      // Handle -Infinity
      .replace(/:\s*-Infinity\s*([,}])/g, ':"__-Infinity__"$1')
      .replace(/:\s*"-Infinity"\s*([,}])/g, ':"__-Infinity__"$1');

    // Parse the sanitized string
    const parsed = JSON.parse(sanitizedString);

    // Helper function to recursively restore special values
    const restoreSpecialValues = (obj) => {
      if (obj === null || obj === undefined) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => restoreSpecialValues(item));
      }

      if (typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
          obj[key] = restoreSpecialValues(obj[key]);
        });
        return obj;
      }

      // Restore special values
      if (obj === '__NaN__') return NaN;
      if (obj === '__Infinity__') return Infinity;
      if (obj === '__-Infinity__') return -Infinity;

      return obj;
    };

    // Restore special values in the parsed object
    return restoreSpecialValues(parsed);
  } catch (error) {
    console.error('Error in safeJSONParse:', error);
    console.error('JSON string preview:', jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : ''));

    // Try to identify the problematic part of the JSON
    const match = error.message.match(/position (\d+)/);
    if (match && match[1]) {
      const position = parseInt(match[1]);
      const start = Math.max(0, position - 20);
      const end = Math.min(jsonString.length, position + 20);
      console.error(`JSON error near position ${position}:`, jsonString.substring(start, end));
    }

    throw new Error(`Failed to parse JSON: ${error.message}`);
  }
};

// Create an axios instance with default config
const api = axios.create({
  baseURL: '/api',  // Use relative URL to work with Vite's proxy
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  }
})

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method.toUpperCase()} ${config.baseURL}${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    if (error.response) {
      console.error(`API Error: ${error.response.status} ${error.config?.url}`, error.response.data);
    } else if (error.request) {
      console.error('API Error: No response received', error.request);
    } else {
      console.error('API Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// API functions for fetching data from the backend
export const fetchAssets = async () => {
  try {
    const response = await api.get('/assets')
    return response.data
  } catch (error) {
    console.error('Error fetching assets:', error)
    throw error
  }
}

export const fetchAssetData = async (symbol, timeframe = '1d', limit = 100) => {
  try {
    const response = await api.get(`/assets/${symbol}/ohlcv`, {
      params: { timeframe, limit },
    })
    return response.data
  } catch (error) {
    console.error(`Error fetching data for ${symbol}:`, error)
    throw error
  }
}

export const fetchScores = async (date = null) => {
  try {
    console.log('Fetching asset scores from API...')

    // Add date parameter to the request if provided
    const params = date ? { date } : {}
    if (date) {
      console.log(`Requesting scores for specific date: ${date}`)
    }

    const response = await api.get('/scores', { params })
    console.log('Asset scores API response:', response)
    console.log('Asset scores API response data:', response.data)
    console.log('Asset scores API response data type:', typeof response.data)

    // Handle different response data formats
    let formattedData;

    // If response.data is a string, try to parse it as JSON
    if (typeof response.data === 'string') {
      try {
        console.log('Attempting to parse string response as JSON')
        formattedData = safeJSONParse(response.data)
        console.log('Successfully parsed string response:', formattedData)
      } catch (parseError) {
        console.error('Error parsing string response:', parseError)
        throw new Error('Invalid JSON string in response')
      }
    } else if (typeof response.data === 'object' && response.data !== null) {
      // If it's already an object, use it directly
      formattedData = response.data
    } else {
      // If it's neither a string nor an object, throw an error
      console.error('Invalid response data format:', response.data)
      throw new Error('Invalid response data format')
    }

    console.log('Formatted asset scores data:', formattedData)
    return formattedData
  } catch (error) {
    console.error('Error fetching scores:', error)
    // Return a default structure in case of error
    return {
      'BTC/USDT': 4,
      'ETH/USDT': 2,
      'SOL/USDT': 5,
      'XRP/USDT': 1,
      'ADA/USDT': 3,
      'DOGE/USDT': 0
    }
  }
}

export const fetchPerformanceMetrics = async () => {
  try {
    const response = await api.get('/performance')
    return response.data
  } catch (error) {
    console.error('Error fetching performance metrics:', error)
    throw error
  }
}

export const fetchEquityCurves = async () => {
  try {
    const response = await api.get('/equity')
    return response.data
  } catch (error) {
    console.error('Error fetching equity curves:', error)
    throw error
  }
}

export const fetchSettings = async () => {
  try {
    console.log('Fetching settings from API...');
    const response = await api.get('/settings');
    console.log('Settings API response:', response.data);

    // Validate response data
    if (!response.data || typeof response.data !== 'object') {
      console.error('Invalid settings data format received:', response.data);
      throw new Error('Invalid settings data format received from server');
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching settings:', error);

    // Add more context to the error
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);

      // Enhance error message with status code
      error.message = `Server error (${error.response.status}): ${error.message}`;
    } else if (error.request) {
      console.error('No response received:', error.request);
      error.message = 'No response received from server. Please check your network connection.';
    }

    throw error;
  }
}

export const updateSettings = async (settings) => {
  try {
    // Log the settings being sent to the API
    console.log('Sending settings to API:', JSON.stringify(settings, null, 2));

    // Create a copy of the settings to sanitize
    const sanitizedSettings = { ...settings };

    // Ensure mtpi_lower_threshold is a valid number
    if (sanitizedSettings.mtpi_lower_threshold !== undefined) {
      // Convert to number if it's a string
      if (typeof sanitizedSettings.mtpi_lower_threshold === 'string') {
        const numValue = parseFloat(sanitizedSettings.mtpi_lower_threshold);
        if (!isNaN(numValue)) {
          sanitizedSettings.mtpi_lower_threshold = numValue;
        } else {
          // If conversion fails, use default
          console.warn('Invalid mtpi_lower_threshold, using default -0.58');
          sanitizedSettings.mtpi_lower_threshold = -0.58;
        }
      }

      // Validate range
      if (sanitizedSettings.mtpi_lower_threshold > 0 || sanitizedSettings.mtpi_lower_threshold < -5) {
        console.warn(`mtpi_lower_threshold out of range: ${sanitizedSettings.mtpi_lower_threshold}, using default -0.58`);
        sanitizedSettings.mtpi_lower_threshold = -0.58;
      }
    } else {
      // If missing, use default
      sanitizedSettings.mtpi_lower_threshold = -0.58;
    }

    // Ensure mtpi_upper_threshold is a valid number
    if (sanitizedSettings.mtpi_upper_threshold !== undefined) {
      // Convert to number if it's a string
      if (typeof sanitizedSettings.mtpi_upper_threshold === 'string') {
        const numValue = parseFloat(sanitizedSettings.mtpi_upper_threshold);
        if (!isNaN(numValue)) {
          sanitizedSettings.mtpi_upper_threshold = numValue;
        } else {
          // If conversion fails, use default
          console.warn('Invalid mtpi_upper_threshold, using default 1.1');
          sanitizedSettings.mtpi_upper_threshold = 1.1;
        }
      }

      // Validate range
      if (sanitizedSettings.mtpi_upper_threshold < 0.1 || sanitizedSettings.mtpi_upper_threshold > 5) {
        console.warn(`mtpi_upper_threshold out of range: ${sanitizedSettings.mtpi_upper_threshold}, using default 1.1`);
        sanitizedSettings.mtpi_upper_threshold = 1.1;
      }
    } else {
      // If missing, use default
      sanitizedSettings.mtpi_upper_threshold = 1.1;
    }

    // Ensure mtpi_pgo_length is a valid number
    if (sanitizedSettings.mtpi_pgo_length !== undefined) {
      // Convert to number if it's a string
      if (typeof sanitizedSettings.mtpi_pgo_length === 'string') {
        const numValue = parseInt(sanitizedSettings.mtpi_pgo_length);
        if (!isNaN(numValue)) {
          sanitizedSettings.mtpi_pgo_length = numValue;
        } else {
          // If conversion fails, use default
          console.warn('Invalid mtpi_pgo_length, using default 35');
          sanitizedSettings.mtpi_pgo_length = 35;
        }
      }

      // Validate range
      if (sanitizedSettings.mtpi_pgo_length < 5 || sanitizedSettings.mtpi_pgo_length > 100) {
        console.warn(`mtpi_pgo_length out of range: ${sanitizedSettings.mtpi_pgo_length}, using default 35`);
        sanitizedSettings.mtpi_pgo_length = 35;
      }
    } else {
      // If missing, use default
      sanitizedSettings.mtpi_pgo_length = 35;
    }

    console.log('Sanitized settings being sent to API:', JSON.stringify(sanitizedSettings, null, 2));

    // Make the API call with sanitized settings
    const response = await api.post('/settings', sanitizedSettings);
    console.log('Settings update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating settings:', error);
    console.error('Request data that caused error:', settings);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

export const refreshData = async () => {
  try {
    console.log('Forcing refresh of cached data...')
    // Fix the API endpoint path - remove the duplicate '/api'
    const response = await api.post('/refresh')
    console.log('Refresh response:', response.data)
    return response.data
  } catch (error) {
    console.error('Error refreshing data:', error)

    // Add more context to the error
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    }

    throw error
  }
}

export const fetchAvailableAssets = async () => {
  try {
    console.log('Fetching available assets from API...');
    const response = await api.get('/available-assets');
    console.log('Available assets API response:', response.data);

    if (!Array.isArray(response.data)) {
      console.error('Invalid response data format for available assets:', response.data);
      throw new Error('Invalid response data format for available assets');
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching available assets:', error);
    throw error;
  }
};

export const getAllData = async () => {
  try {
    console.log('Fetching all dashboard data...');

    // Fetch all data in parallel
    const [equityCurves, performanceMetrics, assetScores] = await Promise.all([
      fetchEquityCurves(),
      fetchPerformanceMetrics(),
      fetchScores()
    ]);

    console.log('All dashboard data fetched successfully:');
    console.log('- Equity Curves:', equityCurves);
    console.log('- Performance Metrics:', performanceMetrics);
    console.log('- Asset Scores:', assetScores);

    // Process asset scores based on their type
    let formattedAssetScores;

    // Handle string response
    if (typeof assetScores === 'string') {
      try {
        console.log('Attempting to parse asset scores string in getAllData');
        const parsedScores = safeJSONParse(assetScores);
        console.log('Successfully parsed asset scores string:', parsedScores);

        // Use the parsed scores for further processing
        assetScores = parsedScores;
      } catch (parseError) {
        console.error('Error parsing asset scores string in getAllData:', parseError);
        // Create a default object if parsing fails
        assetScores = {
          'BTC/USDT': 0,
          'ETH/USDT': 0,
          'SOL/USDT': 0
        };
      }
    }

    // Format asset scores for backward compatibility if needed
    formattedAssetScores = assetScores;

    // Check if assetScores is a flat object (new API format)
    if (assetScores && typeof assetScores === 'object' && !assetScores.scores) {
      console.log('Converting flat asset scores to nested format for backward compatibility');

      // Find the best asset (highest score)
      let bestAsset = '';
      let highestScore = -Infinity;

      Object.entries(assetScores).forEach(([asset, score]) => {
        const numScore = typeof score === 'string' ? parseFloat(score) : score;
        if (numScore > highestScore) {
          highestScore = numScore;
          bestAsset = asset;
        }
      });

      // Create a nested structure for backward compatibility
      formattedAssetScores = {
        date: new Date().toISOString().split('T')[0],
        scores: assetScores,
        bestAsset: bestAsset
      };

      console.log('Formatted asset scores for backward compatibility:', formattedAssetScores);
    }

    return {
      equityCurves,
      performanceMetrics,
      assetScores: formattedAssetScores
    };
  } catch (error) {
    console.error('Error fetching all dashboard data:', error);

    // Return default data structure in case of error
    return {
      equityCurves: null,
      performanceMetrics: null,
      assetScores: {
        date: new Date().toISOString().split('T')[0],
        scores: {
          'BTC/USDT': 0,
          'ETH/USDT': 0,
          'SOL/USDT': 0
        },
        bestAsset: 'BTC/USDT'
      }
    };
  }
};

export default {
  fetchAssets,
  fetchAssetData,
  fetchScores,
  fetchPerformanceMetrics,
  fetchEquityCurves,
  fetchSettings,
  updateSettings,
  refreshData,
  getAllData,
  fetchAvailableAssets
}
