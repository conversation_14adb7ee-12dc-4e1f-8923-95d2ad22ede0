// Script to save backend data to a JSON file
import axios from 'axios';
import fs from 'fs/promises';

async function saveBackendData() {
  try {
    console.log('Fetching data from backend API...');
    const response = await axios.get('http://localhost:5001/api/equity');
    const data = response.data;

    console.log('Saving backend data to backend-data.json...');
    await fs.writeFile('backend-data.json', JSON.stringify(data, null, 2));

    console.log('Backend data saved successfully!');

    // Print some basic stats about the data
    if (data.curves && data.curves.strategy) {
      console.log(`Strategy data points: ${data.curves.strategy.length}`);
    }

    if (data.assetChanges) {
      console.log(`Asset changes: ${data.assetChanges.length}`);
    }

    if (data.metadata && data.metadata.rawMtpiSignals) {
      console.log(`MTPI signals: ${data.metadata.rawMtpiSignals.length}`);
    }

  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the function
saveBackendData();
