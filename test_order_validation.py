#!/usr/bin/env python3
"""
Test script to verify the order validation fix for Kraken.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.order import OrderManager
from src.config_manager import get_trading_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_order_validation():
    """Test order validation with Kraken exchange."""
    
    print("Testing order validation with Kraken...")
    
    try:
        # Initialize order manager for Kraken
        order_manager = OrderManager(
            exchange_id='kraken',
            test_mode=False,  # We need real market data
            config_path='config/settings_kraken_eur.yaml'
        )
        
        # Test symbols that were failing
        test_symbols = ['BTC/EUR', 'TRX/EUR']
        
        for symbol in test_symbols:
            print(f"\n=== Testing {symbol} ===")
            
            # Test market buy order validation (without actually placing the order)
            try:
                # Get current price
                if hasattr(order_manager, 'exchange') and order_manager.exchange:
                    ticker = order_manager.exchange.fetch_ticker(symbol)
                    price = ticker['last']
                    print(f"Current price: {price:.8f}")
                    
                    # Check market info
                    markets = order_manager.exchange.markets
                    if symbol in markets:
                        market = markets[symbol]
                        limits = market.get('limits', {})
                        
                        print(f"Market limits: {limits}")
                        
                        # Check for None values in limits
                        amount_limits = limits.get('amount', {})
                        cost_limits = limits.get('cost', {})
                        
                        print(f"Amount limits: {amount_limits}")
                        print(f"Cost limits: {cost_limits}")
                        
                        # Check for None values
                        if amount_limits:
                            min_amount = amount_limits.get('min')
                            max_amount = amount_limits.get('max')
                            print(f"Min amount: {min_amount} (type: {type(min_amount)})")
                            print(f"Max amount: {max_amount} (type: {type(max_amount)})")
                        
                        if cost_limits:
                            min_cost = cost_limits.get('min')
                            max_cost = cost_limits.get('max')
                            print(f"Min cost: {min_cost} (type: {type(min_cost)})")
                            print(f"Max cost: {max_cost} (type: {type(max_cost)})")
                    else:
                        print(f"Symbol {symbol} not found in markets")
                        
                else:
                    print("Exchange not initialized")
                    
            except Exception as e:
                print(f"Error testing {symbol}: {e}")
                import traceback
                traceback.print_exc()

    except Exception as e:
        print(f"Error in order validation test: {e}")
        import traceback
        traceback.print_exc()

def test_market_info_extraction():
    """Test extracting market info to identify None values."""
    
    print("\n=== Testing Market Info Extraction ===")
    
    try:
        import ccxt
        
        # Initialize Kraken exchange
        exchange = ccxt.kraken({
            'enableRateLimit': True,
            'sandbox': False,
        })
        
        # Load markets
        markets = exchange.load_markets()
        print(f"Loaded {len(markets)} markets")
        
        # Check specific EUR pairs for None values
        eur_pairs = [symbol for symbol in markets.keys() if symbol.endswith('/EUR')]
        print(f"Found {len(eur_pairs)} EUR pairs")
        
        for symbol in ['BTC/EUR', 'TRX/EUR', 'ETH/EUR']:
            if symbol in markets:
                market = markets[symbol]
                limits = market.get('limits', {})
                
                print(f"\n{symbol}:")
                print(f"  Active: {market.get('active')}")
                print(f"  Limits: {limits}")
                
                # Check for None values in nested structures
                amount_limits = limits.get('amount', {})
                cost_limits = limits.get('cost', {})
                
                if amount_limits:
                    for key, value in amount_limits.items():
                        if value is None:
                            print(f"  ⚠️ amount.{key} is None")
                        else:
                            print(f"  ✓ amount.{key}: {value}")
                
                if cost_limits:
                    for key, value in cost_limits.items():
                        if value is None:
                            print(f"  ⚠️ cost.{key} is None")
                        else:
                            print(f"  ✓ cost.{key}: {value}")
            else:
                print(f"{symbol} not found in markets")
                
    except Exception as e:
        print(f"Error in market info extraction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing order validation and market info extraction...")
    test_market_info_extraction()
    test_order_validation()
    print("\nTest completed!")
