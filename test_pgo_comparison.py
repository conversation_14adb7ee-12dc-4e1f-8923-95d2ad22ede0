#!/usr/bin/env python3
"""
Comprehensive PGO Signal Comparison Test
========================================

This script tests the PGO signal calculation differences between:
1. test_allocation.py (AllocationTester)
2. allocation_report.py (AllocationReporter)

It will help identify exactly where and why the scoring mechanisms diverge.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import both implementations
from test_allocation import AllocationTester
from allocation_report import AllocationReporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pgo_comparison_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PGOComparisonTester:
    """Comprehensive tester for PGO signal differences"""
    
    def __init__(self):
        self.test_params = {
            'timeframe': '1d',
            'mtpi_timeframe': '1d',
            'analysis_start_date': '2023-09-19',
            'n_assets': 1,
            'transaction_fee_rate': 0.001,
            'selected_assets': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],  # Start with 3 assets
            'use_cache': True,
            'initial_capital': 1.0,
            'wait_for_confirmed_signals': True,
            'use_mtpi': False,  # Disable MTPI to focus on PGO
            'use_weighted_allocation': False,
            'weights': None
        }
        
        # Initialize both implementations
        self.tester = None
        self.reporter = None
        
    def setup_implementations(self):
        """Initialize both AllocationTester and AllocationReporter"""
        logger.info("Setting up both implementations...")
        
        # Create tester instance
        self.tester = AllocationTester(
            timeframe=self.test_params['timeframe'],
            mtpi_timeframe=self.test_params['mtpi_timeframe'],
            analysis_start_date=self.test_params['analysis_start_date'],
            n_assets=self.test_params['n_assets'],
            transaction_fee_rate=self.test_params['transaction_fee_rate'],
            selected_assets=self.test_params['selected_assets'],
            use_cache=self.test_params['use_cache'],
            initial_capital=self.test_params['initial_capital'],
            wait_for_confirmed_signals=self.test_params['wait_for_confirmed_signals'],
            use_mtpi=self.test_params['use_mtpi'],
            use_weighted_allocation=self.test_params['use_weighted_allocation'],
            weights=self.test_params['weights']
        )
        
        # Create reporter instance
        self.reporter = AllocationReporter(
            timeframe=self.test_params['timeframe'],
            mtpi_timeframe=self.test_params['mtpi_timeframe'],
            analysis_start_date=self.test_params['analysis_start_date'],
            n_assets=self.test_params['n_assets'],
            transaction_fee_rate=self.test_params['transaction_fee_rate'],
            selected_assets=self.test_params['selected_assets'],
            use_cache=self.test_params['use_cache'],
            initial_capital=self.test_params['initial_capital'],
            wait_for_confirmed_signals=self.test_params['wait_for_confirmed_signals'],
            use_mtpi=self.test_params['use_mtpi'],
            use_weighted_allocation=self.test_params['use_weighted_allocation'],
            weights=self.test_params['weights']
        )
        
        logger.info("Both implementations initialized successfully")
        
    def fetch_data_both(self):
        """Fetch data using both implementations and compare"""
        logger.info("=== FETCHING DATA FROM BOTH IMPLEMENTATIONS ===")
        
        # Fetch data from tester
        logger.info("Fetching data from AllocationTester...")
        tester_data = self.tester.fetch_data()
        
        # Fetch data from reporter  
        logger.info("Fetching data from AllocationReporter...")
        reporter_data = self.reporter.fetch_data()
        
        return tester_data, reporter_data
        
    def compare_raw_data(self, tester_data, reporter_data):
        """Compare the raw OHLCV data between implementations"""
        logger.info("=== COMPARING RAW DATA ===")
        
        data_identical = True
        
        for asset in self.test_params['selected_assets']:
            if asset in tester_data and asset in reporter_data:
                tester_df = tester_data[asset]
                reporter_df = reporter_data[asset]
                
                logger.info(f"\n{asset}:")
                logger.info(f"  Tester: {len(tester_df)} rows, {tester_df.index[0]} to {tester_df.index[-1]}")
                logger.info(f"  Reporter: {len(reporter_df)} rows, {reporter_df.index[0]} to {reporter_df.index[-1]}")
                
                # Compare shapes
                if tester_df.shape != reporter_df.shape:
                    logger.warning(f"  Shape mismatch: Tester {tester_df.shape} vs Reporter {reporter_df.shape}")
                    data_identical = False
                    continue
                
                # Compare close prices (most important for PGO)
                close_diff = np.abs(tester_df['close'] - reporter_df['close']).max()
                logger.info(f"  Max close price difference: {close_diff}")
                
                if close_diff > 1e-10:
                    logger.warning(f"  Close price differences detected!")
                    data_identical = False
                    
                    # Show first few differences
                    diff_mask = np.abs(tester_df['close'] - reporter_df['close']) > 1e-10
                    if diff_mask.any():
                        diff_dates = tester_df.index[diff_mask][:5]
                        for date in diff_dates:
                            logger.warning(f"    {date}: Tester={tester_df.loc[date, 'close']:.8f}, Reporter={reporter_df.loc[date, 'close']:.8f}")
            else:
                logger.error(f"Asset {asset} missing in one of the datasets")
                data_identical = False
        
        if data_identical:
            logger.info("✓ Raw data is identical between implementations")
        else:
            logger.error("✗ Raw data differs between implementations")
            
        return data_identical
        
    def test_pgo_calculation_direct(self, tester_data, reporter_data):
        """Test PGO calculation directly on the same data"""
        logger.info("=== TESTING DIRECT PGO CALCULATION ===")
        
        # Test on BTC/USDT first
        asset = 'BTC/USDT'
        if asset not in tester_data or asset not in reporter_data:
            logger.error(f"Asset {asset} not available for testing")
            return
            
        # Get the data
        tester_df = tester_data[asset].copy()
        reporter_df = reporter_data[asset].copy()
        
        # Ensure we have the same data
        if not tester_df.equals(reporter_df):
            logger.warning("Data frames are not identical, using tester data for both")
            test_data = tester_df.copy()
        else:
            test_data = tester_df.copy()
            
        logger.info(f"Testing PGO calculation on {asset} with {len(test_data)} rows")
        logger.info(f"Date range: {test_data.index[0]} to {test_data.index[-1]}")
        
        # Test PGO calculation using both methods
        pgo_length = 35
        
        # Method 1: Try to extract PGO calculation from tester
        logger.info("Calculating PGO using tester method...")
        try:
            tester_pgo = self._calculate_pgo_tester_method(test_data, pgo_length)
            logger.info(f"Tester PGO calculated: {len(tester_pgo)} values")
            logger.info(f"Tester PGO first 5: {tester_pgo[:5].tolist()}")
            logger.info(f"Tester PGO last 5: {tester_pgo[-5:].tolist()}")
        except Exception as e:
            logger.error(f"Failed to calculate tester PGO: {e}")
            tester_pgo = None
            
        # Method 2: Try to extract PGO calculation from reporter
        logger.info("Calculating PGO using reporter method...")
        try:
            reporter_pgo = self._calculate_pgo_reporter_method(test_data, pgo_length)
            logger.info(f"Reporter PGO calculated: {len(reporter_pgo)} values")
            logger.info(f"Reporter PGO first 5: {reporter_pgo[:5].tolist()}")
            logger.info(f"Reporter PGO last 5: {reporter_pgo[-5:].tolist()}")
        except Exception as e:
            logger.error(f"Failed to calculate reporter PGO: {e}")
            reporter_pgo = None
            
        # Compare PGO values
        if tester_pgo is not None and reporter_pgo is not None:
            self._compare_pgo_values(tester_pgo, reporter_pgo, test_data.index)
            
    def _calculate_pgo_tester_method(self, data, length):
        """Extract PGO calculation method from AllocationTester"""
        # This will need to be implemented based on the actual PGO calculation in test_allocation.py
        # For now, let's try to call the method directly
        
        # Try to access the PGO calculation method
        if hasattr(self.tester, 'calculate_pgo'):
            return self.tester.calculate_pgo(data, length)
        elif hasattr(self.tester, '_calculate_pgo'):
            return self.tester._calculate_pgo(data, length)
        else:
            # Manual PGO calculation based on typical implementation
            return self._manual_pgo_calculation(data, length)
            
    def _calculate_pgo_reporter_method(self, data, length):
        """Extract PGO calculation method from AllocationReporter"""
        # This will need to be implemented based on the actual PGO calculation in allocation_report.py
        # For now, let's try to call the method directly
        
        # Try to access the PGO calculation method
        if hasattr(self.reporter, 'calculate_pgo'):
            return self.reporter.calculate_pgo(data, length)
        elif hasattr(self.reporter, '_calculate_pgo'):
            return self.reporter._calculate_pgo(data, length)
        else:
            # Manual PGO calculation based on typical implementation
            return self._manual_pgo_calculation(data, length)
            
    def _manual_pgo_calculation(self, data, length):
        """Manual PGO calculation for comparison"""
        # Basic PGO calculation: (close - SMA) / ATR
        
        # Calculate SMA
        sma = data['close'].rolling(window=length).mean()
        
        # Calculate ATR
        high_low = data['high'] - data['low']
        high_close_prev = np.abs(data['high'] - data['close'].shift(1))
        low_close_prev = np.abs(data['low'] - data['close'].shift(1))
        
        true_range = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
        atr = true_range.rolling(window=length).mean()
        
        # Calculate PGO
        pgo = (data['close'] - sma) / atr
        
        return pgo.values
        
    def _compare_pgo_values(self, tester_pgo, reporter_pgo, dates):
        """Compare PGO values between implementations"""
        logger.info("=== COMPARING PGO VALUES ===")
        
        # Ensure same length
        min_len = min(len(tester_pgo), len(reporter_pgo))
        tester_pgo = tester_pgo[:min_len]
        reporter_pgo = reporter_pgo[:min_len]
        dates = dates[:min_len]
        
        # Calculate differences
        differences = np.abs(tester_pgo - reporter_pgo)
        max_diff = np.nanmax(differences)
        mean_diff = np.nanmean(differences)
        
        logger.info(f"PGO comparison results:")
        logger.info(f"  Max difference: {max_diff}")
        logger.info(f"  Mean difference: {mean_diff}")
        
        # Find significant differences
        significant_diff_threshold = 0.01
        significant_diffs = differences > significant_diff_threshold
        
        if np.any(significant_diffs):
            logger.warning(f"Found {np.sum(significant_diffs)} significant differences (>{significant_diff_threshold})")
            
            # Show first 10 significant differences
            diff_indices = np.where(significant_diffs)[0][:10]
            for idx in diff_indices:
                if not np.isnan(tester_pgo[idx]) and not np.isnan(reporter_pgo[idx]):
                    logger.warning(f"  {dates[idx]}: Tester={tester_pgo[idx]:.6f}, Reporter={reporter_pgo[idx]:.6f}, Diff={differences[idx]:.6f}")
        else:
            logger.info("✓ PGO values are very similar between implementations")
            
        # Save detailed comparison
        comparison_df = pd.DataFrame({
            'date': dates,
            'tester_pgo': tester_pgo,
            'reporter_pgo': reporter_pgo,
            'difference': differences
        })
        comparison_df.to_csv('pgo_comparison_detailed.csv', index=False)
        logger.info("Detailed PGO comparison saved to pgo_comparison_detailed.csv")

def main():
    """Main test execution"""
    logger.info("=== STARTING PGO COMPARISON TEST ===")
    
    # Create tester instance
    tester = PGOComparisonTester()
    
    try:
        # Setup both implementations
        tester.setup_implementations()
        
        # Fetch data from both
        tester_data, reporter_data = tester.fetch_data_both()
        
        # Compare raw data
        data_identical = tester.compare_raw_data(tester_data, reporter_data)
        
        # Test PGO calculation
        tester.test_pgo_calculation_direct(tester_data, reporter_data)
        
        logger.info("=== PGO COMPARISON TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
