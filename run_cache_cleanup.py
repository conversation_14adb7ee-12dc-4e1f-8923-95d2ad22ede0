#!/usr/bin/env python3
"""
Simple script to run cache cleanup once for USDT assets.
This cleans up the cached OHLCV data by removing duplicates and ensuring proper sorting.
"""

import os
import sys
import logging
from src.data_cache import cleanup_cache_file

def main():
    """Run cache cleanup for USDT assets."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=" * 60)
    print("CACHE CLEANUP - ONE-TIME EXECUTION")
    print("=" * 60)
    
    # Define the USDT assets to clean up
    usdt_assets = [
        'BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT',
        'XRP/USDT', 'AVAX/USDT', 'ADA/USDT', 'AAVE/USDT',
        'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT',
        'BNB/USDT', 'DOT/USDT'
    ]
    
    exchange_id = 'binance'  # Data source exchange
    timeframe = '1d'
    
    print(f"Cleaning up cache files for {len(usdt_assets)} USDT assets")
    print(f"Exchange: {exchange_id}")
    print(f"Timeframe: {timeframe}")
    print()
    
    success_count = 0
    failure_count = 0
    
    for symbol in usdt_assets:
        print(f"Cleaning up cache file for {symbol}...")
        
        success = cleanup_cache_file(
            exchange_id=exchange_id,
            symbol=symbol,
            timeframe=timeframe
        )
        
        if success:
            success_count += 1
            print(f"  ✓ Successfully cleaned up cache file for {symbol}")
        else:
            failure_count += 1
            print(f"  ✗ Failed to clean up cache file for {symbol}")
    
    print()
    print("=" * 60)
    print("CACHE CLEANUP COMPLETED")
    print("=" * 60)
    print(f"Successfully cleaned: {success_count} files")
    print(f"Failed to clean: {failure_count} files")
    print(f"Total processed: {len(usdt_assets)} files")
    
    if failure_count == 0:
        print("🎉 All cache files cleaned successfully!")
    else:
        print(f"⚠️  {failure_count} files failed (probably don't exist yet)")

if __name__ == "__main__":
    main()
