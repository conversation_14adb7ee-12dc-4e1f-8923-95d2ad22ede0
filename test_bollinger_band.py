#!/usr/bin/env python3
"""
Test script to verify the Bollinger Band indicator implementation
against the provided sample data and Pine Script logic.
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from indicators.custom_indicators import calculate_bollinger_band_signal, calculate_bollinger_band_position

def test_bollinger_band_indicator():
    """Test the Bollinger Band indicator with the provided sample data."""
    print("=" * 80)
    print("TESTING BOLLINGER BAND INDICATOR")
    print("=" * 80)
    
    # Load the sample data
    data_file = "data/ohlcv/geckoterminal/1d/SOLANA_2JcXacFw.csv"
    print(f"Loading data from: {data_file}")
    
    try:
        # Read the CSV data
        df = pd.read_csv(data_file)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        print(f"Loaded {len(df)} rows of data")
        print(f"Date range: {df.index[0]} to {df.index[-1]}")
        print(f"Columns: {df.columns.tolist()}")
        print(f"\nFirst 5 rows:")
        print(df.head())
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # Test parameters from Pine Script
    length = 20
    multiplier = 2.0
    long_threshold = 55.0
    short_threshold = 45.0
    
    print(f"\nTesting with parameters:")
    print(f"  Length: {length}")
    print(f"  Multiplier: {multiplier}")
    print(f"  Long threshold: {long_threshold}")
    print(f"  Short threshold: {short_threshold}")
    
    # Test 1: Basic Bollinger Band calculation
    print(f"\n" + "="*50)
    print("TEST 1: Basic Bollinger Band Calculation")
    print("="*50)
    
    bb_data = calculate_bollinger_band_position(
        df, 
        length=length, 
        multiplier=multiplier, 
        src_col='close'
    )
    
    print(f"Bollinger Band data calculated:")
    print(f"Columns: {bb_data.columns.tolist()}")
    print(f"\nFirst 25 rows (to see when calculation starts):")
    print(bb_data.head(25)[['basis', 'upper_band', 'lower_band', 'position_pct']].round(4))
    
    # Test 2: Signal generation
    print(f"\n" + "="*50)
    print("TEST 2: Signal Generation")
    print("="*50)
    
    bb_signals = calculate_bollinger_band_signal(
        df,
        length=length,
        multiplier=multiplier,
        long_threshold=long_threshold,
        short_threshold=short_threshold,
        src_col='close'
    )
    
    print(f"Signals generated: {len(bb_signals)} values")
    
    # Analyze signal distribution
    signal_counts = bb_signals.value_counts().sort_index()
    total_signals = len(bb_signals)
    
    print(f"\nSignal distribution:")
    for signal, count in signal_counts.items():
        signal_name = "Long" if signal == 1 else "Short" if signal == -1 else "Neutral"
        pct = (count / total_signals) * 100
        print(f"  {signal_name} ({signal}): {count} ({pct:.1f}%)")
    
    # Test 3: Compare with and without Heikin-Ashi
    print(f"\n" + "="*50)
    print("TEST 3: Heikin-Ashi vs Regular Candles")
    print("="*50)
    
    # Regular candles
    bb_signals_regular = calculate_bollinger_band_signal(
        df, length=length, multiplier=multiplier,
        long_threshold=long_threshold, short_threshold=short_threshold,
        src_col='close', use_heikin_ashi=False
    )
    
    # Heikin-Ashi candles
    bb_signals_heikin = calculate_bollinger_band_signal(
        df, length=length, multiplier=multiplier,
        long_threshold=long_threshold, short_threshold=short_threshold,
        use_heikin_ashi=True, heikin_src='close'
    )
    
    # Compare signal distributions
    regular_counts = bb_signals_regular.value_counts().sort_index()
    heikin_counts = bb_signals_heikin.value_counts().sort_index()
    
    print(f"Signal comparison:")
    print(f"{'Signal':<10} {'Regular':<15} {'Heikin-Ashi':<15} {'Difference':<15}")
    print("-" * 55)
    
    for signal in [-1, 0, 1]:
        reg_count = regular_counts.get(signal, 0)
        heikin_count = heikin_counts.get(signal, 0)
        diff = heikin_count - reg_count
        signal_name = "Long" if signal == 1 else "Short" if signal == -1 else "Neutral"
        print(f"{signal_name:<10} {reg_count:<15} {heikin_count:<15} {diff:+<15}")
    
    # Test 4: Detailed analysis of recent signals
    print(f"\n" + "="*50)
    print("TEST 4: Recent Signal Analysis")
    print("="*50)
    
    # Combine data for analysis
    analysis_df = df.copy()
    analysis_df['bb_position'] = bb_data['position_pct']
    analysis_df['bb_signal'] = bb_signals_regular
    analysis_df['bb_signal_heikin'] = bb_signals_heikin
    
    # Show last 20 rows
    recent_data = analysis_df[['close', 'bb_position', 'bb_signal', 'bb_signal_heikin']].tail(20)
    print(f"Last 20 candles:")
    print(recent_data.round(2))
    
    # Test 5: Signal transitions
    print(f"\n" + "="*50)
    print("TEST 5: Signal Transitions Analysis")
    print("="*50)
    
    # Find signal changes
    signal_changes = bb_signals_regular.diff().fillna(0)
    transition_indices = signal_changes[signal_changes != 0].index
    
    print(f"Found {len(transition_indices)} signal transitions")
    print(f"\nRecent transitions (last 10):")
    
    for idx in transition_indices[-10:]:
        date_str = idx.strftime('%Y-%m-%d')
        position = bb_data.loc[idx, 'position_pct']
        signal_before = bb_signals_regular.shift(1).loc[idx]
        signal_after = bb_signals_regular.loc[idx]
        close_price = df.loc[idx, 'close']
        
        print(f"{date_str}: ${close_price:.6f} | Position: {position:.1f}% | {signal_before} → {signal_after}")
    
    # Test 6: Visualize recent data
    print(f"\n" + "="*50)
    print("TEST 6: Creating Visualization")
    print("="*50)
    
    try:
        # Plot the last 60 days
        plot_days = min(60, len(df))
        plot_data = analysis_df.tail(plot_days)
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), sharex=True)
        
        # Plot 1: Price with Bollinger Bands
        ax1.plot(plot_data.index, plot_data['close'], label='Close Price', color='black', linewidth=2)
        ax1.plot(plot_data.index, bb_data['upper_band'].tail(plot_days), label='Upper Band', color='red', alpha=0.7)
        ax1.plot(plot_data.index, bb_data['basis'].tail(plot_days), label='Basis (SMA)', color='blue', alpha=0.7)
        ax1.plot(plot_data.index, bb_data['lower_band'].tail(plot_days), label='Lower Band', color='green', alpha=0.7)
        ax1.fill_between(plot_data.index, bb_data['upper_band'].tail(plot_days), bb_data['lower_band'].tail(plot_days), alpha=0.1, color='gray')
        ax1.set_title('Price with Bollinger Bands')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: BB Position Percentage
        ax2.plot(plot_data.index, plot_data['bb_position'], label='BB Position %', color='purple', linewidth=2)
        ax2.axhline(y=long_threshold, color='green', linestyle='--', alpha=0.8, label=f'Long Threshold ({long_threshold})')
        ax2.axhline(y=short_threshold, color='red', linestyle='--', alpha=0.8, label=f'Short Threshold ({short_threshold})')
        ax2.axhline(y=50, color='black', linestyle='-', alpha=0.3, label='Middle (50%)')
        ax2.fill_between(plot_data.index, long_threshold, 100, alpha=0.2, color='green', label='Long Zone')
        ax2.fill_between(plot_data.index, 0, short_threshold, alpha=0.2, color='red', label='Short Zone')
        ax2.set_title('Bollinger Band Position Percentage')
        ax2.set_ylabel('Position %')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Signals
        ax3.plot(plot_data.index, plot_data['bb_signal'], label='BB Signal (Regular)', color='blue', linewidth=2, marker='o', markersize=3)
        ax3.plot(plot_data.index, plot_data['bb_signal_heikin'], label='BB Signal (Heikin-Ashi)', color='orange', linewidth=1, alpha=0.7)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_title('Bollinger Band Signals')
        ax3.set_ylabel('Signal')
        ax3.set_xlabel('Date')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(-1.5, 1.5)
        
        plt.tight_layout()
        plt.savefig('bollinger_band_test.png', dpi=150, bbox_inches='tight')
        print("Visualization saved as 'bollinger_band_test.png'")
        plt.show()
        
    except Exception as e:
        print(f"Error creating visualization: {e}")
    
    print(f"\n" + "="*80)
    print("BOLLINGER BAND INDICATOR TEST COMPLETE")
    print("="*80)
    
    return analysis_df, bb_data, bb_signals_regular

if __name__ == "__main__":
    test_bollinger_band_indicator()