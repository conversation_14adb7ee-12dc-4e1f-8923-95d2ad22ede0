from main_program import run_strategy_for_web

# Test the fixed code
try:
    results = run_strategy_for_web(
        use_mtpi_signal=True,
        mtpi_timeframe='1d',
        timeframe='1d',
        analysis_start_date='2023-10-20'
    )

    if results and 'error' not in results:
        print('Success! The function executed correctly.')
        print(f"Results keys: {list(results.keys())}")
        if 'mtpi_filtering_metadata' in results:
            print(f"MTPI metadata: {results['mtpi_filtering_metadata']}")
    else:
        error = results.get('error', 'Unknown error')
        print(f"Error: {error}")
except Exception as e:
    print(f"Exception: {e}")
