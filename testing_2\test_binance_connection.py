#!/usr/bin/env python
"""
Test script for Binance API connection.
This script tests the connection to Binance API and retrieves account information.
"""

import os
import sys
import logging
import argparse
import ccxt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.config_manager import load_config, get_exchange_credentials, get_trading_config

def test_binance_connection(api_key=None, api_secret=None, exchange_id='binance'):
    """Test connection to Binance API."""
    logging.info(f"Testing connection to {exchange_id.capitalize()} API...")

    # Use provided credentials or get from config/environment
    if not api_key or not api_secret:
        credentials = get_exchange_credentials(exchange_id)
        api_key = credentials.get('api_key')
        api_secret = credentials.get('api_secret')

    if not api_key or not api_secret:
        logging.error(f"{exchange_id.capitalize()} API key or secret not provided.")
        logging.error("Please provide them as arguments or set environment variables.")
        return False

    try:
        # Initialize exchange
        exchange_class = getattr(ccxt, exchange_id, None)

        if not exchange_class:
            logging.error(f"Exchange '{exchange_id}' not found in ccxt.")
            return False

        exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'spot',
                'warnOnFetchOpenOrdersWithoutSymbol': False,  # Suppress the warning about fetching open orders without symbol
                'adjustForTimeDifference': True,  # Automatically adjust for time differences
                'recvWindow': 10000,  # Increase receive window to 10 seconds
            }
        })

        # Test connection by fetching balance
        logging.info("Fetching account balance...")
        balance = exchange.fetch_balance()

        # Print available balances
        logging.info("Available balances:")
        for currency, amount in balance['free'].items():
            if float(amount) > 0:
                logging.info(f"  {currency}: {amount}")

        # Fetch open orders for a specific symbol
        logging.info("Fetching open orders for BTC/USDT...")
        open_orders = exchange.fetch_open_orders('BTC/USDT')
        logging.info(f"Number of open orders for BTC/USDT: {len(open_orders)}")

        # Fetch recent trades
        logging.info("Fetching recent trades for BTC/USDT...")
        trades = exchange.fetch_my_trades('BTC/USDT', limit=5)
        logging.info(f"Number of recent trades: {len(trades)}")

        logging.info(f"Successfully connected to {exchange_id.capitalize()} API!")
        return True

    except ccxt.AuthenticationError:
        logging.error(f"Authentication error. Please check your {exchange_id.capitalize()} API credentials.")
        return False
    except ccxt.NetworkError as e:
        logging.error(f"Network error: {e}")
        return False
    except Exception as e:
        logging.error(f"Error testing {exchange_id.capitalize()} connection: {e}")
        return False

def test_paper_trading():
    """Test paper trading functionality."""
    logging.info("Testing paper trading functionality...")

    try:
        # Import paper trading module
        from src.trading.paper_trading import PaperTradingExecutor
        from src.trading.executor import TradingExecutor

        # Initialize paper trading
        paper_trading = PaperTradingExecutor()

        # Initialize trading executor
        trading_config = get_trading_config()
        trading_config['enabled'] = True
        trading_config['mode'] = 'paper'

        executor = TradingExecutor('binance')

        # Test buy order
        logging.info("Testing paper buy order...")
        buy_result = executor.enter_position('BTC/USDT')
        logging.info(f"Buy result: {buy_result}")

        # Test sell order
        logging.info("Testing paper sell order...")
        sell_result = executor.exit_position('BTC/USDT')
        logging.info(f"Sell result: {sell_result}")

        logging.info("Paper trading test completed successfully!")
        return True

    except Exception as e:
        logging.error(f"Error testing paper trading: {e}")
        return False

def test_swap_functionality(api_key=None, api_secret=None, exchange_id='binance',
                           from_currency='USDC', to_currency='SUI', amount=None, dry_run=True):
    """
    Test cryptocurrency swap functionality.

    Args:
        api_key: API key for the exchange
        api_secret: API secret for the exchange
        exchange_id: Exchange ID (default: 'binance')
        from_currency: Currency to swap from (default: 'USDC')
        to_currency: Currency to swap to (default: 'SUI')
        amount: Amount to swap (if None, will use a small test amount)
        dry_run: If True, will only simulate the swap without executing it

    Returns:
        bool: True if successful, False otherwise
    """
    logging.info(f"Testing swap functionality from {from_currency} to {to_currency}...")

    # Use provided credentials or get from config/environment
    if not api_key or not api_secret:
        credentials = get_exchange_credentials(exchange_id)
        api_key = credentials.get('api_key')
        api_secret = credentials.get('api_secret')

    if not api_key or not api_secret:
        logging.error(f"{exchange_id.capitalize()} API key or secret not provided.")
        logging.error("Please provide them as arguments or set environment variables.")
        return False

    try:
        # Initialize exchange
        exchange_class = getattr(ccxt, exchange_id, None)

        if not exchange_class:
            logging.error(f"Exchange '{exchange_id}' not found in ccxt.")
            return False

        exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'spot',
                'warnOnFetchOpenOrdersWithoutSymbol': False
            }
        })

        # Fetch balance to check available funds
        balance = exchange.fetch_balance()

        # Check if we have the source currency
        if from_currency not in balance['free'] or float(balance['free'][from_currency]) <= 0:
            logging.error(f"No {from_currency} available for swap.")
            return False

        available_amount = float(balance['free'][from_currency])
        logging.info(f"Available {from_currency}: {available_amount}")

        # If amount is not specified, use a small test amount (1% of available balance)
        if amount is None:
            # Calculate 1% of available balance
            percent_amount = available_amount * 0.01
            # Cap at 10 units to be safe
            amount = min(percent_amount, 10.0)
            logging.info(f"Using test amount: {amount} {from_currency} (1% of available balance, capped at 10 {from_currency})")
        elif amount > available_amount:
            logging.error(f"Requested amount {amount} exceeds available balance {available_amount}")
            return False

        # Construct the trading pair symbol
        symbol = f"{to_currency}/{from_currency}"

        # Check if the trading pair exists
        try:
            exchange.load_markets()
            if symbol not in exchange.markets:
                # Try the reverse pair
                reverse_symbol = f"{from_currency}/{to_currency}"
                if reverse_symbol not in exchange.markets:
                    logging.error(f"Trading pair {symbol} or {reverse_symbol} not found.")
                    return False
                symbol = reverse_symbol
                is_sell = True
            else:
                is_sell = False

            logging.info(f"Using trading pair: {symbol}")

        except Exception as e:
            logging.error(f"Error checking trading pair: {e}")
            return False

        # Get current market price
        ticker = exchange.fetch_ticker(symbol)
        current_price = ticker['last']
        logging.info(f"Current price for {symbol}: {current_price}")

        # Calculate the amount to buy/sell
        if is_sell:
            # We're selling the from_currency
            base_amount = amount
        else:
            # We're buying the to_currency with from_currency
            base_amount = amount / current_price

        logging.info(f"{'Selling' if is_sell else 'Buying'} {base_amount} of {symbol.split('/')[0]}")

        # If dry run, don't execute the order
        if dry_run:
            logging.info("DRY RUN - No actual swap executed")

            # Calculate and display the estimated outcome
            if is_sell:
                # If selling, we're selling base_amount of the base currency
                base_currency = symbol.split('/')[0]  # First part of the symbol (e.g., USDC in USDC/SUI)
                quote_currency = symbol.split('/')[1]  # Second part of the symbol (e.g., SUI in USDC/SUI)
                estimated_receive = base_amount * current_price

                logging.info(f"Would sell {base_amount:.6f} {base_currency} at approximately {current_price:.6f}")
                logging.info(f"Would receive approximately {estimated_receive:.6f} {quote_currency}")
                logging.info(f"Swap summary: {base_amount:.6f} {base_currency} → {estimated_receive:.6f} {quote_currency}")
            else:
                # If buying, we're buying base_amount of the base currency
                base_currency = symbol.split('/')[0]  # First part of the symbol (e.g., SUI in SUI/USDC)
                quote_currency = symbol.split('/')[1]  # Second part of the symbol (e.g., USDC in SUI/USDC)
                cost = base_amount * current_price

                logging.info(f"Would buy {base_amount:.6f} {base_currency} at approximately {current_price:.6f}")
                logging.info(f"Would spend approximately {cost:.6f} {quote_currency}")
                logging.info(f"Swap summary: {cost:.6f} {quote_currency} → {base_amount:.6f} {base_currency}")

            return True

        # Execute the swap by creating a market order
        try:
            logging.info("EXECUTING REAL SWAP - This will use actual funds")

            if is_sell:
                base_currency = symbol.split('/')[0]
                quote_currency = symbol.split('/')[1]
                logging.info(f"Creating market sell order: {base_amount:.6f} {base_currency}")
                order = exchange.create_market_sell_order(symbol, base_amount)
            else:
                base_currency = symbol.split('/')[0]
                quote_currency = symbol.split('/')[1]
                logging.info(f"Creating market buy order: {base_amount:.6f} {base_currency}")
                order = exchange.create_market_buy_order(symbol, base_amount)

            # Extract and display the important parts of the order
            order_id = order.get('id', 'Unknown')
            order_status = order.get('status', 'Unknown')
            filled = order.get('filled', 0)
            cost = order.get('cost', 0)
            fee = order.get('fee', {}).get('cost', 0)
            fee_currency = order.get('fee', {}).get('currency', '')

            logging.info(f"Swap order executed successfully:")
            logging.info(f"  Order ID: {order_id}")
            logging.info(f"  Status: {order_status}")
            logging.info(f"  Filled: {filled:.6f} {base_currency}")
            logging.info(f"  Cost: {cost:.6f} {quote_currency}")
            if fee > 0:
                logging.info(f"  Fee: {fee:.6f} {fee_currency}")

            # Fetch updated balance to confirm the swap
            updated_balance = exchange.fetch_balance()
            if to_currency in updated_balance['free']:
                logging.info(f"Updated {to_currency} balance: {updated_balance['free'][to_currency]}")
            if from_currency in updated_balance['free']:
                logging.info(f"Updated {from_currency} balance: {updated_balance['free'][from_currency]}")

            return True

        except Exception as e:
            logging.error(f"Error executing swap: {e}")
            return False

    except ccxt.AuthenticationError:
        logging.error(f"Authentication error. Please check your {exchange_id.capitalize()} API credentials.")
        return False
    except ccxt.NetworkError as e:
        logging.error(f"Network error: {e}")
        return False
    except Exception as e:
        logging.error(f"Error testing swap functionality: {e}")
        return False

def main():
    """Main entry point for the test script."""
    parser = argparse.ArgumentParser(description='Test Binance API Connection')
    parser.add_argument('--api-key', help='Binance API key')
    parser.add_argument('--api-secret', help='Binance API secret')
    parser.add_argument('--exchange', default='binance', help='Exchange ID (default: binance)')
    parser.add_argument('--paper', action='store_true', help='Test paper trading only')
    parser.add_argument('--swap', action='store_true', help='Test swap functionality')
    parser.add_argument('--from-currency', default='USDC', help='Currency to swap from (default: USDC)')
    parser.add_argument('--to-currency', default='SUI', help='Currency to swap to (default: SUI)')
    parser.add_argument('--amount', type=float, help='Amount to swap (default: small test amount)')
    parser.add_argument('--execute', action='store_true', help='Actually execute the swap (default: dry run)')
    args = parser.parse_args()

    if args.paper:
        success = test_paper_trading()
    elif args.swap:
        success = test_swap_functionality(
            api_key=args.api_key,
            api_secret=args.api_secret,
            exchange_id=args.exchange,
            from_currency=args.from_currency,
            to_currency=args.to_currency,
            amount=args.amount,
            dry_run=not args.execute
        )
    else:
        success = test_binance_connection(args.api_key, args.api_secret, args.exchange)

    if success:
        logging.info("Test completed successfully.")
        sys.exit(0)
    else:
        logging.error("Test failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
