#!/usr/bin/env python
"""
Test script for the resilient trading strategy with network watchdog.
This script simulates running a trading strategy with network resilience features.
"""

import os
import sys
import time
import logging
import threading
import json
import random
from datetime import datetime, timedelta

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"resilient_strategy_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

# Import the network watchdog and recovery manager
from src.network_watchdog import NetworkWatchdog
from src.recovery_manager import RecoveryManager

# Directory for saving state
state_dir = 'data/state'
os.makedirs(state_dir, exist_ok=True)

class TestResilientStrategy:
    """Test class that simulates a trading strategy with resilience features."""
    
    def __init__(self):
        """Initialize the test strategy."""
        self.is_running = False
        self.scheduler_thread = None
        self.last_execution_time = None
        self.last_signal = None
        self.last_best_asset = None
        self.execution_count = 0
        self.missed_executions = 0
        self.run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Available assets for simulation
        self.available_assets = ["BTC/USDC", "ETH/USDC", "SOL/USDC", "XRP/USDC", "ADA/USDC"]
        
        # Initialize network watchdog
        self.initialize_network_watchdog()
        
        # Initialize recovery manager
        self.initialize_recovery_manager()
        
        logging.info("Test resilient strategy initialized")
        print("Test resilient strategy initialized")
    
    def initialize_network_watchdog(self):
        """Initialize the network watchdog."""
        self.network_watchdog = NetworkWatchdog(
            check_interval=5,  # Check every 5 seconds for testing
            recovery_callback=self.network_recovery_callback,
            state_save_callback=self.save_state_callback,
            max_failures=2  # Consider network down after 2 consecutive failures for faster testing
        )
        
        # Add a critical time window for the next 10 minutes
        now = datetime.now()
        self.network_watchdog.add_critical_time(
            hour=now.hour,
            minute=now.minute,
            duration_minutes=10,
            description="Test critical window"
        )
        
        logging.info("Network watchdog initialized")
        print("Network watchdog initialized with 5-second check interval")
        print("Added critical time window for the next 10 minutes")
    
    def initialize_recovery_manager(self):
        """Initialize the recovery manager."""
        recovery_callbacks = {
            'missed_execution': self.recover_missed_execution,
            'network_failure': self.recover_from_network_failure,
            'data_fetch_failure': self.recover_from_data_fetch_failure,
            'strategy_execution_failure': self.recover_from_strategy_failure
        }
        
        self.recovery_manager = RecoveryManager(
            state_dir=state_dir,
            checkpoint_interval=30,  # 30 seconds for testing
            max_recovery_attempts=3,
            recovery_callbacks=recovery_callbacks
        )
        
        logging.info("Recovery manager initialized")
        print("Recovery manager initialized")
    
    def network_recovery_callback(self, downtime_seconds):
        """Called when network connectivity is restored."""
        logging.info(f"Network recovered after {downtime_seconds:.1f} seconds")
        print(f"\n[RECOVERY] Network connection restored after {downtime_seconds:.1f} seconds downtime")
        
        # Check for missed executions
        self.check_missed_executions()
        
        return True
    
    def save_state_callback(self):
        """Called when network issues are detected to save state."""
        logging.info("Saving state due to network issues")
        print("\n[SAVE STATE] Network issues detected, saving current state...")
        
        # Create state dictionary
        state = {
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'last_signal': self.last_signal,
            'last_best_asset': self.last_best_asset,
            'execution_count': self.execution_count,
            'missed_executions': self.missed_executions,
            'run_id': self.run_id,
            'timestamp': datetime.now().isoformat()
        }
        
        # Save state to file
        state_file = os.path.join(state_dir, f"test_strategy_{self.run_id}.json")
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2)
        
        print(f"[SAVE STATE] State saved to {state_file}")
        return True
    
    def check_missed_executions(self):
        """Check if any scheduled executions were missed during network downtime."""
        if not self.last_execution_time:
            logging.info("No previous execution time, cannot check for missed executions")
            print("[RECOVERY] No previous execution time, cannot check for missed executions")
            return
        
        # Get the current time
        now = datetime.now()
        
        # For testing, we'll consider an execution missed if it's been more than 1 minute
        # since the last execution
        time_since_last = (now - self.last_execution_time).total_seconds()
        if time_since_last > 60:
            self.missed_executions += 1
            logging.warning(f"Missed execution detected! Last execution was {time_since_last:.1f} seconds ago")
            print(f"[RECOVERY] Missed execution detected! Last execution was {time_since_last:.1f} seconds ago")
            
            # Add a pending operation to recover from the missed execution
            self.recovery_manager.add_pending_operation(
                'missed_execution',
                {
                    'expected_time': (self.last_execution_time + timedelta(minutes=1)).isoformat(),
                    'current_time': now.isoformat()
                }
            )
            
            # Process pending operations
            self.recovery_manager.process_pending_operations()
        else:
            print(f"[RECOVERY] No missed executions detected. Last execution was {time_since_last:.1f} seconds ago")
    
    def recover_missed_execution(self, details):
        """Recover from a missed execution."""
        logging.info(f"Recovering from missed execution: {details}")
        print(f"\n[RECOVERY] Recovering from missed execution: {details}")
        
        # Execute the strategy with recovery flag
        self.execute_strategy(is_recovery=True)
        
        return True
    
    def recover_from_network_failure(self, details):
        """Recover from a network failure."""
        logging.info(f"Recovering from network failure: {details}")
        print(f"\n[RECOVERY] Recovering from network failure: {details}")
        
        # Check if we're connected now
        if not self.network_watchdog.is_connected:
            logging.warning("Still not connected to network, cannot recover yet")
            print("[RECOVERY] Still not connected to network, cannot recover yet")
            return False
        
        # Check for missed executions
        self.check_missed_executions()
        
        return True
    
    def recover_from_data_fetch_failure(self, details):
        """Recover from a data fetch failure."""
        logging.info(f"Recovering from data fetch failure: {details}")
        print(f"\n[RECOVERY] Recovering from data fetch failure: {details}")
        
        # Simulate data fetch recovery
        print("[RECOVERY] Attempting to fetch data with force_refresh=True")
        time.sleep(2)  # Simulate data fetching
        
        # 80% chance of success for testing
        if random.random() < 0.8:
            print("[RECOVERY] Successfully fetched data")
            return True
        else:
            print("[RECOVERY] Failed to fetch data")
            return False
    
    def recover_from_strategy_failure(self, details):
        """Recover from a strategy execution failure."""
        logging.info(f"Recovering from strategy failure: {details}")
        print(f"\n[RECOVERY] Recovering from strategy failure: {details}")
        
        # Execute the strategy with recovery flag
        return self.execute_strategy(is_recovery=True)
    
    def start(self):
        """Start the test strategy."""
        if self.is_running:
            logging.warning("Strategy is already running")
            return
        
        self.is_running = True
        
        # Start the network watchdog
        self.network_watchdog.start()
        logging.info("Network watchdog started")
        print("Network watchdog started")
        
        # Start the scheduler in a separate thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        
        logging.info("Test strategy started")
        print("Test strategy started")
    
    def stop(self):
        """Stop the test strategy."""
        if not self.is_running:
            logging.warning("Strategy is not running")
            return
        
        self.is_running = False
        
        # Stop the network watchdog
        self.network_watchdog.stop()
        logging.info("Network watchdog stopped")
        print("Network watchdog stopped")
        
        # Wait for the scheduler thread to finish
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        # Save final state
        self.save_state_callback()
        
        logging.info("Test strategy stopped")
        print("Test strategy stopped")
    
    def _run_scheduler(self):
        """Run the scheduler loop."""
        print("\nScheduler loop started - executing strategy every 30 seconds")
        
        while self.is_running:
            # Only run if network is connected
            if self.network_watchdog.is_connected:
                self.execute_strategy()
            else:
                # If network is down, log it but don't try to run
                print("\n[SCHEDULER] Network is down, skipping strategy execution")
            
            # Wait for next execution
            time.sleep(30)  # Run every 30 seconds for testing
    
    def execute_strategy(self, is_recovery=False):
        """Execute the test strategy."""
        try:
            # Check network connectivity first
            if not self.network_watchdog.is_connected and not is_recovery:
                logging.warning("Network is down, skipping strategy execution")
                print("\n[STRATEGY] Network is down, skipping strategy execution")
                return False
            
            self.execution_count += 1
            execution_start_time = datetime.now()
            
            logging.info(f"Executing strategy (run #{self.execution_count})...")
            print(f"\n[STRATEGY] Executing strategy (run #{self.execution_count}) - {execution_start_time}")
            
            if is_recovery:
                print("[STRATEGY] THIS IS A RECOVERY EXECUTION")
            
            # Simulate strategy execution
            print("[STRATEGY] Fetching market data...")
            time.sleep(1)
            
            # Simulate network check during execution
            if not self.network_watchdog.is_connected and not is_recovery:
                raise Exception("Network connection lost during execution")
            
            print("[STRATEGY] Calculating signals...")
            time.sleep(1)
            
            # Generate random signal (0 or 1)
            signal = random.randint(0, 1)
            print(f"[STRATEGY] Generated signal: {'bullish' if signal == 1 else 'bearish'}")
            
            # Select random best asset
            best_asset = random.choice(self.available_assets)
            print(f"[STRATEGY] Selected best asset: {best_asset}")
            
            # Update state variables
            self.last_execution_time = execution_start_time
            self.last_signal = signal
            self.last_best_asset = best_asset
            
            # Save state
            state = {
                'last_execution_time': self.last_execution_time.isoformat(),
                'last_signal': self.last_signal,
                'last_best_asset': self.last_best_asset,
                'execution_count': self.execution_count,
                'missed_executions': self.missed_executions,
                'run_id': self.run_id
            }
            
            # Save state to file
            state_file = os.path.join(state_dir, f"test_strategy_{self.run_id}.json")
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
            
            # Record successful execution
            self.recovery_manager.record_successful_execution(
                'strategy_execution',
                {
                    'signal': signal,
                    'best_asset': best_asset,
                    'execution_time': execution_start_time.isoformat()
                }
            )
            
            # Calculate execution duration
            execution_duration = (datetime.now() - execution_start_time).total_seconds()
            logging.info(f"Strategy execution completed in {execution_duration:.1f} seconds")
            print(f"[STRATEGY] Strategy execution completed in {execution_duration:.1f} seconds")
            
            return True
            
        except Exception as e:
            logging.error(f"Error executing strategy: {e}")
            print(f"\n[STRATEGY] ERROR EXECUTING STRATEGY: {e}")
            
            # Record the strategy execution failure
            self.recovery_manager.record_failed_execution(
                'strategy_execution',
                str(e),
                {
                    'is_recovery': is_recovery
                }
            )
            
            # Try to recover from the strategy execution failure
            if not is_recovery:  # Avoid infinite recursion
                self.recovery_manager.recover_from_failure(
                    'strategy_execution_failure',
                    {
                        'error': str(e),
                        'is_recovery': is_recovery
                    }
                )
            
            return False

def main():
    """Main function to test the resilient strategy."""
    print("\n" + "=" * 80)
    print(f"RESILIENT STRATEGY TEST - {datetime.now()}")
    print("=" * 80)
    print(f"Log file: {log_file}")
    print("=" * 80 + "\n")
    
    # Create and start the test strategy
    strategy = TestResilientStrategy()
    strategy.start()
    
    try:
        print("\nTest is running. You can now simulate network interruptions.")
        print("Instructions:")
        print("1. Disconnect your WiFi or unplug your network cable")
        print("2. Wait for the watchdog to detect the disconnection (about 10-15 seconds)")
        print("3. Observe the state saving action")
        print("4. Reconnect your network")
        print("5. Observe the recovery action and missed execution detection")
        print("\nPress Ctrl+C to stop the test\n")
        
        # Keep the main thread alive
        while True:
            # Print current status every 15 seconds
            status = strategy.network_watchdog.get_connection_status()
            print(f"\nCurrent status: {'Connected' if status['is_connected'] else 'Disconnected'}")
            print(f"In critical window: {status['in_critical_window']}")
            print(f"Consecutive failures: {status['consecutive_failures']}")
            print(f"Execution count: {strategy.execution_count}")
            print(f"Missed executions: {strategy.missed_executions}")
            
            time.sleep(15)
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        # Stop the strategy
        strategy.stop()
        print("Test strategy stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
