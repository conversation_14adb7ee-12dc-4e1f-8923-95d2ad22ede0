#!/usr/bin/env python3
"""
Simple verification script to check if Median Score matches PineScript exactly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score

def verify_median_pinescript():
    """Verify Median Score implementation matches PineScript."""
    print("=" * 60)
    print("MEDIAN SCORE PINESCRIPT VERIFICATION")
    print("=" * 60)
    
    # Fetch recent BTC data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-05-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # PineScript parameters (exact match)
    params = {
        'atr_period': 12,      # subject1
        'multiplier': 1.45,    # mul1
        'median_length': 27,   # slen
        'src_col': 'high'      # src_me
    }
    
    print(f"\nPineScript Parameters:")
    print(f"  subject1 (atr_period): {params['atr_period']}")
    print(f"  mul1 (multiplier): {params['multiplier']}")
    print(f"  slen (median_length): {params['median_length']}")
    print(f"  src_me (src_col): {params['src_col']}")
    
    # Calculate signals
    signal, supertrend_line, direction = calculate_median_score(btc_df, **params)
    
    # Show recent signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"\nRecent Signal Changes (Last 10):")
    print("-" * 60)
    
    for change_date in change_indices[-10:]:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            high_price = btc_df['high'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            supertrend_val = supertrend_line.iloc[idx]
            
            # Determine signal type
            signal_type = "LONG" if curr_signal == 1 else "SHORT" if curr_signal == -1 else "NEUTRAL"
            direction_change = f"{prev_direction}→{direction_val}"
            
            print(f"{change_date.strftime('%Y-%m-%d')}: {prev_signal}→{curr_signal} ({signal_type})")
            print(f"  Direction: {direction_change}")
            print(f"  Price: ${close_price:.2f}, High: ${high_price:.2f}")
            print(f"  Supertrend: ${supertrend_val:.2f}")
            print(f"  Price vs ST: {((close_price - supertrend_val) / supertrend_val * 100):+.2f}%")
            print()
    
    # Current status
    current_signal = signal.iloc[-1]
    current_direction = direction.iloc[-1]
    current_price = btc_df['close'].iloc[-1]
    current_supertrend = supertrend_line.iloc[-1]
    
    print("=" * 60)
    print("CURRENT STATUS")
    print("=" * 60)
    print(f"Current Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Current Direction: {current_direction}")
    print(f"Current Price: ${current_price:.2f}")
    print(f"Current Supertrend: ${current_supertrend:.2f}")
    print(f"Price vs Supertrend: {((current_price - current_supertrend) / current_supertrend * 100):+.2f}%")
    
    # Verify logic consistency
    print("\n" + "=" * 60)
    print("LOGIC VERIFICATION")
    print("=" * 60)
    
    # Check if current signal makes sense
    if current_signal == 1:  # Long
        if current_direction == -1:
            print("✅ CORRECT: Long signal with direction = -1 (matches PineScript logic)")
        else:
            print("❌ INCORRECT: Long signal but direction != -1")
    elif current_signal == -1:  # Short
        if current_direction == 1:
            print("✅ CORRECT: Short signal with direction = 1 (matches PineScript logic)")
        else:
            print("❌ INCORRECT: Short signal but direction != 1")
    
    # Signal distribution
    signal_counts = signal.value_counts().sort_index()
    print(f"\nSignal Distribution:")
    for sig, count in signal_counts.items():
        sig_name = "SHORT" if sig == -1 else "NEUTRAL" if sig == 0 else "LONG"
        percentage = (count / len(signal)) * 100
        print(f"  {sig_name} ({sig}): {count} candles ({percentage:.1f}%)")
    
    print(f"\n✅ Median Score verification complete!")
    print(f"Parameters match PineScript exactly:")
    print(f"  subject1={params['atr_period']}, mul1={params['multiplier']}")
    print(f"  slen={params['median_length']}, src_me={params['src_col']}")

if __name__ == "__main__":
    verify_median_pinescript()
