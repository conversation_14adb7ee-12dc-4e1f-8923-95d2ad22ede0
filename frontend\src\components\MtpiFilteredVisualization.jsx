import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON><PERSON>, ColorType, CrosshairMode } from 'lightweight-charts';
import axios from 'axios';
import './TradingViewDashboard.css';

// This component applies asset coloring AND MTPI filtering to the backend data
const MtpiFilteredVisualization = () => {
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const seriesRef = useRef({});
  const resizeObserverRef = useRef(null);
  const [isLogScale, setIsLogScale] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  // Fetch data directly from the backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching data from backend...');
        const response = await axios.get('/api/equity');
        console.log('Backend data received:', response.data);
        setData(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Create chart when data is available
  useEffect(() => {
    if (!data || !chartContainerRef.current) return;

    // Define asset colors
    const assetColors = {
      'BTC/USDT': '#f7931a', // Bitcoin orange
      'ETH/USDT': '#627eea', // Ethereum blue
      'SOL/USDT': '#00ffbd', // Solana green
      'out-of-market': '#999999' // Gray for out-of-market periods
    };

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 500,
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#333333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      rightPriceScale: {
        mode: isLogScale ? 1 : 0, // 1 = logarithmic, 0 = linear
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
    });

    chartRef.current = chart;

    // Add series for each curve
    const series = {};

    // Add buy-and-hold series first (so they appear behind the strategy line)
    const assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'];

    assets.forEach(asset => {
      if (data.curves && data.curves[asset] && data.curves[asset].length > 0) {
        console.log(`Adding ${asset} series with ${data.curves[asset].length} data points`);
        series[asset] = chart.addLineSeries({
          color: assetColors[asset] ? assetColors[asset] + '80' : '#99999980', // Add 50% transparency
          lineWidth: 1,
          lineStyle: 0, // 0 = solid line (no dashes)
          title: `${asset} B&H`,
        });
        series[asset].setData(data.curves[asset]);
      } else {
        console.log(`No data for ${asset} or empty array`);
      }
    });

    // Add strategy series with asset coloring AND MTPI filtering
    if (data.curves && data.curves.strategy && data.curves.strategy.length > 0 && data.assetChanges) {
      console.log(`Adding strategy series with ${data.curves.strategy.length} data points`);
      
      // Create a single continuous line series for the strategy
      const strategySeries = chart.addLineSeries({
        lineWidth: 3,
        title: 'Strategy (Asset Colored + MTPI Filtered)',
        lastValueVisible: true,
        priceLineVisible: true,
        lineType: 0, // 0 = solid line (no dashes)
        lineStyle: 0, // 0 = solid line (no dashes)
        disableSegmentRendering: false, // Enable segment rendering for colored segments
      });
      
      // Sort asset changes by date to ensure chronological order
      const sortedAssetChanges = [...data.assetChanges].sort((a, b) => a.date - b.date);
      
      // Create a map of time to asset for quick lookup
      const timeToAssetMap = {};
      
      // Process all data points to assign the correct asset
      for (let i = 0; i < data.curves.strategy.length; i++) {
        const point = data.curves.strategy[i];
        const pointTime = point.time;
        
        // Find the most recent asset change before this point
        let currentAsset = 'out-of-market'; // Default if no asset change is found
        
        for (let j = sortedAssetChanges.length - 1; j >= 0; j--) {
          if (pointTime >= sortedAssetChanges[j].date) {
            currentAsset = sortedAssetChanges[j].asset || 'out-of-market';
            break; // Found the most recent asset change
          }
        }
        
        // Store the asset for this time
        timeToAssetMap[pointTime] = currentAsset;
      }
      
      // Now map the strategy data points with the correct colors based on asset AND MTPI filtering
      const coloredData = data.curves.strategy.map(point => {
        // Get the asset for this point
        const asset = timeToAssetMap[point.time] || 'out-of-market';
        
        // Find the most recent MTPI signal before this point
        let mtpiValue = null;
        if (data.metadata?.rawMtpiSignals) {
          const mtpiSignals = data.metadata.rawMtpiSignals;
          for (let j = mtpiSignals.length - 1; j >= 0; j--) {
            if (point.time >= mtpiSignals[j].time) {
              mtpiValue = mtpiSignals[j].value;
              break;
            }
          }
        }
        
        // Determine if MTPI allows trading
        const mtpiAllowsTrade = mtpiValue === null || mtpiValue === 1;
        
        // If MTPI doesn't allow trading, use out-of-market color
        const assetToUse = mtpiAllowsTrade ? asset : 'out-of-market';
        
        // Get the color for this asset
        let color = assetColors[assetToUse] || '#0d6efd';
        
        // If the asset is empty string, it means we're out of market
        if (assetToUse === '') {
          color = assetColors['out-of-market'];
        }
        
        return {
          time: point.time,
          value: point.value,
          color: color
        };
      });
      
      // Set the data with colors
      strategySeries.setData(coloredData);
      
      // Apply a default color for the series (will be overridden by point colors)
      strategySeries.applyOptions({
        color: '#0d6efd' // Default blue color as fallback
      });
      
      console.log('Strategy data colored by asset AND filtered by MTPI');
      
      // Count how many points are out of market due to MTPI
      const outOfMarketPoints = coloredData.filter(point => point.color === assetColors['out-of-market']).length;
      console.log(`Out of market points due to MTPI: ${outOfMarketPoints} (${(outOfMarketPoints / coloredData.length * 100).toFixed(1)}%)`);
    } else {
      console.log('No data for strategy or empty array');
    }

    // Fit content
    chart.timeScale().fitContent();

    // Save references
    chartRef.current = chart;
    seriesRef.current = series;

    // Set up resize observer
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    resizeObserverRef.current = new ResizeObserver(entries => {
      if (chartRef.current && entries.length > 0) {
        const { width } = entries[0].contentRect;
        chartRef.current.applyOptions({ width });
        chartRef.current.timeScale().fitContent();
      }
    });

    resizeObserverRef.current.observe(chartContainerRef.current);

    // Cleanup function
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [data, isLogScale]);

  // Function to toggle between log and linear scale
  const toggleScale = () => {
    setIsLogScale(!isLogScale);
  };

  if (loading) {
    return <div className="loading">Loading chart data...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="chart-container">
      <h2 style={{ textAlign: 'center', marginBottom: '10px' }}>MTPI Filtered Visualization</h2>
      <div className="chart-controls">
        <button
          className={`scale-toggle-btn ${isLogScale ? 'active' : ''}`}
          onClick={toggleScale}
        >
          {isLogScale ? 'Log Scale' : 'Linear Scale'}
        </button>
      </div>
      <div ref={chartContainerRef} className="chart" style={{ height: '500px' }}></div>
      <div className="legend">
        <div className="legend-item">
          <div className="legend-color" style={{ background: 'linear-gradient(90deg, #f7931a 0%, #627eea 33%, #00ffbd 66%, #999999 100%)' }}></div>
          <span>Strategy (Asset Colored + MTPI Filtered)</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#f7931a', opacity: 0.5 }}></div>
          <span>BTC/USDT B&H</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#627eea', opacity: 0.5 }}></div>
          <span>ETH/USDT B&H</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#00ffbd', opacity: 0.5 }}></div>
          <span>SOL/USDT B&H</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#999999' }}></div>
          <span>Out of Market</span>
        </div>
      </div>
    </div>
  );
};

export default MtpiFilteredVisualization;
