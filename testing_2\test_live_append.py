#!/usr/bin/env python
"""
Test script for live data fetching and appending to cache.

This script demonstrates fetching missing data from Binance and appending it to cache files,
then continuing to fetch and append new data in real-time as each minute closes.
"""

import os
import sys
import logging
import pandas as pd
import time
import shutil
from datetime import datetime, timedelta
import ccxt

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.data_cache import (
    append_to_cache,
    load_from_cache,
    cleanup_cache_file,
    get_cache_path
)
from src.utils import setup_logging
from src.state_manager import save_state, load_state

def setup_test():
    """Set up the test environment."""
    setup_logging(level=logging.INFO)

    print("\n" + "=" * 70)
    print("TESTING LIVE DATA FETCHING AND APPENDING TO CACHE")
    print("=" * 70)

    # Test parameters
    exchange_id = 'binance'
    symbols = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC']
    timeframe = '1m'

    # Create backup of original files
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"

        if os.path.exists(cache_path):
            print(f"Creating backup of {cache_path} to {backup_path}")
            shutil.copy2(cache_path, backup_path)

    return exchange_id, symbols, timeframe

def restore_backups(exchange_id, symbols, timeframe):
    """Restore original files from backups."""
    print("\nRestoring original files from backups...")
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"

        if os.path.exists(backup_path):
            print(f"Restoring {cache_path} from {backup_path}")
            shutil.copy2(backup_path, cache_path)
            os.remove(backup_path)

def fetch_and_append_historical_data(exchange, symbol, timeframe, last_timestamp):
    """Fetch historical data from last_timestamp to now and append to cache."""
    print(f"\nFetching historical data for {symbol} from {last_timestamp} to now...")

    # Convert timestamp to milliseconds for CCXT
    since_ms = int(pd.to_datetime(last_timestamp).timestamp() * 1000)

    # Initialize variables for pagination
    all_candles = []
    current_since = since_ms

    # Fetch data in batches until we reach the current time
    while True:
        try:
            # Fetch a batch of candles
            candles = exchange.fetch_ohlcv(symbol, timeframe, since=current_since, limit=1000)

            if not candles:
                print(f"No more candles available for {symbol}")
                break

            print(f"Fetched {len(candles)} candles for {symbol}")
            all_candles.extend(candles)

            # Update the since parameter for the next batch
            last_candle_time = candles[-1][0]
            if last_candle_time <= current_since:
                print(f"No newer candles available for {symbol}")
                break

            current_since = last_candle_time + 1

            # If we've reached close to the current time, stop
            current_time_ms = int(datetime.now().timestamp() * 1000)
            if current_since >= current_time_ms - (60 * 1000):  # Within 1 minute of now
                break

            # Add a small delay to avoid rate limits
            time.sleep(0.5)

        except Exception as e:
            print(f"Error fetching historical data for {symbol}: {e}")
            break

    if not all_candles:
        print(f"No historical data fetched for {symbol}")
        return None

    # Convert to DataFrame
    columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    df = pd.DataFrame(all_candles, columns=columns)

    # Convert timestamp to datetime
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

    # Set timestamp as index
    df.set_index('timestamp', inplace=True)

    # Remove any data that's already in the cache
    # Convert last_timestamp to the same type as df.index for comparison
    last_ts = pd.to_datetime(last_timestamp)
    df = df[df.index > last_ts]

    if df.empty:
        print(f"No new historical data to append for {symbol}")
        return None

    print(f"Prepared {len(df)} new candles for {symbol} from {df.index.min()} to {df.index.max()}")

    return df

def fetch_and_append_live_data(exchange, exchange_id, symbol, timeframe, last_timestamp):
    """Fetch live data for a few minutes and append to cache."""
    print(f"\nFetching live data for {symbol} for the next few minutes...")

    # Number of minutes to fetch live data
    minutes_to_fetch = 3

    for i in range(minutes_to_fetch):
        try:
            # Wait until the next minute closes
            current_time = datetime.now()
            seconds_to_next_minute = 60 - current_time.second
            print(f"Waiting {seconds_to_next_minute} seconds for the next minute to close...")
            time.sleep(seconds_to_next_minute + 2)  # Add 2 seconds to ensure the candle is closed

            # Fetch the latest candle
            candles = exchange.fetch_ohlcv(symbol, timeframe, limit=1)

            if not candles:
                print(f"No live candle available for {symbol}")
                continue

            # Convert to DataFrame
            columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            df = pd.DataFrame(candles, columns=columns)

            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Set timestamp as index
            df.set_index('timestamp', inplace=True)

            # Remove any data that's already in the cache
            last_ts = pd.to_datetime(last_timestamp)
            if df.index.max() <= last_ts:
                print(f"Live candle for {symbol} is not newer than last timestamp")
                continue

            print(f"Fetched live candle for {symbol} at {df.index[0]}")
            print(df)

            # Append to cache
            append_success = append_to_cache(df, exchange_id, symbol, timeframe)

            if append_success:
                print(f"✅ Successfully appended live candle for {symbol} at {df.index[0]}")
                last_timestamp = df.index.max()
            else:
                print(f"❌ Failed to append live candle for {symbol}")

        except Exception as e:
            print(f"Error fetching live data for {symbol}: {e}")

    return last_timestamp

def main():
    """Main function to run the test."""
    try:
        # Set up the test
        exchange_id, symbols, timeframe = setup_test()

        # Initialize the exchange
        exchange = ccxt.binance()

        # Process each symbol
        for symbol in symbols:
            try:
                # Load current data
                current_data = load_from_cache(exchange_id, symbol, timeframe)
                if current_data is None or current_data.empty:
                    print(f"No data found for {symbol}, skipping...")
                    continue

                last_timestamp = current_data.index.max()
                print(f"\nProcessing {symbol}...")
                print(f"Loaded {len(current_data)} rows from cache")
                print(f"Last timestamp: {last_timestamp}")

                # Fetch and append historical data
                historical_df = fetch_and_append_historical_data(exchange, symbol, timeframe, last_timestamp)

                if historical_df is not None and not historical_df.empty:
                    # Append to cache
                    append_success = append_to_cache(historical_df, exchange_id, symbol, timeframe)

                    if append_success:
                        print(f"✅ Successfully appended {len(historical_df)} historical candles for {symbol}")
                        last_timestamp = historical_df.index.max()

                        # Load the data again to verify
                        loaded_data = load_from_cache(exchange_id, symbol, timeframe)
                        print(f"Loaded {len(loaded_data)} rows after append")
                        print(f"New last timestamp: {loaded_data.index.max()}")

                        # Show the last few rows
                        print("\nLast 5 rows of the updated file:")
                        print(loaded_data.tail(5))
                    else:
                        print(f"❌ Failed to append historical data for {symbol}")

                # Fetch and append live data
                new_last_timestamp = fetch_and_append_live_data(exchange, exchange_id, symbol, timeframe, last_timestamp)

                if new_last_timestamp != last_timestamp:
                    # Load the data again to verify
                    loaded_data = load_from_cache(exchange_id, symbol, timeframe)
                    print(f"\nFinal state: Loaded {len(loaded_data)} rows")
                    print(f"Final last timestamp: {loaded_data.index.max()}")

                    # Show the last few rows
                    print("\nLast 5 rows of the final file:")
                    print(loaded_data.tail(5))

            except Exception as e:
                print(f"Error processing {symbol}: {e}")

        print("\nTest completed successfully")

    except Exception as e:
        print(f"Error in test: {e}")
    finally:
        # Restore original files
        restore_backups(exchange_id, symbols, timeframe)
        print("\nTest completed and original files restored")

if __name__ == "__main__":
    main()
