{"name": "asset-screener-frontend", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "axios": "^1.6.2", "chart.js": "^4.4.9", "express": "^4.18.3", "framer-motion": "^10.16.16", "lightweight-charts": "^4.2.3", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-query": "^3.39.3", "react-router-dom": "^7.5.2", "recharts": "^2.10.3", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^6.3.2"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}}