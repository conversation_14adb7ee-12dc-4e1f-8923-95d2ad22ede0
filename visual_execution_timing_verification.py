#!/usr/bin/env python3
"""
Visual verification of execution timing logic.
Creates charts and tables to visually verify the manual execution timing.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import logging
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

def setup_logging():
    """Setup logging."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_execution_timing_comparison():
    """Create a visual comparison of execution timing."""
    logging.info("🎨 Creating visual execution timing comparison...")
    
    try:
        from src.data_fetcher import fetch_ohlcv_data
        
        # Fetch data
        start_date = '2024-06-01'
        symbol = 'BTC/USDT'
        
        # Get daily data
        daily_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='1d',
            since=start_date,
            use_cache=True
        )
        
        # Get 12h data
        twelve_h_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='12h',
            since=start_date,
            use_cache=True
        )
        
        daily_data = daily_data_dict[symbol]
        twelve_h_data = twelve_h_data_dict[symbol]
        
        # Filter to recent period for clarity
        end_date = daily_data.index.max()
        start_plot = end_date - pd.Timedelta(days=30)
        
        daily_sample = daily_data[daily_data.index >= start_plot]
        twelve_h_sample = twelve_h_data[twelve_h_data.index >= start_plot]
        
        # Create the plot
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle('Manual vs Automatic Execution Timing Verification', fontsize=16, fontweight='bold')
        
        # Plot 1: Price data with execution points
        ax1.plot(daily_sample.index, daily_sample['close'], 'b-', label='Daily Close (Auto Execution)', linewidth=2)
        
        # Add 12h candle close prices
        midnight_candles = twelve_h_sample[twelve_h_sample.index.hour == 0]
        ax1.scatter(midnight_candles.index, midnight_candles['close'], 
                   color='red', s=50, label='12 PM Close (Manual Execution)', zorder=5)
        
        ax1.set_title('Price Comparison: Daily Close vs 12 PM Close')
        ax1.set_ylabel('Price (USD)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Return comparison
        returns_auto = daily_sample['close'].pct_change()
        
        # Calculate manual returns
        returns_manual = pd.Series(index=daily_sample.index, dtype=float)
        for i in range(1, len(daily_sample)):
            current_date = daily_sample.index[i]
            prev_close = daily_sample['close'].iloc[i-1]
            
            midnight_date = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
            if midnight_date in midnight_candles.index:
                twelve_pm_close = midnight_candles.loc[midnight_date, 'close']
                returns_manual.iloc[i] = (twelve_pm_close - prev_close) / prev_close
        
        ax2.plot(returns_auto.index, returns_auto * 100, 'b-', label='Auto Returns (Daily Close)', linewidth=2)
        ax2.plot(returns_manual.index, returns_manual * 100, 'r--', label='Manual Returns (12 PM)', linewidth=2)
        ax2.set_title('Daily Returns Comparison')
        ax2.set_ylabel('Return (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Plot 3: Cumulative difference
        return_diff = (returns_manual - returns_auto).fillna(0)
        cumulative_diff = return_diff.cumsum() * 100
        
        ax3.plot(cumulative_diff.index, cumulative_diff, 'g-', linewidth=2)
        ax3.fill_between(cumulative_diff.index, cumulative_diff, alpha=0.3, color='green')
        ax3.set_title('Cumulative Return Difference (Manual - Auto)')
        ax3.set_ylabel('Cumulative Difference (%)')
        ax3.set_xlabel('Date')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Format x-axis
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        plt.savefig('execution_timing_verification.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Create summary table
        create_summary_table(returns_auto, returns_manual, daily_sample.index)
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to create visual comparison: {e}")
        return False

def create_summary_table(returns_auto, returns_manual, dates):
    """Create a detailed summary table."""
    logging.info("\n📊 EXECUTION TIMING SUMMARY TABLE")
    logging.info("=" * 80)
    
    # Calculate statistics
    valid_auto = returns_auto.dropna()
    valid_manual = returns_manual.dropna()
    
    stats = {
        'Metric': [
            'Total Returns',
            'Average Daily Return',
            'Standard Deviation',
            'Sharpe Ratio (approx)',
            'Max Daily Return',
            'Min Daily Return',
            'Positive Days',
            'Negative Days'
        ],
        'Automatic (Daily Close)': [
            f"{(valid_auto + 1).prod() - 1:.2%}",
            f"{valid_auto.mean():.4%}",
            f"{valid_auto.std():.4%}",
            f"{valid_auto.mean() / valid_auto.std():.2f}" if valid_auto.std() > 0 else "N/A",
            f"{valid_auto.max():.4%}",
            f"{valid_auto.min():.4%}",
            f"{(valid_auto > 0).sum()} ({(valid_auto > 0).mean():.1%})",
            f"{(valid_auto < 0).sum()} ({(valid_auto < 0).mean():.1%})"
        ],
        'Manual (12 PM)': [
            f"{(valid_manual + 1).prod() - 1:.2%}",
            f"{valid_manual.mean():.4%}",
            f"{valid_manual.std():.4%}",
            f"{valid_manual.mean() / valid_manual.std():.2f}" if valid_manual.std() > 0 else "N/A",
            f"{valid_manual.max():.4%}",
            f"{valid_manual.min():.4%}",
            f"{(valid_manual > 0).sum()} ({(valid_manual > 0).mean():.1%})",
            f"{(valid_manual < 0).sum()} ({(valid_manual < 0).mean():.1%})"
        ]
    }
    
    df_stats = pd.DataFrame(stats)
    print(df_stats.to_string(index=False))
    
    # Detailed day-by-day comparison (last 15 days)
    logging.info(f"\n📅 DETAILED DAY-BY-DAY COMPARISON (Last 15 Days)")
    logging.info("=" * 80)
    
    comparison_data = []
    for i in range(max(0, len(dates) - 15), len(dates)):
        if i > 0:  # Skip first day
            date = dates[i]
            auto_ret = returns_auto.iloc[i] if not pd.isna(returns_auto.iloc[i]) else 0
            manual_ret = returns_manual.iloc[i] if not pd.isna(returns_manual.iloc[i]) else 0
            diff = manual_ret - auto_ret
            
            comparison_data.append({
                'Date': date.strftime('%Y-%m-%d'),
                'Auto Return': f"{auto_ret:.4%}",
                'Manual Return': f"{manual_ret:.4%}",
                'Difference': f"{diff:.4%}",
                'Better': 'Manual' if diff > 0 else 'Auto' if diff < 0 else 'Same'
            })
    
    df_comparison = pd.DataFrame(comparison_data)
    print(df_comparison.to_string(index=False))

def create_data_quality_report():
    """Create a data quality report."""
    logging.info("\n🔍 DATA QUALITY REPORT")
    logging.info("=" * 50)
    
    try:
        from src.data_fetcher import fetch_ohlcv_data
        
        symbol = 'BTC/USDT'
        start_date = '2024-06-01'
        
        # Fetch both datasets
        daily_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='1d',
            since=start_date,
            use_cache=True
        )
        
        twelve_h_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='12h',
            since=start_date,
            use_cache=True
        )
        
        daily_data = daily_data_dict[symbol]
        twelve_h_data = twelve_h_data_dict[symbol]
        
        # Data quality checks
        logging.info(f"Daily data: {len(daily_data)} candles from {daily_data.index.min()} to {daily_data.index.max()}")
        logging.info(f"12h data: {len(twelve_h_data)} candles from {twelve_h_data.index.min()} to {twelve_h_data.index.max()}")
        
        # Check for missing data
        expected_12h_candles = len(daily_data) * 2
        actual_12h_candles = len(twelve_h_data)
        logging.info(f"Expected 12h candles: {expected_12h_candles}, Actual: {actual_12h_candles}")
        
        # Check midnight candle availability
        midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0]
        logging.info(f"Midnight candles available: {len(midnight_candles)} out of {len(daily_data)} daily candles")
        
        # Coverage percentage
        coverage = len(midnight_candles) / len(daily_data) * 100
        logging.info(f"12 PM execution coverage: {coverage:.1f}%")
        
        if coverage < 95:
            logging.warning("⚠️  Low coverage detected! Manual execution may fall back to approximations frequently.")
        else:
            logging.info("✅ Good coverage for manual execution timing.")
            
        return True
        
    except Exception as e:
        logging.error(f"Failed to create data quality report: {e}")
        return False

def main():
    """Run visual verification."""
    setup_logging()
    
    logging.info("🎨 VISUAL EXECUTION TIMING VERIFICATION")
    logging.info("=" * 50)
    
    # Create visual comparison
    if create_execution_timing_comparison():
        logging.info("✅ Visual comparison created successfully")
    else:
        logging.error("❌ Failed to create visual comparison")
    
    # Create data quality report
    if create_data_quality_report():
        logging.info("✅ Data quality report completed")
    else:
        logging.error("❌ Failed to create data quality report")
    
    logging.info("\n🎯 VISUAL VERIFICATION COMPLETE")
    logging.info("Check 'execution_timing_verification.png' for the visual comparison")

if __name__ == "__main__":
    main()
