#!/usr/bin/env python3
"""
Simple test to verify execution timing difference with a single asset.
This eliminates asset selection complexity and focuses purely on execution timing.
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_asset_execution_timing():
    """Test execution timing with a single asset buy-and-hold strategy."""
    
    print("=" * 80)
    print("SINGLE ASSET EXECUTION TIMING TEST")
    print("=" * 80)
    
    # Create sample data for testing
    dates = pd.date_range('2024-06-01', periods=10, freq='D', tz='UTC')
    
    # Sample daily OHLCV data with some volatility
    np.random.seed(42)  # For reproducible results
    base_price = 70000
    daily_data = []
    
    for i, date in enumerate(dates):
        if i == 0:
            open_price = base_price
        else:
            open_price = daily_data[i-1]['close']
        
        # Add some random movement
        daily_change = np.random.normal(0, 0.02)  # 2% daily volatility
        close_price = open_price * (1 + daily_change)
        
        # Create realistic OHLC
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
        
        daily_data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000000
        })
    
    daily_df = pd.DataFrame(daily_data, index=dates)
    
    # Create 12h data (00:00 and 12:00 for each day)
    twelve_h_dates = []
    twelve_h_data = []
    
    for i, date in enumerate(dates):
        daily_row = daily_df.iloc[i]
        
        # 00:00 candle (first half of the day)
        twelve_h_dates.append(date.replace(hour=0))
        midday_price = (daily_row['open'] + daily_row['close']) / 2 + np.random.normal(0, daily_row['close'] * 0.005)
        twelve_h_data.append({
            'open': daily_row['open'],
            'high': (daily_row['open'] + daily_row['high']) / 2,
            'low': (daily_row['open'] + daily_row['low']) / 2,
            'close': midday_price,
            'volume': 500000
        })
        
        # 12:00 candle (second half of the day)
        twelve_h_dates.append(date.replace(hour=12))
        # Make 12PM close different from daily close by adding some random variation
        twelve_pm_close = daily_row['close'] + np.random.normal(0, daily_row['close'] * 0.01)  # 1% variation
        twelve_h_data.append({
            'open': midday_price,
            'high': max(midday_price, twelve_pm_close, daily_row['high']),
            'low': min(midday_price, twelve_pm_close, daily_row['low']),
            'close': twelve_pm_close,  # Different from daily close
            'volume': 500000
        })
    
    twelve_h_df = pd.DataFrame(twelve_h_data, index=pd.DatetimeIndex(twelve_h_dates))
    
    print("Sample Data Created:")
    print(f"Daily data: {len(daily_df)} rows")
    print(f"12h data: {len(twelve_h_df)} rows")
    print("\nFirst 5 days of daily data:")
    print(daily_df[['open', 'high', 'low', 'close']].head().round(2))
    
    # Calculate returns for both execution timings
    print("\n" + "="*50)
    print("CALCULATING RETURNS")
    print("="*50)
    
    # 1. Automatic execution (close to close)
    auto_returns = daily_df['close'].pct_change().fillna(0.0)
    print(f"\nAutomatic execution returns (close-to-close):")
    for i, (date, ret) in enumerate(auto_returns.items()):
        if i > 0:  # Skip first day (no return)
            prev_close = daily_df['close'].iloc[i-1]
            curr_close = daily_df['close'].iloc[i]
            print(f"  {date.date()}: {ret:.4f} ({ret*100:.2f}%) - ${prev_close:.2f} -> ${curr_close:.2f}")
    
    # 2. Manual execution (close to 12PM)
    manual_returns = pd.Series(index=daily_df.index, dtype=float)
    manual_returns.iloc[0] = 0.0
    
    print(f"\nManual execution returns (close-to-12PM):")
    for i in range(1, len(daily_df)):
        date = daily_df.index[i]
        prev_close = daily_df['close'].iloc[i-1]
        
        # Find 12PM candle for this date
        twelve_pm_date = date.replace(hour=12)
        if twelve_pm_date in twelve_h_df.index:
            twelve_pm_close = twelve_h_df.loc[twelve_pm_date, 'close']
            manual_returns.iloc[i] = (twelve_pm_close - prev_close) / prev_close
            
            print(f"  {date.date()}: {manual_returns.iloc[i]:.4f} ({manual_returns.iloc[i]*100:.2f}%) - ${prev_close:.2f} -> ${twelve_pm_close:.2f} (12PM)")
        else:
            manual_returns.iloc[i] = 0.0
            print(f"  {date.date()}: No 12PM data available")
    
    # Calculate portfolio performance
    print("\n" + "="*50)
    print("PORTFOLIO PERFORMANCE")
    print("="*50)
    
    initial_capital = 10000
    
    # Automatic execution portfolio
    auto_equity = [initial_capital]
    for i in range(1, len(auto_returns)):
        new_equity = auto_equity[-1] * (1 + auto_returns.iloc[i])
        auto_equity.append(new_equity)
    
    # Manual execution portfolio
    manual_equity = [initial_capital]
    for i in range(1, len(manual_returns)):
        new_equity = manual_equity[-1] * (1 + manual_returns.iloc[i])
        manual_equity.append(new_equity)
    
    # Results
    auto_final = auto_equity[-1]
    manual_final = manual_equity[-1]
    auto_total_return = (auto_final / initial_capital - 1) * 100
    manual_total_return = (manual_final / initial_capital - 1) * 100
    difference = manual_total_return - auto_total_return
    
    print(f"\nResults:")
    print(f"  Initial Capital: ${initial_capital:,.2f}")
    print(f"  Automatic Execution Final: ${auto_final:,.2f}")
    print(f"  Manual Execution Final: ${manual_final:,.2f}")
    print(f"  Automatic Total Return: {auto_total_return:.4f}%")
    print(f"  Manual Total Return: {manual_total_return:.4f}%")
    print(f"  Difference: {difference:.4f}%")
    
    # Day-by-day comparison
    print(f"\nDay-by-day equity progression:")
    print(f"{'Date':<12} {'Auto Equity':<12} {'Manual Equity':<14} {'Difference':<12}")
    print("-" * 55)
    for i, date in enumerate(daily_df.index):
        auto_eq = auto_equity[i]
        manual_eq = manual_equity[i]
        diff = manual_eq - auto_eq
        print(f"{date.date()} ${auto_eq:>9.2f}   ${manual_eq:>11.2f}   ${diff:>9.2f}")
    
    # Check if results are different
    if abs(difference) < 0.001:
        print(f"\n❌ PROBLEM: Execution timings produce nearly identical results!")
        print(f"   This suggests there might be a bug in the implementation.")
    else:
        print(f"\n✅ SUCCESS: Execution timings produce different results!")
        print(f"   Manual execution {'outperforms' if difference > 0 else 'underperforms'} by {abs(difference):.4f}%")
    
    return {
        'auto_returns': auto_returns,
        'manual_returns': manual_returns,
        'auto_final': auto_final,
        'manual_final': manual_final,
        'difference_pct': difference
    }

if __name__ == "__main__":
    test_single_asset_execution_timing()
