"""
Test script for paper trading functionality.
"""

import sys
import os
import logging

# Add the src directory to the Python path
sys.path.append(os.path.abspath('.'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import the necessary modules
from src.trading.strategy_integration import StrategyTrader
from src.config_manager import load_config, save_config

def enable_paper_trading():
    """Enable paper trading in the configuration."""
    config = load_config()
    if 'trading' not in config:
        config['trading'] = {}

    config['trading']['enabled'] = True
    config['trading']['mode'] = 'paper'
    config['trading']['initial_capital'] = 10000.0

    save_config(config)
    logging.info("Paper trading enabled in configuration")

def test_paper_trading_reset():
    """Test resetting the paper trading account."""
    # Enable paper trading
    enable_paper_trading()

    # Initialize the strategy trader
    trader = StrategyTrader('binance')

    # Get initial status
    initial_status = trader.get_status()
    logging.info(f"Initial status: {initial_status}")

    # Reset the paper trading account
    trader.executor.reset_paper_trading()

    # Get updated status
    updated_status = trader.get_status()
    logging.info(f"Status after reset: {updated_status}")

    # Verify that the account was reset
    if 'trading_status' in updated_status and 'portfolio_value' in updated_status['trading_status']:
        if updated_status['trading_status']['portfolio_value'] == 10000.0:
            logging.info("✅ Paper trading account reset successful")
        else:
            logging.error("❌ Paper trading account reset failed")
    else:
        logging.error("❌ Could not verify reset - missing portfolio value in status")

def test_manual_execution():
    """Test manual execution of a trade."""
    # Enable paper trading
    enable_paper_trading()

    # Initialize the strategy trader
    trader = StrategyTrader('binance')

    # Get initial status
    initial_status = trader.get_status()
    logging.info(f"Initial status: {initial_status}")

    # Execute a trade
    logging.info("Executing trade for BTC/USDT...")
    result = trader.manual_execute('BTC/USDT', 1)
    logging.info(f"Trade execution result: {result}")

    # Get updated status
    updated_status = trader.get_status()
    logging.info(f"Updated status: {updated_status}")

    # Check if the trade was successful
    if result.get('success', False):
        logging.info("✅ Trade executed successfully!")
    else:
        logging.error(f"❌ Trade execution failed: {result.get('reason', 'Unknown reason')}")

    # Execute another trade with selected assets
    logging.info("Executing trade with selected assets...")
    selected_assets = ['ETH/USDT', 'SOL/USDT', 'LINK/USDT']
    result = trader.manual_execute(None, 1, selected_assets)
    logging.info(f"Trade execution result with selected assets: {result}")

    # Get updated status
    updated_status = trader.get_status()
    logging.info(f"Updated status: {updated_status}")

    # Verify that the selected assets were used
    if result.get('success', False):
        logging.info("✅ Trade execution with selected assets successful")
    else:
        logging.error(f"❌ Trade execution with selected assets failed: {result.get('reason', 'Unknown reason')}")

    return result

def test_mtpi_signal_handling():
    """Test handling of MTPI signals."""
    # Enable paper trading
    enable_paper_trading()

    # Initialize the strategy trader
    trader = StrategyTrader('binance')

    # Execute a trade with bullish MTPI signal
    logging.info("Executing trade with bullish MTPI signal...")
    result = trader.manual_execute('BTC/USDT', 1)
    logging.info(f"Trade execution result with bullish signal: {result}")

    # Get status
    status = trader.get_status()
    logging.info(f"Status after bullish trade: {status}")

    # Execute a trade with bearish MTPI signal
    logging.info("Executing trade with bearish MTPI signal...")
    result = trader.manual_execute('ETH/USDT', -1)
    logging.info(f"Trade execution result with bearish signal: {result}")

    # Get updated status
    updated_status = trader.get_status()
    logging.info(f"Status after bearish trade: {updated_status}")

    # Verify that positions were exited with bearish signal
    if result.get('success', False):
        logging.info("✅ Bearish MTPI signal handling successful")
    else:
        logging.error(f"❌ Bearish MTPI signal handling failed: {result.get('reason', 'Unknown reason')}")

if __name__ == "__main__":
    logging.info("=== Testing Paper Trading Functionality ===")

    # Run tests
    test_paper_trading_reset()
    test_manual_execution()
    test_mtpi_signal_handling()

    logging.info("=== Paper Trading Tests Complete ===")
