#!/usr/bin/env python3
"""
Test script to verify user-specific logging works correctly
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_user_logging():
    """Test the user-specific logging setup"""
    
    print("🔍 Testing User-Specific Logging")
    
    # Test with Liepa's Kraken config
    print("\n=== Testing Liepa's Kraken Configuration ===")
    try:
        from background_service import BackgroundService
        
        # Load environment variables from .env.Liepa
        from dotenv import load_dotenv
        load_dotenv('.env.Liepa', override=True)
        
        # Test with Liepa's Kraken config
        service = BackgroundService(
            config_path='config/settings_kraken_eur.yaml',
            notification_config_path='config/notifications_kraken_liepa.json',
            test_mode=True
        )
        
        # Log some test messages
        logging.info("Test message for <PERSON><PERSON>'s Kraken service")
        logging.warning("Test warning for <PERSON><PERSON>'s Kraken service")
        
        print("✅ <PERSON><PERSON>'s Kraken logging test completed")
        
        # Clean up
        service.stop()
        
    except Exception as e:
        print(f"❌ Error testing Liepa's Kraken logging: {e}")
        import traceback
        traceback.print_exc()
    
    # Test with Liepa's Bitvavo config
    print("\n=== Testing Liepa's Bitvavo Configuration ===")
    try:
        service2 = BackgroundService(
            config_path='config/settings_bitvavo_eur.yaml',
            notification_config_path='config/notifications_bitvavo_liepa.json',
            test_mode=True
        )
        
        # Log some test messages
        logging.info("Test message for Liepa's Bitvavo service")
        logging.warning("Test warning for Liepa's Bitvavo service")
        
        print("✅ Liepa's Bitvavo logging test completed")
        
        # Clean up
        service2.stop()
        
    except Exception as e:
        print(f"❌ Error testing Liepa's Bitvavo logging: {e}")
        import traceback
        traceback.print_exc()
    
    # Check what log files were created
    print("\n=== User-Specific Log Files Created ===")
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.startswith('background_service_')]
        for log_file in sorted(log_files):
            file_path = os.path.join(log_dir, log_file)
            file_size = os.path.getsize(file_path)
            
            # Identify user from filename
            if '_liepa_' in log_file:
                user_info = "👤 LIEPA's"
            else:
                user_info = "👤 YOUR"
                
            print(f"📄 {user_info} {log_file} ({file_size} bytes)")
            
            # Show first few lines of recent logs
            if file_size > 0 and log_file.endswith(f"{datetime.now().strftime('%Y%m%d')}_"):
                try:
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[-3:]  # Last 3 lines
                        for i, line in enumerate(lines, 1):
                            print(f"   {i}: {line.strip()}")
                except Exception as e:
                    print(f"   Error reading file: {e}")
    else:
        print("❌ No logs directory found")

if __name__ == "__main__":
    test_user_logging()
