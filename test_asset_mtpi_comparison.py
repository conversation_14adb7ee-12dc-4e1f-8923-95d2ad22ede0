#!/usr/bin/env python3
"""
Test script to compare MTPI vs PGO vs RSI asset trend detection methods.

This script validates the new MTPI asset trend detection implementation
and compares its performance against existing methods.
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedel<PERSON>

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.strategy import calculate_daily_scores
from src.indicators.ratio_indicators import calculate_daily_pgo_scores, calculate_daily_ratio_scores
from src.indicators.asset_mtpi_indicators import calculate_daily_mtpi_scores

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_asset_mtpi_comparison():
    """
    Test and compare different asset trend detection methods.
    """
    print("=" * 80)
    print("ASSET TREND DETECTION COMPARISON TEST")
    print("=" * 80)
    
    # Load configuration
    config = load_config()
    
    # Test parameters
    assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT']
    timeframe = '1d'
    limit = 100  # Last 100 days
    
    print(f"📊 Testing with assets: {assets}")
    print(f"⏰ Timeframe: {timeframe}")
    print(f"📈 Data points: {limit}")
    
    # Fetch data
    print(f"\n🔄 Fetching data...")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=assets,
            timeframe=timeframe,
            limit=limit
        )

        if data_dict:
            for asset, df in data_dict.items():
                if not df.empty:
                    print(f"✅ {asset}: {len(df)} candles from {df.index[0]} to {df.index[-1]}")
                else:
                    print(f"❌ {asset}: No data received")
        else:
            print("❌ No data received from fetch_ohlcv_data")
            return

    except Exception as e:
        print(f"❌ Error fetching data: {e}")
        return

    if not data_dict:
        print("❌ No data available for testing")
        return
    
    print(f"\n✅ Successfully fetched data for {len(data_dict)} assets")
    
    # Test 1: RSI Method
    print(f"\n" + "="*50)
    print("TEST 1: RSI METHOD")
    print("="*50)
    
    try:
        rsi_scores, rsi_debug = calculate_daily_ratio_scores(
            data_dict=data_dict,
            rsi_length=14,
            sma_length=14,
            close_col='close',
            debug=True
        )
        print(f"✅ RSI Method: {rsi_scores.shape[0]} days, {rsi_scores.shape[1]} assets")
        print(f"📊 Latest RSI scores:")
        if not rsi_scores.empty:
            latest_rsi = rsi_scores.iloc[-1]
            for asset, score in latest_rsi.items():
                print(f"   {asset}: {score:.2f}")
    except Exception as e:
        print(f"❌ RSI Method failed: {e}")
        rsi_scores = pd.DataFrame()
    
    # Test 2: PGO Method
    print(f"\n" + "="*50)
    print("TEST 2: PGO METHOD")
    print("="*50)
    
    try:
        pgo_scores, pgo_debug = calculate_daily_pgo_scores(
            data_dict=data_dict,
            pgo_length=35,
            close_col='close',
            debug=True,
            long_threshold=1.1,
            short_threshold=-0.58
        )
        print(f"✅ PGO Method: {pgo_scores.shape[0]} days, {pgo_scores.shape[1]} assets")
        print(f"📊 Latest PGO scores:")
        if not pgo_scores.empty:
            latest_pgo = pgo_scores.iloc[-1]
            for asset, score in latest_pgo.items():
                print(f"   {asset}: {score:.2f}")
    except Exception as e:
        print(f"❌ PGO Method failed: {e}")
        pgo_scores = pd.DataFrame()
    
    # Test 3: MTPI Method
    print(f"\n" + "="*50)
    print("TEST 3: MTPI METHOD")
    print("="*50)
    
    try:
        # Create MTPI configuration for asset trend detection (using 3 indicators - odd number)
        mtpi_config = {
            'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score'],  # 3 indicators = odd number
            'combination_method': 'consensus',
            'long_threshold': 0.1,
            'short_threshold': -0.1,
            'pgo': {
                'length': 35,
                'upper_threshold': 1.35,
                'lower_threshold': -0.58
            },
            'bollinger_bands': {
                'length': 33,
                'multiplier': 2.0,
                'long_threshold': 76.0,
                'short_threshold': 31.0,
                'use_heikin_ashi': False,
                'heikin_src': 'close'
            },
            'dwma_score': {
                'smoothing_style': 'Weighted SD',
                'src_col': 'close',
                'length': 17,
                'ma_type': 'EMA',
                'ma_smooth_length': 12,
                'sd_length': 33,
                'upper_sd_weight': 1.031,
                'lower_sd_weight': 0.996,
                'atr_period': 12,
                'atr_multiplier': 1.0,
                'loop_start': 1,
                'loop_end': 60,
                'long_threshold': 30,
                'short_threshold': 0
            }
        }
        
        mtpi_scores, mtpi_debug = calculate_daily_mtpi_scores(
            data_dict=data_dict,
            mtpi_config=mtpi_config,
            close_col='close',
            debug=True
        )
        print(f"✅ MTPI Method: {mtpi_scores.shape[0]} days, {mtpi_scores.shape[1]} assets")
        print(f"📊 Latest MTPI scores:")
        if not mtpi_scores.empty:
            latest_mtpi = mtpi_scores.iloc[-1]
            for asset, score in latest_mtpi.items():
                print(f"   {asset}: {score:.2f}")
    except Exception as e:
        print(f"❌ MTPI Method failed: {e}")
        logging.error(f"MTPI Method error details: {e}", exc_info=True)
        mtpi_scores = pd.DataFrame()
    
    # Comparison Analysis
    print(f"\n" + "="*50)
    print("COMPARISON ANALYSIS")
    print("="*50)
    
    methods = []
    if not rsi_scores.empty:
        methods.append(('RSI', rsi_scores))
    if not pgo_scores.empty:
        methods.append(('PGO', pgo_scores))
    if not mtpi_scores.empty:
        methods.append(('MTPI', mtpi_scores))
    
    if len(methods) >= 2:
        print(f"📊 Comparing {len(methods)} methods over the last 10 days:")
        
        # Get common dates for comparison
        common_dates = None
        for method_name, scores in methods:
            if common_dates is None:
                common_dates = scores.index
            else:
                common_dates = common_dates.intersection(scores.index)
        
        if len(common_dates) >= 10:
            recent_dates = sorted(common_dates)[-10:]
            
            print(f"\n📅 Date Range: {recent_dates[0]} to {recent_dates[-1]}")
            print(f"{'Date':<12} {'RSI Best':<12} {'PGO Best':<12} {'MTPI Best':<12}")
            print("-" * 60)
            
            for date in recent_dates:
                row = f"{date.strftime('%Y-%m-%d'):<12}"
                
                for method_name, scores in methods:
                    if date in scores.index:
                        day_scores = scores.loc[date]
                        best_asset = day_scores.idxmax()
                        best_score = day_scores.max()
                        row += f"{best_asset.split('/')[0]:<8}({best_score:.1f})"
                    else:
                        row += f"{'N/A':<12}"
                
                print(row)
        
        # Calculate agreement between methods
        if len(methods) == 3:
            print(f"\n🤝 Method Agreement Analysis:")
            agreement_count = 0
            total_days = 0
            
            for date in common_dates[-30:]:  # Last 30 days
                best_assets = []
                for method_name, scores in methods:
                    if date in scores.index:
                        day_scores = scores.loc[date]
                        best_asset = day_scores.idxmax()
                        best_assets.append(best_asset)
                
                if len(best_assets) == 3:
                    total_days += 1
                    if len(set(best_assets)) == 1:  # All methods agree
                        agreement_count += 1
            
            if total_days > 0:
                agreement_pct = (agreement_count / total_days) * 100
                print(f"   All methods agree: {agreement_count}/{total_days} days ({agreement_pct:.1f}%)")
    
    print(f"\n✅ Asset trend detection comparison test completed!")
    print(f"🎯 All methods are working and can be compared for performance")

if __name__ == "__main__":
    test_asset_mtpi_comparison()
