#!/usr/bin/env python
"""
Test script to directly append data to a CSV file.
This will help verify if the append functionality is working correctly.
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import time

# File path
csv_file = 'data/ohlcv/binance/1m/BTC_USDC.csv'

# Check if the file exists
if not os.path.exists(csv_file):
    print(f"Error: File {csv_file} does not exist")
    exit(1)

# Read the last few rows to see what's already there
print(f"Reading existing data from {csv_file}")
try:
    existing_data = pd.read_csv(csv_file, index_col=0, parse_dates=True)
    print(f"File has {len(existing_data)} rows")
    print("Last 3 rows:")
    print(existing_data.tail(3))

    # Get the last timestamp
    last_timestamp = existing_data.index.max()
    print(f"Last timestamp: {last_timestamp}")

    # Create new data with timestamps after the last one
    new_timestamps = [
        last_timestamp + timedelta(minutes=1),
        last_timestamp + timedelta(minutes=2),
        last_timestamp + timedelta(minutes=3)
    ]

    # Create new data
    new_data = pd.DataFrame({
        'open': [103050.0, 103055.0, 103060.0],
        'high': [103060.0, 103065.0, 103070.0],
        'low': [103040.0, 103045.0, 103050.0],
        'close': [103055.0, 103060.0, 103065.0],
        'volume': [1.5, 2.5, 3.5]
    }, index=new_timestamps)

    print("\nNew data to append:")
    print(new_data)

    # Append to the file
    print(f"\nAppending {len(new_data)} rows to {csv_file}")
    # Append each row individually without extra newlines
    with open(csv_file, 'a') as f:
        for idx, row in new_data.iterrows():
            # Format the row as CSV
            row_str = f"{idx},{row['open']},{row['high']},{row['low']},{row['close']},{row['volume']}\n"
            f.write(row_str)

    # Verify the append
    time.sleep(1)  # Wait a moment for file system to update

    # Read the file again to verify
    updated_data = pd.read_csv(csv_file, index_col=0, parse_dates=True)
    print(f"\nAfter append, file has {len(updated_data)} rows")
    print("Last 5 rows:")
    print(updated_data.tail(5))

    # Check if the new rows were added
    if len(updated_data) > len(existing_data):
        print(f"\nSuccess! Added {len(updated_data) - len(existing_data)} new rows")
    else:
        print("\nError: No new rows were added")

except Exception as e:
    print(f"Error: {e}")
