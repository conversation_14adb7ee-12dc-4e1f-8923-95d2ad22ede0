#!/usr/bin/env python
"""
Example script to demonstrate how to use GeckoTerminal integration to fetch memecoin data.
This script shows how to fetch price data for tokens that are not available on centralized
exchanges like Binance, using the GeckoTerminal API.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime, timedel<PERSON>

# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Try to import the improved GeckoTerminal fetcher first
try:
    from src.geckoterminal_improved import fetch_with_improved_pagination
    GECKOTERMINAL_AVAILABLE = True
    fetch_geckoterminal_data = fetch_with_improved_pagination
    logging.info("Using improved GeckoTerminal fetcher with enhanced pagination")
except ImportError:
    try:
        # Fall back to enhanced implementation
        from src.geckoterminal_enhanced import fetch_token_with_pagination
        GECKOTERMINAL_AVAILABLE = True
        fetch_geckoterminal_data = fetch_token_with_pagination
        logging.info("Using enhanced GeckoTerminal fetcher")
    except ImportError:
        try:
            # Fall back to original implementation
            from src.geckoterminal_fetcher import fetch_geckoterminal_data
            GECKOTERMINAL_AVAILABLE = True
            logging.info("Using original GeckoTerminal fetcher")
        except ImportError:
            GECKOTERMINAL_AVAILABLE = False
            logging.error("GeckoTerminal fetcher not available. Make sure src/geckoterminal_fetcher.py exists.")
            sys.exit(1)

def fetch_memecoin_data():
    """Fetch data for popular memecoins from GeckoTerminal."""
    # Example tokens
    tokens = [
        # Solana memecoins
        {"network": "solana", "token_address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "name": "BONK", "symbol": "BONK/USDT"},  # BONK
        {"network": "solana", "token_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", "name": "SAMO", "symbol": "SAMO/USDT"},  # SAMO
        {"network": "solana", "token_address": "BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump", "name": "AUTISM", "symbol": "AUTISM/USDT"},  # AUTISM

        # Ethereum memecoins
        {"network": "eth", "token_address": "******************************************", "name": "SHIB", "symbol": "SHIB/USDT"},  # SHIB
        {"network": "eth", "token_address": "******************************************", "name": "PEPE", "symbol": "PEPE/USDT"},  # PEPE
    ]

    results = {}

    for token in tokens:
        network = token["network"]
        token_address = token["token_address"]
        name = token["name"]
        symbol = token["symbol"]

        logging.info(f"Fetching data for {name} ({symbol}) on {network}...")

        try:
            # Fetch historical data with pagination
            df = fetch_geckoterminal_data(
                network=network,
                token_address=token_address,
                timeframe="1d",
                max_pages=10,  # Fetch up to 10 pages of data
                page_delay=1.0,
                use_cache=True
            )

            if not df.empty:
                results[name] = df
                logging.info(f"Successfully fetched {len(df)} candles for {name} from {df.index.min().date()} to {df.index.max().date()}")
                logging.info(f"Current price: ${df['close'].iloc[-1]:.8f}")
                logging.info(f"24h volume: ${df['volume'].iloc[-1]:.2f}")
                logging.info("---")
            else:
                logging.error(f"Failed to fetch data for {name}")
        except Exception as e:
            logging.error(f"Error fetching {name}: {e}")

    return results

def run_test_allocation_with_memecoins():
    """Run test_allocation.py with memecoin data."""
    import subprocess

    # Example command to run test_allocation.py with GeckoTerminal tokens
    cmd = [
        "python", "test_allocation.py",
        "--timeframe", "1d",
        "--mtpi-timeframe", "1d",
        "--analysis-start-date", "2024-01-01",
        "--n-assets", "3",
        "--assets", "BTC/USDT", "ETH/USDT", "SOL/USDT",
        "--geckoterminal-tokens",
        "solana:DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263:BONK/USDT",
        "solana:7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU:SAMO/USDT",
        "--include-autism"
    ]

    logging.info(f"Running command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logging.info("Command completed successfully")
        logging.info(result.stdout)
    except subprocess.CalledProcessError as e:
        logging.error(f"Command failed with exit code {e.returncode}")
        logging.error(e.stderr)

if __name__ == "__main__":
    # Fetch memecoin data directly
    memecoin_data = fetch_memecoin_data()

    # Print the results
    for name, df in memecoin_data.items():
        print(f"\n{name} OHLCV Data:")
        print(df)

    # Run test_allocation.py with memecoins
    print("\nRunning test_allocation.py with memecoins...")
    run_test_allocation_with_memecoins()
