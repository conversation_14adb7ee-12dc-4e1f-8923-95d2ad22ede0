#!/usr/bin/env python3
"""
Test script for Median Score indicator - MTPI Component #3

Tests the Median Supertrend implementation with TradingView parameters:
- Supertrend Length: 10
- Multiplier: 1.55  
- Median Length: 7
- Source: close
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import (
    generate_median_score_signal,
    calculate_median_score,
    calculate_median_smoothing,
    calculate_median_supertrend,
    DEFAULT_MEDIAN_SCORE_CONFIG
)

def create_sample_data(length: int = 100) -> pd.DataFrame:
    """Create sample OHLCV data for testing."""
    np.random.seed(42)
    dates = pd.date_range(start='2024-06-01', periods=length, freq='D')
    
    # Generate realistic price data
    base_price = 50000
    returns = np.random.normal(0, 0.02, length)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, length))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, length))
    df['volume'] = np.random.uniform(1000, 10000, length)
    
    return df

def test_median_score_basic_functionality():
    """Test basic functionality of all Median Score components."""
    print("\nTesting Median Score basic functionality...")
    
    df = create_sample_data(100)
    
    try:
        # Test median smoothing
        median_smooth = calculate_median_smoothing(df, length=7)
        assert len(median_smooth) == len(df), "Median smoothing length mismatch"
        assert not median_smooth.isna().all(), "All median smoothing values are NaN"
        print("  ✅ Median smoothing working correctly")
        
        # Test median supertrend
        supertrend_line, direction = calculate_median_supertrend(df)
        assert len(supertrend_line) == len(df), "Supertrend line length mismatch"
        assert len(direction) == len(df), "Direction length mismatch"
        assert set(direction.dropna().unique()).issubset({-1, 1}), "Invalid direction values"
        print("  ✅ Median Supertrend calculation working correctly")
        
        # Test signal generation
        signal = generate_median_score_signal(df)
        assert len(signal) == len(df), "Signal length mismatch"
        assert signal.dtype in [int, 'int64'], "Signal should be integer type"
        assert set(signal.unique()).issubset({-1, 0, 1}), "Invalid signal values"
        print("  ✅ Signal generation working correctly")
        
        # Test complete calculation
        signal2, st_line, direction2 = calculate_median_score(df)
        assert len(signal2) == len(df), "Complete calculation signal length mismatch"
        print("  ✅ Complete Median Score calculation working correctly")
        
        # Show statistics
        signal_counts = signal.value_counts().sort_index()
        print(f"  Signal distribution: {dict(signal_counts)}")
        print(f"  Non-zero signals: {(signal != 0).sum()}/{len(signal)}")
        print(f"  NaN values: {signal.isna().sum()}")
        
    except Exception as e:
        print(f"  ❌ Error in basic functionality test: {e}")

def test_median_score_parameters():
    """Test Median Score with different parameter combinations."""
    print("\nTesting Median Score with different parameters...")
    
    df = create_sample_data(100)
    
    # Test different parameter combinations
    test_configs = [
        {"atr_period": 5, "multiplier": 1.0, "median_length": 5},
        {"atr_period": 15, "multiplier": 2.0, "median_length": 10},
        {"atr_period": 20, "multiplier": 3.0, "median_length": 14},
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\nTest config {i+1}: {config}")
        
        try:
            signal = generate_median_score_signal(df=df, **config)
            
            # Basic validation
            assert len(signal) == len(df), "Signal length mismatch"
            assert signal.dtype in [int, 'int64'], "Signal should be integer type"
            assert set(signal.unique()).issubset({-1, 0, 1}), "Invalid signal values"
            
            print(f"  ✅ Config {i+1} passed validation")
            
        except Exception as e:
            print(f"  ❌ Config {i+1} failed: {e}")

def test_median_score_with_real_data(start_date: str = '2024-06-19'):
    """Test Median Score with real BTC data using TradingView parameters."""
    print(f"\nTesting Median Score with real BTC data from {start_date}...")
    
    try:
        # Fetch real BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=start_date
        )
        
        if 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return None, None, None
        
        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} BTC candles (range: {btc_df.index[0].date()} to {btc_df.index[-1].date()})")
        
        # TradingView parameters
        tv_params = DEFAULT_MEDIAN_SCORE_CONFIG.copy()
        print(f"Using TradingView parameters: {tv_params}")
        
        # Test Median Score with TradingView parameters
        signal, supertrend_line, direction = calculate_median_score(df=btc_df, **tv_params)
        
        # Analyze results
        signal_changes = (signal != signal.shift(1)).sum()
        latest_signal = signal.iloc[-1]
        
        print(f"Signal changes: {signal_changes}")
        print(f"Latest signal: {latest_signal}")
        print(f"Signal distribution: {dict(signal.value_counts())}")
        
        # Show recent signals
        print("\nRecent signals (last 15):")
        recent_data = pd.DataFrame({
            'Date': btc_df.index[-15:].strftime('%Y-%m-%d'),
            'Close': btc_df['close'].iloc[-15:].round(2),
            'Signal': signal.iloc[-15:]
        })
        print(recent_data.to_string(index=False))
        
        print("✅ Real data test completed successfully")
        return btc_df, signal, tv_params
        
    except Exception as e:
        print(f"❌ Error with real data test: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def visualize_median_score_signals(btc_df, signal, tv_params, save_plot: bool = True):
    """Create visualization of Median Score signals."""
    print("\nGenerating Median Score visualization...")

    try:
        # Calculate components for visualization
        signal_full, supertrend_line, direction = calculate_median_score(df=btc_df, **tv_params)
        median_smooth = calculate_median_smoothing(btc_df, tv_params['median_length'])

        # Create visualization
        plt.style.use('dark_background')
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 12), sharex=True,
                                          gridspec_kw={'height_ratios': [3, 1, 1]})

        # Main price chart with supertrend
        ax1.plot(range(len(btc_df)), btc_df['close'], color='white', linewidth=1, label='BTC Close', alpha=0.8)
        ax1.plot(range(len(btc_df)), median_smooth, color='orange', linewidth=1.5, label='Median Smooth (7)', alpha=0.9)
        ax1.plot(range(len(btc_df)), supertrend_line, color='cyan', linewidth=2, label='Median Supertrend', alpha=0.9)

        # Add signal markers
        long_signals = signal == 1
        short_signals = signal == -1

        if long_signals.any():
            long_indices = np.where(long_signals)[0]
            ax1.scatter(long_indices, btc_df['close'].iloc[long_indices],
                       color='lime', marker='^', s=80, label='Long Signal',
                       edgecolors='white', linewidth=1, zorder=5)
            
        if short_signals.any():
            short_indices = np.where(short_signals)[0]
            ax1.scatter(short_indices, btc_df['close'].iloc[short_indices],
                       color='red', marker='v', s=80, label='Short Signal',
                       edgecolors='white', linewidth=1, zorder=5)

        ax1.set_title('BTC/USDT - Median Score Indicator (TradingView Style)', 
                     color='white', fontsize=14, fontweight='bold')
        ax1.legend(loc='upper left', framealpha=0.8)
        ax1.grid(True, alpha=0.2, color='gray')
        ax1.set_ylabel('Price (USDT)', color='white')

        # Direction panel
        direction_colors = ['red' if d == -1 else 'lime' if d == 1 else 'gray' for d in direction]
        ax2.bar(range(len(direction)), direction, color=direction_colors, alpha=0.8, width=0.8)
        ax2.axhline(y=0, color='white', linestyle='-', alpha=0.5, linewidth=1)
        ax2.set_ylim(-1.5, 1.5)
        ax2.set_ylabel('Direction', color='white')
        ax2.grid(True, alpha=0.2, color='gray')

        # Signal panel
        signal_colors = ['red' if s == -1 else 'lime' if s == 1 else 'gray' for s in signal]
        ax3.bar(range(len(signal)), signal, color=signal_colors, alpha=0.8, width=0.8)
        ax3.axhline(y=0, color='white', linestyle='-', alpha=0.5, linewidth=1)
        ax3.set_ylim(-1.5, 1.5)
        ax3.set_ylabel('Signal', color='white')
        ax3.set_xlabel('Days', color='white')
        ax3.grid(True, alpha=0.2, color='gray')

        # Add parameter info
        param_text = f"Median Supertrend | ATR: {tv_params['atr_period']} | Mult: {tv_params['multiplier']} | Median: {tv_params['median_length']}"
        ax1.text(0.02, 0.98, param_text, transform=ax1.transAxes,
                color='#cccccc', fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

        # Format x-axis with dates
        date_labels = [btc_df.index[i].strftime('%m/%d') for i in range(0, len(btc_df), max(1, len(btc_df)//10))]
        date_positions = list(range(0, len(btc_df), max(1, len(btc_df)//10)))
        ax3.set_xticks(date_positions)
        ax3.set_xticklabels(date_labels, rotation=45, color='white')

        plt.tight_layout()

        if save_plot:
            plt.savefig('median_score_visualization.png', dpi=300, bbox_inches='tight',
                       facecolor='#1e1e1e', edgecolor='none')
            print("Median Score plot saved as 'median_score_visualization.png'")

        plt.show()
        print("✅ Median Score visualization completed")

    except Exception as e:
        print(f"❌ Median Score visualization failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all Median Score tests."""
    print("=" * 70)
    print("Median Score Indicator Test Suite - TradingView Compatible")
    print("=" * 70)
    
    # Run basic tests
    test_median_score_basic_functionality()
    test_median_score_parameters()
    
    # Test with real BTC data using TradingView parameters
    btc_df, signal, tv_params = test_median_score_with_real_data()
    
    # Create visualization if data is available
    if btc_df is not None and signal is not None:
        try:
            visualize_median_score_signals(btc_df, signal, tv_params, save_plot=True)
        except Exception as e:
            print(f"Skipping visualization: {e}")
    
    print("\n" + "=" * 70)
    print("Median Score Test Suite Completed - Ready for MTPI Integration")
    print("=" * 70)

if __name__ == "__main__":
    main()
