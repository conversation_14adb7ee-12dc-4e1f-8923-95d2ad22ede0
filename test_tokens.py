#!/usr/bin/env python3
"""
Test script to check which GeckoTerminal tokens are available
"""

import requests
import time

def test_token_exists(symbol, network, address):
    """Test if a token exists on GeckoTerminal"""
    url = f'https://api.geckoterminal.com/api/v2/networks/{network}/tokens/{address}/pools'
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            pools = data.get('data', [])
            print(f'✅ {symbol}: Found {len(pools)} pools')
            return True
        elif response.status_code == 404:
            print(f'❌ {symbol}: 404 Not Found')
            return False
        else:
            print(f'⚠️ {symbol}: HTTP {response.status_code}')
            return False
    except Exception as e:
        print(f'💥 {symbol}: Error - {e}')
        return False

def main():
    tokens_to_test = [
        ('AUTISM', 'solana', 'BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump'),
        ('FWOG', 'solana', 'A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump'),
        ('TOSHI', 'base', '******************************************'),
        ('ALTURA', 'bsc', '******************************************'),
        ('SIGMA', 'solana', '5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUIbpump'),
        ('SPX', 'solana', 'J3NKxoXZcnNiMjKw9hYb2K4LUxgwB6t1fPtQvSv3KFr'),
        ('SKI', 'eth', '******************************************'),
        ('CHILLGUY', 'solana', 'D6fyfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump'),
        ('COCORO', 'eth', '******************************************'),
    ]

    print('Testing GeckoTerminal token availability:')
    print('=' * 50)

    working_tokens = []
    failed_tokens = []

    for symbol, network, address in tokens_to_test:
        if test_token_exists(symbol, network, address):
            working_tokens.append((symbol, network, address))
        else:
            failed_tokens.append((symbol, network, address))
        time.sleep(1)  # Rate limiting

    print('\n' + '=' * 50)
    print(f'Summary: {len(working_tokens)} working, {len(failed_tokens)} failed')
    
    if working_tokens:
        print('\nWorking tokens:')
        for symbol, network, address in working_tokens:
            print(f'  {symbol} ({network})')
    
    if failed_tokens:
        print('\nFailed tokens:')
        for symbol, network, address in failed_tokens:
            print(f'  {symbol} ({network}): {address}')

if __name__ == '__main__':
    main()
