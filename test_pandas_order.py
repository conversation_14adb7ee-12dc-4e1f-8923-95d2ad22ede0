#!/usr/bin/env python3

import pandas as pd
import numpy as np

# Test pandas DataFrame column ordering and Series.to_dict() behavior
print("Testing pandas DataFrame column ordering...")

# Create assets list in specific order (BTC before TRX)
assets = ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
print(f"Original assets order: {assets}")

# Create a DataFrame with these columns
df = pd.DataFrame(index=[pd.Timestamp('2025-06-25')], columns=assets)

# Fill with scores (BTC and TRX both get 12)
scores = [8.0, 12.0, 6.0, 2.0, 9.0, 9.0, 3.0, 3.0, 6.0, 12.0, 0.0, 3.0, 11.0, 1.0]
df.loc[pd.Timestamp('2025-06-25')] = scores

print(f"DataFrame columns order: {list(df.columns)}")
print(f"DataFrame values: {df.iloc[0].to_dict()}")

# Extract a row as Series
row_series = df.loc[pd.Timestamp('2025-06-25')]
print(f"Series index order: {list(row_series.index)}")

# Convert to dictionary
row_dict = row_series.to_dict()
print(f"Dictionary keys order: {list(row_dict.keys())}")

# Test tie-breaking
max_score = max(row_dict.values())
tied_assets = [asset for asset, score in row_dict.items() if score == max_score]
print(f"Max score: {max_score}")
print(f"Tied assets: {tied_assets}")
print(f"Winner (first in dict order): {tied_assets[0]}")

# Check positions
btc_pos = list(row_dict.keys()).index('BTC/EUR')
trx_pos = list(row_dict.keys()).index('TRX/EUR')
print(f"BTC position: {btc_pos}, TRX position: {trx_pos}")
print(f"BTC comes before TRX: {btc_pos < trx_pos}")
