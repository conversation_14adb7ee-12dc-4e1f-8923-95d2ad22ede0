#!/usr/bin/env python3
"""
Debug script to verify that execution timing produces different returns.
"""

import sys
import os
import pandas as pd
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_program import AllocationTester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_returns_calculation():
    """Debug the returns calculation for both execution timings."""
    
    print("=" * 80)
    print("DEBUGGING EXECUTION TIMING RETURNS CALCULATION")
    print("=" * 80)
    
    # Common parameters
    common_params = {
        'timeframe': '1d',
        'mtpi_timeframe': '1d', 
        'analysis_start_date': '2024-06-01',
        'n_assets': 1,  # Use just 1 asset to simplify debugging
        'transaction_fee_rate': 0.001,
        'selected_assets': ['BTC/USDT'],
        'use_cache': True,
        'initial_capital': 10000,
        'wait_for_confirmed_signals': True,
        'use_mtpi': False,  # Disable MTPI to simplify
        'use_weighted_allocation': False,
        'weights': None,
        'enable_rebalancing': False,
        'rebalance_threshold': 0.05
    }
    
    # Test 1: Automatic execution
    print("\n1. Testing AUTOMATIC execution (candle_close)...")
    tester_auto = AllocationTester(**common_params, execution_timing='candle_close')
    tester_auto.fetch_data()
    
    # Get the data for BTC/USDT
    btc_data = tester_auto.data_dict['BTC/USDT']
    print(f"   BTC data shape: {btc_data.shape}")
    print(f"   Date range: {btc_data.index.min()} to {btc_data.index.max()}")
    
    # Calculate automatic returns (close to close)
    auto_returns = btc_data['close'].pct_change().fillna(0.0)
    print(f"   Auto returns (first 5): {auto_returns.head().values}")
    
    # Test 2: Manual execution
    print("\n2. Testing MANUAL execution (manual_12pm)...")
    tester_manual = AllocationTester(**common_params, execution_timing='manual_12pm')
    tester_manual.fetch_data()
    
    # Calculate manual returns
    manual_returns = tester_manual._calculate_manual_execution_returns('BTC/USDT', btc_data)
    print(f"   Manual returns (first 5): {manual_returns.head().values}")
    
    # Compare the returns
    print("\n3. Comparing returns...")
    comparison_df = pd.DataFrame({
        'date': btc_data.index,
        'close': btc_data['close'],
        'auto_return': auto_returns,
        'manual_return': manual_returns,
        'difference': manual_returns - auto_returns
    })
    
    # Show first 10 days
    print("\nFirst 10 days comparison:")
    print(comparison_df.head(10).to_string(index=False))
    
    # Calculate summary statistics
    non_zero_diffs = comparison_df['difference'][comparison_df['difference'] != 0]
    print(f"\nSummary:")
    print(f"   Total days: {len(comparison_df)}")
    print(f"   Days with different returns: {len(non_zero_diffs)}")
    print(f"   Max difference: {comparison_df['difference'].max():.6f}")
    print(f"   Min difference: {comparison_df['difference'].min():.6f}")
    print(f"   Mean absolute difference: {comparison_df['difference'].abs().mean():.6f}")
    
    # Test if returns are actually different
    returns_are_different = not auto_returns.equals(manual_returns)
    print(f"   Returns are different: {returns_are_different}")
    
    if returns_are_different:
        print("   ✅ SUCCESS: Manual and automatic execution produce different returns!")
        
        # Show some examples of differences
        print("\nExamples of differences:")
        diff_examples = comparison_df[comparison_df['difference'].abs() > 0.001].head(5)
        for _, row in diff_examples.iterrows():
            print(f"   {row['date'].date()}: Auto={row['auto_return']:.4f}, Manual={row['manual_return']:.4f}, Diff={row['difference']:.4f}")
    else:
        print("   ❌ PROBLEM: Manual and automatic execution produce identical returns!")
    
    return returns_are_different

if __name__ == "__main__":
    debug_returns_calculation()
