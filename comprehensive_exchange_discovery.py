#!/usr/bin/env python3
"""
Comprehensive Exchange Asset Discovery

This script fetches ALL available ASSET/USDT, ASSET/USDC, ASSET/USD, ASSET/EUR pairs
from multiple exchanges and creates a comprehensive comparison Excel file.

Supported exchanges: OKX, Bybit, Kraken, Bitvavo
"""

import ccxt
import pandas as pd
import time
import logging
from typing import Dict, List, Set, Optional
import argparse
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveExchangeDiscovery:
    def __init__(self, min_age_days: int = 35):
        self.min_age_days = min_age_days
        self.exchanges = {
            'okx': ccxt.okx({'sandbox': False, 'enableRateLimit': True}),
            'bybit': ccxt.bybit({'sandbox': False, 'enableRateLimit': True}),
            'kraken': ccxt.kraken({'sandbox': False, 'enableRateLimit': True}),
            'bitvavo': ccxt.bitvavo({'sandbox': False, 'enableRateLimit': True})
        }
        
        # Target quote currencies
        self.target_quotes = ['USDT', 'USDC', 'USD', 'EUR']
        
        # Store all discovered pairs
        self.all_pairs = {}  # {exchange_name: {quote: [base_assets]}}
        self.pair_details = []  # Final results

    def get_all_pairs_for_exchange(self, exchange_name: str, exchange_obj) -> Dict[str, List[str]]:
        """
        Get all trading pairs for target quote currencies from an exchange.
        
        Returns:
            Dict with quote currencies as keys and lists of base assets as values
        """
        try:
            logger.info(f"Loading all markets for {exchange_name}...")
            markets = exchange_obj.load_markets()
            logger.info(f"Found {len(markets)} total markets on {exchange_name}")
            
            pairs_by_quote = {quote: [] for quote in self.target_quotes}
            
            for symbol, market_info in markets.items():
                try:
                    # Parse the symbol to get base and quote
                    if '/' in symbol:
                        base, quote = symbol.split('/', 1)
                    else:
                        # Handle symbols without '/' separator
                        continue
                    
                    # Clean up the quote currency (remove any suffixes)
                    quote_clean = quote.split(':')[0]  # Remove derivative suffixes like :USDT
                    
                    # Check if this is one of our target quote currencies
                    if quote_clean in self.target_quotes:
                        # Skip if it's a derivative or future (basic filtering)
                        if market_info.get('type') == 'spot' or market_info.get('spot', True):
                            pairs_by_quote[quote_clean].append(base)
                            
                except Exception as e:
                    logger.debug(f"Error parsing symbol {symbol}: {e}")
                    continue
            
            # Log summary
            for quote, bases in pairs_by_quote.items():
                if bases:
                    logger.info(f"{exchange_name} - {quote}: {len(bases)} pairs found")
            
            return pairs_by_quote
            
        except Exception as e:
            logger.error(f"Failed to get pairs for {exchange_name}: {str(e)}")
            return {quote: [] for quote in self.target_quotes}

    def check_data_availability(
        self,
        exchange: ccxt.Exchange,
        symbol: str,
        min_days: int = None,
        timeframe: str = '1d'
    ) -> tuple:
        """
        Check if a trading pair has sufficient historical data.
        
        Returns:
            Tuple of (has_enough_data, days_available)
        """
        if min_days is None:
            min_days = self.min_age_days
            
        try:
            # Calculate the start timestamp
            now = datetime.now()
            since = int((now - timedelta(days=min_days)).timestamp() * 1000)

            # Fetch the OHLCV data
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=min_days + 5)

            # Check if we have enough data
            days_available = len(ohlcv)
            has_enough_data = days_available >= min_days

            return has_enough_data, days_available
        except Exception as e:
            logger.debug(f"Could not fetch history for {symbol}: {e}")
            return False, 0

    def discover_all_pairs(self):
        """Discover all pairs across all exchanges."""
        logger.info("Starting comprehensive pair discovery across all exchanges...")
        
        # Get all pairs from each exchange
        for exchange_name, exchange_obj in self.exchanges.items():
            self.all_pairs[exchange_name] = self.get_all_pairs_for_exchange(exchange_name, exchange_obj)
            time.sleep(2)  # Rate limiting between exchanges
        
        # Create a master list of all unique base assets across all exchanges and quotes
        all_base_assets = set()
        for exchange_pairs in self.all_pairs.values():
            for quote_pairs in exchange_pairs.values():
                all_base_assets.update(quote_pairs)
        
        logger.info(f"Found {len(all_base_assets)} unique base assets across all exchanges")
        
        # Now check each combination of base asset + quote currency across all exchanges
        total_combinations = len(all_base_assets) * len(self.target_quotes)
        current_check = 0
        
        for base_asset in sorted(all_base_assets):
            for quote in self.target_quotes:
                current_check += 1
                if current_check % 100 == 0:
                    logger.info(f"Progress: {current_check}/{total_combinations} combinations checked")
                
                pair_data = {
                    'Base_Asset': base_asset,
                    'Quote_Currency': quote,
                    'Pair': f"{base_asset}/{quote}"
                }
                
                # Check availability on each exchange
                for exchange_name, exchange_obj in self.exchanges.items():
                    exchange_pairs = self.all_pairs[exchange_name]
                    
                    # Check if this base asset exists with this quote on this exchange
                    is_available = base_asset in exchange_pairs.get(quote, [])
                    pair_data[f"{exchange_name.upper()}_available"] = is_available
                    
                    if is_available:
                        # Check trading history
                        symbol = f"{base_asset}/{quote}"
                        has_enough_data, days_available = self.check_data_availability(
                            exchange_obj, symbol, self.min_age_days
                        )
                        
                        pair_data[f"{exchange_name.upper()}_has_enough_data"] = has_enough_data
                        pair_data[f"{exchange_name.upper()}_days_available"] = days_available
                        
                        if has_enough_data:
                            logger.debug(f"{symbol} on {exchange_name.upper()}: ✓ {days_available} days")
                        else:
                            logger.debug(f"{symbol} on {exchange_name.upper()}: ⚠ Only {days_available} days")
                    else:
                        pair_data[f"{exchange_name.upper()}_has_enough_data"] = False
                        pair_data[f"{exchange_name.upper()}_days_available"] = 0
                
                self.pair_details.append(pair_data)
        
        logger.info(f"Completed discovery of {len(self.pair_details)} pair combinations")

    def save_to_excel(self, output_file: str = "comprehensive_exchange_pairs.xlsx"):
        """Save results to Excel file with multiple sheets."""
        if not self.pair_details:
            logger.warning("No data to save")
            return
        
        df = pd.DataFrame(self.pair_details)
        
        # Add summary columns
        exchange_cols = [col for col in df.columns if col.endswith('_available')]
        exchange_data_cols = [col for col in df.columns if col.endswith('_has_enough_data')]
        
        df['Total_Exchanges_Available'] = df[exchange_cols].sum(axis=1)
        df['Total_Exchanges_With_Enough_Data'] = df[exchange_data_cols].sum(axis=1)
        
        df['Available_On'] = df.apply(
            lambda row: ', '.join([col.replace('_available', '') for col in exchange_cols if row[col]]),
            axis=1
        )
        
        df['Available_With_Enough_Data_On'] = df.apply(
            lambda row: ', '.join([col.replace('_has_enough_data', '') for col in exchange_data_cols if row[col]]),
            axis=1
        )
        
        # Sort by total availability and base asset name
        df = df.sort_values(['Total_Exchanges_With_Enough_Data', 'Base_Asset'], ascending=[False, True])
        
        # Create Excel file with multiple sheets
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Main comprehensive sheet
            df.to_excel(writer, sheet_name='All_Pairs_Comprehensive', index=False)
            
            # Create separate sheets for each quote currency
            for quote in self.target_quotes:
                quote_df = df[df['Quote_Currency'] == quote].copy()
                if not quote_df.empty:
                    quote_df.to_excel(writer, sheet_name=f'{quote}_Pairs', index=False)
            
            # Create a summary sheet
            self.create_summary_sheet(writer, df)
        
        logger.info(f"Results saved to {output_file}")
        self.print_summary(df)

    def create_summary_sheet(self, writer, df):
        """Create a summary statistics sheet."""
        summary_data = []
        
        # Overall statistics
        total_pairs = len(df)
        summary_data.append(['Total Pair Combinations Checked', total_pairs])
        summary_data.append(['', ''])
        
        # Exchange statistics
        for exchange in ['OKX', 'BYBIT', 'KRAKEN', 'BITVAVO']:
            available_col = f"{exchange}_available"
            data_col = f"{exchange}_has_enough_data"
            
            if available_col in df.columns:
                available_count = df[available_col].sum()
                data_count = df[data_col].sum()
                
                summary_data.append([f'{exchange} - Pairs Available', available_count])
                summary_data.append([f'{exchange} - Pairs with {self.min_age_days}+ days data', data_count])
        
        summary_data.append(['', ''])
        
        # Quote currency statistics
        for quote in self.target_quotes:
            quote_df = df[df['Quote_Currency'] == quote]
            available_count = quote_df['Total_Exchanges_Available'].sum()
            data_count = quote_df['Total_Exchanges_With_Enough_Data'].sum()
            
            summary_data.append([f'{quote} - Total Availability', available_count])
            summary_data.append([f'{quote} - Total with Enough Data', data_count])
        
        summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

    def print_summary(self, df):
        """Print summary statistics to console."""
        logger.info("\n" + "="*80)
        logger.info("COMPREHENSIVE EXCHANGE DISCOVERY SUMMARY")
        logger.info("="*80)
        
        total_pairs = len(df)
        logger.info(f"Total pair combinations analyzed: {total_pairs}")
        
        # Exchange breakdown
        for exchange in ['OKX', 'BYBIT', 'KRAKEN', 'BITVAVO']:
            available_col = f"{exchange}_available"
            data_col = f"{exchange}_has_enough_data"
            
            if available_col in df.columns:
                available_count = df[available_col].sum()
                data_count = df[data_col].sum()
                available_pct = (available_count / total_pairs) * 100
                data_pct = (data_count / total_pairs) * 100
                
                logger.info(f"{exchange}: {available_count} pairs ({available_pct:.1f}%), "
                           f"{data_count} with {self.min_age_days}+ days ({data_pct:.1f}%)")
        
        # Quote currency breakdown
        logger.info(f"\nBreakdown by quote currency:")
        for quote in self.target_quotes:
            quote_df = df[df['Quote_Currency'] == quote]
            pairs_count = len(quote_df)
            available_count = quote_df['Total_Exchanges_Available'].sum()
            data_count = quote_df['Total_Exchanges_With_Enough_Data'].sum()
            
            logger.info(f"{quote}: {pairs_count} possible pairs, {available_count} available, "
                       f"{data_count} with sufficient data")

def main():
    parser = argparse.ArgumentParser(description='Comprehensive cryptocurrency pair discovery across exchanges')
    parser.add_argument('--output', '-o', default='comprehensive_exchange_pairs.xlsx',
                        help='Output Excel file name (default: comprehensive_exchange_pairs.xlsx)')
    parser.add_argument('--exchanges', nargs='+', choices=['okx', 'bybit', 'kraken', 'bitvavo'],
                        help='Specific exchanges to check (default: all)')
    parser.add_argument('--quotes', nargs='+', choices=['USDT', 'USDC', 'USD', 'EUR'],
                        help='Specific quote currencies to check (default: all)')
    parser.add_argument('--min-age-days', type=int, default=35,
                        help='Minimum days of trading history required (default: 35)')
    
    args = parser.parse_args()
    
    discoverer = ComprehensiveExchangeDiscovery(min_age_days=args.min_age_days)
    
    # Override exchanges if specified
    if args.exchanges:
        discoverer.exchanges = {name: discoverer.exchanges[name] for name in args.exchanges if name in discoverer.exchanges}
        logger.info(f"Checking specific exchanges: {list(discoverer.exchanges.keys())}")
    
    # Override quote currencies if specified
    if args.quotes:
        discoverer.target_quotes = args.quotes
        logger.info(f"Checking specific quote currencies: {discoverer.target_quotes}")
    
    try:
        discoverer.discover_all_pairs()
        discoverer.save_to_excel(args.output)
        logger.info("Comprehensive exchange discovery completed successfully!")
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        raise

if __name__ == "__main__":
    main()
