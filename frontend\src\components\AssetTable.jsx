import React from 'react'
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Box,
  Text,
  Badge,
  Flex,
  Tooltip,
  Icon,
  Button,
} from '@chakra-ui/react'
import { InfoIcon, TriangleUpIcon, TriangleDownIcon, RepeatIcon } from '@chakra-ui/icons'

const AssetTable = ({ assets, scores, bestAsset }) => {
  // Add debugging logs to understand the structure of the scores object
  console.log('AssetTable received scores:', scores);
  console.log('AssetTable received scores type:', typeof scores);
  console.log('AssetTable received bestAsset:', bestAsset);
  // Filter assets to only include those that have scores
  // This ensures we only show selected assets when a strategy is run with specific assets
  const filteredAssets = assets.filter(asset =>
    scores && Object.keys(scores).includes(asset.symbol)
  );

  console.log('Filtered assets to display:', filteredAssets.map(a => a.symbol));
  console.log('Available scores:', scores ? Object.keys(scores) : 'No scores');

  return (
    <Box
      bg="white"
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      overflow="hidden"
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="bold">
          Asset Comparison
        </Text>
        <Flex align="center">
          {bestAsset && (
            <Flex align="center" mr={4}>
              <Text mr={2}>Best Asset:</Text>
              <Badge colorScheme="green" fontSize="md" px={2} py={1}>
                {bestAsset}
              </Badge>
            </Flex>
          )}
          <Button
            size="sm"
            colorScheme="blue"
            leftIcon={<RepeatIcon />}
            onClick={() => {
              // Force a refresh of the data by calling the refresh API endpoint
              fetch('/api/refresh', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                  console.log('Refresh response:', data);
                  // Reload the page to see the updated data
                  window.location.reload();
                })
                .catch(error => console.error('Error refreshing data:', error));
            }}
          >
            Refresh Scores
          </Button>
        </Flex>
      </Flex>

      <TableContainer>
        <Table variant="simple" size="sm">
          <Thead>
            <Tr>
              <Th>Asset</Th>
              <Th isNumeric>
                <Flex align="center" justify="flex-end">
                  Score
                  <Tooltip label="Calculated based on pairwise trend analysis">
                    <Icon as={InfoIcon} ml={1} boxSize={3} />
                  </Tooltip>
                </Flex>
              </Th>
              <Th isNumeric>Price</Th>
              <Th isNumeric>24h Change</Th>
              <Th isNumeric>7d Change</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredAssets.map((asset) => {
              const score = scores?.[asset.symbol] || 0
              const is24hPositive = asset.change24h > 0
              const is7dPositive = asset.change7d > 0
              const isBest = asset.symbol === bestAsset

              return (
                <Tr
                  key={asset.symbol}
                  bg={isBest ? 'green.50' : undefined}
                >
                  <Td fontWeight={isBest ? 'bold' : 'normal'}>
                    {asset.symbol}
                  </Td>
                  <Td isNumeric fontWeight="semibold">
                    {score.toFixed(2)}
                  </Td>
                  <Td isNumeric>${asset.price.toFixed(2)}</Td>
                  <Td isNumeric>
                    <Flex align="center" justify="flex-end">
                      <Text
                        color={is24hPositive ? 'green.500' : 'red.500'}
                        fontWeight="medium"
                      >
                        {is24hPositive ? '+' : ''}
                        {asset.change24h.toFixed(2)}%
                      </Text>
                      <Icon
                        as={is24hPositive ? TriangleUpIcon : TriangleDownIcon}
                        color={is24hPositive ? 'green.500' : 'red.500'}
                        ml={1}
                      />
                    </Flex>
                  </Td>
                  <Td isNumeric>
                    <Flex align="center" justify="flex-end">
                      <Text
                        color={is7dPositive ? 'green.500' : 'red.500'}
                        fontWeight="medium"
                      >
                        {is7dPositive ? '+' : ''}
                        {asset.change7d.toFixed(2)}%
                      </Text>
                      <Icon
                        as={is7dPositive ? TriangleUpIcon : TriangleDownIcon}
                        color={is7dPositive ? 'green.500' : 'red.500'}
                        ml={1}
                      />
                    </Flex>
                  </Td>
                </Tr>
              )
            })}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  )
}

export default AssetTable
