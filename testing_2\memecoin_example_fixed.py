#!/usr/bin/env python
"""
Example script to demonstrate how to use GeckoTerminal integration to fetch historical memecoin data.
This script shows how to fetch price data for tokens that are not available on centralized
exchanges like Binance, using the GeckoTerminal API.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Import GeckoTerminal fetcher
try:
    from src.geckoterminal_fetcher import fetch_geckoterminal_data, search_pools
    GECKOTERMINAL_AVAILABLE = True
except ImportError:
    GECKOTERMINAL_AVAILABLE = False
    logging.error("GeckoTerminal fetcher not available. Make sure src/geckoterminal_fetcher.py exists.")
    sys.exit(1)

def fetch_memecoin_data():
    """Fetch historical data for popular memecoins from GeckoTerminal."""
    # Example tokens with correct addresses
    tokens = [
        # Solana memecoins
        {"network": "solana", "token_address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "name": "BONK"},  # BONK
        {"network": "solana", "token_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", "name": "SAMO"},  # SAMO
        {"network": "solana", "token_address": "BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump", "name": "AUTISM"},  # AUTISM

        # Ethereum memecoins - using correct addresses
        {"network": "eth", "token_address": "******************************************", "name": "SHIB"},  # SHIB - lowercase address
        {"network": "eth", "token_address": "******************************************", "name": "PEPE"},  # PEPE - correct address
    ]

    results = {}

    for token in tokens:
        network = token["network"]
        token_address = token["token_address"]
        name = token["name"]

        logging.info(f"Fetching data for {name} on {network}...")

        # First, find pools for this token
        pools = search_pools(network, token_address)

        if pools:
            logging.info(f"Found {len(pools)} pools for {name}")

            # Try to sort pools by liquidity and use the most liquid one
            try:
                # Sort pools by reserve_in_usd (liquidity)
                sorted_pools = sorted(
                    pools,
                    key=lambda x: float(x.get('attributes', {}).get('reserve_in_usd', '0') or '0'),
                    reverse=True
                )

                if sorted_pools:
                    top_pool = sorted_pools[0]
                    pool_address = top_pool.get('attributes', {}).get('address')

                    if pool_address:
                        logging.info(f"Using most liquid pool: {pool_address}")

                        # Now fetch historical OHLCV data
                        df = fetch_geckoterminal_data(
                            network=network,
                            token_address=token_address,
                            timeframe="1d",
                            since="2023-01-01"
                        )

                        if not df.empty:
                            results[name] = df
                            logging.info(f"Successfully fetched {len(df)} days of historical data for {name}")
                        else:
                            logging.error(f"Failed to fetch historical data for {name}")
                    else:
                        logging.error(f"Could not find pool address for {name}")
                else:
                    logging.error(f"No valid pools found for {name}")
            except Exception as e:
                logging.error(f"Error processing pools for {name}: {e}")
        else:
            logging.error(f"No pools found for {name} on {network}")

    return results

def plot_memecoin_data(data_dict):
    """Plot historical price data for memecoins."""
    if not data_dict:
        logging.error("No data to plot")
        return

    plt.figure(figsize=(12, 8))

    # Plot each memecoin
    for name, df in data_dict.items():
        plt.plot(df.index, df['close'], label=name)

    plt.title('Memecoin Historical Prices')
    plt.xlabel('Date')
    plt.ylabel('Price (USD)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')  # Use log scale for better visualization

    # Save the plot
    plt.savefig('memecoin_historical_prices.png')
    logging.info("Saved plot to memecoin_historical_prices.png")

    # Show the plot
    plt.show()

def run_test_allocation_with_memecoins():
    """Run test_allocation.py with memecoin data."""
    import subprocess

    # Example command to run test_allocation.py with GeckoTerminal tokens
    cmd = [
        "python", "test_allocation.py",
        "--timeframe", "1d",
        "--mtpi-timeframe", "1d",
        "--analysis-start-date", "2023-01-01",
        "--n-assets", "4",
        "--assets", "BTC/USDT", "ETH/USDT", "SOL/USDT",
        "--geckoterminal-tokens",
        "solana:DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263:BONK/USDT",
        "solana:7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU:SAMO/USDT",
        "solana:BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump:AUTISM/USDT"
    ]

    logging.info(f"Running command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logging.info("Command completed successfully")
        logging.info(result.stdout)
    except subprocess.CalledProcessError as e:
        logging.error(f"Command failed with exit code {e.returncode}")
        logging.error(e.stderr)

if __name__ == "__main__":
    # Fetch memecoin data directly
    memecoin_data = fetch_memecoin_data()

    # Print the results
    for name, df in memecoin_data.items():
        print(f"\n{name} OHLCV Data (Last 5 days):")
        print(df.tail())

    # Plot the data
    plot_memecoin_data(memecoin_data)

    # Run test_allocation.py with memecoins
    print("\nRunning test_allocation.py with memecoins...")
    run_test_allocation_with_memecoins()
