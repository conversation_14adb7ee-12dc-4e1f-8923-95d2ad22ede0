#!/bin/bash
# Setup script for 24/7 Asset Rotation Strategy Service
# This script sets up the systemd service, log rotation, and environment variables
# Uses background_service.py as the main service script

# Text colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}====================================================${NC}"
echo -e "${GREEN}  Asset Rotation Strategy 24/7 Service Setup        ${NC}"
echo -e "${GREEN}====================================================${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}Please run as root (use sudo)${NC}"
  exit 1
fi

# Get the current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
echo -e "${YELLOW}Working directory: ${SCRIPT_DIR}${NC}"

# Check if .env file exists
if [ ! -f "${SCRIPT_DIR}/.env" ]; then
  echo -e "${RED}Error: .env file not found!${NC}"
  echo -e "${YELLOW}Creating template .env file...${NC}"
  cat > "${SCRIPT_DIR}/.env" << EOL
# Environment variables for Asset Rotation Strategy
# Replace with your actual API keys and secrets

# Binance API credentials
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# Telegram Bot credentials
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
EOL
  echo -e "${YELLOW}Please edit ${SCRIPT_DIR}/.env with your API keys and secrets${NC}"
  exit 1
fi

# Check if systemd service file exists
if [ ! -f "${SCRIPT_DIR}/asset_rotation_service.service" ]; then
  echo -e "${RED}Error: asset_rotation_service.service file not found!${NC}"
  exit 1
fi

# Install required packages
echo -e "${GREEN}Installing required packages...${NC}"
apt-get update
apt-get install -y python3 python3-pip logrotate

# Install Python dependencies
echo -e "${GREEN}Installing Python dependencies...${NC}"
pip3 install -r "${SCRIPT_DIR}/requirements.txt"

# Setup log rotation
echo -e "${GREEN}Setting up log rotation...${NC}"
cat > /etc/logrotate.d/asset_rotation << EOL
${SCRIPT_DIR}/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
}
EOL

# Copy systemd service file
echo -e "${GREEN}Setting up systemd service...${NC}"
cp "${SCRIPT_DIR}/asset_rotation_service.service" /etc/systemd/system/
systemctl daemon-reload

# Enable and start the service
echo -e "${GREEN}Enabling and starting the service...${NC}"
systemctl enable asset_rotation_service
systemctl start asset_rotation_service

# Check service status
echo -e "${GREEN}Checking service status...${NC}"
systemctl status asset_rotation_service

echo -e "${GREEN}====================================================${NC}"
echo -e "${GREEN}  Setup Complete!                                   ${NC}"
echo -e "${GREEN}====================================================${NC}"
echo -e "${YELLOW}Service commands:${NC}"
echo -e "  ${YELLOW}Check status:${NC} sudo systemctl status asset_rotation_service"
echo -e "  ${YELLOW}View logs:${NC} sudo journalctl -u asset_rotation_service -f"
echo -e "  ${YELLOW}Restart:${NC} sudo systemctl restart asset_rotation_service"
echo -e "  ${YELLOW}Stop:${NC} sudo systemctl stop asset_rotation_service"
echo -e "${GREEN}====================================================${NC}"
