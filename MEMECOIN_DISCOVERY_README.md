# Memecoin Discovery System

An AI-powered system for discovering and analyzing promising memecoins through Twitter data scraping and intelligent analysis.

## Features

- **Twitter Data Scraping**: Monitors Twitter for memecoin mentions using snscrape (no API keys required)
- **AI Analysis**: Uses OpenAI GPT-4 to analyze social sentiment and token potential
- **Intelligent Scoring**: Combines multiple factors to score tokens (0-100 scale)
- **Risk Assessment**: Identifies potential scams and rug pulls
- **Web Dashboard**: Clean, responsive interface for reviewing discoveries
- **Manual Review**: Approve/reject tokens with detailed analysis
- **Export Functionality**: Export discoveries to JSON for further analysis

## Installation

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

The system requires these additional packages:
- `openai>=1.0.0` - OpenAI API integration
- `snscrape>=0.7.0` - Twitter scraping (no API keys needed)
- `beautifulsoup4>=4.12.0` - Web scraping
- `textblob>=0.17.1` - Text analysis
- `vaderSentiment>=3.3.2` - Sentiment analysis

### 2. Set Up API Keys

Create a `.env` file in the project root with your API credentials:

```bash
# OpenAI API (required for AI analysis)
OPENAI_API_KEY=your_openai_api_key_here

# Note: Twitter API keys are no longer needed!
# The system now uses snscrape for Twitter data scraping
```

### 3. Configure the System

Edit `config/memecoin_discovery.yaml` to customize:
- Discovery parameters (keywords, influencers, thresholds)
- AI analysis settings (model, temperature, cost limits)
- Scoring weights and risk thresholds
- Notification preferences

## Usage

### Command Line Interface

The system includes a CLI tool for running discoveries and managing tokens:

```bash
# Check system status
python memecoin_discovery_cli.py status

# Run a discovery cycle
python memecoin_discovery_cli.py discover

# Show dashboard summary
python memecoin_discovery_cli.py dashboard

# Approve a token
python memecoin_discovery_cli.py approve SYMBOL

# Reject a token
python memecoin_discovery_cli.py reject SYMBOL --reason "Low quality"
```

### Web Interface

Start the web server:

```bash
python memecoin_discovery_server.py --host 0.0.0.0 --port 5001
```

Then open your browser to `http://localhost:5001`

The web interface provides:
- **System Status**: Check API connections and dependencies
- **Discovery Dashboard**: View discovered tokens with filtering and sorting
- **Token Details**: Detailed analysis for each discovered token
- **Manual Actions**: Approve/reject tokens with one click
- **Export**: Download discovery data as JSON

### Automated Discovery

For continuous operation, you can set up automated discovery cycles:

```bash
# Run discovery every hour
*/60 * * * * cd /path/to/project && python memecoin_discovery_cli.py discover
```

## How It Works

### 1. Data Collection
- Scrapes Twitter for memecoin-related keywords
- Monitors influential crypto Twitter accounts
- Extracts token symbols and contract addresses
- Collects engagement metrics and sentiment data

### 2. AI Analysis
- Sends social media data to OpenAI GPT-4
- Analyzes sentiment, community strength, and risk factors
- Identifies potential scams and rug pulls
- Generates detailed reasoning for recommendations

### 3. Token Scoring
The system combines multiple factors to score tokens:

- **AI Analysis (35%)**: Overall AI assessment and confidence
- **Social Sentiment (25%)**: Twitter sentiment and consistency
- **Engagement Metrics (20%)**: Quality and volume of social engagement
- **Technical Indicators (15%)**: Price action and volume analysis
- **Risk Assessment (-5%)**: Penalty for identified risk factors

### 4. Manual Review
Tokens are categorized for review:
- **Auto-Approve** (Score ≥80, Low Risk): Automatically flagged as promising
- **Manual Review** (Score ≥65): Requires human evaluation
- **Auto-Reject** (Score <65): Filtered out automatically

## Configuration

### Discovery Settings
```yaml
discovery:
  max_tokens_per_run: 50          # Limit tokens analyzed per cycle
  min_mentions_threshold: 3       # Minimum Twitter mentions required
  hours_lookback: 24             # How far back to search
  cycle_frequency_minutes: 60    # How often to run discovery
```

### AI Analysis
```yaml
ai_analysis:
  model: "gpt-4o-mini"           # OpenAI model to use
  temperature: 0.3               # Consistency vs creativity
  max_cost_per_day: 25.0         # Daily spending limit (USD) - reduced as gpt-4o-mini is more cost-effective
  enable_scam_detection: true    # Enable scam risk analysis
```

### Scoring Weights
```yaml
evaluation:
  scoring_weights:
    ai_analysis: 0.35           # AI assessment weight
    social_sentiment: 0.25      # Social media sentiment
    engagement_metrics: 0.20    # Engagement quality
    technical_indicators: 0.15  # Technical analysis
    risk_assessment: -0.05      # Risk penalty
```

## API Endpoints

The web server provides a REST API:

- `GET /api/status` - System status and health check
- `POST /api/discover` - Run discovery cycle
- `GET /api/tokens` - List discovered tokens (with filters)
- `GET /api/tokens/{symbol}` - Get token details
- `POST /api/tokens/{symbol}/update_status` - Update token status
- `GET /api/summary` - Discovery summary statistics
- `GET /api/export` - Export data as JSON

## Database Schema

The system uses SQLite with these main tables:

- **discovered_tokens**: Core token information and scores
- **token_analysis**: Detailed AI analysis results
- **token_performance**: Price and performance tracking
- **twitter_mentions**: Individual tweet data
- **discovery_metrics**: System performance metrics

## Security & Privacy

- Social media data is sanitized to remove personal information
- API keys are stored securely in environment variables
- Database includes data retention and anonymization features
- Rate limiting prevents API abuse

## Troubleshooting

### Common Issues

1. **"Discovery system not available"**
   - Check that all dependencies are installed
   - Verify API keys are set correctly
   - Run `python memecoin_discovery_cli.py status` to diagnose

2. **"Twitter API rate limit exceeded"**
   - Reduce `requests_per_minute` in config
   - Increase delays between requests
   - Consider using multiple Twitter accounts

3. **"OpenAI API cost limit reached"**
   - Check `max_cost_per_day` setting
   - Reduce `max_tokens_per_run`
   - Consider using `gpt-3.5-turbo` for even lower costs if needed

4. **"No tokens discovered"**
   - Check Twitter API credentials
   - Verify keywords are relevant
   - Lower `min_mentions_threshold`

### Logs

Check these log files for debugging:
- `logs/memecoin_discovery.log` - Main system logs
- `logs/memecoin_discovery_cli.log` - CLI tool logs

## Performance Tips

1. **Optimize API Usage**:
   - Use batch processing for AI analysis
   - Cache results to avoid duplicate API calls
   - Set appropriate rate limits

2. **Improve Discovery Quality**:
   - Regularly update keyword lists
   - Monitor influential accounts for new voices
   - Adjust scoring weights based on results

3. **Resource Management**:
   - Monitor memory usage for large datasets
   - Clean up old data regularly
   - Use database indexing for better performance

## Contributing

To extend the system:

1. **Add New Data Sources**: Implement new scrapers in `src/memecoin_discovery/`
2. **Improve AI Analysis**: Enhance prompts and analysis logic
3. **Custom Scoring**: Modify evaluation weights and criteria
4. **UI Enhancements**: Extend the web interface with new features

## License

This project is part of the Asset Rotation Strategy system. See the main project license for details.
