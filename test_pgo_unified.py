#!/usr/bin/env python3
"""
Test that both PGO implementations now produce the same results.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.pgo_score import generate_pgo_score_signal
from src.MTPI_signal_handler import generate_pgo_signal, load_mtpi_indicators_config

def test_pgo_implementations():
    """Test that both PGO implementations produce identical results."""
    print("=" * 60)
    print("PGO IMPLEMENTATIONS COMPARISON TEST")
    print("=" * 60)
    
    # Load configuration
    config = load_mtpi_indicators_config()
    pgo_config = config.get('pgo', {})
    
    # Extract parameters
    length = pgo_config.get('length', 35)
    upper_threshold = pgo_config.get('upper_threshold', 1.35)
    lower_threshold = pgo_config.get('lower_threshold', -0.58)
    
    print(f"Testing with parameters:")
    print(f"  Length: {length}")
    print(f"  Upper Threshold: {upper_threshold}")
    print(f"  Lower Threshold: {lower_threshold}")
    
    # Fetch test data
    print("\nFetching test data...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        limit=100
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Fetched {len(btc_df)} candles")
    
    # Test both implementations
    print("\nTesting both implementations...")
    
    # New implementation (pgo_score.py)
    signals_new = generate_pgo_score_signal(
        df=btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False
    )
    
    # Old implementation (MTPI_signal_handler.py)
    signals_old = generate_pgo_signal(
        df=btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False
    )
    
    # Compare results
    print("\nComparing results...")
    
    # Check if they're identical
    are_identical = signals_new.equals(signals_old)
    print(f"Signals identical: {'✅ YES' if are_identical else '❌ NO'}")
    
    if not are_identical:
        # Find differences
        differences = (signals_new != signals_old).sum()
        print(f"Number of differences: {differences}")
        
        # Show first few differences
        diff_mask = signals_new != signals_old
        diff_indices = diff_mask[diff_mask].index[:5]
        
        print("\nFirst 5 differences:")
        for idx in diff_indices:
            print(f"  {idx}: New={signals_new[idx]}, Old={signals_old[idx]}")
    
    # Show signal distribution
    print(f"\nSignal distribution (New implementation):")
    new_counts = signals_new.value_counts().sort_index()
    for signal, count in new_counts.items():
        print(f"  Signal {signal}: {count} occurrences")
    
    print(f"\nSignal distribution (Old implementation):")
    old_counts = signals_old.value_counts().sort_index()
    for signal, count in old_counts.items():
        print(f"  Signal {signal}: {count} occurrences")
    
    # Show recent signals
    print(f"\nLast 10 signals comparison:")
    comparison_df = pd.DataFrame({
        'Date': btc_df.index[-10:].strftime('%Y-%m-%d'),
        'New': signals_new.iloc[-10:].values,
        'Old': signals_old.iloc[-10:].values,
        'Match': (signals_new.iloc[-10:] == signals_old.iloc[-10:]).values
    })
    print(comparison_df.to_string(index=False))
    
    return are_identical

if __name__ == '__main__':
    success = test_pgo_implementations()
    
    if success:
        print("\n🎉 Both PGO implementations are now IDENTICAL!")
        print("The visualization should now match TradingView behavior.")
    else:
        print("\n💥 PGO implementations still differ!")
        print("Further investigation needed.")
