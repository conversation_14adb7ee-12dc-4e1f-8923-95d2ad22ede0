#!/usr/bin/env python3
"""
Test script to verify the precision fix for NoneType comparison error.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.precision import adjust_amount_for_precision, get_asset_precision
from src.config_manager import get_trading_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_precision_with_none_values():
    """Test precision adjustment with potential None values in market info."""
    
    # Test symbols that were failing
    test_symbols = ['BTC/EUR', 'TRX/EUR']
    
    # Load trading config
    try:
        trading_config = get_trading_config('config/settings_kraken_eur.yaml')
        print(f"Loaded trading config successfully")
    except Exception as e:
        print(f"Error loading trading config: {e}")
        trading_config = None
    
    for symbol in test_symbols:
        print(f"\n=== Testing {symbol} ===")
        
        # Test get_asset_precision
        try:
            precision_info = get_asset_precision(symbol, trading_config=trading_config)
            print(f"Precision info: {precision_info}")
            
            # Verify no None values
            for key, value in precision_info.items():
                if value is None:
                    print(f"ERROR: {key} is None!")
                else:
                    print(f"✓ {key}: {value}")
                    
        except Exception as e:
            print(f"ERROR in get_asset_precision: {e}")
            continue
        
        # Test adjust_amount_for_precision with various amounts
        test_amounts = [0.00021595, 21.47893967, 0.001, 100.0]
        test_price = 92167.0 if symbol == 'BTC/EUR' else 0.23166300
        
        for amount in test_amounts:
            try:
                print(f"\nTesting amount {amount:.8f} at price {test_price:.8f}")
                adjusted = adjust_amount_for_precision(
                    amount=amount,
                    symbol=symbol,
                    price=test_price,
                    is_buy=True,
                    trading_config=trading_config
                )
                print(f"✓ Adjusted amount: {adjusted:.8f}")
                
            except Exception as e:
                print(f"ERROR in adjust_amount_for_precision: {e}")
                import traceback
                traceback.print_exc()

def test_market_info_with_none():
    """Test with simulated market info containing None values."""
    
    # Simulate problematic market info
    problematic_market_info = {
        'precision': {
            'amount': 0.00001,
            'price': 0.01
        },
        'limits': {
            'amount': {
                'min': None  # This could cause the issue
            },
            'cost': {
                'min': None  # This could cause the issue
            }
        }
    }
    
    print("\n=== Testing with problematic market info ===")
    
    try:
        precision_info = get_asset_precision('BTC/EUR', market_info=problematic_market_info)
        print(f"Precision info with None values: {precision_info}")
        
        # Verify no None values
        for key, value in precision_info.items():
            if value is None:
                print(f"ERROR: {key} is None!")
            else:
                print(f"✓ {key}: {value}")
                
        # Test adjustment
        adjusted = adjust_amount_for_precision(
            amount=0.00021595,
            symbol='BTC/EUR',
            market_info=problematic_market_info,
            price=92167.0,
            is_buy=True
        )
        print(f"✓ Adjusted amount with None market info: {adjusted:.8f}")
        
    except Exception as e:
        print(f"ERROR with None market info: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing precision fix for NoneType comparison error...")
    test_precision_with_none_values()
    test_market_info_with_none()
    print("\nTest completed!")
