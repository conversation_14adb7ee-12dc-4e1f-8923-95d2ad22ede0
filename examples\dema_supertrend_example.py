#!/usr/bin/env python3
"""
DEMA Supertrend Indicator Example

This example demonstrates how to use the newly implemented DEMA Supertrend indicator
in the MTPI system. The DEMA Supertrend combines Double Exponential Moving Average (DEMA)
with Supertrend to create a responsive trend-following indicator.

Features demonstrated:
1. Basic DEMA Supertrend calculation
2. Signal generation
3. Multi-indicator MTPI integration
4. Configuration options
"""

import sys
import os
import pandas as pd
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.dema_supertrend import (
    generate_dema_supertrend_signal, 
    calculate_dema_supertrend_score
)
from src.MTPI_signal_handler import fetch_multi_indicator_mtpi_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def demonstrate_dema_supertrend():
    """Demonstrate basic DEMA Supertrend usage."""
    print("🔹 DEMA Supertrend Indicator Example")
    print("="*50)
    
    # Fetch BTC data
    print("Fetching BTC data...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        limit=100
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"✅ Fetched {len(btc_df)} candles")
    
    # Calculate DEMA Supertrend with default parameters
    print("\n📊 Calculating DEMA Supertrend...")
    print("Parameters:")
    print("  - ATR Period: 19 (Supertrend length)")
    print("  - Multiplier: 2.8 (Supertrend multiplier)")
    print("  - DEMA Length: 17 (DEMA calculation period)")
    print("  - Source: close (price source for DEMA)")
    
    signal, supertrend_line, direction = calculate_dema_supertrend_score(btc_df)
    
    # Show results
    print(f"\n📈 Results:")
    signal_counts = signal.value_counts().sort_index()
    print(f"Signal distribution: {dict(signal_counts)}")
    print(f"Latest signal: {signal.iloc[-1]}")
    print(f"Latest supertrend value: {supertrend_line.iloc[-1]:.2f}")
    print(f"Latest direction: {direction.iloc[-1]}")
    
    # Show recent signals
    print(f"\n📅 Recent signals (last 5 days):")
    for i in range(max(0, len(signal)-5), len(signal)):
        date = signal.index[i].strftime('%Y-%m-%d')
        sig = signal.iloc[i]
        direction_val = direction.iloc[i]
        supertrend_val = supertrend_line.iloc[i]
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[sig]
        print(f"  {date}: {signal_name:7} (Direction: {direction_val:2.0f}, ST: {supertrend_val:8.2f})")

def demonstrate_multi_indicator_mtpi():
    """Demonstrate multi-indicator MTPI with DEMA Supertrend."""
    print("\n🔹 Multi-Indicator MTPI with DEMA Supertrend")
    print("="*50)
    
    # Test different combination methods
    methods = ['consensus', 'majority', 'weighted_consensus']
    
    for method in methods:
        print(f"\n📊 Testing {method.upper()} method...")
        
        signal = fetch_multi_indicator_mtpi_signal(
            timeframe='1d',
            enabled_indicators=['pgo', 'bollinger_bands', 'dema_super_score'],
            combination_method=method,
            limit=50
        )
        
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[signal]
        print(f"Combined signal: {signal_name} ({signal})")

def demonstrate_custom_parameters():
    """Demonstrate DEMA Supertrend with custom parameters."""
    print("\n🔹 DEMA Supertrend with Custom Parameters")
    print("="*50)
    
    # Fetch data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        limit=100
    )
    btc_df = data_dict['BTC/USDT']
    
    # Test different parameter sets
    parameter_sets = [
        {
            'name': 'Conservative (Longer periods)',
            'atr_period': 25,
            'multiplier': 3.0,
            'dema_length': 21,
            'src_col': 'close'
        },
        {
            'name': 'Aggressive (Shorter periods)',
            'atr_period': 14,
            'multiplier': 2.0,
            'dema_length': 12,
            'src_col': 'close'
        },
        {
            'name': 'Default (Recommended)',
            'atr_period': 19,
            'multiplier': 2.8,
            'dema_length': 17,
            'src_col': 'close'
        }
    ]
    
    for params in parameter_sets:
        print(f"\n📊 {params['name']}:")
        print(f"  ATR Period: {params['atr_period']}")
        print(f"  Multiplier: {params['multiplier']}")
        print(f"  DEMA Length: {params['dema_length']}")
        print(f"  Source: {params['src_col']}")
        
        # Calculate signal
        signal = generate_dema_supertrend_signal(
            df=btc_df,
            atr_period=params['atr_period'],
            multiplier=params['multiplier'],
            dema_length=params['dema_length'],
            src_col=params['src_col']
        )
        
        # Show results
        signal_counts = signal.value_counts().sort_index()
        latest_signal = signal.iloc[-1]
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        
        print(f"  Distribution: {dict(signal_counts)}")
        print(f"  Latest signal: {signal_name} ({latest_signal})")

def main():
    """Run all demonstrations."""
    print("🚀 DEMA Supertrend Indicator Examples")
    print("This example demonstrates the newly implemented DEMA Supertrend indicator")
    print("for the MTPI (Market Trend Position Indicator) system.\n")
    
    try:
        # Run demonstrations
        demonstrate_dema_supertrend()
        demonstrate_multi_indicator_mtpi()
        demonstrate_custom_parameters()
        
        print("\n" + "="*50)
        print("✅ All examples completed successfully!")
        print("\nThe DEMA Supertrend indicator is now ready for use in your trading strategies.")
        print("\nKey features:")
        print("• Uses DEMA for smoother trend detection")
        print("• Configurable ATR period and multiplier")
        print("• Integrates seamlessly with existing MTPI system")
        print("• Supports multiple combination methods")
        print("• Compatible with existing backtesting framework")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        logging.error(f"Error in examples: {e}")

if __name__ == "__main__":
    main()
