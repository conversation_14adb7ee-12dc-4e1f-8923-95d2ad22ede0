#!/usr/bin/env python3
"""
DPSD (DEMA Percentile Standard Deviation) Score Indicator - MTPI Component

This module implements the DPSD indicator which combines:
- DEMA (Double Exponential Moving Average)
- Percentile calculations
- Standard deviation analysis

Parameters from user specification:
- DEMA Length = 9
- Percentile Length = 58  
- SD Length = 27
- EMA Length = 14
- Percentile types (60/45, 60/40, 55/45, 55/40)

Signal Logic:
- Long (1): When conditions favor bullish trend
- Short (-1): When conditions favor bearish trend  
- Neutral (0): When no clear trend direction
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_dema(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate DEMA (Double Exponential Moving Average).
    
    Args:
        src: Source price series
        length: Period length for DEMA calculation
        
    Returns:
        DEMA series
    """
    try:
        # Calculate first EMA
        ema1 = src.ewm(span=length, adjust=False).mean()
        
        # Calculate second EMA (EMA of EMA1)
        ema2 = ema1.ewm(span=length, adjust=False).mean()
        
        # Calculate DEMA: 2 * EMA1 - EMA2
        dema = 2 * ema1 - ema2
        
        return dema
        
    except Exception as e:
        logging.error(f"Error calculating DEMA: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_percentile_rank(src: pd.Series, length: int, percentile: float) -> pd.Series:
    """
    Calculate percentile rank over a rolling window.
    
    Args:
        src: Source series
        length: Rolling window length
        percentile: Percentile value (0-100)
        
    Returns:
        Percentile rank series
    """
    try:
        def rolling_percentile(window):
            if len(window) < length:
                return np.nan
            return np.percentile(window, percentile)
        
        return src.rolling(window=length, min_periods=length).apply(rolling_percentile)
        
    except Exception as e:
        logging.error(f"Error calculating percentile rank: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_dpsd_score(df: pd.DataFrame,
                        dema_length: int = 6,
                        dema_src: str = 'high',
                        percentile_length: int = 56,
                        pertype: str = '60/45',
                        sd_length: int = 27,
                        ema_length: int = 75,
                        include_ema: bool = True) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate DPSD (DEMA Percentile Standard Deviation) Score - Exact TradingView Logic.

    This implementation follows the exact PineScript logic with:
    - State variables (T, Trend, PerUp, PerDown) that persist values
    - Conditional percentile calculation based on pertype
    - SDL condition logic
    - L and S condition logic
    - PT momentum calculation
    - EMA confluence option

    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        dema_src: Source for DEMA calculation ('high', 'low', 'close', etc.)
        percentile_length: Percentile calculation length
        pertype: Percentile type ('60/40', '60/45', '55/40', '55/45')
        sd_length: Standard deviation calculation length
        ema_length: EMA smoothing length
        include_ema: Whether to use EMA confluence

    Returns:
        Tuple of (Trend, PT, EMA, T, signal_for_mtpi)
    """
    try:
        # Get data columns
        dema_source = df[dema_src]
        close = df['close']

        # Step 1: Calculate DEMA from specified source
        dema = calculate_dema(dema_source, dema_length)

        # Step 2: Initialize state variables (equivalent to PineScript 'var')
        # These will be updated iteratively to maintain state
        T = pd.Series(0.0, index=df.index, dtype=float)
        Trend = pd.Series(0.0, index=df.index, dtype=float)
        PerUp = pd.Series(0.0, index=df.index, dtype=float)
        PerDown = pd.Series(0.0, index=df.index, dtype=float)

        # Step 3: Set percentile values based on pertype (equivalent to PineScript if statements)
        if pertype == "60/40":
            per_up_val = 60
            per_down_val = 40
        elif pertype == "60/45":
            per_up_val = 60
            per_down_val = 45
        elif pertype == "55/40":
            per_up_val = 55
            per_down_val = 40
        elif pertype == "55/45":
            per_up_val = 55
            per_down_val = 45
        else:
            # Default to 60/45
            per_up_val = 60
            per_down_val = 45

        # Step 4: Calculate percentile ranks (equivalent to ta.percentile_nearest_rank)
        PerUp = calculate_percentile_rank(dema, percentile_length, per_up_val)
        PerDown = calculate_percentile_rank(dema, percentile_length, per_down_val)

        # Step 5: Calculate standard deviation and SDL condition
        sd = PerDown.rolling(window=sd_length, min_periods=sd_length).std()
        sdl = PerDown + sd
        SDL = close > sdl

        # Step 6: Calculate L and S conditions
        L = (close > PerUp) & SDL
        S = close < PerDown

        # Step 7: Calculate T (trend direction) with state persistence
        for i in range(len(df)):
            if i == 0:
                T.iloc[i] = 0.0
            else:
                T.iloc[i] = T.iloc[i-1]  # Maintain previous state

            # Update T based on L and S conditions
            if L.iloc[i]:
                T.iloc[i] = 1.0
            elif S.iloc[i]:
                T.iloc[i] = -1.0

        # Step 8: Calculate PT (momentum) - exact PineScript logic
        PT = pd.Series(0.0, index=df.index, dtype=float)

        for i in range(len(df)):
            if T.iloc[i] == 1:
                PT.iloc[i] = close.iloc[i] - PerDown.iloc[i]
            else:
                if PerUp.iloc[i] > sdl.iloc[i]:
                    PT.iloc[i] = close.iloc[i] - PerUp.iloc[i]
                else:
                    PT.iloc[i] = close.iloc[i] - sdl.iloc[i]

        # Step 9: Calculate EMA of PT
        EMA = PT.ewm(span=ema_length, adjust=False).mean()

        # Step 10: Calculate final Trend with state persistence
        for i in range(len(df)):
            if i == 0:
                Trend.iloc[i] = 0.0
            else:
                Trend.iloc[i] = Trend.iloc[i-1]  # Maintain previous state

            # Update Trend based on PT and EMA conditions
            if include_ema:
                # Use EMA confluence (IncluedeEma = true)
                if PT.iloc[i] > 0 and PT.iloc[i] > EMA.iloc[i]:
                    Trend.iloc[i] = 1.0
                elif PT.iloc[i] < 0 and PT.iloc[i] < EMA.iloc[i]:
                    Trend.iloc[i] = -1.0
            else:
                # Without EMA confluence (IncluedeEma = false)
                if PT.iloc[i] > 0:
                    Trend.iloc[i] = 1.0
                elif PT.iloc[i] < 0:
                    Trend.iloc[i] = -1.0

        # Convert to integer for MTPI compatibility
        trend_signal = Trend.astype(int)
        t_direction = T.astype(int)

        return trend_signal, PT, EMA, t_direction, trend_signal

    except Exception as e:
        logging.error(f"Error calculating DPSD score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(0.0, index=df.index, dtype=float),
                pd.Series(0.0, index=df.index, dtype=float),
                pd.Series(0, index=df.index, dtype=int),
                pd.Series(0, index=df.index, dtype=int))

def generate_dpsd_signal(df: pd.DataFrame,
                        dema_length: int = 6,
                        dema_src: str = 'high',
                        percentile_length: int = 56,
                        pertype: str = '60/45',
                        sd_length: int = 27,
                        ema_length: int = 75,
                        include_ema: bool = True) -> pd.Series:
    """
    Generate DPSD binary signal for MTPI integration.

    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        dema_src: Source for DEMA calculation
        percentile_length: Percentile calculation length
        pertype: Percentile type ('60/40', '60/45', '55/40', '55/45')
        sd_length: Standard deviation calculation length
        ema_length: EMA smoothing length
        include_ema: Whether to use EMA confluence

    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        trend_signal, _, _, _, signal = calculate_dpsd_score(
            df=df,
            dema_length=dema_length,
            dema_src=dema_src,
            percentile_length=percentile_length,
            pertype=pertype,
            sd_length=sd_length,
            ema_length=ema_length,
            include_ema=include_ema
        )

        # Log signal statistics
        signal_counts = signal.value_counts().sort_index()
        logging.info(f"Generated DPSD signals")
        logging.info(f"Signal distribution: {dict(signal_counts)}")

        return signal

    except Exception as e:
        logging.error(f"Error generating DPSD signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def generate_dpsd_signal_with_variants(df: pd.DataFrame,
                                     dema_length: int = 6,
                                     dema_src: str = 'high',
                                     percentile_length: int = 56,
                                     variant: str = '60/45',
                                     sd_length: int = 27,
                                     ema_length: int = 75,
                                     include_ema: bool = True) -> pd.Series:
    """
    Generate DPSD signal with different percentile variants.

    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        dema_src: Source for DEMA calculation
        percentile_length: Percentile calculation length
        variant: Percentile variant ('60/45', '60/40', '55/45', '55/40')
        sd_length: Standard deviation calculation length
        ema_length: EMA smoothing length
        include_ema: Whether to use EMA confluence

    Returns:
        Binary signal series
    """
    # Validate variant
    valid_variants = ['60/45', '60/40', '55/45', '55/40']

    if variant not in valid_variants:
        logging.warning(f"Unknown variant {variant}, using default 60/45")
        variant = '60/45'

    return generate_dpsd_signal(
        df=df,
        dema_length=dema_length,
        dema_src=dema_src,
        percentile_length=percentile_length,
        pertype=variant,
        sd_length=sd_length,
        ema_length=ema_length,
        include_ema=include_ema
    )

# Default configuration matching TradingView PineScript
DEFAULT_DPSD_CONFIG = {
    'dema_length': 6,           # DEMA Length
    'dema_src': 'high',         # DEMA Source
    'percentile_length': 56,    # Percentile Length
    'pertype': '60/45',         # Percentile Type
    'sd_length': 27,            # SD Length
    'ema_length': 75,           # EMA Length
    'include_ema': True         # Use EMA confluence
}

def plot_dpsd_analysis(df: pd.DataFrame,
                      dema_length: int = 6,
                      dema_src: str = 'high',
                      percentile_length: int = 56,
                      pertype: str = '60/45',
                      sd_length: int = 27,
                      ema_length: int = 75,
                      include_ema: bool = True,
                      days_to_show: int = 100,
                      save_path: str = None) -> None:
    """
    Plot comprehensive DPSD analysis with all components.

    Args:
        df: DataFrame with OHLCV data
        dema_length: DEMA calculation length
        dema_src: Source for DEMA calculation
        percentile_length: Percentile calculation length
        pertype: Percentile type ('60/40', '60/45', '55/40', '55/45')
        sd_length: Standard deviation calculation length
        ema_length: EMA smoothing length
        include_ema: Whether to use EMA confluence
        days_to_show: Number of recent days to display
        save_path: Optional path to save the plot
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates

        # Calculate DPSD components
        trend_signal, PT, EMA_line, T_direction, signal = calculate_dpsd_score(
            df, dema_length, dema_src, percentile_length, pertype,
            sd_length, ema_length, include_ema
        )

        # Calculate DEMA for plotting
        src = df[dema_src]
        dema = calculate_dema(src, dema_length)

        # Get recent data
        if days_to_show > 0:
            df_plot = df.tail(days_to_show).copy()
            signal_plot = signal.tail(days_to_show)
            PT_plot = PT.tail(days_to_show)
            EMA_plot = EMA_line.tail(days_to_show)
            trend_plot = trend_signal.tail(days_to_show)
            dema_plot = dema.tail(days_to_show)
            src_plot = src.tail(days_to_show)
        else:
            df_plot = df.copy()
            signal_plot = signal
            PT_plot = PT
            EMA_plot = EMA_line
            trend_plot = trend_signal
            dema_plot = dema
            src_plot = src

        # Create figure with subplots
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle(f'DPSD (DEMA Percentile Standard Deviation) Analysis\n'
                    f'Parameters: DEMA={dema_length}, Percentile={percentile_length}, '
                    f'SD={sd_length}, EMA={ema_length}, Type={pertype}',
                    fontsize=14, fontweight='bold')

        # Subplot 1: Price and DEMA
        ax1 = axes[0]
        ax1.plot(df_plot.index, src_plot, label=f'{dema_src.title()} Price', color='black', linewidth=1)
        ax1.plot(df_plot.index, dema_plot, label=f'DEMA({dema_length})', color='blue', linewidth=2)
        ax1.set_title('Price and DEMA')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Subplot 2: PT (Momentum) and EMA
        ax2 = axes[1]
        ax2.plot(df_plot.index, PT_plot, label='PT (Momentum)', color='blue', linewidth=2)
        ax2.plot(df_plot.index, EMA_plot, label=f'EMA({ema_length})', color='orange', linewidth=2)
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5, label='Zero Line')
        ax2.set_title('PT Momentum and EMA Confluence')
        ax2.set_ylabel('PT Value')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Subplot 3: Signals
        ax3 = axes[2]

        # Create signal areas
        long_mask = signal_plot == 1
        short_mask = signal_plot == -1
        neutral_mask = signal_plot == 0

        # Plot signal areas
        ax3.fill_between(df_plot.index, 0, 1, where=long_mask,
                        color='green', alpha=0.3, label='LONG Signal')
        ax3.fill_between(df_plot.index, -1, 0, where=short_mask,
                        color='red', alpha=0.3, label='SHORT Signal')
        ax3.fill_between(df_plot.index, -0.1, 0.1, where=neutral_mask,
                        color='gray', alpha=0.3, label='NEUTRAL Signal')

        # Plot signal line
        ax3.plot(df_plot.index, signal_plot, color='black', linewidth=2, label='Signal')

        ax3.set_title('DPSD Trading Signals')
        ax3.set_ylabel('Signal')
        ax3.set_ylim(-1.2, 1.2)
        ax3.set_yticks([-1, 0, 1])
        ax3.set_yticklabels(['SHORT', 'NEUTRAL', 'LONG'])
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Subplot 4: Signal Statistics
        ax4 = axes[3]

        # Calculate signal statistics
        signal_counts = signal_plot.value_counts().sort_index()
        signal_labels = []
        signal_values = []
        colors = []

        for sig_val in [-1, 0, 1]:
            count = signal_counts.get(sig_val, 0)
            if sig_val == -1:
                signal_labels.append(f'SHORT\n({count})')
                colors.append('red')
            elif sig_val == 0:
                signal_labels.append(f'NEUTRAL\n({count})')
                colors.append('gray')
            else:
                signal_labels.append(f'LONG\n({count})')
                colors.append('green')
            signal_values.append(count)

        bars = ax4.bar(signal_labels, signal_values, color=colors, alpha=0.7)
        ax4.set_title('Signal Distribution')
        ax4.set_ylabel('Count')

        # Add value labels on bars
        for bar, value in zip(bars, signal_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(signal_values)*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold')

        ax4.grid(True, alpha=0.3, axis='y')

        # Format x-axis for time series
        for ax in axes[:3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Add current values text box
        latest_signal = signal_plot.iloc[-1]
        latest_PT = PT_plot.iloc[-1]
        latest_EMA = EMA_plot.iloc[-1]
        latest_price = src_plot.iloc[-1]
        latest_date = df_plot.index[-1].strftime('%Y-%m-%d')

        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]

        info_text = f"""Current Status ({latest_date}):
• Signal: {signal_name} ({latest_signal})
• PT Value: {latest_PT:.2f}
• EMA Value: {latest_EMA:.2f}
• Price: ${latest_price:.2f}
• Total Signals: {len(signal_plot)}"""

        fig.text(0.02, 0.02, info_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))

        plt.tight_layout()
        plt.subplots_adjust(top=0.93, bottom=0.15)

        # Save plot if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info(f"DPSD plot saved to: {save_path}")

        plt.show()

        # Print summary statistics
        print(f"\n📊 DPSD Analysis Summary:")
        print(f"Period: {df_plot.index[0].strftime('%Y-%m-%d')} to {df_plot.index[-1].strftime('%Y-%m-%d')}")
        print(f"Total Days: {len(df_plot)}")
        print(f"Signal Distribution: {dict(signal_counts)}")
        print(f"Current Signal: {signal_name} ({latest_signal})")
        print(f"Current PT: {latest_PT:.2f}")
        print(f"Current EMA: {latest_EMA:.2f}")
        print(f"PT Range: {PT_plot.min():.2f} to {PT_plot.max():.2f}")

    except ImportError:
        logging.error("matplotlib is required for plotting. Install with: pip install matplotlib")
        print("❌ matplotlib is required for plotting. Install with: pip install matplotlib")
    except Exception as e:
        logging.error(f"Error creating DPSD plot: {e}")
        print(f"❌ Error creating DPSD plot: {e}")
