- ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-11 00:02:02,295 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 00:02:02,295 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 00:02:02,295 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: []
2025-07-11 00:02:02,295 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=False, filtered_scores=True
2025-07-11 00:02:02,296 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,296 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Today: 2025-07-07 00:00:00+00:00, Yesterday: 2025-07-06 00:00:00+00:00, i: 207, start_idx: 60, prev_score_date: 2025-07-05 00:00:00+00:00
2025-07-11 00:02:02,297 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ETH/USDT: 7.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BTC/USDT: 11.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SOL/USDT: 7.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SUI/USDT: 5.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] XRP/USDT: 9.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AAVE/USDT: 10.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AVAX/USDT: 3.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ADA/USDT: 1.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] LINK/USDT: 6.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] TRX/USDT: 11.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] PEPE/USDT: 0.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,297 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOGE/USDT: 3.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BNB/USDT: 8.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 1.0 (filtered, from 2025-07-05 00:00:00+00:00)
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,298 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: []
2025-07-11 00:02:02,298 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=False, filtered_scores=True
2025-07-11 00:02:02,299 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Today: 2025-07-08 00:00:00+00:00, Yesterday: 2025-07-07 00:00:00+00:00, i: 208, start_idx: 60, prev_score_date: 2025-07-06 00:00:00+00:00
2025-07-11 00:02:02,300 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ETH/USDT: 7.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BTC/USDT: 11.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SOL/USDT: 7.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SUI/USDT: 5.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] XRP/USDT: 9.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AAVE/USDT: 11.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,300 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AVAX/USDT: 3.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ADA/USDT: 1.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] LINK/USDT: 6.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] TRX/USDT: 11.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] PEPE/USDT: 0.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOGE/USDT: 4.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BNB/USDT: 8.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 0.0 (filtered, from 2025-07-06 00:00:00+00:00)
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,301 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: []
2025-07-11 00:02:02,301 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=False, filtered_scores=True
2025-07-11 00:02:02,303 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,303 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Today: 2025-07-09 00:00:00+00:00, Yesterday: 2025-07-08 00:00:00+00:00, i: 209, start_idx: 60, prev_score_date: 2025-07-07 00:00:00+00:00
2025-07-11 00:02:02,303 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,303 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ETH/USDT: 7.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,303 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BTC/USDT: 10.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SOL/USDT: 7.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SUI/USDT: 5.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] XRP/USDT: 9.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AAVE/USDT: 11.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AVAX/USDT: 3.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ADA/USDT: 1.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] LINK/USDT: 6.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] TRX/USDT: 12.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] PEPE/USDT: 0.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOGE/USDT: 4.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BNB/USDT: 8.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 0.0 (filtered, from 2025-07-07 00:00:00+00:00)
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,304 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,304 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 00:02:02,304 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 00:02:02,304 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-11 00:02:02,305 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-11 00:02:02,305 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,305 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,305 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: []
2025-07-11 00:02:02,305 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=False, filtered_scores=True
2025-07-11 00:02:02,306 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,306 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Today: 2025-07-10 00:00:00+00:00, Yesterday: 2025-07-09 00:00:00+00:00, i: 210, start_idx: 60, prev_score_date: 2025-07-08 00:00:00+00:00
2025-07-11 00:02:02,306 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ETH/USDT: 8.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BTC/USDT: 8.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SOL/USDT: 6.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SUI/USDT: 5.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] XRP/USDT: 11.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AAVE/USDT: 13.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AVAX/USDT: 3.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ADA/USDT: 1.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] LINK/USDT: 7.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] TRX/USDT: 11.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] PEPE/USDT: 0.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOGE/USDT: 4.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BNB/USDT: 6.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 0.0 (filtered, from 2025-07-08 00:00:00+00:00)
2025-07-11 00:02:02,307 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,308 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,308 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:02,308 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: []
2025-07-11 00:02:02,308 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=False, filtered_scores=True
2025-07-11 00:02:02,399 - [KRAKEN] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: PEPE/USDT
2025-07-11 00:02:02,400 - [KRAKEN] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-07-11 00:02:02,400 - [KRAKEN] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-11 00:02:02,401 - [KRAKEN] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 00:02:02,401 - [KRAKEN] - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-11 00:02:02,401 - [KRAKEN] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 00:02:02,401 - [KRAKEN] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-11 00:02:02,402 - [KRAKEN] - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-07-11 00:02:02,404 - [KRAKEN] - root - INFO - Strategy execution completed in 0s
2025-07-11 00:02:02,405 - [KRAKEN] - root - INFO - DEBUG: self.elapsed_time = 0.7320277690887451 seconds
2025-07-11 00:02:02,423 - [KRAKEN] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_momentum_2025-02-10.csv
2025-07-11 00:02:02,423 - [KRAKEN] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-11 00:02:02,424 - [KRAKEN] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-11 00:02:02,425 - [KRAKEN] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-11 00:02:02,429 - [KRAKEN] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,432 - [KRAKEN] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,435 - [KRAKEN] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,438 - [KRAKEN] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,441 - [KRAKEN] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,444 - [KRAKEN] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,447 - [KRAKEN] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,449 - [KRAKEN] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,452 - [KRAKEN] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,455 - [KRAKEN] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,458 - [KRAKEN] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,461 - [KRAKEN] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,466 - [KRAKEN] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,469 - [KRAKEN] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 00:02:02,474 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 211 points
2025-07-11 00:02:02,474 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 10.90%
2025-07-11 00:02:02,479 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 211 points
2025-07-11 00:02:02,479 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 19.07%
2025-07-11 00:02:02,484 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 211 points
2025-07-11 00:02:02,484 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -18.02%
2025-07-11 00:02:02,489 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 211 points
2025-07-11 00:02:02,489 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 8.55%
2025-07-11 00:02:02,493 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 211 points
2025-07-11 00:02:02,494 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 5.09%
2025-07-11 00:02:02,498 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 211 points
2025-07-11 00:02:02,499 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 22.23%
2025-07-11 00:02:02,503 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 211 points
2025-07-11 00:02:02,504 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -19.36%
2025-07-11 00:02:02,508 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 211 points
2025-07-11 00:02:02,509 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: -4.86%
2025-07-11 00:02:02,513 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 211 points
2025-07-11 00:02:02,514 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -19.03%
2025-07-11 00:02:02,518 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 211 points
2025-07-11 00:02:02,519 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 19.37%
2025-07-11 00:02:02,523 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 211 points
2025-07-11 00:02:02,524 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 29.41%
2025-07-11 00:02:02,528 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 211 points
2025-07-11 00:02:02,529 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -23.63%
2025-07-11 00:02:02,533 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 211 points
2025-07-11 00:02:02,533 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 10.85%
2025-07-11 00:02:02,538 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 00:02:02,539 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 00:02:02,542 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:02:02,565 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-11 00:02:02,580 - [KRAKEN] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 00:02:02,786 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:02:02,800 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-11 00:02:05,338 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,339 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,340 - [KRAKEN] - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,341 - [KRAKEN] - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,342 - [KRAKEN] - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,342 - [KRAKEN] - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,370 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 00:02:05,370 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 00:02:05,373 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 00:02:05,373 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:02:05,387 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-11 00:02:05,388 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 00:02:05,388 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 00:02:05,388 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-11 00:02:05,388 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 00:02:05,388 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 00:02:05,410 - [KRAKEN] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 00:02:05,412 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 00:02:05,412 - [KRAKEN] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (after filtering).
2025-07-11 00:02:05,412 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 00:02:05,412 - [KRAKEN] - root - INFO - Fetched BTC data: 2152 candles from 2019-08-20 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:05,412 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 00:02:05,821 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1122)}
2025-07-11 00:02:05,821 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-11 00:02:05,821 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 00:02:05,941 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1126)}
2025-07-11 00:02:05,941 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-11 00:02:07,741 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:12,306 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 00:02:12,307 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-11 00:02:13,313 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 00:02:13,314 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(883)}
2025-07-11 00:02:13,314 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-11 00:02:13,314 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-11 00:02:15,096 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-11 00:02:15,096 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(978)}
2025-07-11 00:02:15,096 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 00:02:15,097 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-11 00:02:15,204 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 00:02:15,204 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 00:02:15,204 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-11 00:02:16,039 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 00:02:16,040 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 00:02:16,040 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-11 00:02:17,599 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-11 00:02:17,599 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-11 00:02:17,599 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-11 00:02:17,599 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-11 00:02:17,599 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-11 00:02:17,603 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 00:02:17,604 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:17,604 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:17,604 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 00:02:17,604 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 00:02:17,604 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 00:02:17,604 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:17,605 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:17,606 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:17,612 - [KRAKEN] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250629_130151.csv
2025-07-11 00:02:17,612 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250629_130151.csv
2025-07-11 00:02:17,612 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 00:02:17,612 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-11 00:02:17,612 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 271 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-11 00:02:17,613 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-11 00:02:17,615 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 00:02:17,615 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-07-11 00:02:17,616 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00    
2025-07-07 00:00:00+00:00    
2025-07-08 00:00:00+00:00    
2025-07-09 00:00:00+00:00    
2025-07-10 00:00:00+00:00    
dtype: object
2025-07-11 00:02:17,616 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:17,616 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:17,616 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: momentum
2025-07-11 00:02:17,616 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 00:02:17,616 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:17,616 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:17,617 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 00:02:17,617 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 00:02:17,617 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION:  -> SUI/EUR
2025-07-11 00:02:17,617 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 00:02:17,644 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 00:02:17,645 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-11 00:02:17,645 - [KRAKEN] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-11 00:02:17,722 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:17,726 - [KRAKEN] - root - INFO - Sent MTPI signal change notification: -1 -> 1
2025-07-11 00:02:17,726 - [KRAKEN] - root - ERROR - Missing data for template asset_rotation: 'previous_asset'
2025-07-11 00:02:17,726 - [KRAKEN] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 00:02:17,727 - [KRAKEN] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-11 00:02:18,081 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:25,841 - [KRAKEN] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 00:02:25,841 - [KRAKEN] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 00:02:25,841 - [KRAKEN] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-11 00:02:25,842 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 00:02:25,842 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-11 00:02:25,842 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 00:02:25,842 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 00:02:25,842 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: kraken
2025-07-11 00:02:25,842 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-11 00:02:25,842 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-11 00:02:25,843 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 00:02:25,933 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 00:02:25,933 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': None, 'datetime': None, 'high': 2.9835, 'low': 2.6096, 'bid': 2.9771, 'bidVolume': 19.0, 'ask': 2.9792, 'askVolume': 19.0, 'vwap': 2.83441872, 'open': 2.6308, 'close': 2.9835, 'last': 2.9835, 'previousClose': None, 'change': 0.3527, 'percentage': 13.406568344229893, 'average': 2.8071, 'baseVolume': 673542.8083, 'quoteVolume': 1909102.3445668914, 'info': {'a': ['2.97920000', '19', '19.000'], 'b': ['2.97710000', '19', '19.000'], 'c': ['2.********', '49.90698'], 'v': ['673542.80830', '673542.80830'], 'p': ['2.83441872', '2.83441872'], 't': ['3424', '3424'], 'l': ['2.60960000', '2.60960000'], 'h': ['2.********', '2.********'], 'o': '2.63080000'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 00:02:25,933 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9835
2025-07-11 00:02:25,933 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9835
2025-07-11 00:02:25,933 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 00:02:25,934 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 00:02:25,934 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 00:02:25,934 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 00:02:28,349 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:28,799 - [KRAKEN] - root - INFO - Available balance for EUR: 4420.********
2025-07-11 00:02:28,800 - [KRAKEN] - root - INFO - Reserved 26.518104 USDC for fees (rate: 0.4% with buffer)
2025-07-11 00:02:28,800 - [KRAKEN] - root - INFO - Calculated position size for SUI/EUR: 1465.******** (using 99.99% of 4420.126, accounting for fees)
2025-07-11 00:02:28,800 - [KRAKEN] - root - INFO - Calculated position size: 1465.******** SUI
2025-07-11 00:02:28,800 - [KRAKEN] - root - INFO - Entering position for SUI/EUR: 1465.******** units at 2.******** (value: 4371.******** EUR)
2025-07-11 00:02:28,800 - [KRAKEN] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-11 00:02:28,916 - [KRAKEN] - root - INFO - Adjusted base amount for SUI/EUR: 1465.******** -> 1457.********
2025-07-11 00:02:29,032 - [KRAKEN] - root - WARNING - CCXT NoneType comparison error for SUI/EUR: '>' not supported between instances of 'NoneType' and 'int'
2025-07-11 00:02:29,032 - [KRAKEN] - root - INFO - This is a known Kraken issue - order likely succeeded despite the error
2025-07-11 00:02:29,032 - [KRAKEN] - root - INFO - Created recovery order object for SUI/EUR - trade likely successful
2025-07-11 00:02:29,032 - [KRAKEN] - root - INFO - Filled amount: 1465.******** SUI
2025-07-11 00:02:29,032 - [KRAKEN] - root - INFO - Successfully entered position: SUI/EUR, amount: 1465.********, price: 2.********
2025-07-11 00:02:29,040 - [KRAKEN] - root - INFO - Trade executed: BUY SUI/EUR, amount=1465.********, price=2.********, filled=1465.********
2025-07-11 00:02:29,040 - [KRAKEN] - root - INFO - TRADE SUCCESS - SUI/EUR: Successfully updated current asset
2025-07-11 00:02:29,047 - [KRAKEN] - root - INFO - Trade executed: BUY SUI/EUR, amount=1465.********, price=2.********, filled=1465.********
2025-07-11 00:02:29,047 - [KRAKEN] - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 00:02:29,047 - [KRAKEN] - root - INFO - Trade executed: {'success': True, 'symbol': 'SUI/EUR', 'side': 'buy', 'amount': 1465.1248714792098, 'price': 2.9835, 'order': {'id': 'kraken-SUI/EUR-1752192149', 'symbol': 'SUI/EUR', 'amount': 1465.1248714792098, 'side': 'buy', 'type': 'market', 'status': 'closed', 'filled': 1465.1248714792098, 'cost': 4371.200054058222, 'average': 2.9835, 'timestamp': 1752192149032, 'datetime': '2025-07-11T00:02:29.032395', 'fee': None, 'trades': [], 'info': {'kraken_nonetype_error_recovery': True}}, 'filled_amount': 1465.1248714792098, 'fee': {}, 'quote_currency': 'EUR', 'base_currency': 'SUI', 'timestamp': '2025-07-11T00:02:29.032757'}
2025-07-11 00:02:29,105 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:29,116 - [KRAKEN] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 00:02:29,117 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-11 00:02:29,117 - [KRAKEN] - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 00:02:29,117 - [KRAKEN] - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,117 - [KRAKEN] - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,117 - [KRAKEN] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:29,118 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 00:02:29,119 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:29,157 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:29,160 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 52.57 seconds
2025-07-11 00:02:29,165 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-11 00:02:38,618 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:48,891 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:59,158 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:03:09,428 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:03:19,700 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:03:29,970 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:03:40,241 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:03:50,510 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:00,785 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:11,056 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:21,322 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:31,589 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:41,855 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:04:52,118 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:02,384 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:12,643 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:22,910 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:33,178 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:43,442 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:05:53,710 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:03,981 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:14,243 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:24,510 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:29,169 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:34,778 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:45,044 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:06:55,309 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:05,578 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:15,846 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:26,112 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:36,389 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:46,655 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:07:56,922 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:07,188 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:17,456 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:27,723 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:37,986 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:48,255 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:08:58,516 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:09:08,785 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:09:19,054 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:09:29,322 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:09:39,589 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:09:49,855 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
