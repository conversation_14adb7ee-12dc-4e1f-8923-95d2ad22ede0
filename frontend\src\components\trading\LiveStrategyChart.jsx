import React, { useEffect, useRef, useState } from 'react';
import {
  Spinner,
  Switch,
  FormControl,
  FormLabel,
  Box,
  Text,
  Select,
} from '@chakra-ui/react';
import axios from 'axios';
import { useQuery } from 'react-query';
import './LiveStrategyChart.css';
import { ASSET_COLORS, getAssetColor } from '../../utils/assetColors';
import {
  addListener,
  removeListener,
  subscribeToMarketData,
  unsubscribeFromMarketData
} from '../../services/websocketService';
import { fetchSettings } from '../../services/api';

// Map for special cases in live trading
const LIVE_TRADING_COLORS = {
  'Out of Market': ASSET_COLORS['out-of-market']
};

const LiveStrategyChart = ({
  isLoading: parentLoading,
  selectedAssets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
}) => {
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const seriesRef = useRef({});
  const [isLogScale, setIsLogScale] = useState(true);
  const [chartLoading, setChartLoading] = useState(false);
  const [liveDataEnabled, setLiveDataEnabled] = useState(false);
  const [liveDataTimeframe, setLiveDataTimeframe] = useState('1m');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState({});
  const [connectionError, setConnectionError] = useState(null);
  const marketDataRef = useRef({});
  const reconnectTimeoutRef = useRef(null);

  // Fetch trading status
  const { data: tradingStatus, isLoading: statusLoading } = useQuery(
    ['tradingStatus'],
    async () => {
      const response = await axios.get('/api/trading/status');
      return response.data;
    },
    { refetchInterval: 30000 } // Refetch every 30 seconds
  );

  // Fetch trading positions
  const { isLoading: positionsLoading } = useQuery(
    ['tradingPositions'],
    async () => {
      const response = await axios.get('/api/trading/positions');
      return response.data;
    },
    { refetchInterval: 30000 } // Refetch every 30 seconds
  );

  // Fetch trading performance
  const { data: performance, isLoading: performanceLoading } = useQuery(
    ['tradingPerformance'],
    async () => {
      const response = await axios.get('/api/trading/performance');
      return response.data;
    },
    { refetchInterval: 30000 } // Refetch every 30 seconds
  );

  // Fetch trading history
  const { data: history, isLoading: historyLoading } = useQuery(
    ['tradingHistory'],
    async () => {
      const response = await axios.get('/api/trading/history');
      return response.data;
    },
    { refetchInterval: 30000 } // Refetch every 30 seconds
  );

  // Fetch settings
  const { isLoading: settingsLoading } = useQuery(
    ['settings'],
    fetchSettings,
    {
      refetchInterval: 60000, // Refetch every minute
      onSuccess: (data) => {
        // Use the timeframe from settings for live data if available
        if (data && data.timeframe) {
          setLiveDataTimeframe(data.timeframe);
        }
      }
    }
  );

  // Store normalization factors for each asset
  const normalizationFactorsRef = useRef({});

  // Add WebSocket event listeners for real-time updates
  useEffect(() => {
    // Handle trade execution updates
    const handleTradeExecuted = (data) => {
      console.log('Chart received trade execution update:', data);

      // If we have a strategy series and a valid trade, add a marker
      if (seriesRef.current.strategy && data.trade) {
        const trade = data.trade;
        const time = new Date(data.timestamp).getTime() / 1000;

        // Create a marker for the trade
        const marker = {
          time,
          position: trade.side === 'buy' ? 'belowBar' : 'aboveBar',
          color: trade.side === 'buy' ? '#26a69a' : '#ef5350',
          shape: trade.side === 'buy' ? 'arrowUp' : 'arrowDown',
          text: `${trade.side.toUpperCase()} ${trade.symbol} @ ${trade.price?.toFixed(2) || 'N/A'}`,
        };

        // Get existing markers
        const existingMarkers = seriesRef.current.strategy.markers() || [];

        // Add the new marker
        seriesRef.current.strategy.setMarkers([...existingMarkers, marker]);
      }
    };

    // Handle market data updates
    const handleMarketDataUpdate = (data) => {
      console.log('Received market data update:', data);

      // Store the data in our ref
      if (data.symbol && data.data) {
        // Clear any connection error
        setConnectionError(null);

        // Update connection status from data.data.connected
        if (data.data.connected !== undefined) {
          console.log(`Connection status for ${data.symbol}: ${data.data.connected}`);
          setConnectionStatus(prev => ({
            ...prev,
            [data.symbol]: data.data.connected
          }));
        }

        // Also update connection status from the dedicated connection_status field if available
        if (data.connection_status) {
          console.log('Connection status from server:', data.connection_status);
          setConnectionStatus(prev => ({
            ...prev,
            ...data.connection_status
          }));
        }

        // Store the data
        marketDataRef.current[data.symbol] = data.data;
        setLastUpdate(new Date());
        console.log(`Updated market data for ${data.symbol}:`, data.data);

        // Check if this is a candle close or just a price update
        const isNewCandle = data.data.is_candle_closed === true;
        const timestamp = Math.floor(data.data.timestamp / 1000);
        const closePrice = data.data.close;

        // Update the buy-and-hold curve for this symbol
        if (seriesRef.current[data.symbol]) {
          // Only update the buy-and-hold curve on candle close
          if (isNewCandle) {
            console.log(`Updating buy-and-hold curve for ${data.symbol} at candle close`);

            // Get the current data from the series
            const currentData = seriesRef.current[data.symbol].data();

            // Apply normalization factor if available
            const normalizationFactor = normalizationFactorsRef.current[data.symbol] || 1;
            const normalizedClosePrice = closePrice * normalizationFactor;

            console.log(`Applying normalization factor ${normalizationFactor} to ${data.symbol} close price: ${closePrice} -> ${normalizedClosePrice}`);

            // Create a new data point with normalized value
            const newPoint = {
              time: timestamp,
              value: normalizedClosePrice,
            };

            // Check if we already have a point with this timestamp
            const existingPointIndex = currentData.findIndex(point => point.time === newPoint.time);

            if (existingPointIndex >= 0) {
              // Update the existing point
              currentData[existingPointIndex] = newPoint;
              seriesRef.current[data.symbol].update(newPoint);
            } else {
              // Add a new point
              seriesRef.current[data.symbol].update(newPoint);
            }

            console.log(`Updated buy-and-hold curve for ${data.symbol} with normalized point:`, newPoint);
          } else {
            // For real-time price updates (not candle close), just update the label
            // This will show the current price without adding a new point to the chart
            // We can show both the actual price and the normalized value
            const normalizationFactor = normalizationFactorsRef.current[data.symbol] || 1;
            const normalizedClosePrice = closePrice * normalizationFactor;

            seriesRef.current[data.symbol].applyOptions({
              title: `${data.symbol} B&H: ${normalizedClosePrice.toFixed(2)}`
            });
          }
        } else {
          console.warn(`No series found for ${data.symbol} to update`);
        }

        // Update the strategy equity curve if we have a current position in this asset
        // This should only happen on candle close
        if (isNewCandle && seriesRef.current.strategy && performance && performance.current_position) {
          const currentPosition = performance.current_position;

          // Check if we have a position in this asset
          if (currentPosition.asset === data.symbol) {
            console.log(`Updating strategy equity curve for current position in ${data.symbol}`);

            // Calculate the new equity value based on the current position
            const quantity = currentPosition.quantity || 0;
            const newEquityValue = quantity * closePrice;

            // Get the current data from the strategy series
            const strategyData = seriesRef.current.strategy.data();

            if (strategyData.length > 0) {
              // Create a new point for the strategy equity curve
              const newStrategyPoint = {
                time: timestamp,
                value: newEquityValue,
                color: getAssetColor(data.symbol)
              };

              // Check if we already have a point with this timestamp
              const existingStrategyPointIndex = strategyData.findIndex(point => point.time === newStrategyPoint.time);

              if (existingStrategyPointIndex >= 0) {
                // Update the existing point
                strategyData[existingStrategyPointIndex] = newStrategyPoint;
                seriesRef.current.strategy.update(newStrategyPoint);
              } else {
                // Add a new point
                seriesRef.current.strategy.update(newStrategyPoint);
              }

              console.log(`Updated strategy equity curve with point:`, newStrategyPoint);

              // Also update the strategy title with current value
              seriesRef.current.strategy.applyOptions({
                title: `Strategy: ${newEquityValue.toFixed(2)}`
              });
            }
          } else {
            // If we're not in a position for this asset, just update the strategy title
            if (performance.current_value) {
              seriesRef.current.strategy.applyOptions({
                title: `Strategy: ${performance.current_value.toFixed(2)}`
              });
            }
          }
        }
      } else {
        console.warn('Received market data update with missing symbol or data:', data);
      }
    };

    // Handle subscription status updates
    const handleSubscriptionStatus = (data) => {
      console.log('Received subscription status:', data);
      if (!data.success) {
        // If subscription failed, disable live data
        console.error('Subscription failed:', data.message);
        setLiveDataEnabled(false);
      }
    };

    // Handle unsubscription status updates
    const handleUnsubscriptionStatus = (data) => {
      console.log('Received unsubscription status:', data);
      if (!data.success) {
        console.error('Unsubscription failed:', data.message);
      }
    };

    // Handle WebSocket errors
    const handleError = (error) => {
      console.error('WebSocket error:', error);

      // Store the error message
      setConnectionError(error.message || 'Connection error');

      // If there's a heartbeat timeout, try to reconnect automatically
      if (error.message && error.message.includes('heartbeat timeout')) {
        console.log('Heartbeat timeout detected, attempting automatic reconnection...');

        // Clear any existing reconnect timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
        }

        // Set a timeout to reconnect
        reconnectTimeoutRef.current = setTimeout(() => {
          if (liveDataEnabled) {
            console.log('Attempting automatic reconnection after heartbeat timeout...');
            // Trigger reconnection by toggling liveDataEnabled
            setLiveDataEnabled(false);
            setTimeout(() => {
              setLiveDataEnabled(true);
            }, 1000);
          }
        }, 5000); // Wait 5 seconds before attempting reconnection
      } else {
        // For other errors, disable live data
        setLiveDataEnabled(false);
      }
    };

    // Add event listeners
    addListener('trade_executed', handleTradeExecuted);
    addListener('market_data_update', handleMarketDataUpdate);
    addListener('subscription_status', handleSubscriptionStatus);
    addListener('unsubscription_status', handleUnsubscriptionStatus);
    addListener('error', handleError);

    // Clean up event listeners on unmount
    return () => {
      removeListener('trade_executed', handleTradeExecuted);
      removeListener('market_data_update', handleMarketDataUpdate);
      removeListener('subscription_status', handleSubscriptionStatus);
      removeListener('unsubscription_status', handleUnsubscriptionStatus);
      removeListener('error', handleError);

      // Clear any reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  // Create and update chart
  useEffect(() => {
    if (!chartContainerRef.current || parentLoading || statusLoading || !performance) return;

    const initChart = async () => {
      setChartLoading(true);

      try {
        // Import TradingView's lightweight charts
        const { createChart, CrosshairMode } = await import('lightweight-charts');

        // Clean up previous chart if it exists
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
          seriesRef.current = {};
        }

        // Use dark theme colors for the chart
        const chartBgColor = '#1A202C'; // Dark background
        const chartTextColor = '#E2E8F0'; // Light text
        const chartGridColor = '#4A5568'; // Dark grid

        // Create chart
        const chart = createChart(chartContainerRef.current, {
          width: chartContainerRef.current.clientWidth,
          height: 500,
          layout: {
            background: { type: 'solid', color: chartBgColor },
            textColor: chartTextColor,
          },
          grid: {
            vertLines: { color: chartGridColor, style: 1, visible: true },
            horzLines: { color: chartGridColor, style: 1, visible: true, opacity: 0.2 },
          },
          rightPriceScale: {
            scaleMargins: {
              top: 0.02,
              bottom: 0.02,
            },
            borderVisible: false,
            mode: isLogScale ? 1 : 0, // 1 = logarithmic, 0 = normal
            autoScale: true,
          },
          timeScale: {
            borderVisible: false,
            timeVisible: true,
            secondsVisible: true, // Show seconds for 1-minute timeframe
          },
          crosshair: {
            mode: CrosshairMode.Normal,
          },
        });

        // Save chart reference
        chartRef.current = chart;

        // Use the selected assets for the chart
        const availableAssets = selectedAssets;

        console.log(`Creating series for ${availableAssets.length} assets`);

        // Create series for each asset (buy and hold)
        availableAssets.forEach(asset => {
          // Get initial price if available
          const initialPrice = marketDataRef.current[asset]?.close || 0;
          const priceDisplay = initialPrice > 0 ? `: ${initialPrice.toFixed(2)}` : '';

          const series = chart.addLineSeries({
            color: getAssetColor(asset) + '80', // Add 50% transparency
            lineWidth: 1,
            lineStyle: 0, // 0 = solid line (no dashes)
            title: `${asset} B&H${priceDisplay}`,
            lastValueVisible: true, // Show the last value
            priceLineVisible: true, // Show price line
            priceFormat: {
              type: 'price',
              precision: 2,
              minMove: 0.01
            }
          });

          seriesRef.current[asset] = series;
          console.log(`Created series for ${asset}`);

          // Set empty data for now
          series.setData([]);
        });

        // Get initial strategy value if available
        const initialStrategyValue = performance?.current_value || 10000;

        // Create strategy series
        const strategySeries = chart.addLineSeries({
          lineWidth: 3,
          title: `Strategy: ${initialStrategyValue.toFixed(2)}`,
          lastValueVisible: true,
          priceLineVisible: true,
          lineType: 0, // 0 = solid line (no dashes)
          lineStyle: 0, // 0 = solid line (no dashes)
          disableSegmentRendering: false, // Enable segment rendering for colored segments
          priceFormat: {
            type: 'price',
            precision: 2,
            minMove: 0.01
          }
        });

        seriesRef.current.strategy = strategySeries;
        console.log('Created strategy series');

        // Set empty data for now
        strategySeries.setData([]);

        // If we have performance data, update the chart
        if (performance && performance.equity_curve) {
          console.log(`Loading ${performance.equity_curve.length} points for strategy equity curve`);

          // Get the initial capital from performance data
          const initialCapital = performance.initial_capital || 10000;
          console.log(`Using initial capital: ${initialCapital}`);

          // Format data for strategy series
          const strategyData = performance.equity_curve.map(point => ({
            time: new Date(point.timestamp).getTime() / 1000,
            value: point.value,
            color: point.asset ? getAssetColor(point.asset) : LIVE_TRADING_COLORS['Out of Market'],
          }));

          // Set strategy data
          strategySeries.setData(strategyData);
          console.log(`Set ${strategyData.length} points for strategy series`);

          // Format data for buy and hold series
          if (performance.buy_hold_curves) {
            // Ensure all curves start from the same initial capital
            // Clear previous normalization factors
            normalizationFactorsRef.current = {};

            // Calculate normalization factors for each asset
            availableAssets.forEach(asset => {
              if (performance.buy_hold_curves[asset] && performance.buy_hold_curves[asset].length > 0) {
                const assetData = performance.buy_hold_curves[asset].map(point => ({
                  time: new Date(point.timestamp).getTime() / 1000,
                  value: point.value,
                }));

                // Find the earliest point in this asset's data
                if (assetData.length > 0) {
                  const assetEarliestValue = assetData[0].value;

                  // Calculate normalization factor to make this curve start at initialCapital
                  normalizationFactorsRef.current[asset] = initialCapital / assetEarliestValue;

                  console.log(`Normalization factor for ${asset}: ${normalizationFactorsRef.current[asset]}`);
                }
              }
            });

            // Apply normalization and set data for each asset
            availableAssets.forEach(asset => {
              if (performance.buy_hold_curves[asset]) {
                console.log(`Loading ${performance.buy_hold_curves[asset].length} points for ${asset}`);

                const assetData = performance.buy_hold_curves[asset].map(point => {
                  const time = new Date(point.timestamp).getTime() / 1000;
                  // Apply normalization factor to ensure all curves start from the same level
                  const normalizedValue = point.value * (normalizationFactorsRef.current[asset] || 1);

                  return {
                    time: time,
                    value: normalizedValue,
                  };
                });

                // Set asset data
                seriesRef.current[asset].setData(assetData);
                console.log(`Set ${assetData.length} normalized points for ${asset} series`);
              } else {
                console.warn(`No buy and hold curve data for ${asset}`);
              }
            });
          } else {
            console.warn('No buy and hold curves in performance data');
          }
        } else {
          console.warn('No equity curve in performance data');
        }

        // Add trade markers if we have history
        if (history && history.length > 0) {
          console.log(`Adding ${history.length} trade markers`);

          const markers = history.map(trade => {
            const time = new Date(trade.timestamp).getTime() / 1000;
            return {
              time,
              position: trade.side === 'buy' ? 'belowBar' : 'aboveBar',
              color: trade.side === 'buy' ? '#26a69a' : '#ef5350',
              shape: trade.side === 'buy' ? 'arrowUp' : 'arrowDown',
              text: `${trade.side.toUpperCase()} ${trade.symbol} @ ${trade.price.toFixed(2)}`,
            };
          });

          // Add markers to strategy series
          strategySeries.setMarkers(markers);
          console.log(`Added ${markers.length} trade markers`);
        }

        // Fit content
        chart.timeScale().fitContent();
        console.log('Fitted chart content');

        // Handle resize
        const resizeObserver = new ResizeObserver(entries => {
          if (entries.length === 0 || !chartRef.current) return;
          const { width } = entries[0].contentRect;
          chartRef.current.applyOptions({ width });
        });

        resizeObserver.observe(chartContainerRef.current);

        // Check if we have any market data already
        const marketDataKeys = Object.keys(marketDataRef.current);
        if (marketDataKeys.length > 0) {
          console.log(`Found existing market data for ${marketDataKeys.join(', ')}`);

          // Update the chart with the existing data
          marketDataKeys.forEach(symbol => {
            if (seriesRef.current[symbol]) {
              const data = marketDataRef.current[symbol];
              console.log(`Updating chart with existing data for ${symbol}:`, data);

              const newPoint = {
                time: Math.floor(data.timestamp / 1000),
                value: data.close,
              };

              seriesRef.current[symbol].update(newPoint);
              console.log(`Updated ${symbol} with existing data point:`, newPoint);
            }
          });
        }

        // Cleanup function
        return () => {
          resizeObserver.disconnect();
          if (chartRef.current) {
            chartRef.current.remove();
            chartRef.current = null;
          }
        };
      } catch (error) {
        console.error('Error creating chart:', error);
      } finally {
        setChartLoading(false);
      }
    };

    initChart();
  }, [performance, history, isLogScale, parentLoading, statusLoading, selectedAssets]);

  // Effect to handle live data subscription
  useEffect(() => {
    const handleSubscription = async () => {
      if (liveDataEnabled) {
        try {
          // Set connecting state
          setIsConnecting(true);

          console.log(`Attempting to subscribe to market data for ${selectedAssets.join(', ')} with timeframe ${liveDataTimeframe}`);
          console.log('Current chart series:', Object.keys(seriesRef.current));

          // Subscribe to market data updates with retry logic
          let success = false;
          let retryCount = 0;
          const maxRetries = 3;

          while (!success && retryCount < maxRetries) {
            try {
              console.log(`Subscription attempt ${retryCount + 1}/${maxRetries}`);
              success = await subscribeToMarketData(selectedAssets, liveDataTimeframe);

              if (success) {
                console.log('Successfully subscribed to market data');

                // Check if we have series for each asset
                selectedAssets.forEach(asset => {
                  if (!seriesRef.current[asset]) {
                    console.warn(`No series found for ${asset} after subscription. This will prevent real-time updates.`);
                  } else {
                    console.log(`Series for ${asset} is ready for real-time updates`);
                  }
                });

                break;
              } else {
                console.warn(`Subscription attempt ${retryCount + 1} failed, retrying...`);
                retryCount++;
                // Wait before retrying
                if (retryCount < maxRetries) {
                  await new Promise(resolve => setTimeout(resolve, 2000));
                }
              }
            } catch (subscribeError) {
              console.error(`Error in subscription attempt ${retryCount + 1}:`, subscribeError);
              retryCount++;
              // Wait before retrying
              if (retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 2000));
              }
            }
          }

          // Clear connecting state
          setIsConnecting(false);

          if (!success) {
            console.error(`Failed to subscribe to market data after ${maxRetries} attempts`);
            // Reset the toggle if subscription fails
            setLiveDataEnabled(false);
          } else {
            // Force a refresh of the chart data after successful subscription
            console.log('Forcing chart refresh after successful subscription');

            // Check if we have any initial market data
            const marketDataKeys = Object.keys(marketDataRef.current);
            if (marketDataKeys.length > 0) {
              console.log('Initial market data available:', marketDataKeys);

              // Update the chart with the initial data
              marketDataKeys.forEach(symbol => {
                if (seriesRef.current[symbol]) {
                  const data = marketDataRef.current[symbol];
                  console.log(`Updating chart with initial data for ${symbol}:`, data);

                  const newPoint = {
                    time: Math.floor(data.timestamp / 1000),
                    value: data.close,
                  };

                  seriesRef.current[symbol].update(newPoint);
                }
              });
            } else {
              console.log('No initial market data available');
            }
          }
        } catch (error) {
          console.error('Error in subscription process:', error);
          // Reset the toggle if subscription fails
          setLiveDataEnabled(false);
          setIsConnecting(false);
        }
      } else {
        // Unsubscribe from market data updates
        try {
          console.log('Unsubscribing from market data');
          await unsubscribeFromMarketData();
          console.log('Successfully unsubscribed from market data');
        } catch (error) {
          console.error('Error unsubscribing from market data:', error);
        }
      }
    };

    handleSubscription();

    // Clean up on unmount
    return () => {
      if (liveDataEnabled) {
        console.log('Component unmounting, unsubscribing from market data');
        unsubscribeFromMarketData().catch(error => {
          console.error('Error unsubscribing from market data during cleanup:', error);
        });
      }
    };
  }, [liveDataEnabled, liveDataTimeframe, selectedAssets]);

  // Toggle scale type
  const toggleScale = () => {
    setIsLogScale(!isLogScale);
  };

  // Toggle live data
  const toggleLiveData = () => {
    setLiveDataEnabled(!liveDataEnabled);
  };

  const isLoading = parentLoading || statusLoading || positionsLoading || performanceLoading || historyLoading || chartLoading || settingsLoading;

  return (
    <div className="chart-container">
      <div className="chart-header">
        <div className="chart-title">
          Strategy vs Buy-and-Hold Equity Curves
          {tradingStatus && (
            <span
              className={`status-badge ${tradingStatus.is_running ? 'running' : 'stopped'}`}
            >
              {tradingStatus.is_running ? 'Running' : 'Stopped'}
            </span>
          )}
        </div>

        <div className="chart-controls">
          <button
            className="scale-toggle-btn"
            onClick={toggleScale}
          >
            {isLogScale ? 'Log Scale' : 'Linear Scale'}
          </button>

          <FormControl display="flex" alignItems="center" ml={4}>
            <FormLabel htmlFor="live-data-switch" mb="0" color="white">
              Live Data
            </FormLabel>
            <Switch
              id="live-data-switch"
              colorScheme="green"
              isChecked={liveDataEnabled}
              onChange={toggleLiveData}
              isDisabled={isConnecting}
            />
            {isConnecting && (
              <Spinner size="sm" ml={2} color="white" />
            )}
          </FormControl>

          {liveDataEnabled && (
            <FormControl display="flex" alignItems="center" ml={4} maxW="150px">
              <Select
                size="sm"
                value={liveDataTimeframe}
                onChange={(e) => setLiveDataTimeframe(e.target.value)}
                bg="gray.700"
                color="white"
                borderColor="gray.600"
              >
                <option value="1m">1 Minute</option>
                <option value="5m">5 Minutes</option>
                <option value="15m">15 Minutes</option>
                <option value="30m">30 Minutes</option>
                <option value="1h">1 Hour</option>
                <option value="4h">4 Hours</option>
                <option value="1d">1 Day</option>
              </Select>
            </FormControl>
          )}

          {liveDataEnabled && lastUpdate && (
            <Text ml={4} fontSize="sm" color="white">
              Last update: {lastUpdate.toLocaleTimeString()}
            </Text>
          )}

          {connectionError && (
            <Text ml={4} fontSize="sm" color="red.400">
              Error: {connectionError}
            </Text>
          )}
        </div>
      </div>

      <div
        ref={chartContainerRef}
        style={{ height: "500px", position: "relative" }}
      >
        {isLoading && (
          <div className="loading-overlay">
            <Spinner size="xl" color="white" />
          </div>
        )}
      </div>

      {/* Connection Status */}
      {liveDataEnabled && Object.keys(connectionStatus).length > 0 && (
        <Box mt={2} p={2} bg="gray.800" borderRadius="md">
          <Text fontSize="sm" color="white" mb={1}>Connection Status:</Text>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
            {Object.entries(connectionStatus).map(([symbol, isConnected]) => (
              <Box
                key={symbol}
                px={2}
                py={1}
                borderRadius="md"
                bg={isConnected ? 'green.600' : 'red.600'}
                fontSize="xs"
              >
                {symbol}: {isConnected ? 'Connected' : 'Disconnected'}
              </Box>
            ))}
          </div>
        </Box>
      )}

      {/* Asset Legend */}
      <div className="asset-legend">
        <div className="asset-legend-item">
          <div className="strategy-line" style={{ backgroundColor: '#FFFFFF' }}></div>
          <span>Strategy</span>
        </div>

        {selectedAssets.map(asset => (
          <div key={asset} className="asset-legend-item">
            <div
              className="asset-color-box"
              style={{ backgroundColor: getAssetColor(asset) }}
            ></div>
            <span>{asset}</span>
          </div>
        ))}

        <div className="asset-legend-item">
          <div
            className="asset-color-box"
            style={{ backgroundColor: LIVE_TRADING_COLORS['Out of Market'] }}
          ></div>
          <span>Out of Market</span>
        </div>

        {liveDataEnabled && (
          <div className="live-data-indicator">
            <Box
              w="8px"
              h="8px"
              borderRadius="50%"
              bg="green.400"
              mr="2px"
            />
            <span>Live Data ({liveDataTimeframe})</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveStrategyChart;
