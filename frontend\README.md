# Asset Rotation Dashboard Frontend

This is the frontend for the Asset Rotation Dashboard, built with React and TradingView libraries.

## Features

- Interactive asset price charts using TradingView Lightweight Charts
- Asset comparison table with scores and performance metrics
- Performance metrics visualization
- Equity curve comparison between rotation strategy and buy & hold
- Settings configuration for the asset rotation strategy

## Tech Stack

- **React**: Core UI library
- **Chakra UI**: Component library for styling
- **TradingView Lightweight Charts**: For financial charts
- **Recharts**: For additional charts/graphs
- **React Query**: For data fetching and caching
- **React Router**: For navigation
- **Axios**: For API requests
- **Vite**: Build tool

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Python backend running (see main project README)

### Installation

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

3. Start the development server:
   ```
   npm run dev
   ```
   or
   ```
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

To build the frontend for production:

```
npm run build
```
or
```
yarn build
```

This will create a `dist` directory with the compiled assets.

## Project Structure

```
frontend/
├── public/                # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── Chart.jsx      # TradingView chart component
│   │   ├── AssetTable.jsx # Asset comparison table
│   │   └── ...
│   ├── pages/             # Page components
│   │   ├── Dashboard.jsx  # Main dashboard page
│   │   └── Settings.jsx   # Configuration page
│   ├── services/          # API services
│   │   └── api.js         # API client for backend
│   ├── utils/             # Utility functions
│   ├── App.jsx            # Main App component
│   └── main.jsx           # Entry point
└── package.json           # NPM dependencies
```

## API Integration

The frontend communicates with the Python backend through a REST API. The API endpoints are defined in `src/services/api.js`.

Make sure the Python backend is running and accessible at the URL specified in the `vite.config.js` proxy settings.

## Customization

- **Theme**: The UI theme can be customized in `src/theme.js`
- **API Endpoints**: Update the API endpoints in `src/services/api.js`
- **Components**: Modify or extend the components in `src/components/`

## License

This project is part of the Asset Rotation Strategy system.
