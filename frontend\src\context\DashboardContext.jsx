import React, { createContext, useContext, useState, useEffect } from 'react';

// Create a context for dashboard data
const DashboardContext = createContext();

// Custom hook to use the dashboard context
export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};

// Provider component to wrap the app
export const DashboardProvider = ({ children }) => {
  // State for dashboard data
  const [dashboardData, setDashboardData] = useState({
    equityCurves: null,
    performanceMetrics: null,
    assetScores: null,
    lastUpdated: null,
    lastRunElapsedTime: null,
    lastRunElapsedTimeFormatted: null,
    lastRunTimestamp: null
  });

  // Get current date in YYYY-MM-DD format for default end date
  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // State for strategy parameters
  const [strategyParams, setStrategyParams] = useState({
    use_mtpi_signal: true,
    mtpi_indicator_type: 'PGO',
    mtpi_timeframe: '1d',
    mtpi_pgo_length: 35,
    mtpi_upper_threshold: 1.1,
    mtpi_lower_threshold: -0.58,
    timeframe: '1d',
    analysis_start_date: '2021-02-15',
    analysis_end_date: getCurrentDate(), // Initialize with current date
    save_png: false,
    transaction_fee_rate: 0.001,
    initial_capital: 10000,
    web_mode: true,
    selected_assets: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
    // Allocation mode parameters
    n_assets: 1, // Default to 1 (best asset)
    use_weighted_allocation: false, // Default to equal allocation
    weights: null // Custom weights for weighted allocation
  });

  // Function to update dashboard data
  const updateDashboardData = (newData) => {
    const now = new Date();
    const updatedData = {
      ...newData,
      lastUpdated: now.toISOString()
    };

    // Add timestamp if this is a strategy run with elapsed time
    if (newData.lastRunElapsedTimeFormatted) {
      updatedData.lastRunTimestamp = now.toISOString();
    }

    setDashboardData(updatedData);

    // Save elapsed time and timestamp to localStorage if available
    if (newData.lastRunElapsedTimeFormatted) {
      try {
        localStorage.setItem('lastRunElapsedTime', newData.lastRunElapsedTime);
        localStorage.setItem('lastRunElapsedTimeFormatted', newData.lastRunElapsedTimeFormatted);
        localStorage.setItem('lastRunTimestamp', updatedData.lastRunTimestamp);
      } catch (error) {
        console.error('Error saving elapsed time to localStorage:', error);
      }
    }

    console.log('Dashboard data updated in context:', updatedData);
  };

  // Function to update strategy parameters
  const updateStrategyParams = (newParams) => {
    setStrategyParams(prev => ({
      ...prev,
      ...newParams
    }));
    console.log('Strategy parameters updated in context:', newParams);
  };

  // Load elapsed time from localStorage on mount
  useEffect(() => {
    try {
      const savedElapsedTime = localStorage.getItem('lastRunElapsedTime');
      const savedElapsedTimeFormatted = localStorage.getItem('lastRunElapsedTimeFormatted');
      const savedTimestamp = localStorage.getItem('lastRunTimestamp');

      if (savedElapsedTimeFormatted) {
        setDashboardData(prev => ({
          ...prev,
          lastRunElapsedTime: savedElapsedTime ? parseFloat(savedElapsedTime) : null,
          lastRunElapsedTimeFormatted: savedElapsedTimeFormatted,
          lastRunTimestamp: savedTimestamp
        }));
        console.log('Loaded elapsed time from localStorage:', savedElapsedTimeFormatted);
      }
    } catch (error) {
      console.error('Error loading elapsed time from localStorage:', error);
    }
  }, []);

  // Value object to be provided to consumers
  const value = {
    dashboardData,
    strategyParams,
    updateDashboardData,
    updateStrategyParams
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};
