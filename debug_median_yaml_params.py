#!/usr/bin/env python3
"""
Debug script to trace exactly what parameters are being used for median score
when loaded through the MTPI system vs direct calculation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score, generate_median_score_signal
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config, fetch_multi_indicator_mtpi_signal
from src.config_manager import load_config

def debug_median_yaml_params():
    """Debug median score parameter loading from YAML."""
    print("=" * 80)
    print("MEDIAN SCORE YAML PARAMETER DEBUGGING")
    print("=" * 80)
    
    # 1. Load raw YAML config
    print("\n1. RAW YAML CONFIG:")
    print("-" * 40)
    raw_config = load_config()
    settings = raw_config.get('settings', {})
    mtpi_indicators = settings.get('mtpi_indicators', {})
    median_config_yaml = mtpi_indicators.get('median_score', {})
    
    print(f"Raw YAML median_score config: {median_config_yaml}")
    
    # 2. Load through MTPI config loader
    print("\n2. MTPI CONFIG LOADER:")
    print("-" * 40)
    mtpi_config = load_mtpi_multi_indicator_config()
    median_config_mtpi = mtpi_config.get('median_score', {})
    
    print(f"MTPI loaded median_score config: {median_config_mtpi}")
    
    # 3. Check enabled indicators
    enabled_indicators = mtpi_config.get('enabled_indicators', [])
    print(f"Enabled indicators: {enabled_indicators}")
    print(f"Is median_score enabled? {'median_score' in enabled_indicators}")
    
    # 4. Fetch BTC data for testing
    print("\n3. FETCHING TEST DATA:")
    print("-" * 40)
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-05-01'
    )
    btc_df = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_df)} candles")
    
    # 5. Test direct calculation with YAML params
    print("\n4. DIRECT CALCULATION WITH YAML PARAMS:")
    print("-" * 40)
    
    yaml_params = {
        'atr_period': median_config_yaml.get('atr_period', 12),
        'multiplier': median_config_yaml.get('multiplier', 1.45),
        'median_length': median_config_yaml.get('median_length', 27),
        'src_col': median_config_yaml.get('src_col', 'high')
    }
    
    print(f"Using YAML params: {yaml_params}")
    
    signal_yaml, _, _ = calculate_median_score(btc_df, **yaml_params)
    
    # 6. Test through MTPI signal handler
    print("\n5. MTPI SIGNAL HANDLER CALCULATION:")
    print("-" * 40)
    
    # Check what parameters are actually being passed in the signal handler
    if 'median_score' in enabled_indicators:
        median_config = mtpi_config.get('median_score', {})
        actual_params = {
            'atr_period': median_config.get('atr_period', 12),
            'multiplier': median_config.get('multiplier', 1.45),
            'median_length': median_config.get('median_length', 27),
            'src_col': median_config.get('src_col', 'high')
        }
        
        print(f"MTPI handler params: {actual_params}")
        
        signal_mtpi = generate_median_score_signal(
            df=btc_df,
            **actual_params
        )
        
        # 7. Compare signals
        print("\n6. SIGNAL COMPARISON:")
        print("-" * 40)
        
        # Check if signals are identical
        signals_match = signal_yaml.equals(signal_mtpi)
        print(f"Signals match: {signals_match}")
        
        if not signals_match:
            print("SIGNALS DIFFER!")
            diff_indices = signal_yaml != signal_mtpi
            diff_count = diff_indices.sum()
            print(f"Number of differing signals: {diff_count}")
            
            if diff_count > 0:
                print("First 5 differences:")
                diff_dates = signal_yaml[diff_indices].head(5)
                for date in diff_dates.index:
                    yaml_val = signal_yaml.loc[date]
                    mtpi_val = signal_mtpi.loc[date]
                    print(f"  {date.strftime('%Y-%m-%d')}: YAML={yaml_val}, MTPI={mtpi_val}")
        else:
            print("✅ Signals are identical!")
        
        # 8. Check recent signals
        print("\n7. RECENT SIGNALS:")
        print("-" * 40)
        
        recent_yaml = signal_yaml.tail(10)
        recent_mtpi = signal_mtpi.tail(10)
        
        print("Last 10 signals:")
        for i in range(-10, 0):
            date = btc_df.index[i].strftime('%Y-%m-%d')
            yaml_sig = signal_yaml.iloc[i]
            mtpi_sig = signal_mtpi.iloc[i]
            match = "✅" if yaml_sig == mtpi_sig else "❌"
            print(f"  {date}: YAML={yaml_sig}, MTPI={mtpi_sig} {match}")
    
    else:
        print("❌ median_score is not enabled in MTPI config!")
    
    # 9. Test the full MTPI signal fetch
    print("\n8. FULL MTPI SIGNAL FETCH TEST:")
    print("-" * 40)
    
    try:
        # This should use the YAML parameters
        mtpi_signals = fetch_multi_indicator_mtpi_signal(
            timeframe='1d',
            enabled_indicators=['median_score'],
            limit=100
        )
        
        print(f"MTPI fetch result: {mtpi_signals}")
        
        if 'median_score' in mtpi_signals:
            print(f"Median score signal from MTPI fetch: {mtpi_signals['median_score']}")
        else:
            print("❌ No median_score in MTPI fetch result!")
            
    except Exception as e:
        print(f"❌ Error in MTPI fetch: {e}")
    
    # 10. Summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    print(f"YAML config found: {bool(median_config_yaml)}")
    print(f"MTPI config loaded: {bool(median_config_mtpi)}")
    print(f"Median enabled: {'median_score' in enabled_indicators}")
    
    if median_config_yaml:
        print(f"YAML parameters:")
        for key, value in yaml_params.items():
            print(f"  {key}: {value}")
    
    if median_config_mtpi:
        print(f"MTPI parameters:")
        for key, value in actual_params.items():
            print(f"  {key}: {value}")
    
    # Check for parameter mismatches
    if median_config_yaml and median_config_mtpi:
        param_match = yaml_params == actual_params
        print(f"Parameters match: {param_match}")
        
        if not param_match:
            print("Parameter differences:")
            for key in yaml_params:
                if yaml_params[key] != actual_params.get(key):
                    print(f"  {key}: YAML={yaml_params[key]}, MTPI={actual_params.get(key)}")

if __name__ == "__main__":
    debug_median_yaml_params()
