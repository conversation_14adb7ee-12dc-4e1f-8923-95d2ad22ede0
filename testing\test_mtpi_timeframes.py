# test_mtpi_timeframes.py
# Script to test MTPI signals with different timeframes

import pandas as pd
import logging
from src.MTPI_signal_handler import fetch_mtpi_signal, fetch_historical_mtpi_signals

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_mtpi_timeframes():
    """
    Tests MTPI signal calculation with different timeframes.
    """
    # List of timeframes to test - reduced list for quicker testing
    timeframes = ['1d', '4h']
    
    print("\n===== Testing MTPI signals with different timeframes =====")
    
    for timeframe in timeframes:
        print(f"\n----- Testing {timeframe} timeframe -----")
        
        # Get current MTPI signal
        signal = fetch_mtpi_signal(
            timeframe=timeframe,
            length=35,  # Standard PGO Length
            upper_threshold=1.1,  # Standard threshold
            lower_threshold=-0.58,  # Standard threshold
            skip_warmup=False  # Set to False to ensure signals from the beginning
        )
        
        print(f"Current MTPI signal for {timeframe}: {signal}")
        
        # Get historical signals
        historical_signals = fetch_historical_mtpi_signals(
            timeframe=timeframe,
            length=35,
            upper_threshold=1.1,
            lower_threshold=-0.58,
            limit=100,  # Smaller limit for testing
            skip_warmup=False  # Set to False to ensure signals from the beginning
        )
        
        if historical_signals is not None:
            # Count signal distribution
            signal_counts = historical_signals.value_counts()
            print(f"Historical signal distribution for {timeframe}:")
            print(f"  - Bullish (1): {signal_counts.get(1, 0)}")
            print(f"  - Bearish (-1): {signal_counts.get(-1, 0)}")
            print(f"  - Neutral (0): {signal_counts.get(0, 0)}")
            
            # Print last few signals
            print(f"\nLast 5 signals for {timeframe}:")
            last_signals = pd.DataFrame({'Date': historical_signals.index, 'Signal': historical_signals.values})
            print(last_signals.tail(5).to_string(index=False))
        else:
            print(f"Failed to fetch historical signals for {timeframe}")

if __name__ == "__main__":
    test_mtpi_timeframes() 