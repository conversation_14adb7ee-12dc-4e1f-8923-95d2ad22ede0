import React from 'react';
import {
  Box,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  Spinner,
  useColorModeValue,
  Flex,
  Icon,
} from '@chakra-ui/react';
import { TriangleUpIcon, TriangleDownIcon } from '@chakra-ui/icons';

const TradeHistory = ({ trades, isLoading }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  
  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get color based on trade side
  const getSideColor = (side) => {
    return side.toLowerCase() === 'buy' ? 'green' : 'red';
  };
  
  if (isLoading) {
    return (
      <Box 
        bg={bgColor} 
        p={4} 
        borderRadius="lg" 
        boxShadow="sm"
        height="100%"
      >
        <Flex justify="center" align="center" height="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
    >
      {trades && trades.length > 0 ? (
        <Box overflowX="auto">
          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>Date</Th>
                <Th>Symbol</Th>
                <Th>Type</Th>
                <Th isNumeric>Price</Th>
                <Th isNumeric>Amount</Th>
                <Th isNumeric>Cost</Th>
                <Th isNumeric>Fee</Th>
              </Tr>
            </Thead>
            <Tbody>
              {trades.map((trade) => (
                <Tr key={trade.id}>
                  <Td fontSize="xs">{formatDate(trade.datetime)}</Td>
                  <Td>{trade.symbol}</Td>
                  <Td>
                    <Badge 
                      colorScheme={getSideColor(trade.side)} 
                      display="flex" 
                      alignItems="center" 
                      width="fit-content"
                    >
                      {trade.side.toLowerCase() === 'buy' ? (
                        <Icon as={TriangleUpIcon} mr={1} />
                      ) : (
                        <Icon as={TriangleDownIcon} mr={1} />
                      )}
                      {trade.side}
                    </Badge>
                  </Td>
                  <Td isNumeric>{formatCurrency(trade.price)}</Td>
                  <Td isNumeric>{trade.amount.toFixed(6)}</Td>
                  <Td isNumeric>{formatCurrency(trade.cost)}</Td>
                  <Td isNumeric>{trade.fee ? formatCurrency(trade.fee.cost) : '-'}</Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      ) : (
        <Flex 
          justify="center" 
          align="center" 
          height="200px"
          border="1px dashed"
          borderColor="gray.200"
          borderRadius="md"
        >
          <Text color="gray.500">No trade history available</Text>
        </Flex>
      )}
    </Box>
  );
};

export default TradeHistory;
