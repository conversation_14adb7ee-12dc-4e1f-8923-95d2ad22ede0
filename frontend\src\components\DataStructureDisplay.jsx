import React, { useEffect, useState } from 'react';
import axios from 'axios';

// This component displays the raw data structure from the backend
const DataStructureDisplay = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  // Fetch data directly from the backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching data for structure display...');
        const response = await axios.get('/api/equity');
        console.log('Data received for structure display');
        setData(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="loading">Loading data structure...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  // Function to format a data point for display
  const formatDataPoint = (point) => {
    return JSON.stringify(point, null, 2);
  };

  // Function to format a date
  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toISOString().split('T')[0];
  };

  return (
    <div className="data-structure-display" style={{ padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
      <h2 style={{ textAlign: 'center', marginBottom: '20px' }}>Backend Data Structure</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Strategy Data Points</h3>
        <p>Total points: {data.curves?.strategy?.length || 0}</p>
        {data.curves?.strategy && data.curves.strategy.length > 0 && (
          <div>
            <h4>First Data Point:</h4>
            <pre style={{ backgroundColor: '#eee', padding: '10px', borderRadius: '5px', overflow: 'auto' }}>
              {formatDataPoint(data.curves.strategy[0])}
            </pre>
            <p>Has color property: {data.curves.strategy[0].color !== undefined ? 'Yes' : 'No'}</p>
          </div>
        )}
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Asset Changes</h3>
        <p>Total asset changes: {data.assetChanges?.length || 0}</p>
        {data.assetChanges && data.assetChanges.length > 0 && (
          <div>
            <h4>First 5 Asset Changes:</h4>
            <ul style={{ listStyleType: 'none', padding: 0 }}>
              {[...data.assetChanges]
                .sort((a, b) => a.date - b.date)
                .slice(0, 5)
                .map((change, index) => (
                  <li key={index} style={{ marginBottom: '5px' }}>
                    {formatDate(change.date)}: {change.asset || 'out-of-market'}
                  </li>
                ))}
            </ul>
          </div>
        )}
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>MTPI Signals</h3>
        <p>Total MTPI signals: {data.metadata?.rawMtpiSignals?.length || 0}</p>
        {data.metadata?.rawMtpiSignals && data.metadata.rawMtpiSignals.length > 0 && (
          <div>
            <h4>First 5 MTPI Signals:</h4>
            <ul style={{ listStyleType: 'none', padding: 0 }}>
              {data.metadata.rawMtpiSignals
                .slice(0, 5)
                .map((signal, index) => (
                  <li key={index} style={{ marginBottom: '5px' }}>
                    {formatDate(signal.time)}: {signal.value} ({signal.value === 1 ? 'Bullish' : signal.value === 0 ? 'Neutral' : 'Bearish'})
                  </li>
                ))}
            </ul>
          </div>
        )}
      </div>
      
      <div>
        <h3>Data Structure Summary</h3>
        <ul style={{ listStyleType: 'none', padding: 0 }}>
          <li>Strategy data includes color information: {data.curves?.strategy?.[0]?.color !== undefined ? 'Yes' : 'No'}</li>
          <li>Strategy data includes asset information: {data.curves?.strategy?.[0]?.asset !== undefined ? 'Yes' : 'No'}</li>
          <li>Strategy data includes MTPI information: {data.curves?.strategy?.[0]?.mtpi !== undefined ? 'Yes' : 'No'}</li>
          <li>Asset changes are provided separately: {data.assetChanges !== undefined ? 'Yes' : 'No'}</li>
          <li>MTPI signals are provided separately: {data.metadata?.rawMtpiSignals !== undefined ? 'Yes' : 'No'}</li>
        </ul>
      </div>
    </div>
  );
};

export default DataStructureDisplay;
