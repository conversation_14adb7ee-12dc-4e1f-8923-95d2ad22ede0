#!/usr/bin/env python3
"""
Debug script to trace DWMA parameter flow and identify differences with TradingView.
This script will help identify why DWMA signals differ between our implementation and TradingView.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.dwma_score import calculate_dwma_score, generate_dwma_signal
from src.config_manager import load_config
# from src.MTPI_signal_handler import get_mtpi_config_from_yaml

def debug_parameter_flow():
    """Debug the parameter flow from YAML to DWMA calculation."""
    print("=" * 80)
    print("DWMA PARAMETER FLOW DEBUG")
    print("=" * 80)
    
    # Step 1: Load YAML configuration
    print("\n1. Loading YAML Configuration:")
    print("-" * 40)
    
    config = load_config()
    mtpi_config = config.get('settings', {}).get('mtpi_indicators', {})
    dwma_config = mtpi_config.get('dwma_score', {})
    
    print("Raw YAML dwma_score configuration:")
    for key, value in dwma_config.items():
        print(f"  {key}: {value} ({type(value).__name__})")
    
    # Step 2: Check if configuration is complete
    print("\n2. Configuration Completeness Check:")
    print("-" * 40)

    required_params = [
        'smoothing_style', 'src_col', 'length', 'ma_type', 'ma_smooth_length',
        'sd_length', 'upper_sd_weight', 'lower_sd_weight',
        'atr_period', 'atr_multiplier', 'loop_start', 'loop_end',
        'long_threshold', 'short_threshold'
    ]

    missing_params = []
    for param in required_params:
        if param not in dwma_config:
            missing_params.append(param)
            print(f"  {param}: MISSING ❌")
        else:
            print(f"  {param}: {dwma_config[param]} ✅")

    if missing_params:
        print(f"\nFound {len(missing_params)} missing parameters: {missing_params}")
        return False
    else:
        print("\nAll required parameters present! ✅")

    # Step 3: Fetch test data
    print("\n3. Fetching Test Data:")
    print("-" * 40)
    
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-12-01'  # Recent data
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Fetched {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Step 4: Test parameter extraction for MTPI signal handler
    print("\n4. Testing MTPI Signal Handler Parameter Extraction:")
    print("-" * 40)
    
    # Simulate exactly what MTPI signal handler does
    extracted_params = {
        'smoothing_style': dwma_config.get('smoothing_style', 'Weighted SD'),
        'src_col': dwma_config.get('src_col', 'close'),
        'length': dwma_config.get('length', 17),
        'ma_type': dwma_config.get('ma_type', 'EMA'),
        'ma_smooth_length': dwma_config.get('ma_smooth_length', 12),
        'sd_length': dwma_config.get('sd_length', 33),
        'upper_sd_weight': dwma_config.get('upper_sd_weight', 1.031),
        'lower_sd_weight': dwma_config.get('lower_sd_weight', 0.996),
        'atr_period': dwma_config.get('atr_period', 12),
        'atr_multiplier': dwma_config.get('atr_multiplier', 1.0),
        'loop_start': dwma_config.get('loop_start', 1),
        'loop_end': dwma_config.get('loop_end', 60),
        'long_threshold': dwma_config.get('long_threshold', 30),
        'short_threshold': dwma_config.get('short_threshold', 0)
    }
    
    print("Parameters extracted by MTPI signal handler:")
    for key, value in extracted_params.items():
        print(f"  {key}: {value} ({type(value).__name__})")
    
    # Step 5: Test direct calculation
    print("\n5. Testing Direct DWMA Calculation:")
    print("-" * 40)
    
    try:
        signal_direct, dwma_direct, dwmas_direct = calculate_dwma_score(
            df=btc_df,
            **extracted_params
        )
        print(f"Direct calculation successful!")
        print(f"Signal range: {signal_direct.min()} to {signal_direct.max()}")
        print(f"Last 5 signals: {signal_direct.iloc[-5:].tolist()}")
        
    except Exception as e:
        print(f"Direct calculation failed: {e}")
        return False
    
    # Step 6: Test via generate_dwma_signal function
    print("\n6. Testing via generate_dwma_signal Function:")
    print("-" * 40)
    
    try:
        signal_function = generate_dwma_signal(
            df=btc_df,
            **extracted_params
        )
        print(f"Function calculation successful!")
        print(f"Signal range: {signal_function.min()} to {signal_function.max()}")
        print(f"Last 5 signals: {signal_function.iloc[-5:].tolist()}")
        
    except Exception as e:
        print(f"Function calculation failed: {e}")
        return False
    
    # Step 7: Compare results
    print("\n7. Comparing Calculation Results:")
    print("-" * 40)
    
    signals_match = signal_direct.equals(signal_function)
    print(f"Direct vs Function signals match: {signals_match}")
    
    if not signals_match:
        diff_mask = signal_direct != signal_function
        diff_count = diff_mask.sum()
        print(f"Number of differences: {diff_count}")
        
        if diff_count > 0:
            print("First 5 differences:")
            diff_indices = diff_mask[diff_mask].index[:5]
            for idx in diff_indices:
                date_str = idx.strftime('%Y-%m-%d')
                direct_val = signal_direct.loc[idx]
                func_val = signal_function.loc[idx]
                print(f"  {date_str}: Direct={direct_val}, Function={func_val}")
    
    # Step 8: Show recent signals for manual verification
    print("\n8. Recent Signals for Manual Verification:")
    print("-" * 40)
    
    recent_data = pd.DataFrame({
        'Date': btc_df.index[-10:].strftime('%Y-%m-%d'),
        'Close': btc_df['close'].iloc[-10:].round(2),
        'Signal': signal_direct.iloc[-10:],
        'DWMA': dwma_direct.iloc[-10:].round(4),
        'DWMAS': dwmas_direct.iloc[-10:].round(4)
    })
    
    print(recent_data.to_string(index=False))
    
    # Step 9: Check for parameter type issues
    print("\n9. Parameter Type Verification:")
    print("-" * 40)
    
    expected_types = {
        'smoothing_style': str,
        'src_col': str,
        'length': int,
        'ma_type': str,
        'ma_smooth_length': int,
        'sd_length': int,
        'upper_sd_weight': float,
        'lower_sd_weight': float,
        'atr_period': int,
        'atr_multiplier': float,
        'loop_start': int,
        'loop_end': int,
        'long_threshold': int,
        'short_threshold': int
    }
    
    type_issues = []
    for param, expected_type in expected_types.items():
        if param in extracted_params:
            actual_value = extracted_params[param]
            actual_type = type(actual_value)
            
            if actual_type != expected_type:
                type_issues.append(param)
                print(f"  {param}: Expected {expected_type.__name__}, got {actual_type.__name__} ({actual_value}) ❌")
            else:
                print(f"  {param}: {expected_type.__name__} ✅")
    
    if type_issues:
        print(f"\nFound {len(type_issues)} type issues: {type_issues}")
        return False
    else:
        print("\nAll parameter types are correct! ✅")
    
    return True

if __name__ == '__main__':
    success = debug_parameter_flow()
    
    if success:
        print("\n" + "=" * 80)
        print("✅ PARAMETER FLOW DEBUG COMPLETED SUCCESSFULLY")
        print("✅ No issues found in parameter flow from YAML to calculation")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ PARAMETER FLOW DEBUG FOUND ISSUES")
        print("❌ Check the output above for specific problems")
        print("=" * 80)
