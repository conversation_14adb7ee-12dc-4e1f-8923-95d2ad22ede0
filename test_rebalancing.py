#!/usr/bin/env python3
"""
Test script to demonstrate the new portfolio drift rebalancing functionality.

This script shows how the strategy now automatically rebalances when portfolio weights
drift beyond the specified threshold due to market movements.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_program import AllocationTester

def test_rebalancing_functionality():
    """Test the new rebalancing functionality with different thresholds."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=" * 80)
    print("TESTING PORTFOLIO DRIFT REBALANCING FUNCTIONALITY")
    print("=" * 80)
    
    # Test parameters
    test_configs = [
        {
            "name": "No Rebalancing (Traditional Mode)",
            "enable_rebalancing": False,
            "rebalance_threshold": 0.05,
            "weights": [0.8, 0.2],
            "description": "Traditional behavior - only rebalances when asset selection changes"
        },
        {
            "name": "Conservative Rebalancing (10% threshold)",
            "enable_rebalancing": True,
            "rebalance_threshold": 0.10,
            "weights": [0.8, 0.2],
            "description": "Only rebalances when drift exceeds 10%"
        },
        {
            "name": "Standard Rebalancing (5% threshold)",
            "enable_rebalancing": True,
            "rebalance_threshold": 0.05,
            "weights": [0.8, 0.2],
            "description": "Rebalances when drift exceeds 5% (recommended)"
        },
        {
            "name": "Aggressive Rebalancing (2% threshold)",
            "enable_rebalancing": True,
            "rebalance_threshold": 0.02,
            "weights": [0.8, 0.2],
            "description": "Rebalances when drift exceeds 2%"
        }
    ]
    
    for config in test_configs:
        print(f"\n{'-' * 60}")
        print(f"TEST: {config['name']}")
        print(f"Description: {config['description']}")
        print(f"Weights: {config['weights']}")
        print(f"Rebalancing Enabled: {config['enable_rebalancing']}")
        print(f"Rebalance Threshold: {config['rebalance_threshold']:.1%}")
        print(f"{'-' * 60}")
        
        try:
            # Create the tester with rebalancing enabled
            tester = AllocationTester(
                timeframe='1d',
                mtpi_timeframe='1d',
                analysis_start_date='2025-05-20',
                n_assets=2,
                transaction_fee_rate=0.001,
                selected_assets=['BTC/USDT', 'SOL/USDT', 'ETH/USDT'],
                use_cache=True,
                initial_capital=10000,
                wait_for_confirmed_signals=True,
                use_mtpi=True,
                use_weighted_allocation=True,
                weights=config['weights'],
                enable_rebalancing=config['enable_rebalancing'],
                rebalance_threshold=config['rebalance_threshold'],
                trend_method='PGO For Loop'
            )
            
            print("Fetching data...")
            tester.fetch_data()
            
            print("Fetching MTPI signals...")
            tester.fetch_mtpi_signals()
            
            print("Calculating scores...")
            tester.calculate_scores()
            
            print("Running strategy with rebalancing...")
            tester.run_strategy_with_detailed_logging()
            
            # Analyze the results
            print("\nAnalyzing rebalancing events...")
            rebalancing_events = 0
            drift_rebalances = 0
            asset_change_rebalances = 0
            
            for entry in tester.allocation_history:
                if entry.get('rebalanced', False):
                    rebalancing_events += 1
                    reason = entry.get('rebalancing_reason', '')
                    if 'drift' in reason.lower():
                        drift_rebalances += 1
                    elif 'asset selection changed' in reason.lower():
                        asset_change_rebalances += 1
            
            print(f"Total rebalancing events: {rebalancing_events}")
            print(f"  - Due to portfolio drift: {drift_rebalances}")
            print(f"  - Due to asset selection changes: {asset_change_rebalances}")
            
            # Calculate final performance
            if tester.strategy_equity_curve is not None:
                final_equity = tester.strategy_equity_curve.dropna().iloc[-1]
                initial_equity = tester.strategy_equity_curve.dropna().iloc[0]
                total_return = ((final_equity / initial_equity) - 1) * 100
                print(f"Total return: {total_return:.2f}%")
                
                # Calculate total transaction costs
                total_fees = sum(entry.get('transaction_cost', 0) for entry in tester.allocation_history)
                total_fees_pct = (total_fees / initial_equity) * 100
                print(f"Total transaction costs: {total_fees_pct:.3f}% of initial capital")
            
            print(f"✅ Test completed successfully for {config['name']}")
            
        except Exception as e:
            print(f"❌ Test failed for {config['name']}: {str(e)}")
            logging.error(f"Error in test {config['name']}: {e}", exc_info=True)
    
    print(f"\n{'=' * 80}")
    print("REBALANCING FUNCTIONALITY TESTS COMPLETED")
    print("=" * 80)
    print("\nKey Features Implemented:")
    print("✅ Portfolio drift detection based on market movements")
    print("✅ Configurable rebalance threshold (--rebalance-threshold)")
    print("✅ Automatic rebalancing when drift exceeds threshold")
    print("✅ Transaction cost calculation for rebalancing trades")
    print("✅ Detailed logging of rebalancing events and reasons")
    print("✅ Integration with both main_program.py and background_service.py")
    
    print(f"\nUsage Examples:")
    print("# Traditional mode (no automatic rebalancing)")
    print("py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --weighted --weights 0.8 0.2")
    print("\n# Enable rebalancing with conservative threshold (10%)")
    print("py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.10")
    print("\n# Enable rebalancing with standard threshold (5%)")
    print("py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.05")
    print("\n# Enable rebalancing with aggressive threshold (2%)")
    print("py main_program.py --assets BTC/USDT SOL/USDT ETH/USDT --weighted --weights 0.8 0.2 --enable-rebalancing --rebalance-threshold 0.02")

if __name__ == "__main__":
    test_rebalancing_functionality()
