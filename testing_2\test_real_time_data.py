#!/usr/bin/env python
"""
Test script for real-time data fetching and appending to cache.

This script demonstrates how to properly check for up-to-date data and fetch only the missing data.
"""

import os
import sys
import logging
import pandas as pd
import time
import shutil
from datetime import datetime, timedelta
import ccxt

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.data_cache import (
    append_to_cache,
    load_from_cache,
    get_cache_path
)
from src.utils import setup_logging

def setup_test():
    """Set up the test environment."""
    setup_logging(level=logging.INFO)
    
    print("\n" + "=" * 70)
    print("TESTING REAL-TIME DATA FETCHING AND APPENDING TO CACHE")
    print("=" * 70)
    
    # Test parameters
    exchange_id = 'binance'
    symbols = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC']
    timeframe = '1m'
    
    # Create backup of original files
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"
        
        if os.path.exists(cache_path):
            print(f"Creating backup of {cache_path} to {backup_path}")
            shutil.copy2(cache_path, backup_path)
    
    return exchange_id, symbols, timeframe

def restore_backups(exchange_id, symbols, timeframe):
    """Restore original files from backups."""
    print("\nRestoring original files from backups...")
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"
        
        if os.path.exists(backup_path):
            print(f"Restoring {cache_path} from {backup_path}")
            shutil.copy2(backup_path, cache_path)
            os.remove(backup_path)

def check_and_fetch_missing_data(exchange, exchange_id, symbol, timeframe):
    """Check if we have up-to-date data and fetch missing data if needed."""
    print(f"\nChecking for missing data for {symbol}...")
    
    # Load current data from cache
    current_data = load_from_cache(exchange_id, symbol, timeframe)
    if current_data is None or current_data.empty:
        print(f"No data found for {symbol}, skipping...")
        return None
    
    # Get the last timestamp in the cache
    last_timestamp = current_data.index.max()
    print(f"Last timestamp in cache: {last_timestamp}")
    
    # Calculate the expected current timestamp (rounded down to the nearest minute)
    current_time = datetime.now()
    current_minute = current_time.replace(second=0, microsecond=0)
    
    # For 1m timeframe, we should have data up to the previous minute
    expected_last_timestamp = current_minute - timedelta(minutes=1)
    
    # Make sure expected_last_timestamp is timezone-aware
    if expected_last_timestamp.tzinfo is None:
        expected_last_timestamp = pd.to_datetime(expected_last_timestamp, utc=True)
    
    print(f"Expected last timestamp: {expected_last_timestamp}")
    
    # Check if we need to fetch missing data
    if last_timestamp < expected_last_timestamp:
        print(f"Missing data detected. Need to fetch data from {last_timestamp} to {expected_last_timestamp}")
        
        # Convert timestamp to milliseconds for CCXT
        since_ms = int(pd.to_datetime(last_timestamp).timestamp() * 1000)
        
        try:
            # Fetch candles
            candles = exchange.fetch_ohlcv(symbol, timeframe, since=since_ms, limit=1000)
            
            if not candles:
                print(f"No candles available for {symbol}")
                return None
                
            print(f"Fetched {len(candles)} candles for {symbol}")
            
            # Convert to DataFrame
            columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            df = pd.DataFrame(candles, columns=columns)
            
            # Convert timestamp to datetime with UTC timezone
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            
            # Set timestamp as index
            df.set_index('timestamp', inplace=True)
            
            # Remove any data that's already in the cache
            # Convert last_timestamp to UTC timezone-aware datetime for comparison
            last_ts = pd.to_datetime(last_timestamp)
            if last_ts.tzinfo is None:
                last_ts = last_ts.tz_localize('UTC')
                
            # Now we can safely compare
            df = df[df.index > last_ts]
            
            if df.empty:
                print(f"No new data to append for {symbol}")
                return None
                
            print(f"Prepared {len(df)} new candles for {symbol} from {df.index.min()} to {df.index.max()}")
            
            # Append to cache
            append_success = append_to_cache(df, exchange_id, symbol, timeframe)
            
            if append_success:
                print(f"✅ Successfully appended {len(df)} candles for {symbol}")
                
                # Load the data again to verify
                updated_data = load_from_cache(exchange_id, symbol, timeframe)
                print(f"Cache now has {len(updated_data)} rows")
                print(f"New last timestamp: {updated_data.index.max()}")
                
                # Show the last few rows
                print("\nLast 5 rows of the updated file:")
                print(updated_data.tail(5))
                
                return updated_data
            else:
                print(f"❌ Failed to append data for {symbol}")
                return None
            
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None
    else:
        print(f"Data is up to date for {symbol}")
        return current_data

def main():
    """Main function to run the test."""
    try:
        # Set up the test
        exchange_id, symbols, timeframe = setup_test()
        
        # Initialize the exchange
        exchange = ccxt.binance()
        
        # Process each symbol
        for symbol in symbols:
            try:
                # Check and fetch missing data
                updated_data = check_and_fetch_missing_data(exchange, exchange_id, symbol, timeframe)
                
                if updated_data is not None:
                    print(f"\nSuccessfully processed {symbol}")
                else:
                    print(f"\nFailed to process {symbol}")
                
            except Exception as e:
                print(f"Error processing {symbol}: {e}")
        
        print("\nTest completed successfully")
        
    except Exception as e:
        print(f"Error in test: {e}")
    finally:
        # Restore original files
        restore_backups(exchange_id, symbols, timeframe)
        print("\nTest completed and original files restored")

if __name__ == "__main__":
    main()
