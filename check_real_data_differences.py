#!/usr/bin/env python3
"""
Check the actual differences between daily close and 12PM close prices in real data.
"""

import sys
import os
import pandas as pd

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data

def check_real_data_differences():
    """Check real data to see how different 12PM and daily close prices are."""
    
    print("=" * 80)
    print("CHECKING REAL DATA DIFFERENCES")
    print("=" * 80)
    
    symbol = 'BTC/USDT'
    
    # Fetch daily data
    print("Fetching daily data...")
    daily_data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=[symbol],
        timeframe='1d',
        since='2024-06-01',
        use_cache=True
    )
    daily_data = daily_data_dict[symbol]
    
    # Fetch 12h data
    print("Fetching 12h data...")
    twelve_h_data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=[symbol],
        timeframe='12h',
        since='2024-06-01',
        use_cache=True
    )
    twelve_h_data = twelve_h_data_dict[symbol]
    
    print(f"Daily data: {len(daily_data)} rows")
    print(f"12h data: {len(twelve_h_data)} rows")
    
    # Check the structure of 12h candles more carefully
    print("\nAnalyzing 12h candle structure:")
    print("First 10 12h candles with OHLC:")
    for i in range(min(10, len(twelve_h_data))):
        row = twelve_h_data.iloc[i]
        date = twelve_h_data.index[i]
        print(f"  {date} | O:{row['open']:.2f} H:{row['high']:.2f} L:{row['low']:.2f} C:{row['close']:.2f}")

    # Filter 12h data to get only 12:00 PM UTC candles (candles that START at 12:00)
    twelve_pm_candles = twelve_h_data[twelve_h_data.index.hour == 12]
    print(f"\n12 PM candles (starting at 12:00): {len(twelve_pm_candles)} rows")

    # Also get 00:00 candles for comparison
    midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0]
    print(f"Midnight candles (starting at 00:00): {len(midnight_candles)} rows")

    # Compare prices for the same dates
    print("\nComparing daily close vs 12PM candle close vs midnight candle close:")
    print(f"{'Date':<12} {'Daily Close':<12} {'12PM Close':<12} {'00:00 Close':<12} {'12PM Diff':<10} {'00:00 Diff':<10}")
    print("-" * 85)

    differences_12pm = []
    differences_00 = []
    for i in range(min(10, len(daily_data))):  # First 10 days
        date = daily_data.index[i]
        daily_close = daily_data['close'].iloc[i]

        # Find corresponding 12PM candle (starts at 12:00, closes at 00:00 next day)
        twelve_pm_date = date.replace(hour=12, minute=0, second=0, microsecond=0)

        # Find corresponding 00:00 candle (starts at 00:00, closes at 12:00 same day)
        midnight_date = date.replace(hour=0, minute=0, second=0, microsecond=0)

        twelve_pm_close = "N/A"
        midnight_close = "N/A"
        twelve_pm_diff = "N/A"
        midnight_diff = "N/A"

        if twelve_pm_date in twelve_pm_candles.index:
            twelve_pm_close = twelve_pm_candles.loc[twelve_pm_date, 'close']
            diff_12pm = twelve_pm_close - daily_close
            diff_12pm_pct = (diff_12pm / daily_close) * 100
            differences_12pm.append(abs(diff_12pm_pct))
            twelve_pm_diff = f"{diff_12pm_pct:>6.2f}%"
            twelve_pm_close = f"${twelve_pm_close:>9.2f}"

        if midnight_date in midnight_candles.index:
            midnight_close_val = midnight_candles.loc[midnight_date, 'close']
            diff_00 = midnight_close_val - daily_close
            diff_00_pct = (diff_00 / daily_close) * 100
            differences_00.append(abs(diff_00_pct))
            midnight_diff = f"{diff_00_pct:>6.2f}%"
            midnight_close = f"${midnight_close_val:>9.2f}"

        print(f"{date.date()} ${daily_close:>9.2f}   {twelve_pm_close:<12} {midnight_close:<12} {twelve_pm_diff:<10} {midnight_diff:<10}")
    
    print(f"\nSummary of price differences:")
    if differences_12pm:
        avg_diff_12pm = sum(differences_12pm) / len(differences_12pm)
        max_diff_12pm = max(differences_12pm)
        print(f"  12PM candle differences:")
        print(f"    Average absolute difference: {avg_diff_12pm:.4f}%")
        print(f"    Maximum absolute difference: {max_diff_12pm:.4f}%")
        print(f"    Number of comparisons: {len(differences_12pm)}")

    if differences_00:
        avg_diff_00 = sum(differences_00) / len(differences_00)
        max_diff_00 = max(differences_00)
        print(f"  Midnight candle differences:")
        print(f"    Average absolute difference: {avg_diff_00:.4f}%")
        print(f"    Maximum absolute difference: {max_diff_00:.4f}%")
        print(f"    Number of comparisons: {len(differences_00)}")

    print(f"\n💡 KEY INSIGHT:")
    print(f"   12h candles work as follows:")
    print(f"   - 00:00 candle: Opens at 00:00, closes at 12:00 (midday price)")
    print(f"   - 12:00 candle: Opens at 12:00, closes at 00:00 next day (daily close)")
    print(f"   For manual execution at 12PM, we need the CLOSE of the 00:00 candle!")
    print(f"   The current implementation is using the wrong candle.")
    
    # Check if 12PM is actually the middle of the day
    print(f"\nChecking 12h candle structure:")
    if not twelve_h_data.empty:
        sample_dates = twelve_h_data.index[:10]
        print(f"First 10 12h candle timestamps:")
        for date in sample_dates:
            print(f"  {date} (hour: {date.hour})")
    
    return {
        'daily_data': daily_data,
        'twelve_h_data': twelve_h_data,
        'twelve_pm_candles': twelve_pm_candles,
        'midnight_candles': midnight_candles,
        'avg_difference_12pm': avg_diff_12pm if differences_12pm else 0,
        'avg_difference_00': avg_diff_00 if differences_00 else 0
    }

if __name__ == "__main__":
    check_real_data_differences()
