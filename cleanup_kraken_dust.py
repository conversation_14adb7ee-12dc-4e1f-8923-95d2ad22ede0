#!/usr/bin/env python3
"""
<PERSON>ript to help clean up dust balances on Kraken that are preventing new trades.
This script will show you which balances are significant vs dust, and optionally
help you sell the dust balances.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading.account import AccountManager
from src.trading.order import OrderManager
import yaml

def setup_logging():
    """Setup logging for debugging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_config(config_path):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logging.error(f"Error loading config from {config_path}: {e}")
        return None

def analyze_dust_balances():
    """Analyze and categorize balances as significant vs dust."""
    
    print("=" * 80)
    print("KRAKEN DUST BALANCE CLEANUP ANALYSIS")
    print("=" * 80)
    
    config_path = 'config/settings_kraken_eur.yaml'
    
    # Load configuration
    config = load_config(config_path)
    if not config:
        print(f"❌ Failed to load config from {config_path}")
        return
    
    try:
        account_manager = AccountManager('kraken', config_path=config_path)
        
        # Get all balances
        all_balances = account_manager.get_all_balances()
        print(f"Total non-zero balances found: {len(all_balances)}")
        
        # Get open positions with values
        open_positions = account_manager.get_open_positions()
        
        # Define dust threshold (balances worth less than this are considered dust)
        dust_threshold_eur = 1.0  # €1.00
        
        significant_positions = []
        dust_positions = []
        
        print(f"\n📊 BALANCE ANALYSIS (Dust threshold: €{dust_threshold_eur})")
        print("=" * 60)
        
        for symbol, details in open_positions.items():
            amount = details['amount']
            current_price = details['current_price']
            value_eur = amount * current_price
            
            position_info = {
                'symbol': symbol,
                'amount': amount,
                'price': current_price,
                'value_eur': value_eur,
                'details': details
            }
            
            if value_eur >= dust_threshold_eur:
                significant_positions.append(position_info)
                status = "💰 SIGNIFICANT"
            else:
                dust_positions.append(position_info)
                status = "🧹 DUST"
            
            print(f"{status:15} {symbol:12} Amount: {amount:>15.8f} Price: €{current_price:>8.2f} Value: €{value_eur:>8.4f}")
        
        print(f"\n📈 SUMMARY:")
        print(f"  Significant positions (≥€{dust_threshold_eur}): {len(significant_positions)}")
        print(f"  Dust positions (<€{dust_threshold_eur}): {len(dust_positions)}")
        print(f"  Total positions: {len(open_positions)}")
        
        # Show significant positions
        if significant_positions:
            print(f"\n💰 SIGNIFICANT POSITIONS:")
            total_significant_value = 0
            for pos in significant_positions:
                print(f"  {pos['symbol']:12} €{pos['value_eur']:>8.2f}")
                total_significant_value += pos['value_eur']
            print(f"  {'TOTAL':12} €{total_significant_value:>8.2f}")
        
        # Show dust positions
        if dust_positions:
            print(f"\n🧹 DUST POSITIONS (blocking new trades):")
            total_dust_value = 0
            for pos in dust_positions:
                print(f"  {pos['symbol']:12} €{pos['value_eur']:>8.4f}")
                total_dust_value += pos['value_eur']
            print(f"  {'TOTAL':12} €{total_dust_value:>8.4f}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if len(dust_positions) > 0:
            print(f"1. 🧹 CLEAN UP DUST: You have {len(dust_positions)} dust positions worth €{sum(p['value_eur'] for p in dust_positions):.4f}")
            print(f"   These are preventing new trades because you're at the 10 position limit.")
            print(f"   Options:")
            print(f"   a) Manually sell these small amounts on Kraken")
            print(f"   b) Modify the system to ignore dust balances")
            print(f"   c) Increase max_open_positions in config")
        
        if len(significant_positions) > 1:
            print(f"2. 🎯 STRATEGY MISMATCH: You have {len(significant_positions)} significant positions")
            print(f"   Your strategy is configured for single-asset trading but you have multiple assets.")
            print(f"   Consider:")
            print(f"   a) Consolidating into one asset")
            print(f"   b) Switching to multi-asset strategy")
        
        # Check current config
        max_positions = config.get('risk_management', {}).get('max_open_positions', 1)
        print(f"\n⚙️  CURRENT CONFIG:")
        print(f"   Max open positions: {max_positions}")
        print(f"   Current positions: {len(open_positions)}")
        print(f"   Can trade: {'❌ NO' if len(open_positions) >= max_positions else '✅ YES'}")
        
        return {
            'significant_positions': significant_positions,
            'dust_positions': dust_positions,
            'total_positions': len(open_positions),
            'max_positions': max_positions
        }
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

def suggest_solutions(analysis_result):
    """Suggest specific solutions based on the analysis."""
    
    if not analysis_result:
        return
    
    print(f"\n" + "=" * 80)
    print("SPECIFIC SOLUTIONS")
    print("=" * 80)
    
    dust_count = len(analysis_result['dust_positions'])
    significant_count = len(analysis_result['significant_positions'])
    
    print(f"🎯 IMMEDIATE SOLUTION OPTIONS:")
    print(f"")
    
    if dust_count > 0:
        print(f"OPTION 1: Clean up dust balances")
        print(f"  - You have {dust_count} dust positions blocking trades")
        print(f"  - Manually sell these on Kraken web interface:")
        for pos in analysis_result['dust_positions']:
            if pos['value_eur'] > 0.01:  # Only show if worth more than 1 cent
                print(f"    • {pos['symbol']} (€{pos['value_eur']:.4f})")
        print(f"")
    
    print(f"OPTION 2: Increase position limit temporarily")
    print(f"  - Change max_open_positions from {analysis_result['max_positions']} to {analysis_result['total_positions'] + 5}")
    print(f"  - This allows trading while you clean up dust")
    print(f"")
    
    print(f"OPTION 3: Modify system to ignore dust")
    print(f"  - Add minimum balance threshold to position detection")
    print(f"  - Only count balances worth >€1 as positions")
    print(f"")
    
    if significant_count > 1:
        print(f"OPTION 4: Consolidate significant positions")
        print(f"  - You have {significant_count} significant positions")
        print(f"  - Consider selling all but one to match single-asset strategy")

if __name__ == "__main__":
    setup_logging()
    result = analyze_dust_balances()
    if result:
        suggest_solutions(result)
