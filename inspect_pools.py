#!/usr/bin/env python
"""
Pool inspection tool for GeckoTerminal tokens.
This script helps analyze all available pools for a token and their data quality.
"""

import sys
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import GeckoTerminal functions
try:
    from src.geckoterminal_fetcher import search_pools, get_ohlcv_data
    GECKOTERMINAL_AVAILABLE = True
except ImportError:
    logging.error("GeckoTerminal fetcher not available")
    sys.exit(1)

def inspect_token_pools(network: str, token_address: str, symbol: str, timeframe: str = '1h') -> Dict:
    """
    Inspect all available pools for a token and analyze their data quality.

    Args:
        network: Network ID (e.g., 'solana')
        token_address: Token contract address
        symbol: Token symbol for display
        timeframe: Timeframe to test data fetching

    Returns:
        Dictionary with pool analysis results
    """
    print(f"\n{'='*80}")
    print(f"INSPECTING POOLS FOR {symbol} ({token_address[:8]}...)")
    print(f"Network: {network}")
    print(f"{'='*80}")

    # Search for all pools
    pools = search_pools(network, token_address)

    if not pools:
        print(f"❌ No pools found for {symbol}")
        return {}

    print(f"Found {len(pools)} pools for {symbol}")

    pool_analysis = []

    for i, pool in enumerate(pools):
        try:
            attributes = pool.get('attributes', {})
            pool_address = attributes.get('address')
            reserve_in_usd = attributes.get('reserve_in_usd', '0')
            volume_usd_24h = attributes.get('volume_usd', {}).get('h24', '0')

            # Get DEX info
            relationships = pool.get('relationships', {})
            dex_data = relationships.get('dex', {}).get('data', {})
            dex_id = dex_data.get('id', 'unknown')

            # Convert to float for sorting
            try:
                reserve_float = float(reserve_in_usd) if reserve_in_usd else 0
                volume_float = float(volume_usd_24h) if volume_usd_24h else 0
            except (ValueError, TypeError):
                reserve_float = 0
                volume_float = 0

            # Test data availability
            data_quality = "Unknown"
            candles_available = 0
            date_range = "N/A"

            if pool_address:
                try:
                    # Try to fetch recent data to test availability
                    test_df = get_ohlcv_data(
                        network=network,
                        pool_address=pool_address,
                        timeframe=timeframe,
                        limit=100  # Just test with 100 candles
                    )

                    if not test_df.empty:
                        candles_available = len(test_df)
                        date_range = f"{test_df.index.min().date()} to {test_df.index.max().date()}"
                        data_quality = "✅ Good"
                    else:
                        data_quality = "❌ No data"

                except Exception as e:
                    data_quality = f"❌ Error: {str(e)[:50]}..."

            pool_info = {
                'index': i,
                'pool_address': pool_address,
                'dex': dex_id,
                'reserve_usd': reserve_float,
                'volume_24h_usd': volume_float,
                'data_quality': data_quality,
                'candles_available': candles_available,
                'date_range': date_range
            }

            pool_analysis.append(pool_info)

        except Exception as e:
            logging.error(f"Error analyzing pool {i}: {e}")

    # Sort by liquidity (reserve_in_usd) descending
    pool_analysis.sort(key=lambda x: x['reserve_usd'], reverse=True)

    # Display results
    print(f"\n📊 POOL ANALYSIS RESULTS (sorted by liquidity):")
    print(f"{'Rank':<4} {'Pool Address':<45} {'DEX':<15} {'Liquidity (USD)':<15} {'Volume 24h (USD)':<15} {'Data Quality':<20} {'Candles':<8} {'Date Range'}")
    print("-" * 140)

    for rank, pool in enumerate(pool_analysis, 1):
        liquidity_str = f"${pool['reserve_usd']:,.0f}" if pool['reserve_usd'] > 0 else "N/A"
        volume_str = f"${pool['volume_24h_usd']:,.0f}" if pool['volume_24h_usd'] > 0 else "N/A"

        print(f"{rank:<4} {pool['pool_address']:<45} {pool['dex']:<15} {liquidity_str:<15} {volume_str:<15} {pool['data_quality']:<20} {pool['candles_available']:<8} {pool['date_range']}")

    # Highlight the selected pool (highest liquidity)
    if pool_analysis:
        selected_pool = pool_analysis[0]
        print(f"\n🎯 SELECTED POOL (highest liquidity):")
        print(f"   Address: {selected_pool['pool_address']}")
        print(f"   DEX: {selected_pool['dex']}")
        print(f"   Liquidity: ${selected_pool['reserve_usd']:,.0f}")
        print(f"   Data Quality: {selected_pool['data_quality']}")

        if selected_pool['data_quality'].startswith('❌'):
            print(f"\n⚠️  WARNING: Selected pool has data quality issues!")
            print(f"   Consider using a different pool or investigating the issue.")

            # Show alternative pools with good data
            good_pools = [p for p in pool_analysis if p['data_quality'].startswith('✅')]
            if good_pools:
                print(f"\n💡 ALTERNATIVE POOLS WITH GOOD DATA:")
                for alt_pool in good_pools[:3]:  # Show top 3 alternatives
                    print(f"   {alt_pool['pool_address']} (DEX: {alt_pool['dex']}, Liquidity: ${alt_pool['reserve_usd']:,.0f})")

    return {
        'symbol': symbol,
        'network': network,
        'token_address': token_address,
        'total_pools': len(pools),
        'pools': pool_analysis,
        'selected_pool': pool_analysis[0] if pool_analysis else None
    }

def main():
    """Main function to inspect pools for common tokens."""

    # Test tokens
    test_tokens = [
        {
            'network': 'solana',
            'token_address': 'A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump',
            'symbol': 'FWOG'
        },
        {
            'network': 'solana',
            'token_address': 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
            'symbol': 'BONK'
        },
        {
            'network': 'solana',
            'token_address': 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
            'symbol': 'WIF'
        }
    ]

    results = []

    for token in test_tokens:
        try:
            result = inspect_token_pools(
                network=token['network'],
                token_address=token['token_address'],
                symbol=token['symbol'],
                timeframe='1h'
            )
            results.append(result)
        except Exception as e:
            logging.error(f"Error inspecting {token['symbol']}: {e}")

    # Summary
    print(f"\n{'='*80}")
    print(f"SUMMARY")
    print(f"{'='*80}")

    for result in results:
        if result:
            selected = result.get('selected_pool', {})
            quality = selected.get('data_quality', 'Unknown') if selected else 'No pools'
            print(f"{result['symbol']:<10} | {result['total_pools']:<3} pools | Selected: {quality}")

if __name__ == "__main__":
    main()
