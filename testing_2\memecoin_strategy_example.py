#!/usr/bin/env python
"""
Example script to run a trading strategy with memecoin data from GeckoTerminal.

This script demonstrates how to run a strategy that includes various memecoins
from different blockchain networks using GeckoTerminal data.
"""

import subprocess
import logging
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_memecoin_strategy(
    timeframe='1d',
    analysis_start_date='2023-01-01',
    n_assets=5,
    include_solana=True,
    include_ethereum=True,
    include_bsc=True,
    include_base=True,
    use_mtpi=True,
    trend_method='PGO',
    weighted=False
):
    """
    Run a trading strategy that includes memecoins from GeckoTerminal.

    Args:
        timeframe: Timeframe for the analysis (e.g., '1d', '4h')
        analysis_start_date: Start date for the analysis
        n_assets: Number of top assets to select
        include_solana: Whether to include Solana memecoins
        include_ethereum: Whether to include Ethereum memecoins
        include_bsc: Whether to include BSC memecoins
        include_base: Whether to include Base network tokens
        use_mtpi: Whether to use MTPI signal filtering
        trend_method: Trend detection method ('RSI' or 'PGO')
        weighted: Whether to use weighted allocation
    """
    # Prepare command
    cmd = [
        "python", "run_strategy_with_geckoterminal.py",
        "--timeframe", timeframe,
        "--mtpi-timeframe", "1d",  # Always use 1d for MTPI
        "--analysis-start-date", analysis_start_date,
        "--n-assets", str(n_assets)
    ]

    # Add tokens based on selected networks
    tokens = []

    if include_solana:
        tokens.extend(["BONK", "SAMO", "AUTISM", "RAY", "FWOG"])

    if include_ethereum:
        tokens.extend(["SHIB", "PEPE", "WIF"])

    if include_bsc:
        tokens.extend(["FLOKI", "WBNB", "ALTURA"])

    # Add Base network tokens
    if include_base:
        tokens.extend(["TOSHI"])

    if tokens:
        cmd.extend(["--tokens"] + tokens)

    # Add MTPI settings
    if not use_mtpi:
        cmd.append("--no-mtpi")

    # Add trend method
    if trend_method:
        cmd.extend(["--trend-method", trend_method])

    # Add weighted allocation
    if weighted:
        cmd.append("--weighted")

    # Log the command
    logging.info(f"Running command: {' '.join(cmd)}")

    # Run the command
    try:
        subprocess.run(cmd, check=True)
        logging.info("Memecoin strategy run completed successfully")
    except subprocess.CalledProcessError as e:
        logging.error(f"Memecoin strategy run failed with exit code {e.returncode}")

def main():
    """Parse command line arguments and run the strategy."""
    parser = argparse.ArgumentParser(description='Run trading strategy with memecoin data')

    # Basic strategy parameters
    parser.add_argument('--timeframe', type=str, default='1d', help='Timeframe for asset data')
    parser.add_argument('--analysis-start-date', type=str, default='2023-01-01', help='Analysis start date')
    parser.add_argument('--n-assets', type=int, default=5, help='Number of top assets to select')

    # Network selection
    parser.add_argument('--no-solana', action='store_true', help='Exclude Solana memecoins')
    parser.add_argument('--no-ethereum', action='store_true', help='Exclude Ethereum memecoins')
    parser.add_argument('--no-bsc', action='store_true', help='Exclude BSC memecoins')
    parser.add_argument('--no-base', action='store_true', help='Exclude Base network tokens')

    # Strategy settings
    parser.add_argument('--no-mtpi', action='store_true', help='Disable MTPI signal filtering')
    parser.add_argument('--trend-method', type=str, choices=['RSI', 'PGO'], default='PGO', help='Trend detection method')
    parser.add_argument('--weighted', action='store_true', help='Use weighted allocation')

    args = parser.parse_args()

    # Run the strategy
    run_memecoin_strategy(
        timeframe=args.timeframe,
        analysis_start_date=args.analysis_start_date,
        n_assets=args.n_assets,
        include_solana=not args.no_solana,
        include_ethereum=not args.no_ethereum,
        include_bsc=not args.no_bsc,
        include_base=not args.no_base,
        use_mtpi=not args.no_mtpi,
        trend_method=args.trend_method,
        weighted=args.weighted
    )

if __name__ == '__main__':
    main()
