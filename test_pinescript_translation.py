#!/usr/bin/env python3
"""
Test the PineScript-translated median supertrend implementation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score, calculate_atr
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config

def percentile_nearest_rank(series, window, percentile):
    return series.rolling(window).apply(lambda x: np.percentile(x, percentile), raw=True)

def median_supertrend(df, atr_period=10, multiplier=2.15, smoothing_len=14, src_col='close'):
    df = df.copy()

    # 1. Median Smoothing
    df['smooth'] = percentile_nearest_rank(df[src_col], smoothing_len, 50)

    # 2. ATR Calculation (using our existing function)
    df['atr'] = calculate_atr(df, atr_period)

    # 3. Supertrend Calculation
    df['upper'] = df['smooth'] + multiplier * df['atr']
    df['lower'] = df['smooth'] - multiplier * df['atr']
    df['prev_upper'] = df['upper'].shift(1)
    df['prev_lower'] = df['lower'].shift(1)

    # Adaptive band logic
    df['lower'] = np.where((df['lower'] > df['prev_lower']) | (df['close'].shift(1) < df['prev_lower']),
                           df['lower'], df['prev_lower'])
    df['upper'] = np.where((df['upper'] < df['prev_upper']) | (df['close'].shift(1) > df['prev_upper']),
                           df['upper'], df['prev_upper'])

    # Trend Direction Initialization
    direction = [np.nan]
    supertrend = [np.nan]

    for i in range(1, len(df)):
        prev_st = supertrend[-1]
        if np.isnan(df['atr'].iloc[i - 1]):
            d = 1
        elif prev_st == df['prev_upper'].iloc[i]:
            d = -1 if df['close'].iloc[i] > df['upper'].iloc[i] else 1
        else:
            d = 1 if df['close'].iloc[i] < df['lower'].iloc[i] else -1

        st_val = df['lower'].iloc[i] if d == -1 else df['upper'].iloc[i]
        direction.append(d)
        supertrend.append(st_val)

    df['direction'] = direction
    df['supertrend'] = supertrend

    # Signal Logic
    df['signal'] = 0
    df.loc[(df['direction'].shift(1) == -1) & (df['direction'] == 1), 'signal'] = 1   # Long
    df.loc[(df['direction'].shift(1) == 1) & (df['direction'] == -1), 'signal'] = -1  # Short

    return df[['close', 'smooth', 'atr', 'supertrend', 'direction', 'signal']]

def test_pinescript_vs_our_implementation():
    """Compare PineScript translation with our implementation."""
    print("=" * 100)
    print("PINESCRIPT TRANSLATION VS OUR IMPLEMENTATION")
    print("=" * 100)
    
    # Load config and data
    config = load_mtpi_multi_indicator_config()
    median_config = config.get('median_score', {})
    
    # Parameters from YAML (our TradingView parameters)
    atr_period = median_config.get('atr_period', 12)
    multiplier = median_config.get('multiplier', 1.45)
    median_length = median_config.get('median_length', 27)
    src_col = median_config.get('src_col', 'high')
    
    print(f"Our parameters: ATR={atr_period}, Mult={multiplier}, MedianLen={median_length}, Src={src_col}")
    
    # Fetch data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Data: {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Test 1: PineScript translation with our parameters
    print(f"\n" + "=" * 100)
    print("TEST 1: PINESCRIPT TRANSLATION WITH OUR PARAMETERS")
    print("=" * 100)
    
    pinescript_result = median_supertrend(
        btc_df, 
        atr_period=atr_period, 
        multiplier=multiplier, 
        smoothing_len=median_length, 
        src_col=src_col
    )
    
    # Test 2: Our implementation
    print(f"Running our implementation...")
    our_signal, our_supertrend, our_direction = calculate_median_score(
        btc_df,
        atr_period=atr_period,
        multiplier=multiplier,
        median_length=median_length,
        src_col=src_col
    )
    
    # Compare results
    print(f"\n" + "=" * 100)
    print("COMPARISON RESULTS")
    print("=" * 100)
    
    # Find signal changes for both implementations
    pinescript_signals = pinescript_result['signal']
    pinescript_changes = pinescript_signals != 0
    pinescript_change_dates = pinescript_changes[pinescript_changes].index
    
    our_signal_changes = our_signal != our_signal.shift(1)
    our_change_dates = our_signal_changes[our_signal_changes].index
    
    print(f"PineScript translation signal changes: {len(pinescript_change_dates)}")
    print(f"Our implementation signal changes: {len(our_change_dates)}")
    
    # Show recent values comparison
    print(f"\nLast 10 days comparison:")
    print(f"{'Date':<12} {'PS_Signal':<10} {'Our_Signal':<10} {'PS_Dir':<8} {'Our_Dir':<8} {'PS_ST':<12} {'Our_ST':<12}")
    print("-" * 85)
    
    for i in range(-10, 0):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        ps_signal = pinescript_signals.iloc[i]
        our_sig = our_signal.iloc[i]
        ps_dir = pinescript_result['direction'].iloc[i]
        our_dir = our_direction.iloc[i]
        ps_st = pinescript_result['supertrend'].iloc[i]
        our_st = our_supertrend.iloc[i]
        
        signal_match = "✅" if ps_signal == our_sig else "❌"
        dir_match = "✅" if ps_dir == our_dir else "❌"
        
        print(f"{date:<12} {ps_signal:<10} {our_sig:<10} {ps_dir:<8.0f} {our_dir:<8.0f} {ps_st:<12.2f} {our_st:<12.2f} {signal_match} {dir_match}")
    
    # Detailed signal change comparison
    print(f"\n" + "=" * 100)
    print("SIGNAL CHANGES COMPARISON")
    print("=" * 100)
    
    print(f"PineScript translation signal changes:")
    for i, change_date in enumerate(pinescript_change_dates[-10:]):  # Last 10 changes
        idx = btc_df.index.get_loc(change_date)
        signal_val = pinescript_signals.iloc[idx]
        direction_val = pinescript_result['direction'].iloc[idx]
        close_price = btc_df['close'].iloc[idx]
        
        signal_type = "LONG" if signal_val == 1 else "SHORT" if signal_val == -1 else "NEUTRAL"
        print(f"  {change_date.strftime('%Y-%m-%d')}: Signal={signal_val} ({signal_type}), Dir={direction_val}, Price=${close_price:.2f}")
    
    print(f"\nOur implementation signal changes:")
    for i, change_date in enumerate(our_change_dates[-10:]):  # Last 10 changes
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = our_signal.iloc[idx-1]
            curr_signal = our_signal.iloc[idx]
            direction_val = our_direction.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            
            signal_change = f"{prev_signal}→{curr_signal}"
            signal_type = "LONG" if curr_signal == 1 else "SHORT" if curr_signal == -1 else "NEUTRAL"
            print(f"  {change_date.strftime('%Y-%m-%d')}: {signal_change} ({signal_type}), Dir={direction_val}, Price=${close_price:.2f}")
    
    # Test 3: Try with default PineScript parameters
    print(f"\n" + "=" * 100)
    print("TEST 2: PINESCRIPT TRANSLATION WITH DEFAULT PARAMETERS")
    print("=" * 100)
    
    # Default parameters from the PineScript function
    default_result = median_supertrend(
        btc_df, 
        atr_period=10, 
        multiplier=2.15, 
        smoothing_len=14, 
        src_col='close'
    )
    
    default_signals = default_result['signal']
    default_changes = default_signals != 0
    default_change_dates = default_changes[default_changes].index
    
    print(f"Default parameters signal changes: {len(default_change_dates)}")
    print(f"Recent signal changes with default parameters:")
    for change_date in default_change_dates[-5:]:  # Last 5 changes
        idx = btc_df.index.get_loc(change_date)
        signal_val = default_signals.iloc[idx]
        direction_val = default_result['direction'].iloc[idx]
        close_price = btc_df['close'].iloc[idx]
        
        signal_type = "LONG" if signal_val == 1 else "SHORT" if signal_val == -1 else "NEUTRAL"
        print(f"  {change_date.strftime('%Y-%m-%d')}: Signal={signal_val} ({signal_type}), Dir={direction_val}, Price=${close_price:.2f}")
    
    # Analysis
    print(f"\n" + "=" * 100)
    print("ANALYSIS AND RECOMMENDATIONS")
    print("=" * 100)
    
    # Check if PineScript translation matches our implementation better
    recent_ps_signals = pinescript_signals.iloc[-50:].values
    recent_our_signals = our_signal.iloc[-50:].values
    
    signal_matches = np.sum(recent_ps_signals == recent_our_signals)
    total_recent = len(recent_ps_signals)
    match_percentage = (signal_matches / total_recent) * 100
    
    print(f"Recent signal match rate: {signal_matches}/{total_recent} ({match_percentage:.1f}%)")
    
    if match_percentage > 90:
        print(f"✅ HIGH MATCH RATE: The implementations are very similar")
        print(f"   Small differences might be due to:")
        print(f"   - Floating point precision")
        print(f"   - Different signal persistence logic")
        print(f"   - Edge case handling")
    elif match_percentage > 70:
        print(f"⚠️  MODERATE MATCH RATE: Some differences exist")
        print(f"   Consider adopting the PineScript translation approach")
    else:
        print(f"❌ LOW MATCH RATE: Significant differences")
        print(f"   The PineScript translation might be more accurate")
    
    print(f"\nNext steps:")
    print(f"1. Test the PineScript translation against your TradingView chart")
    print(f"2. If it matches better, we can replace our implementation")
    print(f"3. Focus on the specific differences in signal logic")
    
    return pinescript_result, our_signal, our_supertrend, our_direction

if __name__ == "__main__":
    test_pinescript_vs_our_implementation()
