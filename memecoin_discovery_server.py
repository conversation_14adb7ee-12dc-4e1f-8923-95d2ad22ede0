#!/usr/bin/env python3
"""
Standalone Memecoin Discovery Web Server

A simple Flask web server for the memecoin discovery system with a clean web interface.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import discovery system components
try:
    from src.memecoin_discovery.discovery_engine import MemecoinDiscoveryEngine
    from src.memecoin_discovery.database import DiscoveryDatabase
    DISCOVERY_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Memecoin discovery system not available: {e}")
    DISCOVERY_AVAILABLE = False

# Initialize Flask app
app = Flask(__name__, 
           template_folder='templates/memecoin_discovery',
           static_folder='static/memecoin_discovery')
CORS(app)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Global variables
discovery_engine = None
database = None

def initialize_discovery_system():
    """Initialize the discovery system components."""
    global discovery_engine, database
    
    if not DISCOVERY_AVAILABLE:
        return False
    
    try:
        database = DiscoveryDatabase()
        discovery_engine = MemecoinDiscoveryEngine()
        logging.info("Discovery system initialized successfully")
        return True
    except Exception as e:
        logging.error(f"Failed to initialize discovery system: {e}")
        return False

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """Get system status."""
    try:
        # Check dependencies
        dependencies = {}
        deps_to_check = ['openai', 'snscrape', 'textblob', 'vaderSentiment']

        for dep in deps_to_check:
            try:
                __import__(dep)
                dependencies[dep] = True
            except ImportError:
                dependencies[dep] = False

        # Check API keys (only OpenAI needed now)
        api_keys = {
            'openai': bool(os.getenv('OPENAI_API_KEY')),
            'snscrape': dependencies.get('snscrape', False),  # snscrape doesn't need API keys
        }
        
        # Check database
        db_status = False
        if database:
            try:
                recent_tokens = database.get_discovered_tokens(days_back=1, limit=1)
                db_status = True
            except:
                db_status = False
        
        return jsonify({
            'status': 'ok' if DISCOVERY_AVAILABLE else 'error',
            'discovery_available': DISCOVERY_AVAILABLE,
            'dependencies': dependencies,
            'api_keys': api_keys,
            'database': db_status,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/discover', methods=['POST'])
def api_discover():
    """Run discovery cycle."""
    if not DISCOVERY_AVAILABLE or not discovery_engine:
        return jsonify({'error': 'Discovery system not available'}), 503
    
    try:
        logging.info("Starting discovery cycle via API")
        results = discovery_engine.run_discovery_cycle()
        logging.info("Discovery cycle completed via API")
        return jsonify(results)
        
    except Exception as e:
        logging.error(f"Discovery cycle failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tokens')
def api_tokens():
    """Get discovered tokens."""
    if not database:
        return jsonify({'error': 'Database not available'}), 503
    
    try:
        # Get query parameters
        status = request.args.get('status')
        days_back = int(request.args.get('days_back', 7))
        limit = int(request.args.get('limit', 50))
        
        # Get tokens from database
        tokens = database.get_discovered_tokens(
            status=status,
            days_back=days_back,
            limit=limit
        )
        
        # Convert to JSON-serializable format
        token_list = []
        for token in tokens:
            token_data = {
                'symbol': token.symbol,
                'name': token.name,
                'network': token.network,
                'discovery_date': token.discovery_date.isoformat() if token.discovery_date else None,
                'source': token.source,
                'final_score': round((token.final_score or 0) * 100, 1),
                'ai_score': round((token.ai_score or 0) * 100, 1),
                'sentiment_score': round((token.sentiment_score or 0) * 100, 1),
                'risk_score': round((token.risk_score or 0) * 100, 1),
                'status': token.status,
                'twitter_mentions': token.twitter_mentions,
                'contract_address': token.contract_address,
                'metadata': token.metadata
            }
            token_list.append(token_data)
        
        return jsonify({
            'tokens': token_list,
            'count': len(token_list),
            'filters': {
                'status': status,
                'days_back': days_back,
                'limit': limit
            }
        })
        
    except Exception as e:
        logging.error(f"Error getting tokens: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tokens/<symbol>')
def api_token_detail(symbol):
    """Get detailed information about a specific token."""
    if not database:
        return jsonify({'error': 'Database not available'}), 503
    
    try:
        # Get token from database
        tokens = database.get_discovered_tokens()
        token = next((t for t in tokens if t.symbol == symbol), None)
        
        if not token:
            return jsonify({'error': 'Token not found'}), 404
        
        # Get performance history if available
        performance_history = database.get_token_performance_history(symbol, days=30)
        
        # Convert to JSON-serializable format
        token_data = {
            'symbol': token.symbol,
            'name': token.name,
            'network': token.network,
            'contract_address': token.contract_address,
            'discovery_date': token.discovery_date.isoformat() if token.discovery_date else None,
            'source': token.source,
            'final_score': round((token.final_score or 0) * 100, 1),
            'ai_score': round((token.ai_score or 0) * 100, 1),
            'sentiment_score': round((token.sentiment_score or 0) * 100, 1),
            'risk_score': round((token.risk_score or 0) * 100, 1),
            'status': token.status,
            'twitter_mentions': token.twitter_mentions,
            'initial_price': token.initial_price,
            'market_cap': token.market_cap,
            'volume_24h': token.volume_24h,
            'metadata': token.metadata,
            'performance_history': [
                {
                    'date': p.date.isoformat(),
                    'price': p.price,
                    'volume_24h': p.volume_24h,
                    'market_cap': p.market_cap,
                    'price_change_1d': p.price_change_1d,
                    'price_change_7d': p.price_change_7d,
                    'price_change_30d': p.price_change_30d
                }
                for p in performance_history
            ]
        }
        
        return jsonify(token_data)
        
    except Exception as e:
        logging.error(f"Error getting token detail: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/summary')
def api_summary():
    """Get discovery summary statistics."""
    if not discovery_engine:
        return jsonify({'error': 'Discovery engine not available'}), 503
    
    try:
        summary = discovery_engine.get_discovery_summary(days=7)
        return jsonify(summary)
        
    except Exception as e:
        logging.error(f"Error getting summary: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tokens/<symbol>/update_status', methods=['POST'])
def api_update_token_status(symbol):
    """Update token status (approve/reject)."""
    if not database:
        return jsonify({'error': 'Database not available'}), 503
    
    try:
        data = request.get_json()
        new_status = data.get('status')
        
        if new_status not in ['approved', 'rejected', 'monitoring']:
            return jsonify({'error': 'Invalid status'}), 400
        
        success = database.update_token_status(symbol, new_status)
        
        if success:
            return jsonify({'success': True, 'message': f'Token {symbol} status updated to {new_status}'})
        else:
            return jsonify({'error': 'Failed to update status'}), 500
            
    except Exception as e:
        logging.error(f"Error updating token status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/export')
def api_export():
    """Export discovered tokens to JSON."""
    if not database:
        return jsonify({'error': 'Database not available'}), 503
    
    try:
        # Get query parameters
        status = request.args.get('status')
        days_back = int(request.args.get('days_back', 30))
        
        # Get tokens from database
        tokens = database.get_discovered_tokens(
            status=status,
            days_back=days_back
        )
        
        # Convert to export format
        export_data = {
            'export_date': datetime.now().isoformat(),
            'filters': {
                'status': status,
                'days_back': days_back
            },
            'tokens': []
        }
        
        for token in tokens:
            token_data = {
                'symbol': token.symbol,
                'name': token.name,
                'network': token.network,
                'contract_address': token.contract_address,
                'discovery_date': token.discovery_date.isoformat() if token.discovery_date else None,
                'source': token.source,
                'final_score': token.final_score,
                'ai_score': token.ai_score,
                'sentiment_score': token.sentiment_score,
                'risk_score': token.risk_score,
                'status': token.status,
                'twitter_mentions': token.twitter_mentions,
                'metadata': token.metadata
            }
            export_data['tokens'].append(token_data)
        
        return jsonify(export_data)
        
    except Exception as e:
        logging.error(f"Error exporting data: {e}")
        return jsonify({'error': str(e)}), 500

# Static file serving
@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files."""
    return send_from_directory('static/memecoin_discovery', filename)

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Memecoin Discovery Web Server')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5001, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    # Initialize discovery system
    if initialize_discovery_system():
        print("✓ Discovery system initialized successfully")
    else:
        print("✗ Discovery system initialization failed")
        print("  Some features may not be available")
    
    print(f"\nStarting Memecoin Discovery Web Server...")
    print(f"URL: http://{args.host}:{args.port}")
    print(f"Debug mode: {args.debug}")
    
    # Create directories if they don't exist
    os.makedirs('templates/memecoin_discovery', exist_ok=True)
    os.makedirs('static/memecoin_discovery', exist_ok=True)
    
    # Run the Flask app
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug,
        threaded=True
    )

if __name__ == '__main__':
    main()
