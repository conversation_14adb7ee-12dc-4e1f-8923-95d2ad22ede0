#!/usr/bin/env python3
"""
Test script for DPSD (DEMA Percentile Standard Deviation) indicator implementation.

This script tests the DPSD indicator against real BTC data with customizable
start date and provides comprehensive visualization similar to DWMA and BB tests.
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.indicators.dpsd_score import (
    generate_dpsd_signal, 
    calculate_dpsd_score,
    generate_dpsd_signal_with_variants,
    DEFAULT_DPSD_CONFIG
)
from src.data_fetcher import fetch_ohlcv_data


def test_dpsd_with_real_data(start_date: str = '2024-01-01'):
    """Test DPSD with real BTC data using your exact parameters."""
    print(f"\nTesting DPSD with real BTC data from {start_date}...")

    try:
        end_date = datetime.now().strftime('%Y-%m-%d')
        print(f"Fetching BTC data from {start_date} to {end_date}")
        
        # Fetch real BTC data with specific start date
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=start_date
        )

        if 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return None, None

        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} BTC candles")

        # Your exact DPSD parameters
        dpsd_params = {
            'dema_length': 9,           # DEMA Length
            'percentile_length': 58,    # Percentile Length
            'sd_length': 27,            # SD Length
            'ema_length': 14,           # EMA Length
            'percentile_upper': 60.0,   # Upper percentile
            'percentile_lower': 45.0,   # Lower percentile
            'src_col': 'close'          # Source column
        }

        print(f"Using your exact DPSD parameters: {dpsd_params}")

        # Test DPSD signal generation
        signal = generate_dpsd_signal(df=btc_df, **dpsd_params)

        # Analyze results
        signal_changes = (signal != signal.shift(1)).sum()
        latest_signal = signal.iloc[-1]
        signal_counts = signal.value_counts().sort_index()

        print(f"Signal changes: {signal_changes}")
        print(f"Latest signal: {latest_signal}")
        print(f"Signal distribution: {dict(signal_counts)}")

        # Show recent signals
        print("\nRecent signals (last 15):")
        recent_data = pd.DataFrame({
            'Date': btc_df.index[-15:].strftime('%Y-%m-%d'),
            'Close': btc_df['close'].iloc[-15:].round(2),
            'Signal': signal.iloc[-15:]
        })
        print(recent_data.to_string(index=False))

        print("✅ Real data test completed successfully")
        return btc_df, signal

    except Exception as e:
        print(f"❌ Error with real data test: {e}")
        return None, None


def test_dpsd_percentile_variants(btc_df):
    """Test DPSD with different percentile variants."""
    print("\nTesting DPSD percentile variants...")
    
    variants = ['60/45', '60/40', '55/45', '55/40']
    
    for variant in variants:
        print(f"\n--- Testing variant {variant} ---")
        
        try:
            signal = generate_dpsd_signal_with_variants(
                df=btc_df,
                variant=variant
            )
            
            signal_counts = signal.value_counts().sort_index()
            latest_signal = signal.iloc[-1]
            signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
            
            print(f"Distribution: {dict(signal_counts)}")
            print(f"Latest signal: {signal_name} ({latest_signal})")
            
        except Exception as e:
            print(f"❌ Error with variant {variant}: {e}")


def visualize_dpsd_analysis(btc_df, signal, save_plot: bool = True):
    """Create comprehensive DPSD visualization similar to DWMA style."""
    print("\nGenerating DPSD analysis visualization...")

    try:
        # Calculate DPSD components with your exact parameters
        dpsd_signal, dpsd_raw, dpsd_smoothed = calculate_dpsd_score(
            df=btc_df,
            dema_length=9,
            percentile_length=58,
            sd_length=27,
            ema_length=14,
            percentile_upper=60.0,
            percentile_lower=45.0,
            src_col='high'
        )

        # Calculate DEMA for plotting
        from src.indicators.dpsd_score import calculate_dema
        dema = calculate_dema(btc_df['close'], 9)

        # Create dark theme visualization
        plt.style.use('dark_background')
        fig, axes = plt.subplots(4, 1, figsize=(16, 12), sharex=True,
                                gridspec_kw={'height_ratios': [3, 2, 1, 1]})

        # Panel 1: Price and DEMA
        ax1 = axes[0]
        
        # Plot candlestick-style price data
        for i in range(len(btc_df)):
            color = '#00ff88' if btc_df['close'].iloc[i] >= btc_df['open'].iloc[i] else '#ff4444'
            ax1.plot([i, i], [btc_df['low'].iloc[i], btc_df['high'].iloc[i]],
                    color=color, linewidth=1, alpha=0.6)
            ax1.plot([i, i], [btc_df['open'].iloc[i], btc_df['close'].iloc[i]],
                    color=color, linewidth=2, alpha=0.8)

        # Plot DEMA
        ax1.plot(range(len(btc_df)), dema, color='#00d4ff', linewidth=2,
                label='DEMA(9)', alpha=0.9)

        ax1.set_title('BTC/USDT - DPSD (DEMA Percentile Standard Deviation) Analysis',
                     color='white', fontsize=14, fontweight='bold')
        ax1.legend(loc='upper left', framealpha=0.8)
        ax1.grid(True, alpha=0.2, color='gray')
        ax1.set_ylabel('Price (USDT)', color='white')

        # Panel 2: DPSD Raw and Smoothed
        ax2 = axes[1]
        ax2.plot(range(len(btc_df)), dpsd_raw, color='#888888', linewidth=1,
                label='DPSD Raw', alpha=0.7)
        ax2.plot(range(len(btc_df)), dpsd_smoothed, color='#9c27b0', linewidth=2,
                label='DPSD Smoothed (EMA 14)', alpha=0.9)
        
        # Add threshold lines
        ax2.axhline(y=70, color='#00ff88', linestyle='--', alpha=0.7, label='Long Threshold (70)')
        ax2.axhline(y=30, color='#ff4444', linestyle='--', alpha=0.7, label='Short Threshold (30)')
        ax2.axhline(y=0, color='white', linestyle='-', alpha=0.3)
        
        ax2.set_ylabel('DPSD Value', color='white')
        ax2.legend(loc='upper left', framealpha=0.8)
        ax2.grid(True, alpha=0.2, color='gray')

        # Panel 3: Trading Signals
        ax3 = axes[2]
        
        # Create signal areas
        long_mask = signal == 1
        short_mask = signal == -1
        neutral_mask = signal == 0
        
        # Plot signal areas
        ax3.fill_between(range(len(btc_df)), 0, 1, where=long_mask, 
                        color='#00ff88', alpha=0.6, label='LONG Signal')
        ax3.fill_between(range(len(btc_df)), -1, 0, where=short_mask, 
                        color='#ff4444', alpha=0.6, label='SHORT Signal')
        ax3.fill_between(range(len(btc_df)), -0.1, 0.1, where=neutral_mask, 
                        color='#666666', alpha=0.4, label='NEUTRAL Signal')
        
        # Plot signal line
        ax3.plot(range(len(btc_df)), signal, color='white', linewidth=2, alpha=0.8)
        
        ax3.set_ylabel('Signal', color='white')
        ax3.set_ylim(-1.2, 1.2)
        ax3.set_yticks([-1, 0, 1])
        ax3.set_yticklabels(['SHORT', 'NEUTRAL', 'LONG'])
        ax3.legend(loc='upper left', framealpha=0.8)
        ax3.grid(True, alpha=0.2, color='gray')

        # Panel 4: Signal Distribution
        ax4 = axes[3]
        
        signal_counts = signal.value_counts().sort_index()
        signal_labels = []
        signal_values = []
        colors = []
        
        for sig_val in [-1, 0, 1]:
            count = signal_counts.get(sig_val, 0)
            if sig_val == -1:
                signal_labels.append(f'SHORT\n({count})')
                colors.append('#ff4444')
            elif sig_val == 0:
                signal_labels.append(f'NEUTRAL\n({count})')
                colors.append('#666666')
            else:
                signal_labels.append(f'LONG\n({count})')
                colors.append('#00ff88')
            signal_values.append(count)
        
        bars = ax4.bar(signal_labels, signal_values, color=colors, alpha=0.8)
        ax4.set_ylabel('Count', color='white')
        ax4.grid(True, alpha=0.2, color='gray', axis='y')
        
        # Add value labels on bars
        for bar, value in zip(bars, signal_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(signal_values)*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold', color='white')

        # Add parameter info
        param_text = f"DPSD | DEMA: 9 | Percentile: 58 | SD: 27 | EMA: 14 | Thresholds: 60/45"
        ax1.text(0.02, 0.98, param_text, transform=ax1.transAxes,
                color='#cccccc', fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

        # Add current status info
        latest_signal = signal.iloc[-1]
        latest_dpsd = dpsd_smoothed.iloc[-1]
        latest_price = btc_df['close'].iloc[-1]
        latest_date = btc_df.index[-1].strftime('%Y-%m-%d')
        
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        
        status_text = f"Current Status ({latest_date}):\nSignal: {signal_name} ({latest_signal})\nDPSD: {latest_dpsd:.2f}\nPrice: ${latest_price:.2f}"
        ax2.text(0.98, 0.98, status_text, transform=ax2.transAxes,
                color='#cccccc', fontsize=9, verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

        # Format x-axis with dates
        date_labels = [btc_df.index[i].strftime('%m/%d') for i in range(0, len(btc_df), max(1, len(btc_df)//10))]
        date_positions = list(range(0, len(btc_df), max(1, len(btc_df)//10)))
        ax4.set_xticks(date_positions)
        ax4.set_xticklabels(date_labels, rotation=45, color='white')
        ax4.set_xlabel('Date', color='white')

        plt.tight_layout()

        if save_plot:
            plt.savefig('dpsd_analysis_visualization.png', dpi=300, bbox_inches='tight',
                       facecolor='#1e1e1e', edgecolor='none')
            print("DPSD analysis plot saved as 'dpsd_analysis_visualization.png'")

        plt.show()

        print("✅ DPSD visualization completed")

    except Exception as e:
        print(f"❌ DPSD visualization failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run DPSD tests with customizable start date."""
    print("=" * 70)
    print("DPSD (DEMA Percentile Standard Deviation) Indicator Test Suite")
    print("=" * 70)
    
    # Customizable start date - change this to test different periods
    start_date = '2024-01-01'  # Change this date as needed
    print(f"Using start date: {start_date}")
    print("(You can modify the start_date variable in main() to test different periods)")

    # Test with real BTC data using your exact parameters
    btc_df, signal = test_dpsd_with_real_data(start_date)

    if btc_df is not None and signal is not None:
        # Test percentile variants
        test_dpsd_percentile_variants(btc_df)
        
        # Create comprehensive visualization
        try:
            visualize_dpsd_analysis(btc_df, signal, save_plot=True)
        except Exception as e:
            print(f"Skipping DPSD visualization: {e}")

        # Print summary
        print(f"\n📊 DPSD Analysis Summary:")
        print(f"Period: {btc_df.index[0].strftime('%Y-%m-%d')} to {btc_df.index[-1].strftime('%Y-%m-%d')}")
        print(f"Total Days: {len(btc_df)}")
        signal_counts = signal.value_counts().sort_index()
        print(f"Signal Distribution: {dict(signal_counts)}")
        latest_signal = signal.iloc[-1]
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        print(f"Current Signal: {signal_name} ({latest_signal})")

    print("\n" + "=" * 70)
    print("DPSD Test Suite Completed - Ready for MTPI Integration")
    print("=" * 70)


if __name__ == "__main__":
    main()
