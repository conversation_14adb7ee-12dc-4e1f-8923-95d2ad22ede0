#!/usr/bin/env python3
"""
Robust verification script for manual execution timing logic.
This script performs multiple independent checks to verify that:
1. 12h candle data is correctly fetched and used
2. Midnight candles (00:00-12:00) contain actual 12 PM close prices
3. Manual execution returns are calculated correctly
4. The timing logic matches the expected behavior
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import sys
import os

# Add src to path
sys.path.append('src')

def setup_logging():
    """Setup detailed logging for verification."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('verification_log.txt', mode='w')
        ]
    )

def load_12h_data():
    """Load the 12h BTC/USDT data from CSV file."""
    try:
        csv_path = 'data/ohlcv/binance/12h/BTC_USDT.csv'
        if not os.path.exists(csv_path):
            logging.error(f"12h data file not found: {csv_path}")
            return None
            
        df = pd.read_csv(csv_path, index_col=0, parse_dates=True)
        logging.info(f"Loaded 12h data: {len(df)} candles from {df.index.min()} to {df.index.max()}")
        return df
    except Exception as e:
        logging.error(f"Failed to load 12h data: {e}")
        return None

def load_daily_data():
    """Load the daily BTC/USDT data from CSV file."""
    try:
        csv_path = 'data/ohlcv/binance/1d/BTC_USDT.csv'
        if not os.path.exists(csv_path):
            logging.error(f"Daily data file not found: {csv_path}")
            return None
            
        df = pd.read_csv(csv_path, index_col=0, parse_dates=True)
        logging.info(f"Loaded daily data: {len(df)} candles from {df.index.min()} to {df.index.max()}")
        return df
    except Exception as e:
        logging.error(f"Failed to load daily data: {e}")
        return None

def verify_12h_candle_structure(df_12h):
    """Verify the structure and timing of 12h candles."""
    logging.info("\n=== VERIFICATION 1: 12H CANDLE STRUCTURE ===")
    
    # Check hour distribution
    hours = df_12h.index.hour
    hour_counts = pd.Series(hours).value_counts().sort_index()
    logging.info(f"Hour distribution in 12h data:")
    for hour, count in hour_counts.items():
        logging.info(f"  Hour {hour:02d}:00: {count} candles")
    
    # Verify we have both 00:00 and 12:00 candles
    midnight_candles = df_12h[df_12h.index.hour == 0]
    noon_candles = df_12h[df_12h.index.hour == 12]
    
    logging.info(f"Midnight candles (00:00): {len(midnight_candles)}")
    logging.info(f"Noon candles (12:00): {len(noon_candles)}")
    
    # Check time differences
    time_diffs = df_12h.index.to_series().diff().dropna()
    unique_diffs = time_diffs.unique()
    logging.info(f"Time differences between candles: {unique_diffs}")
    
    return midnight_candles, noon_candles

def verify_midnight_candle_logic(midnight_candles, noon_candles):
    """Verify that midnight candles represent 00:00-12:00 periods."""
    logging.info("\n=== VERIFICATION 2: MIDNIGHT CANDLE LOGIC ===")
    
    # Sample a few dates to verify the logic
    test_dates = midnight_candles.index[:5]
    
    for date in test_dates:
        midnight_candle = midnight_candles.loc[date]
        
        # Find corresponding noon candle (should be 12 hours later)
        expected_noon_time = date + timedelta(hours=12)
        
        if expected_noon_time in noon_candles.index:
            noon_candle = noon_candles.loc[expected_noon_time]
            
            logging.info(f"\nDate: {date.strftime('%Y-%m-%d %H:%M')}")
            logging.info(f"  Midnight candle (00:00-12:00): Open=${midnight_candle['open']:.2f}, Close=${midnight_candle['close']:.2f}")
            logging.info(f"  Noon candle (12:00-00:00): Open=${noon_candle['open']:.2f}, Close=${noon_candle['close']:.2f}")
            
            # The midnight candle close should be the noon candle open (approximately)
            price_diff = abs(midnight_candle['close'] - noon_candle['open'])
            logging.info(f"  Price continuity check: |{midnight_candle['close']:.2f} - {noon_candle['open']:.2f}| = ${price_diff:.2f}")
            
            if price_diff > 100:  # Allow some tolerance for gaps
                logging.warning(f"  Large price gap detected! This might indicate data issues.")
        else:
            logging.warning(f"  No corresponding noon candle found for {expected_noon_time}")

def manual_calculation_verification(df_daily, df_12h):
    """Manually calculate returns and compare with expected logic."""
    logging.info("\n=== VERIFICATION 3: MANUAL RETURN CALCULATION ===")
    
    midnight_candles = df_12h[df_12h.index.hour == 0]
    
    # Take a sample period for detailed verification
    start_date = pd.Timestamp('2024-06-01', tz='UTC')
    end_date = pd.Timestamp('2024-06-10', tz='UTC')
    
    sample_daily = df_daily[(df_daily.index >= start_date) & (df_daily.index <= end_date)]
    
    logging.info(f"Verifying manual execution logic for period: {start_date.date()} to {end_date.date()}")
    
    for i in range(1, len(sample_daily)):
        current_date = sample_daily.index[i]
        prev_date = sample_daily.index[i-1]
        
        # Previous day's close (signal generation price)
        prev_close = sample_daily.loc[prev_date, 'close']
        
        # Current day's close (automatic execution price)
        current_close = sample_daily.loc[current_date, 'close']
        
        # Find 12 PM price for manual execution
        midnight_date = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        if midnight_date in midnight_candles.index:
            twelve_pm_close = midnight_candles.loc[midnight_date, 'close']
            
            # Calculate returns
            auto_return = (current_close - prev_close) / prev_close
            manual_return = (twelve_pm_close - prev_close) / prev_close
            
            logging.info(f"\n{current_date.strftime('%Y-%m-%d')}:")
            logging.info(f"  Signal price (prev close): ${prev_close:.2f}")
            logging.info(f"  Auto execution (00:00): ${current_close:.2f} → Return: {auto_return:.4f} ({auto_return*100:.2f}%)")
            logging.info(f"  Manual execution (12:00): ${twelve_pm_close:.2f} → Return: {manual_return:.4f} ({manual_return*100:.2f}%)")
            logging.info(f"  Difference: {(manual_return - auto_return)*100:.2f}%")
        else:
            logging.warning(f"  No 12h data available for {midnight_date}")

def cross_reference_with_main_program():
    """Cross-reference our calculations with main_program.py logic."""
    logging.info("\n=== VERIFICATION 4: CROSS-REFERENCE WITH MAIN PROGRAM ===")
    
    try:
        from main_program import AllocationTester
        
        # Create a minimal tester instance
        tester = AllocationTester(
            timeframe='1d',
            analysis_start_date='2024-06-01',
            selected_assets=['BTC/USDT'],
            n_assets=1,
            use_cache=True,
            execution_timing='manual_12pm'
        )
        
        # Load data
        from src.data_fetcher import fetch_ohlcv_data
        
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since='2024-06-01',
            use_cache=True
        )
        
        if 'BTC/USDT' in data_dict:
            df = data_dict['BTC/USDT']
            
            # Use the same method as main_program.py
            returns = tester._calculate_manual_execution_returns('BTC/USDT', df)
            
            logging.info("Sample returns from main_program.py logic:")
            for i in range(1, min(6, len(returns))):
                date = returns.index[i]
                return_val = returns.iloc[i]
                logging.info(f"  {date.strftime('%Y-%m-%d')}: {return_val:.4f} ({return_val*100:.2f}%)")
                
        else:
            logging.error("Failed to load BTC/USDT data for cross-reference")
            
    except Exception as e:
        logging.error(f"Cross-reference failed: {e}")

def price_continuity_check(df_12h):
    """Check price continuity between consecutive 12h candles."""
    logging.info("\n=== VERIFICATION 5: PRICE CONTINUITY CHECK ===")
    
    # Check that close of one candle matches open of next candle
    gaps = []
    for i in range(1, len(df_12h)):
        prev_close = df_12h.iloc[i-1]['close']
        current_open = df_12h.iloc[i]['open']
        gap = abs(prev_close - current_open)
        
        if gap > 50:  # Significant gap
            gaps.append({
                'date': df_12h.index[i],
                'prev_close': prev_close,
                'current_open': current_open,
                'gap': gap
            })
    
    logging.info(f"Found {len(gaps)} significant price gaps (>$50)")
    for gap in gaps[:10]:  # Show first 10
        logging.info(f"  {gap['date']}: ${gap['prev_close']:.2f} → ${gap['current_open']:.2f} (gap: ${gap['gap']:.2f})")

def main():
    """Run all verification tests."""
    setup_logging()
    
    logging.info("🔍 ROBUST VERIFICATION OF MANUAL EXECUTION LOGIC")
    logging.info("=" * 60)
    
    # Load data
    df_12h = load_12h_data()
    df_daily = load_daily_data()
    
    if df_12h is None or df_daily is None:
        logging.error("Failed to load required data files")
        return
    
    # Run verification tests
    midnight_candles, noon_candles = verify_12h_candle_structure(df_12h)
    verify_midnight_candle_logic(midnight_candles, noon_candles)
    manual_calculation_verification(df_daily, df_12h)
    price_continuity_check(df_12h)
    cross_reference_with_main_program()
    
    logging.info("\n" + "=" * 60)
    logging.info("🎯 VERIFICATION COMPLETE")
    logging.info("Check the detailed log above and 'verification_log.txt' for full results")

if __name__ == "__main__":
    main()
