"""
AI analyzer module for memecoin discovery.

This module uses OpenAI API to analyze discovered tokens and social media mentions
to provide intelligent scoring and recommendations.
"""

import os
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import openai
from dataclasses import dataclass

@dataclass
class AIAnalysisResult:
    """Result of AI analysis for a token."""
    token_symbol: str
    overall_score: float  # 0-100
    confidence: float  # 0-1
    recommendation: str  # 'strong_buy', 'buy', 'hold', 'avoid', 'scam_risk'
    reasoning: str
    risk_factors: List[str]
    positive_factors: List[str]
    sentiment_analysis: Dict
    social_metrics_analysis: Dict
    technical_indicators: Dict

class AITokenAnalyzer:
    """AI-powered token analyzer using OpenAI API."""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4"):
        """Initialize AI analyzer with OpenAI API key."""
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        # Initialize OpenAI client
        openai.api_key = self.api_key
        self.model = model
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests
        
        logging.info(f"AI Token Analyzer initialized with model: {model}")
    
    def _rate_limit_wait(self):
        """Implement basic rate limiting for OpenAI API."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def analyze_token_from_social_data(self, token_symbol: str, social_data: List[Dict], 
                                     token_metadata: Dict = None) -> AIAnalysisResult:
        """Analyze a token based on social media data and metadata."""
        try:
            # Prepare data for analysis
            analysis_prompt = self._build_analysis_prompt(token_symbol, social_data, token_metadata)
            
            # Get AI analysis
            self._rate_limit_wait()
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user", 
                        "content": analysis_prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=2000
            )
            
            # Parse response
            analysis_text = response.choices[0].message.content
            parsed_result = self._parse_ai_response(token_symbol, analysis_text)
            
            logging.info(f"AI analysis completed for {token_symbol}")
            return parsed_result
            
        except Exception as e:
            logging.error(f"Error in AI analysis for {token_symbol}: {e}")
            # Return default analysis on error
            return AIAnalysisResult(
                token_symbol=token_symbol,
                overall_score=50.0,
                confidence=0.1,
                recommendation='hold',
                reasoning=f"Analysis failed: {str(e)}",
                risk_factors=['Analysis unavailable'],
                positive_factors=[],
                sentiment_analysis={},
                social_metrics_analysis={},
                technical_indicators={}
            )
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for AI analysis."""
        return """You are an expert cryptocurrency analyst specializing in memecoin and altcoin evaluation. 
        Your task is to analyze social media data and token information to provide investment recommendations.

        You should consider:
        1. Social sentiment and engagement patterns
        2. Token fundamentals (if available)
        3. Risk factors including scam indicators
        4. Market timing and momentum
        5. Community strength and influencer involvement

        Provide your analysis in the following JSON format:
        {
            "overall_score": <0-100 score>,
            "confidence": <0-1 confidence level>,
            "recommendation": "<strong_buy|buy|hold|avoid|scam_risk>",
            "reasoning": "<detailed explanation>",
            "risk_factors": ["<factor1>", "<factor2>", ...],
            "positive_factors": ["<factor1>", "<factor2>", ...],
            "sentiment_analysis": {
                "overall_sentiment": "<positive|negative|neutral>",
                "sentiment_trend": "<improving|declining|stable>",
                "key_themes": ["<theme1>", "<theme2>", ...]
            },
            "social_metrics_analysis": {
                "engagement_quality": "<high|medium|low>",
                "influencer_involvement": "<high|medium|low>",
                "organic_growth": "<high|medium|low>",
                "bot_activity_risk": "<high|medium|low>"
            },
            "technical_indicators": {
                "momentum": "<strong_bullish|bullish|neutral|bearish|strong_bearish>",
                "volume_analysis": "<increasing|stable|decreasing>",
                "price_action": "<breakout|consolidation|decline>"
            }
        }

        Be conservative in your recommendations and always highlight potential risks."""
    
    def _build_analysis_prompt(self, token_symbol: str, social_data: List[Dict], 
                             token_metadata: Dict = None) -> str:
        """Build the analysis prompt with token and social data."""
        prompt = f"Analyze the following memecoin/token: {token_symbol}\n\n"
        
        # Add token metadata if available
        if token_metadata:
            prompt += "Token Metadata:\n"
            prompt += f"- Name: {token_metadata.get('name', 'Unknown')}\n"
            prompt += f"- Network: {token_metadata.get('network', 'Unknown')}\n"
            prompt += f"- Contract Address: {token_metadata.get('contract_address', 'Unknown')}\n"
            if token_metadata.get('market_cap'):
                prompt += f"- Market Cap: ${token_metadata['market_cap']:,.2f}\n"
            if token_metadata.get('volume_24h'):
                prompt += f"- 24h Volume: ${token_metadata['volume_24h']:,.2f}\n"
            prompt += "\n"
        
        # Add social media data
        prompt += "Recent Social Media Activity:\n"
        
        if not social_data:
            prompt += "No recent social media mentions found.\n"
        else:
            # Summarize social data
            total_mentions = len(social_data)
            total_engagement = sum(
                tweet.get('like_count', 0) + tweet.get('retweet_count', 0) 
                for tweet in social_data
            )
            avg_sentiment = sum(
                tweet.get('sentiment_score', 0) for tweet in social_data
            ) / max(len(social_data), 1)
            
            prompt += f"- Total mentions: {total_mentions}\n"
            prompt += f"- Total engagement: {total_engagement:,}\n"
            prompt += f"- Average sentiment: {avg_sentiment:.2f}\n\n"
            
            # Add sample tweets (top 5 by engagement)
            sorted_tweets = sorted(
                social_data, 
                key=lambda x: x.get('like_count', 0) + x.get('retweet_count', 0),
                reverse=True
            )[:5]
            
            prompt += "Top Engaging Tweets:\n"
            for i, tweet in enumerate(sorted_tweets, 1):
                prompt += f"{i}. @{tweet.get('author_username', 'unknown')}: "
                prompt += f"{tweet.get('text', '')[:200]}...\n"
                prompt += f"   Engagement: {tweet.get('like_count', 0)} likes, "
                prompt += f"{tweet.get('retweet_count', 0)} retweets\n"
                prompt += f"   Sentiment: {tweet.get('sentiment_score', 0):.2f}\n\n"
        
        # Add analysis request
        prompt += """
        Based on this information, provide a comprehensive analysis of this token's investment potential.
        Consider both the opportunities and risks, and provide a clear recommendation with reasoning.
        Pay special attention to potential scam indicators or red flags.
        """
        
        return prompt
    
    def _parse_ai_response(self, token_symbol: str, response_text: str) -> AIAnalysisResult:
        """Parse AI response into structured result."""
        try:
            # Try to extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                parsed_data = json.loads(json_text)
                
                return AIAnalysisResult(
                    token_symbol=token_symbol,
                    overall_score=float(parsed_data.get('overall_score', 50)),
                    confidence=float(parsed_data.get('confidence', 0.5)),
                    recommendation=parsed_data.get('recommendation', 'hold'),
                    reasoning=parsed_data.get('reasoning', 'No reasoning provided'),
                    risk_factors=parsed_data.get('risk_factors', []),
                    positive_factors=parsed_data.get('positive_factors', []),
                    sentiment_analysis=parsed_data.get('sentiment_analysis', {}),
                    social_metrics_analysis=parsed_data.get('social_metrics_analysis', {}),
                    technical_indicators=parsed_data.get('technical_indicators', {})
                )
            else:
                # Fallback: parse text response
                return self._parse_text_response(token_symbol, response_text)
                
        except Exception as e:
            logging.error(f"Error parsing AI response: {e}")
            return self._create_fallback_result(token_symbol, response_text)
    
    def _parse_text_response(self, token_symbol: str, response_text: str) -> AIAnalysisResult:
        """Parse text response when JSON parsing fails."""
        # Extract key information using simple text parsing
        text_lower = response_text.lower()
        
        # Determine recommendation
        if any(word in text_lower for word in ['strong buy', 'strongly recommend']):
            recommendation = 'strong_buy'
            score = 80
        elif any(word in text_lower for word in ['buy', 'bullish', 'positive']):
            recommendation = 'buy'
            score = 70
        elif any(word in text_lower for word in ['avoid', 'bearish', 'negative']):
            recommendation = 'avoid'
            score = 30
        elif any(word in text_lower for word in ['scam', 'rug', 'fraud']):
            recommendation = 'scam_risk'
            score = 10
        else:
            recommendation = 'hold'
            score = 50
        
        # Extract confidence (simple heuristic)
        confidence = 0.7 if len(response_text) > 500 else 0.5
        
        return AIAnalysisResult(
            token_symbol=token_symbol,
            overall_score=float(score),
            confidence=confidence,
            recommendation=recommendation,
            reasoning=response_text[:1000],  # Truncate if too long
            risk_factors=[],
            positive_factors=[],
            sentiment_analysis={},
            social_metrics_analysis={},
            technical_indicators={}
        )
    
    def _create_fallback_result(self, token_symbol: str, response_text: str) -> AIAnalysisResult:
        """Create fallback result when parsing fails."""
        return AIAnalysisResult(
            token_symbol=token_symbol,
            overall_score=50.0,
            confidence=0.3,
            recommendation='hold',
            reasoning=f"Analysis parsing failed. Raw response: {response_text[:500]}...",
            risk_factors=['Analysis parsing failed'],
            positive_factors=[],
            sentiment_analysis={},
            social_metrics_analysis={},
            technical_indicators={}
        )
    
    def analyze_batch_tokens(self, tokens_data: List[Dict]) -> List[AIAnalysisResult]:
        """Analyze multiple tokens in batch (with rate limiting)."""
        results = []
        
        for token_data in tokens_data:
            try:
                token_symbol = token_data.get('symbol', 'UNKNOWN')
                social_data = token_data.get('social_data', [])
                metadata = token_data.get('metadata', {})
                
                result = self.analyze_token_from_social_data(
                    token_symbol, social_data, metadata
                )
                results.append(result)
                
                # Add delay between batch requests
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"Error in batch analysis: {e}")
                continue
        
        return results
    
    def get_scam_risk_assessment(self, token_symbol: str, social_data: List[Dict]) -> Dict:
        """Get specific scam risk assessment for a token."""
        try:
            prompt = f"""
            Analyze the following token for scam/rug pull risks: {token_symbol}
            
            Social Media Data:
            {json.dumps(social_data[:3], indent=2)}
            
            Provide a risk assessment focusing on:
            1. Scam indicators
            2. Rug pull risks
            3. Bot activity
            4. Pump and dump patterns
            5. Team credibility
            
            Return a JSON with:
            {{
                "risk_level": "<low|medium|high|critical>",
                "risk_score": <0-100>,
                "scam_indicators": ["indicator1", "indicator2", ...],
                "recommendations": ["rec1", "rec2", ...]
            }}
            """
            
            self._rate_limit_wait()
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a cryptocurrency scam detection expert."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            response_text = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            except:
                return {
                    "risk_level": "medium",
                    "risk_score": 50,
                    "scam_indicators": ["Analysis parsing failed"],
                    "recommendations": ["Manual review required"]
                }
                
        except Exception as e:
            logging.error(f"Error in scam risk assessment: {e}")
            return {
                "risk_level": "high",
                "risk_score": 80,
                "scam_indicators": [f"Assessment failed: {str(e)}"],
                "recommendations": ["Avoid due to analysis failure"]
            }
