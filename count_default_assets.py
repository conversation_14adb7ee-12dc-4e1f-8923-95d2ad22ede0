#!/usr/bin/env python3
"""
Count the exact number of default assets in beta_config.yaml
"""

import yaml

def count_default_assets():
    """Count assets in default_symbols section."""
    try:
        with open('Beta_detection/beta_config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        default_symbols = config['assets']['default_symbols']
        
        print(f"📊 Default Assets Count Analysis")
        print(f"="*50)
        print(f"Total default assets: {len(default_symbols)}")
        
        # Show first few and last few
        print(f"\nFirst 5 assets:")
        for i, asset in enumerate(default_symbols[:5]):
            print(f"  {i+1}. {asset}")
        
        print(f"\nLast 5 assets:")
        for i, asset in enumerate(default_symbols[-5:], len(default_symbols)-4):
            print(f"  {i}. {asset}")
        
        # Check for duplicates
        unique_assets = set(default_symbols)
        if len(unique_assets) != len(default_symbols):
            duplicates = len(default_symbols) - len(unique_assets)
            print(f"\n⚠️  Found {duplicates} duplicate assets!")
        else:
            print(f"\n✅ No duplicates found")
        
        return len(default_symbols)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0

if __name__ == "__main__":
    count = count_default_assets()
    print(f"\n🎯 Final count: {count} default assets")
