#!/usr/bin/env python3
"""
Validate asset availability on Kraken and Bitvavo exchanges.
This script checks if all assets from the configuration files are available for trading.
"""

import os
import sys
import ccxt
import yaml
import logging
from datetime import datetime
from typing import Dict, Any, List, Set

# Add the src directory to the path
sys.path.append('src')

from config_manager import load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_assets_from_config(config_path: str) -> List[str]:
    """Load trading assets from configuration file."""
    try:
        config = load_config(config_path)
        assets = config.get('assets', [])
        print(f"Loaded {len(assets)} assets from {config_path}")
        return assets
    except Exception as e:
        print(f"Error loading config from {config_path}: {e}")
        return []

def get_exchange_markets(exchange_id: str) -> Dict[str, Any]:
    """Get available markets from an exchange."""
    try:
        exchange_class = getattr(ccxt, exchange_id, None)
        if not exchange_class:
            raise ValueError(f"Exchange '{exchange_id}' not found in ccxt")
        
        exchange = exchange_class({
            'enableRateLimit': True,
            'sandbox': False,
        })
        
        markets = exchange.load_markets()
        print(f"Loaded {len(markets)} markets from {exchange_id}")
        return markets
        
    except Exception as e:
        print(f"Error loading markets from {exchange_id}: {e}")
        return {}

def validate_assets_on_exchange(assets: List[str], exchange_id: str, markets: Dict[str, Any]) -> Dict[str, Any]:
    """Validate that assets are available on the exchange."""
    results = {
        'exchange': exchange_id,
        'total_assets': len(assets),
        'available_assets': [],
        'missing_assets': [],
        'asset_details': {}
    }
    
    print(f"\n{'='*50}")
    print(f"VALIDATING ASSETS ON {exchange_id.upper()}")
    print(f"{'='*50}")
    
    for asset in assets:
        if asset in markets:
            results['available_assets'].append(asset)
            market_info = markets[asset]
            results['asset_details'][asset] = {
                'active': market_info.get('active', False),
                'base': market_info.get('base', ''),
                'quote': market_info.get('quote', ''),
                'min_amount': market_info.get('limits', {}).get('amount', {}).get('min'),
                'min_cost': market_info.get('limits', {}).get('cost', {}).get('min'),
            }
            status = "✅ ACTIVE" if market_info.get('active', False) else "⚠️ INACTIVE"
            print(f"  {asset}: {status}")
        else:
            results['missing_assets'].append(asset)
            print(f"  {asset}: ❌ NOT FOUND")
    
    print(f"\nSummary for {exchange_id.upper()}:")
    print(f"  Available: {len(results['available_assets'])}/{len(assets)}")
    print(f"  Missing: {len(results['missing_assets'])}")
    
    return results

def check_asset_liquidity(asset: str, exchange_id: str) -> Dict[str, Any]:
    """Check basic liquidity information for an asset."""
    try:
        exchange_class = getattr(ccxt, exchange_id, None)
        if not exchange_class:
            return {'error': f"Exchange '{exchange_id}' not found"}
        
        exchange = exchange_class({
            'enableRateLimit': True,
            'sandbox': False,
        })
        
        # Fetch ticker to check if asset is actively traded
        ticker = exchange.fetch_ticker(asset)
        
        return {
            'last_price': ticker.get('last'),
            'bid': ticker.get('bid'),
            'ask': ticker.get('ask'),
            'volume': ticker.get('baseVolume'),
            'quote_volume': ticker.get('quoteVolume'),
            'timestamp': ticker.get('timestamp')
        }
        
    except Exception as e:
        return {'error': str(e)}

def print_detailed_report(kraken_results: Dict[str, Any], bitvavo_results: Dict[str, Any]):
    """Print a detailed report of asset availability."""
    print(f"\n{'='*70}")
    print(f"DETAILED ASSET AVAILABILITY REPORT")
    print(f"{'='*70}")
    
    all_assets = set(kraken_results['available_assets'] + kraken_results['missing_assets'])
    
    print(f"\n{'Asset':<15} {'Kraken':<15} {'Bitvavo':<15} {'Status'}")
    print(f"{'-'*15} {'-'*15} {'-'*15} {'-'*15}")
    
    for asset in sorted(all_assets):
        kraken_status = "✅" if asset in kraken_results['available_assets'] else "❌"
        bitvavo_status = "✅" if asset in bitvavo_results['available_assets'] else "❌"
        
        if asset in kraken_results['available_assets'] and asset in bitvavo_results['available_assets']:
            overall_status = "BOTH"
        elif asset in kraken_results['available_assets'] or asset in bitvavo_results['available_assets']:
            overall_status = "PARTIAL"
        else:
            overall_status = "NONE"
        
        print(f"{asset:<15} {kraken_status:<15} {bitvavo_status:<15} {overall_status}")
    
    # Summary statistics
    both_available = len(set(kraken_results['available_assets']) & set(bitvavo_results['available_assets']))
    kraken_only = len(set(kraken_results['available_assets']) - set(bitvavo_results['available_assets']))
    bitvavo_only = len(set(bitvavo_results['available_assets']) - set(kraken_results['available_assets']))
    neither = len(set(kraken_results['missing_assets']) & set(bitvavo_results['missing_assets']))
    
    print(f"\n{'='*70}")
    print(f"SUMMARY STATISTICS")
    print(f"{'='*70}")
    print(f"Available on both exchanges: {both_available}")
    print(f"Available on Kraken only: {kraken_only}")
    print(f"Available on Bitvavo only: {bitvavo_only}")
    print(f"Not available on either: {neither}")
    print(f"Total assets checked: {len(all_assets)}")

def main():
    """Main function to validate asset availability."""
    try:
        print(f"\n{'='*70}")
        print(f"ASSET AVAILABILITY VALIDATION")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"{'='*70}")
        
        # Load assets from both config files
        kraken_assets = load_assets_from_config('config/settings_kraken_eur.yaml')
        bitvavo_assets = load_assets_from_config('config/settings_bitvavo_eur.yaml')
        
        # Verify both configs have the same assets
        if set(kraken_assets) != set(bitvavo_assets):
            print("⚠️ WARNING: Asset lists differ between Kraken and Bitvavo configs!")
            print(f"Kraken assets: {kraken_assets}")
            print(f"Bitvavo assets: {bitvavo_assets}")
        
        # Use the union of both asset lists
        all_assets = list(set(kraken_assets + bitvavo_assets))
        print(f"Total unique assets to validate: {len(all_assets)}")
        
        # Get markets from both exchanges
        kraken_markets = get_exchange_markets('kraken')
        bitvavo_markets = get_exchange_markets('bitvavo')
        
        if not kraken_markets or not bitvavo_markets:
            print("❌ Failed to load markets from one or both exchanges")
            return
        
        # Validate assets on both exchanges
        kraken_results = validate_assets_on_exchange(all_assets, 'kraken', kraken_markets)
        bitvavo_results = validate_assets_on_exchange(all_assets, 'bitvavo', bitvavo_markets)
        
        # Print detailed report
        print_detailed_report(kraken_results, bitvavo_results)
        
        # Check if all assets are available on both exchanges
        both_available = set(kraken_results['available_assets']) & set(bitvavo_results['available_assets'])
        
        if len(both_available) == len(all_assets):
            print(f"\n🎉 SUCCESS: All {len(all_assets)} assets are available on both exchanges!")
            return True
        else:
            missing_count = len(all_assets) - len(both_available)
            print(f"\n⚠️ WARNING: {missing_count} assets are not available on both exchanges.")
            return False
            
    except KeyboardInterrupt:
        print("\n\nValidation interrupted by user.")
        return False
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        logging.error(f"Unexpected error: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
