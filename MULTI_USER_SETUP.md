# Multi-User Background Service Setup

This guide explains how to run multiple instances of the background service simultaneously with different user credentials.

## Overview

You can run the background service for multiple users simultaneously, each with their own:
- Binance API credentials
- Telegram bot notifications
- Separate log files
- Same trading strategy configuration

## Quick Setup

### 1. Set Up Environment Files

**Keep your credentials in the regular `.env` file:**
```bash
# .env (your credentials)
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_secret
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

**Create `.env.person2` for the other person:**
```bash
# .env.person2 (other person's credentials)
BINANCE_API_KEY=BINACEKEY
BINANCE_API_SECRET=BINANCESECRET
TELEGRAM_BOT_TOKEN=BOTTOKEN
TELEGRAM_CHAT_ID=CHATID
```

### 2. Start Both Services

**Option A: Use the batch script (Windows):**
```bash
start_both_services.bat
```

**Option B: Use PowerShell script:**
```powershell
.\start_both_services.ps1
```

**Option C: Start manually:**
```bash
# Terminal 1 - Your service (uses .env)
python start_service_main.py

# Terminal 2 - Person 2's service (uses .env.person2)
python start_service_person2.py
```

## How It Works

### Environment Variable Loading
- Each startup script loads its specific `.env.personX` file
- The background service automatically uses environment variables for:
  - `BINANCE_API_KEY` and `BINANCE_API_SECRET` for trading
  - `TELEGRAM_BOT_TOKEN` and `TELEGRAM_CHAT_ID` for notifications

### Shared Configuration
- Both services use the same `config/settings.yaml` for strategy parameters
- Same asset list, trading logic, and scheduling
- Only credentials differ between instances

### Separate Logging
- Each service runs independently
- Separate log files and console outputs
- No interference between instances

## Advanced Setup (Production)

### For Linux/Server Deployment

Create separate systemd service files:

**`asset_rotation_service_person1.service`:**
```ini
[Unit]
Description=Asset Rotation Strategy Background Service - Person 1
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=/path/to/Asset_Screener_Python
ExecStart=/path/to/venv/bin/python start_service_person1.py
Restart=always
RestartSec=5s
StandardOutput=append:/var/log/asset-rotation-person1.log
StandardError=append:/var/log/asset-rotation-person1-error.log

[Install]
WantedBy=multi-user.target
```

**`asset_rotation_service_person2.service`:**
```ini
[Unit]
Description=Asset Rotation Strategy Background Service - Person 2
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=/path/to/Asset_Screener_Python
ExecStart=/path/to/venv/bin/python start_service_person2.py
Restart=always
RestartSec=5s
StandardOutput=append:/var/log/asset-rotation-person2.log
StandardError=append:/var/log/asset-rotation-person2-error.log

[Install]
WantedBy=multi-user.target
```

### Enable and Start Services
```bash
# Copy service files
sudo cp asset_rotation_service_person*.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable services
sudo systemctl enable asset_rotation_service_person1
sudo systemctl enable asset_rotation_service_person2

# Start services
sudo systemctl start asset_rotation_service_person1
sudo systemctl start asset_rotation_service_person2

# Check status
sudo systemctl status asset_rotation_service_person1
sudo systemctl status asset_rotation_service_person2
```

## Monitoring

### Check Service Status
```bash
# View logs for Person 1
tail -f /var/log/asset-rotation-person1.log

# View logs for Person 2  
tail -f /var/log/asset-rotation-person2.log
```

### Telegram Notifications
- Each user receives notifications on their own Telegram chat
- Notifications include user identification in the message
- No cross-contamination between users

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Services run independently, no port conflicts
2. **API Rate Limits**: Each user has separate Binance API limits
3. **File Locks**: Services use separate state files automatically
4. **Memory Usage**: Each service uses ~100-200MB RAM

### Verification Steps

1. **Check Environment Variables**:
   ```bash
   python start_service_person1.py
   # Should show loaded credentials (masked)
   ```

2. **Test Telegram Notifications**:
   ```bash
   python test_telegram_notifications.py
   ```

3. **Verify Binance Connection**:
   ```bash
   python test_binance_connection.py
   ```

## Security Notes

- Keep `.env.person*` files secure and never commit to version control
- Each user's credentials are isolated
- No shared API keys or tokens
- Separate notification channels prevent information leakage

## Scaling

You can add more users by:
1. Creating `.env.person3`, `.env.person4`, etc.
2. Creating corresponding startup scripts
3. Adding more service files for production deployment

The system supports unlimited concurrent users with proper resource allocation.
