#!/usr/bin/env python
"""
Test script to trace data flow from backend to frontend.
This script simulates:
1. Data generation in the backend (test_allocation.py)
2. Data formatting for API response (api_server.py)
3. How the frontend would process this data

Usage:
    python test_data_flow.py [--verbose] [--save-json]
"""

import argparse
import json
import logging
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_flow_test')

class DataFlowTester:
    """Test the data flow from backend to frontend."""
    
    def __init__(self, verbose=False, save_json=False):
        """Initialize the tester."""
        self.verbose = verbose
        self.save_json = save_json
        self.output_dir = 'data_flow_test_output'
        
        # Create output directory if it doesn't exist
        if self.save_json and not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_backend_data(self) -> Dict[str, Any]:
        """
        Generate sample data similar to what test_allocation.py would produce.
        
        Returns:
            Dict containing strategy results
        """
        logger.info("Generating backend data (similar to test_allocation.py output)...")
        
        # Create sample date range
        start_date = datetime(2023, 10, 20)
        end_date = datetime.now()
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate strategy equity curve (with some randomness)
        np.random.seed(42)  # For reproducibility
        initial_value = 10000
        daily_returns = np.random.normal(0.001, 0.02, len(date_range))
        cumulative_returns = np.cumprod(1 + daily_returns)
        strategy_equity = pd.Series(initial_value * cumulative_returns, index=date_range)
        
        # Generate buy and hold curves for sample assets
        assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        buy_hold_curves = {}
        
        for i, asset in enumerate(assets):
            # Different seed for each asset
            np.random.seed(42 + i)
            asset_returns = np.random.normal(0.0008, 0.025, len(date_range))
            asset_cumulative = np.cumprod(1 + asset_returns)
            buy_hold_curves[asset] = pd.Series(initial_value * asset_cumulative, index=date_range)
        
        # Generate best asset series (which asset was selected on each day)
        best_asset_series = pd.Series(index=date_range, dtype=object)
        for i, date in enumerate(date_range):
            # Rotate through assets and sometimes be out of market
            if i % 30 < 10:
                best_asset_series[date] = assets[0]
            elif i % 30 < 20:
                best_asset_series[date] = assets[1]
            elif i % 30 < 25:
                best_asset_series[date] = assets[2]
            else:
                best_asset_series[date] = 'out-of-market'
        
        # Generate MTPI signals (1 for bullish, -1 for bearish)
        mtpi_signals = pd.Series(index=date_range, dtype=float)
        for i, date in enumerate(date_range):
            # Create some signal changes
            if i % 45 < 35:
                mtpi_signals[date] = 1.0  # Bullish
            else:
                mtpi_signals[date] = -1.0  # Bearish
        
        # Create performance metrics
        performance_metrics = {
            'Total Return': {
                'Strategy': 100 * (strategy_equity.iloc[-1] / strategy_equity.iloc[0] - 1),
                'BTC/USDT': 100 * (buy_hold_curves['BTC/USDT'].iloc[-1] / buy_hold_curves['BTC/USDT'].iloc[0] - 1),
                'ETH/USDT': 100 * (buy_hold_curves['ETH/USDT'].iloc[-1] / buy_hold_curves['ETH/USDT'].iloc[0] - 1),
                'SOL/USDT': 100 * (buy_hold_curves['SOL/USDT'].iloc[-1] / buy_hold_curves['SOL/USDT'].iloc[0] - 1),
            },
            'Sharpe Ratio': {
                'Strategy': 1.5,
                'BTC/USDT': 0.8,
                'ETH/USDT': 1.1,
                'SOL/USDT': 1.3,
            },
            'Max Drawdown': {
                'Strategy': -25.0,
                'BTC/USDT': -45.0,
                'ETH/USDT': -40.0,
                'SOL/USDT': -50.0,
            }
        }
        
        # Combine all data
        backend_data = {
            'strategy_equity': strategy_equity,
            'buy_hold_curves': buy_hold_curves,
            'best_asset_series': best_asset_series,
            'mtpi_signals': mtpi_signals,
            'performance_metrics': performance_metrics
        }
        
        if self.verbose:
            logger.info(f"Generated strategy equity curve with {len(strategy_equity)} data points")
            logger.info(f"Generated buy and hold curves for {len(buy_hold_curves)} assets")
            logger.info(f"Generated best asset series with {len(best_asset_series)} data points")
            logger.info(f"Generated MTPI signals with {len(mtpi_signals)} data points")
        
        return backend_data
    
    def format_for_api_response(self, backend_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format the backend data as it would be in the API response.
        
        Args:
            backend_data: The data generated by the backend
            
        Returns:
            Dict formatted as an API response
        """
        logger.info("Formatting data for API response (similar to api_server.py)...")
        
        # Convert pandas Series to lists of dicts for JSON serialization
        strategy_equity = backend_data['strategy_equity']
        strategy_data = []
        
        for date, value in strategy_equity.items():
            strategy_data.append({
                'time': int(date.timestamp()),  # Convert to Unix timestamp
                'value': float(value)
            })
        
        # Format buy and hold curves
        buy_hold_data = {}
        for asset, curve in backend_data['buy_hold_curves'].items():
            asset_data = []
            for date, value in curve.items():
                asset_data.append({
                    'time': int(date.timestamp()),
                    'value': float(value)
                })
            buy_hold_data[asset] = asset_data
        
        # Format best asset series
        best_asset_data = []
        for date, asset in backend_data['best_asset_series'].items():
            best_asset_data.append({
                'time': int(date.timestamp()),
                'asset': asset
            })
        
        # Format MTPI signals
        mtpi_data = []
        for date, signal in backend_data['mtpi_signals'].items():
            mtpi_data.append({
                'time': int(date.timestamp()),
                'signal': float(signal)
            })
        
        # Create API response structure
        api_response = {
            'curves': {
                'strategy': strategy_data,
            },
            'buyHoldCurves': buy_hold_data,
            'assetChanges': best_asset_data,
            'metadata': {
                'startDate': backend_data['strategy_equity'].index.min().strftime('%Y-%m-%d'),
                'endDate': backend_data['strategy_equity'].index.max().strftime('%Y-%m-%d'),
                'rawMtpiSignals': mtpi_data
            },
            'performanceMetrics': backend_data['performance_metrics']
        }
        
        if self.verbose:
            logger.info(f"Formatted strategy data with {len(strategy_data)} points")
            logger.info(f"Formatted buy and hold data for {len(buy_hold_data)} assets")
            logger.info(f"Formatted best asset data with {len(best_asset_data)} points")
            logger.info(f"Formatted MTPI data with {len(mtpi_data)} points")
        
        # Save API response to JSON file
        if self.save_json:
            api_file = os.path.join(self.output_dir, 'api_response.json')
            with open(api_file, 'w') as f:
                json.dump(api_response, f, indent=2)
            logger.info(f"Saved API response to {api_file}")
        
        return api_response
    
    def simulate_frontend_processing(self, api_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Simulate how the frontend would process the API response.
        
        Args:
            api_response: The API response data
            
        Returns:
            Dict containing the processed data as it would be used in the frontend
        """
        logger.info("Simulating frontend processing (similar to TradingViewDashboard.jsx)...")
        
        # Create the frontend data structure
        frontend_data = {
            'curves': {},
            'metadata': api_response['metadata']
        }
        
        # Process strategy data
        if 'strategy' in api_response['curves']:
            logger.info(f"Processing strategy data with {len(api_response['curves']['strategy'])} points")
            frontend_data['curves']['strategy'] = api_response['curves']['strategy']
        
        # Process buy and hold curves
        if 'buyHoldCurves' in api_response:
            for asset, data in api_response['buyHoldCurves'].items():
                logger.info(f"Processing {asset} data with {len(data)} points")
                frontend_data['curves'][asset] = data
        
        # Process asset changes for visualization
        if 'assetChanges' in api_response:
            frontend_data['assetChanges'] = api_response['assetChanges']
        
        # Process performance metrics
        if 'performanceMetrics' in api_response:
            frontend_data['performanceMetrics'] = api_response['performanceMetrics']
        
        # Save frontend data to JSON file
        if self.save_json:
            frontend_file = os.path.join(self.output_dir, 'frontend_processed.json')
            with open(frontend_file, 'w') as f:
                json.dump(frontend_data, f, indent=2)
            logger.info(f"Saved frontend processed data to {frontend_file}")
        
        return frontend_data
    
    def run_test(self):
        """Run the complete data flow test."""
        # Step 1: Generate backend data
        backend_data = self.generate_backend_data()
        
        # Step 2: Format for API response
        api_response = self.format_for_api_response(backend_data)
        
        # Step 3: Simulate frontend processing
        frontend_data = self.simulate_frontend_processing(api_response)
        
        # Step 4: Compare data structures and identify potential issues
        self.analyze_data_flow(backend_data, api_response, frontend_data)
        
        return backend_data, api_response, frontend_data
    
    def analyze_data_flow(self, backend_data, api_response, frontend_data):
        """
        Analyze the data flow and identify potential issues.
        
        Args:
            backend_data: The original backend data
            api_response: The API response data
            frontend_data: The processed frontend data
        """
        logger.info("Analyzing data flow for potential issues...")
        
        # Check data point counts
        backend_count = len(backend_data['strategy_equity'])
        api_count = len(api_response['curves']['strategy'])
        frontend_count = len(frontend_data['curves']['strategy'])
        
        logger.info(f"Data point counts - Backend: {backend_count}, API: {api_count}, Frontend: {frontend_count}")
        
        if backend_count != api_count:
            logger.warning(f"Data point count mismatch between backend and API: {backend_count} vs {api_count}")
        
        if api_count != frontend_count:
            logger.warning(f"Data point count mismatch between API and frontend: {api_count} vs {frontend_count}")
        
        # Check for timestamp format consistency
        if 'strategy' in frontend_data['curves'] and frontend_data['curves']['strategy']:
            first_point = frontend_data['curves']['strategy'][0]
            if 'time' in first_point:
                logger.info(f"Frontend timestamp format: {type(first_point['time']).__name__}")
                
                # Check if timestamp is in seconds (Unix timestamp)
                if isinstance(first_point['time'], int) and first_point['time'] > 1000000000:
                    logger.info("Timestamps appear to be Unix timestamps (seconds since epoch)")
                else:
                    logger.warning(f"Unexpected timestamp format: {first_point['time']}")
        
        # Check for value format consistency
        if 'strategy' in frontend_data['curves'] and frontend_data['curves']['strategy']:
            first_point = frontend_data['curves']['strategy'][0]
            if 'value' in first_point:
                logger.info(f"Frontend value format: {type(first_point['value']).__name__}")
        
        # Generate summary report
        logger.info("\n=== DATA FLOW SUMMARY ===")
        logger.info(f"Backend data points: {backend_count}")
        logger.info(f"API response data points: {api_count}")
        logger.info(f"Frontend processed data points: {frontend_count}")
        logger.info(f"Assets in buy & hold curves: {list(backend_data['buy_hold_curves'].keys())}")
        logger.info("========================\n")

def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(description='Test data flow from backend to frontend')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--save-json', action='store_true', help='Save JSON files at each step')
    args = parser.parse_args()
    
    tester = DataFlowTester(verbose=args.verbose, save_json=args.save_json)
    backend_data, api_response, frontend_data = tester.run_test()
    
    logger.info("Data flow test completed successfully!")
    
    # Return data for potential further analysis
    return backend_data, api_response, frontend_data

if __name__ == '__main__':
    main()
