Current setup is configured such that two people could automaticaly run strategy on the cloud with up to three exchanges.

But for now we are using BITVAVO, KRAKEN.

.env.<PERSON><PERSON> is <PERSON><PERSON><PERSON> credentials
.env is <PERSON><PERSON> credentials

Main scripts to run

asset_rotation_bitvavo_service.service #runs Edgaro .env kartu su setupu
asset_rotation_bitvavo_service.service


#runs Liepos .env kartu su setupu
asset_rotation_bitvavo_liepa_service.service
asset_rotation_kraken_liepa_service.service

