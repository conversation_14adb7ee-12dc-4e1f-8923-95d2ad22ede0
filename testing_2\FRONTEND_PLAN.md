# Frontend Visualization Plan: Asset Rotation Strategy Analysis

This document outlines the plan for building a React frontend application to visualize asset rotation strategy analysis, based on the provided requirements and image description.

**1. Project Setup & Technology Stack:**

*   **Framework/Library:** React
*   **Build Tool:** Vite
*   **Charting Library:** Lightweight Charts
*   **Styling:** Tailwind CSS
*   **Language:** JavaScript (JSX)

**2. Application Structure & Layout:**

The application will be a single-page interface resembling the layout described in the user request:

*   **Root Component (`App.jsx`):** Serves as the main container, managing the overall layout and potentially holding shared state or fetching logic if needed, though fetching will primarily be delegated to child components.
*   **Layout:** A vertical flow:
    *   **Header:** Displays the main title "Asset Rotation Strategy Analysis...".
    *   **Equity Chart:** Displays the comparison between the strategy and buy-and-hold equity curves.
    *   **Performance Table:** Displays key performance metrics below the chart.

**3. Core React Components:**

```mermaid
graph TD
    App --> Header;
    App --> EquityChart;
    App --> PerformanceTable;
    EquityChart -- Fetches from --> ApiEquity(GET /api/equity);
    PerformanceTable -- Fetches from --> ApiPerformance(GET /api/performance);
```

*   **`App.jsx`:**
    *   Located at `frontend/src/App.jsx`.
    *   Renders the `Header`, `EquityChart`, and `PerformanceTable` components in the correct order.
    *   Applies basic layout styling using Tailwind CSS (e.g., flex column, padding).
*   **`Header.jsx`:** (or potentially just JSX within `App.jsx`)
    *   Located at `frontend/src/components/Header.jsx` (if created as a separate component).
    *   Displays the static title "Asset Rotation Strategy Analysis...".
    *   Styled using Tailwind CSS.
*   **`EquityChart.jsx`:**
    *   Located at `frontend/src/components/EquityChart.jsx`.
    *   **Responsibility:** Fetch equity data and render the chart.
    *   **Data Fetching:** Uses `fetch` or `axios` within a `useEffect` hook to call the `/api/equity` endpoint upon component mount.
    *   **State:** Manages loading state and stores the fetched `strategy` and `buyHold` data arrays.
    *   **Charting:**
        *   Initializes a Lightweight Charts instance targeting a `div` element within the component.
        *   Formats the fetched data arrays (e.g., `{date, value}`) into the `{time, value}` format required by Lightweight Charts.
        *   Creates two `LineSeries`: one for the 'strategy' data and one for the 'buyHold' data.
        *   Applies basic chart options (e.g., colors, labels if possible via library options).
*   **`PerformanceTable.jsx`:**
    *   Located at `frontend/src/components/PerformanceTable.jsx`.
    *   **Responsibility:** Fetch performance metrics and display them in a table.
    *   **Data Fetching:** Uses `fetch` or `axios` within a `useEffect` hook to call the `/api/performance` endpoint upon component mount.
    *   **State:** Manages loading state and stores the fetched performance metrics object.
    *   **Display:**
        *   Renders an HTML `<table>` element.
        *   Iterates over the key-value pairs of the fetched performance data object.
        *   For each metric, renders a table row (`<tr>`) with two cells (`<td>`): one for the metric name (key) and one for the metric value.
        *   Applies table styling using Tailwind CSS (e.g., borders, padding, text alignment).

**4. Data Fetching:**

*   API calls will be made from the respective components (`EquityChart`, `PerformanceTable`) using the browser's `fetch` API or a library like `axios`.
*   Error handling and loading states will be implemented within each component that fetches data.
*   The existing `frontend/src/services/api.js` might be used or adapted to centralize API call logic if preferred, but direct fetching within components is also acceptable for this scope.

**5. Styling:**

*   Tailwind CSS will be used for all styling, applied directly via class names in the JSX elements.
*   Configuration for Tailwind will be in `frontend/tailwind.config.js` (assuming standard Vite setup).

**6. Next Steps (Implementation Phase):**

1.  Ensure Vite, React, Lightweight Charts, and Tailwind CSS are installed in the `frontend` directory.
2.  Create or modify the specified components (`App.jsx`, `EquityChart.jsx`, `PerformanceTable.jsx`, potentially `Header.jsx`).
3.  Implement the data fetching logic using `useEffect` and `useState` hooks.
4.  Integrate Lightweight Charts into `EquityChart.jsx`, passing the formatted data.
5.  Implement the table rendering logic in `PerformanceTable.jsx`.
6.  Apply Tailwind CSS classes for layout and styling.
7.  Ensure the backend API server (providing `/api/equity` and `/api/performance`) is running during development.