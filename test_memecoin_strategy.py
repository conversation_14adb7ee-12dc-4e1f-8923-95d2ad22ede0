#!/usr/bin/env python3
"""
Test Script for Memecoin Strategy

This script tests the memecoin signal strategy functionality including:
- Configuration loading
- GeckoTerminal data fetching
- Signal generation
- Notification system
- Performance analysis

Usage:
    python test_memecoin_strategy.py [options]

Author: Asset Rotation Strategy Team
Version: 1.0.0
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import the memecoin service
from background_service_memecoins import MemecoinBackgroundService

# Import testing utilities
from test_allocation import run_strategy_for_web

def test_configuration_loading():
    """Test configuration loading for memecoin strategy."""
    print("\n" + "=" * 50)
    print("TESTING CONFIGURATION LOADING")
    print("=" * 50)

    try:
        # Test memecoin config loading
        service = MemecoinBackgroundService(test_mode=True)

        print("✓ Memecoin configuration loaded successfully")
        print(f"  Strategy name: {service.config.get('strategy', {}).get('name', 'Unknown')}")
        print(f"  Timeframe: {service.config.get('settings', {}).get('timeframe', 'Unknown')}")
        print(f"  Number of assets: {service.config.get('settings', {}).get('n_assets', 'Unknown')}")
        print(f"  Signal-only mode: {service.config.get('execution', {}).get('signal_only', 'Unknown')}")

        # Test notification config
        if service.notification_config:
            print("✓ Notification configuration loaded successfully")
            telegram_enabled = service.notification_config.get('telegram', {}).get('enabled', False)
            print(f"  Telegram enabled: {telegram_enabled}")
        else:
            print("✗ Notification configuration failed to load")

        return True

    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False

def test_geckoterminal_assets():
    """Test GeckoTerminal asset loading."""
    print("\n" + "=" * 50)
    print("TESTING GECKOTERMINAL ASSET LOADING")
    print("=" * 50)

    try:
        service = MemecoinBackgroundService(test_mode=True)
        assets = service._get_geckoterminal_assets()

        if assets:
            print(f"✓ Loaded {len(assets)} GeckoTerminal assets")
            print("  Sample assets:")
            for i, asset in enumerate(assets[:5]):
                # Asset is now a dictionary with network, token_address, symbol
                if isinstance(asset, dict):
                    symbol = asset.get('symbol', 'Unknown')
                    network = asset.get('network', 'Unknown')
                    print(f"    {i+1}. {symbol} on {network}")
                else:
                    print(f"    {i+1}. {asset}")
            if len(assets) > 5:
                print(f"    ... and {len(assets) - 5} more")
        else:
            print("✗ No GeckoTerminal assets loaded")
            return False

        return True

    except Exception as e:
        print(f"✗ GeckoTerminal asset loading failed: {e}")
        return False

def test_strategy_execution():
    """Test strategy execution with GeckoTerminal assets."""
    print("\n" + "=" * 50)
    print("TESTING STRATEGY EXECUTION")
    print("=" * 50)

    try:
        service = MemecoinBackgroundService(test_mode=True)
        assets = service._get_geckoterminal_assets()

        if not assets:
            print("✗ No assets available for testing")
            return False

        print(f"Running strategy analysis with {len(assets)} assets...")

        # Limit to first 5 assets for testing to speed up execution
        test_assets = assets[:5]
        print(f"Testing with first {len(test_assets)} assets for speed")

        start_time = datetime.now()
        result = service._run_strategy_analysis(test_assets)
        execution_time = (datetime.now() - start_time).total_seconds()

        if result:
            print(f"✓ Strategy execution completed in {execution_time:.2f} seconds")
            print(f"  MTPI Signal: {result.get('mtpi_signal')}")
            print(f"  Best assets: {len(result.get('best_assets', []))}")
            print(f"  Asset scores: {len(result.get('asset_scores', {}))}")

            # Display top assets
            best_assets = result.get('best_assets', [])[:3]
            asset_scores = result.get('asset_scores', {})

            if best_assets:
                print("  Top 3 assets:")
                for i, asset in enumerate(best_assets):
                    score = asset_scores.get(asset, 0)
                    # Asset names should now be clean symbols from the strategy
                    print(f"    {i+1}. {asset}: {score:.4f}")
        else:
            print("✗ Strategy execution failed")
            return False

        return True

    except Exception as e:
        print(f"✗ Strategy execution failed: {e}")
        logging.error(f"Strategy execution error: {e}", exc_info=True)
        return False

def test_notification_system():
    """Test the notification system."""
    print("\n" + "=" * 50)
    print("TESTING NOTIFICATION SYSTEM")
    print("=" * 50)

    service = None
    try:
        service = MemecoinBackgroundService(test_mode=True)

        if not service.notification_manager:
            print("✗ Notification manager not initialized")
            return False

        print("✓ Notification manager initialized")

        # Test MTPI signal notification
        print("Testing MTPI signal notification...")
        service._send_mtpi_signal_notification(1, -1)
        print("✓ MTPI signal notification sent")

        # Test asset rotation notification
        print("Testing asset rotation notification...")
        test_assets = ['BONK/USDT', 'PEPE/USDT', 'SHIB/USDT']
        test_scores = {'BONK/USDT': 0.85, 'PEPE/USDT': 0.72, 'SHIB/USDT': 0.68}
        service._send_asset_rotation_notification(test_assets, test_scores)
        print("✓ Asset rotation notification sent")

        # Test performance update
        print("Testing performance update notification...")
        test_metrics = {
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': -0.08
        }
        service._send_performance_update(test_metrics, test_scores)
        print("✓ Performance update notification sent")

        return True

    except Exception as e:
        print(f"✗ Notification system test failed: {e}")
        logging.error(f"Notification test error: {e}", exc_info=True)
        return False
    finally:
        # Properly cleanup the service and its connections
        if service and service.notification_manager:
            try:
                # Stop the notification manager if it has a stop method
                if hasattr(service.notification_manager, 'stop'):
                    service.notification_manager.stop()
                # Add a small delay to ensure cleanup
                import time
                time.sleep(1)
            except Exception as cleanup_error:
                print(f"Warning: Error during cleanup: {cleanup_error}")

def test_service_lifecycle():
    """Test service start/stop lifecycle."""
    print("\n" + "=" * 50)
    print("TESTING SERVICE LIFECYCLE")
    print("=" * 50)

    service = None
    try:
        service = MemecoinBackgroundService(test_mode=True)

        print("Starting service...")
        service.start()
        print("✓ Service started successfully")

        # Wait a moment
        import time
        time.sleep(2)

        print("Stopping service...")
        service.stop()
        print("✓ Service stopped successfully")

        # Additional cleanup time
        time.sleep(2)

        return True

    except Exception as e:
        print(f"✗ Service lifecycle test failed: {e}")
        return False
    finally:
        # Ensure service is stopped
        if service:
            try:
                service.stop()
                import time
                time.sleep(1)
            except Exception as cleanup_error:
                print(f"Warning: Error during final cleanup: {cleanup_error}")

def run_full_test_suite():
    """Run the complete test suite."""
    print("=" * 80)
    print("MEMECOIN STRATEGY TEST SUITE")
    print("=" * 80)

    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("GeckoTerminal Assets", test_geckoterminal_assets),
        ("Strategy Execution", test_strategy_execution),
        ("Notification System", test_notification_system),
        ("Service Lifecycle", test_service_lifecycle)
    ]

    results = []

    for i, (test_name, test_func) in enumerate(tests):
        print(f"\nRunning test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))

            # Add delay between tests to prevent Telegram bot conflicts
            if i < len(tests) - 1:  # Don't delay after the last test
                import time
                print("Waiting 3 seconds before next test to prevent bot conflicts...")
                time.sleep(3)

        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results.append((test_name, False))

            # Still add delay even if test failed
            if i < len(tests) - 1:
                import time
                print("Waiting 3 seconds before next test...")
                time.sleep(3)

    # Print summary
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "✓" if result else "✗"
        print(f"{icon} {test_name}: {status}")

        if result:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(results)*100):.1f}%")

    if failed == 0:
        print("\n🎉 All tests passed! The memecoin strategy is ready to deploy.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues before deployment.")

    return failed == 0

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test Memecoin Strategy')
    parser.add_argument('--test', type=str, choices=[
        'config', 'assets', 'strategy', 'notifications', 'lifecycle', 'all'
    ], default='all', help='Specific test to run')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Set the logging level')

    args = parser.parse_args()

    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run specific test or full suite
    if args.test == 'all':
        success = run_full_test_suite()
    elif args.test == 'config':
        success = test_configuration_loading()
    elif args.test == 'assets':
        success = test_geckoterminal_assets()
    elif args.test == 'strategy':
        success = test_strategy_execution()
    elif args.test == 'notifications':
        success = test_notification_system()
    elif args.test == 'lifecycle':
        success = test_service_lifecycle()
    else:
        print(f"Unknown test: {args.test}")
        success = False

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
