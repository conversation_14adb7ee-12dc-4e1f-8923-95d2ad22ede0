2025-05-23 22:45:19,704 - INFO - Starting comprehensive test suite for PGO discrepancy investigation
2025-05-23 22:45:19,705 - INFO - === STARTING COMPREHENSIVE TEST SUITE ===
2025-05-23 22:45:19,705 - INFO - Running 3 test files...
2025-05-23 22:45:19,705 - INFO - === RUNNING test_pgo_methods.py ===
2025-05-23 22:45:23,488 - INFO - === RUNNING test_pgo_comparison.py ===
2025-05-23 22:45:25,904 - INFO - === RUNNING test_scoring_mechanism.py ===
2025-05-23 22:45:28,450 - INFO - === GENERATING SUMMARY REPORT ===
2025-05-23 22:45:28,450 - INFO - Test Results Summary:
2025-05-23 22:45:28,455 - INFO -   Total tests: 3
2025-05-23 22:45:28,455 - INFO -   Successful: 3
2025-05-23 22:45:28,455 - INFO -   Failed: 0
2025-05-23 22:45:28,455 - INFO -   Successful tests:
2025-05-23 22:45:28,459 - INFO - Detailed report saved to: test_report_20250523_224528.txt
2025-05-23 22:45:28,459 - INFO - === COLLECTING GENERATED FILES ===
2025-05-23 22:45:28,465 - WARNING - Missing expected files:
2025-05-23 22:45:28,472 - INFO - Additional generated files:
2025-05-23 22:45:28,472 - INFO -   + allocation_history_weighted_20-80-0_1d_1d_2021-02-15.csv
2025-05-23 22:45:28,472 - INFO -   + allocation_history_weighted_20-80-0_1d_1d_2021-02-16.csv
2025-05-23 22:45:28,472 - INFO -   + allocation_history_weighted_20-80-0_1d_1d_2024-02-10.csv
2025-05-23 22:45:28,472 - INFO -   + allocation_history_weighted_20-80-0_1d_1d_2025-02-10.csv
2025-05-23 22:45:28,472 - INFO -   + allocation_history_weighted_20-80-0_1d_1d_2025-04-25.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_70-20-10_1d_1d_2023-10-20.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_70-20-10_1d_1d_2025-02-10.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2021-04-25.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2023-04-12.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2023-10-19.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-02-09.csv
2025-05-23 22:45:28,473 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-02-11.csv
2025-05-23 22:45:28,475 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-03-10.csv
2025-05-23 22:45:28,475 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-10.csv
2025-05-23 22:45:28,475 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-12.csv
2025-05-23 22:45:28,475 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-13.csv
2025-05-23 22:45:28,476 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-15.csv
2025-05-23 22:45:28,476 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-21.csv
2025-05-23 22:45:28,476 - INFO -   + allocation_history_weighted_80-20-0_1d_1d_2025-04-25.csv
2025-05-23 22:45:28,476 - INFO -   + allocation_report.py
2025-05-23 22:45:28,476 - INFO -   + All_USDC_PAIRS_FROM_BINANCE_ATLEAST_35_AGE.txt
2025-05-23 22:45:28,478 - INFO -   + background_service.py
2025-05-23 22:45:28,478 - INFO -   + background_service_debug.py
2025-05-23 22:45:28,478 - INFO -   + background_service_incremental.py
2025-05-23 22:45:28,478 - INFO -   + batch_strategy_runner.py
2025-05-23 22:45:28,478 - INFO -   + binance_usdc_pairs.txt
2025-05-23 22:45:28,478 - INFO -   + clear_mtpi_cache.py
2025-05-23 22:45:28,478 - INFO -   + compare_asset_selection.py
2025-05-23 22:45:28,478 - INFO -   + compare_mtpi_signals.py
2025-05-23 22:45:28,478 - INFO -   + compare_signals_and_assets.py
2025-05-23 22:45:28,478 - INFO -   + compare_test_allocation.py
2025-05-23 22:45:28,478 - INFO -   + comprehensive_test_results.log
2025-05-23 22:45:28,478 - INFO -   + correlation_brackets.py
2025-05-23 22:45:28,480 - INFO -   + count_src_code.py
2025-05-23 22:45:28,480 - INFO -   + debug_discrepancy.log
2025-05-23 22:45:28,480 - INFO -   + debug_discrepancy.py
2025-05-23 22:45:28,480 - INFO -   + debug_mtpi_2025.py
2025-05-23 22:45:28,481 - INFO -   + debug_mtpi_cache.py
2025-05-23 22:45:28,482 - INFO -   + fetch_binance_usdc_pairs.py
2025-05-23 22:45:28,482 - INFO -   + fetch_geckoterminal_tokens.py
2025-05-23 22:45:28,482 - INFO -   + generate_performance_report.py
2025-05-23 22:45:28,482 - INFO -   + manual_fix_hosts.py
2025-05-23 22:45:28,482 - INFO -   + preload_data.py
2025-05-23 22:45:28,482 - INFO -   + reporter_scores.csv
2025-05-23 22:45:28,482 - INFO -   + requirements.txt
2025-05-23 22:45:28,483 - INFO -   + restore_hosts.py
2025-05-23 22:45:28,483 - INFO -   + run_all_data_flow_tests.py
2025-05-23 22:45:28,483 - INFO -   + run_all_tests.py
2025-05-23 22:45:28,484 - INFO -   + run_api_server.py
2025-05-23 22:45:28,484 - INFO -   + run_background_service.py
2025-05-23 22:45:28,484 - INFO -   + run_background_service_with_watchdog.py
2025-05-23 22:45:28,484 - INFO -   + run_resilient_service.py
2025-05-23 22:45:28,484 - INFO -   + run_strategy_with_geckoterminal.py
2025-05-23 22:45:28,484 - INFO -   + run_with_all_tokens.py
2025-05-23 22:45:28,484 - INFO -   + run_with_cached_data.py
2025-05-23 22:45:28,484 - INFO -   + run_with_new_tokens.py
2025-05-23 22:45:28,484 - INFO -   + simple_telegram_test.py
2025-05-23 22:45:28,484 - INFO -   + simple_watchdog_test.py
2025-05-23 22:45:28,485 - INFO -   + simulate_network_issues.py
2025-05-23 22:45:28,485 - INFO -   + tester_scores.csv
2025-05-23 22:45:28,485 - INFO -   + test_allocation.py
2025-05-23 22:45:28,485 - INFO -   + test_mtpi_signal.py
2025-05-23 22:45:28,485 - INFO -   + test_report_20250523_224528.txt
2025-05-23 22:45:28,485 - INFO -   + unified_asset_rotation_example.py
2025-05-23 22:45:28,485 - INFO - === ANALYZING KEY FINDINGS ===
2025-05-23 22:45:28,487 - INFO - === COMPREHENSIVE TEST SUITE COMPLETED ===
2025-05-23 22:45:28,487 - INFO - Check the generated files for detailed analysis:
2025-05-23 22:45:28,487 - INFO -   - comprehensive_test_results.log (this log)
2025-05-23 22:45:28,487 - INFO -   - test_report_*.txt (detailed report)
2025-05-23 22:45:28,487 - INFO -   - Various CSV and source code files
