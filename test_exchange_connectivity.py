#!/usr/bin/env python3
"""
Test script to verify Kraken and Bitvavo exchange connectivity.
This script tests API credentials and basic functionality for both exchanges.
"""

import os
import sys
import ccxt
import logging
from datetime import datetime
from typing import Dict, Any, List

# Add the src directory to the path
sys.path.append('src')

from config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_exchange_connection(exchange_id: str) -> Dict[str, Any]:
    """
    Test connection to a specific exchange.
    
    Args:
        exchange_id: The exchange ID (e.g., 'kraken', 'bitvavo')
        
    Returns:
        Dictionary with test results
    """
    results = {
        'exchange': exchange_id,
        'connection': False,
        'markets_loaded': False,
        'balance_accessible': False,
        'test_symbol_available': False,
        'errors': []
    }
    
    try:
        print(f"\n{'='*50}")
        print(f"TESTING {exchange_id.upper()} CONNECTIVITY")
        print(f"{'='*50}")
        
        # Get credentials
        credentials = get_exchange_credentials(exchange_id)
        if not credentials.get('api_key') or not credentials.get('api_secret'):
            error_msg = f"Missing API credentials for {exchange_id}"
            results['errors'].append(error_msg)
            print(f"❌ {error_msg}")
            return results
        
        print(f"✅ API credentials found for {exchange_id}")
        
        # Get exchange class
        exchange_class = getattr(ccxt, exchange_id, None)
        if not exchange_class:
            error_msg = f"Exchange '{exchange_id}' not found in ccxt"
            results['errors'].append(error_msg)
            print(f"❌ {error_msg}")
            return results
        
        print(f"✅ Exchange class found in ccxt")
        
        # Create exchange instance
        exchange = exchange_class({
            'apiKey': credentials.get('api_key'),
            'secret': credentials.get('api_secret'),
            'enableRateLimit': True,
            'sandbox': False,  # Use live environment
            'options': {
                'defaultType': 'spot',
            }
        })
        
        results['connection'] = True
        print(f"✅ Exchange instance created successfully")
        
        # Test loading markets
        try:
            markets = exchange.load_markets()
            results['markets_loaded'] = True
            print(f"✅ Markets loaded successfully ({len(markets)} markets)")
        except Exception as e:
            error_msg = f"Failed to load markets: {e}"
            results['errors'].append(error_msg)
            print(f"❌ {error_msg}")
        
        # Test balance access (only if markets loaded)
        if results['markets_loaded']:
            try:
                balance = exchange.fetch_balance()
                results['balance_accessible'] = True
                print(f"✅ Balance accessible")
                
                # Show non-zero balances
                non_zero_balances = {
                    currency: amount 
                    for currency, amount in balance['free'].items() 
                    if float(amount) > 0
                }
                if non_zero_balances:
                    print(f"💰 Non-zero balances: {non_zero_balances}")
                else:
                    print(f"💰 No non-zero balances found")
                    
            except Exception as e:
                error_msg = f"Failed to fetch balance: {e}"
                results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        
        # Test specific symbol availability
        test_symbols = ['BTC/EUR', 'ETH/EUR', 'SOL/EUR']
        available_symbols = []
        
        if results['markets_loaded']:
            for symbol in test_symbols:
                if symbol in markets:
                    available_symbols.append(symbol)
            
            if available_symbols:
                results['test_symbol_available'] = True
                print(f"✅ Test symbols available: {available_symbols}")
            else:
                error_msg = f"None of the test symbols {test_symbols} are available"
                results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        
        # Test fetching ticker for BTC/EUR if available
        if 'BTC/EUR' in available_symbols:
            try:
                ticker = exchange.fetch_ticker('BTC/EUR')
                print(f"📊 BTC/EUR ticker: {ticker['last']:.2f} EUR")
            except Exception as e:
                error_msg = f"Failed to fetch BTC/EUR ticker: {e}"
                results['errors'].append(error_msg)
                print(f"⚠️ {error_msg}")
        
    except Exception as e:
        error_msg = f"Unexpected error testing {exchange_id}: {e}"
        results['errors'].append(error_msg)
        print(f"❌ {error_msg}")
    
    return results

def test_all_exchanges() -> Dict[str, Dict[str, Any]]:
    """Test connectivity for all configured exchanges."""
    exchanges_to_test = ['kraken', 'bitvavo']
    results = {}
    
    print(f"\n{'='*70}")
    print(f"EXCHANGE CONNECTIVITY TEST")
    print(f"Testing: {', '.join(exchanges_to_test)}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*70}")
    
    for exchange_id in exchanges_to_test:
        results[exchange_id] = test_exchange_connection(exchange_id)
    
    return results

def print_summary(results: Dict[str, Dict[str, Any]]):
    """Print a summary of all test results."""
    print(f"\n{'='*70}")
    print(f"TEST SUMMARY")
    print(f"{'='*70}")
    
    for exchange_id, result in results.items():
        print(f"\n{exchange_id.upper()}:")
        print(f"  Connection: {'✅' if result['connection'] else '❌'}")
        print(f"  Markets: {'✅' if result['markets_loaded'] else '❌'}")
        print(f"  Balance: {'✅' if result['balance_accessible'] else '❌'}")
        print(f"  Test symbols: {'✅' if result['test_symbol_available'] else '❌'}")
        
        if result['errors']:
            print(f"  Errors:")
            for error in result['errors']:
                print(f"    - {error}")
    
    # Overall status
    all_passed = all(
        result['connection'] and result['markets_loaded'] and result['balance_accessible']
        for result in results.values()
    )
    
    print(f"\n{'='*70}")
    if all_passed:
        print(f"🎉 ALL TESTS PASSED! Both exchanges are ready for trading.")
    else:
        print(f"⚠️ SOME TESTS FAILED. Please check the errors above.")
    print(f"{'='*70}")

def main():
    """Main function to run all tests."""
    try:
        # Test all exchanges
        results = test_all_exchanges()
        
        # Print summary
        print_summary(results)
        
        # Exit with appropriate code
        all_passed = all(
            result['connection'] and result['markets_loaded'] and result['balance_accessible']
            for result in results.values()
        )
        
        sys.exit(0 if all_passed else 1)
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        logging.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
