#!/usr/bin/env python3
"""
Test script for Bollinger Band indicator with Pine Script configuration
Testing with BTC/USDT from Binance using the exact parameters from the image:
- Length: 33
- Source: Close
- Use Heikin-Ashi: True
- Heikin src: close
- Long threshold: 76
- Short threshold: 31

Starting from 2023-10-19 for TradingView cross-checking with equal weight combination.
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from MTPI_signal_handler import (
    fetch_historical_combined_mtpi_signals, 
    fetch_combined_mtpi_signal,
    generate_bollinger_band_signal,
    calculate_pgo
)
from indicators.custom_indicators import calculate_bollinger_band_position
from data_fetcher import fetch_ohlcv_data

def test_bb_pine_script_config():
    """Test Bollinger Band with the exact Pine Script configuration."""
    print("=" * 80)
    print("TESTING BOLLINGER BAND WITH PINE SCRIPT CONFIGURATION")
    print("Testing with BTC/USDT from Binance starting 2023-10-19")
    print("Using equal weight combination method for cross-checking with TradingView")
    print("=" * 80)
    
    # Pine Script configuration from the image
    bb_config = {
        'length': 33,
        'multiplier': 2.0,
        'long_threshold': 76.0,
        'short_threshold': 31.0,
        'use_heikin_ashi': True,
        'heikin_src': 'close'
    }
    
    # PGO configuration (default)
    pgo_config = {
        'length': 35,
        'upper_threshold': 1.35,
        'lower_threshold': -1.0
    }
    
    # Test configuration
    test_start_date = '2023-10-19'
    combination_method = 'equal_weight'
    
    print(f"Test Start Date: {test_start_date}")
    print(f"Combination Method: {combination_method}")
    print(f"\nBollinger Band Configuration:")
    for key, value in bb_config.items():
        print(f"  {key}: {value}")
    
    print(f"\nPGO Configuration:")
    for key, value in pgo_config.items():
        print(f"  {key}: {value}")
    
    # Test 1: Fetch BTC/USDT data from Binance from 2023-10-19
    print(f"\n" + "="*60)
    print("TEST 1: Fetching BTC/USDT Data from Binance")
    print("="*60)
    
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=test_start_date,
            limit=500  # Get enough data for thorough analysis
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("ERROR: Failed to fetch BTC/USDT data from Binance")
            return
        
        btc_df = data_dict['BTC/USDT']
        print(f"✓ Successfully fetched {len(btc_df)} candles")
        print(f"  Date range: {btc_df.index[0]} to {btc_df.index[-1]}")
        print(f"  Columns: {btc_df.columns.tolist()}")
        
        # Show data around the start date
        print(f"\nData around start date ({test_start_date}):")
        start_data = btc_df[['open', 'high', 'low', 'close', 'volume']].head(10)
        print(start_data.round(2))
        
    except Exception as e:
        print(f"ERROR: Failed to fetch data: {e}")
        return
    
    # Test 2: Calculate individual signals
    print(f"\n" + "="*60)
    print("TEST 2: Individual Signal Calculations")
    print("="*60)
    
    # Calculate Bollinger Band signals
    bb_signals = generate_bollinger_band_signal(
        df=btc_df,
        **bb_config
    )
    
    # Get BB position data for analysis
    bb_data = calculate_bollinger_band_position(
        df=btc_df,
        length=bb_config['length'],
        multiplier=bb_config['multiplier'],
        use_heikin_ashi=bb_config['use_heikin_ashi'],
        heikin_src=bb_config['heikin_src']
    )
    
    # Calculate PGO signals
    pgo_values = calculate_pgo(btc_df, length=pgo_config['length'])
    from MTPI_signal_handler import generate_pgo_signal
    pgo_signals = generate_pgo_signal(
        df=btc_df,
        length=pgo_config['length'],
        upper_threshold=pgo_config['upper_threshold'],
        lower_threshold=pgo_config['lower_threshold'],
        skip_warmup=False
    )
    
    print(f"✓ Generated {len(bb_signals)} Bollinger Band signals")
    print(f"✓ Generated {len(pgo_signals)} PGO signals")
    
    # Signal distributions
    bb_counts = bb_signals.value_counts().sort_index()
    pgo_counts = pgo_signals.value_counts().sort_index()
    total_signals = len(bb_signals)
    
    print(f"\nBollinger Band Signal Distribution:")
    for signal, count in bb_counts.items():
        signal_name = "Long" if signal == 1 else "Short" if signal == -1 else "Neutral"
        pct = (count / total_signals) * 100
        print(f"  {signal_name} ({signal}): {count} ({pct:.1f}%)")
    
    print(f"\nPGO Signal Distribution:")
    for signal, count in pgo_counts.items():
        signal_name = "Long" if signal == 1 else "Short" if signal == -1 else "Neutral"
        pct = (count / total_signals) * 100
        print(f"  {signal_name} ({signal}): {count} ({pct:.1f}%)")
    
    # Test 3: Combined signals with equal weight
    print(f"\n" + "="*60)
    print("TEST 3: Equal Weight Combined Signal Analysis")
    print("="*60)
    
    combined_df = fetch_historical_combined_mtpi_signals(
        timeframe='1d',
        pgo_length=pgo_config['length'],
        pgo_upper_threshold=pgo_config['upper_threshold'],
        pgo_lower_threshold=pgo_config['lower_threshold'],
        bb_length=bb_config['length'],
        bb_multiplier=bb_config['multiplier'],
        bb_long_threshold=bb_config['long_threshold'],
        bb_short_threshold=bb_config['short_threshold'],
        bb_use_heikin_ashi=bb_config['use_heikin_ashi'],
        bb_heikin_src=bb_config['heikin_src'],
        since=test_start_date,
        limit=500,
        skip_warmup=False,
        combination_method=combination_method
    )
    
    if combined_df is not None:
        combined_counts = combined_df['combined_signal'].value_counts().sort_index()
        print(f"✓ Generated {len(combined_df)} combined signals")
        print(f"\nEqual Weight Combined Signal Distribution:")
        for signal, count in combined_counts.items():
            signal_name = "Long" if signal == 1 else "Short" if signal == -1 else "Neutral"
            pct = (count / len(combined_df)) * 100
            print(f"  {signal_name} ({signal}): {count} ({pct:.1f}%)")
    else:
        print("ERROR: Failed to calculate combined signals")
        return
    
    # Test 4: Signal agreement analysis
    print(f"\n" + "="*60)
    print("TEST 4: Signal Agreement Analysis")
    print("="*60)
    
    # Analyze how often signals agree
    agreement_analysis = pd.DataFrame({
        'pgo': combined_df['pgo_signal'],
        'bb': combined_df['bb_signal'],
        'combined': combined_df['combined_signal']
    })
    
    # Count different agreement scenarios
    both_long = (agreement_analysis['pgo'] == 1) & (agreement_analysis['bb'] == 1)
    both_short = (agreement_analysis['pgo'] == -1) & (agreement_analysis['bb'] == -1)
    both_neutral = (agreement_analysis['pgo'] == 0) & (agreement_analysis['bb'] == 0)
    disagree = ~(both_long | both_short | both_neutral)
    
    print(f"Signal Agreement Analysis:")
    print(f"  Both Long: {both_long.sum()} ({both_long.sum()/len(agreement_analysis)*100:.1f}%)")
    print(f"  Both Short: {both_short.sum()} ({both_short.sum()/len(agreement_analysis)*100:.1f}%)")
    print(f"  Both Neutral: {both_neutral.sum()} ({both_neutral.sum()/len(agreement_analysis)*100:.1f}%)")
    print(f"  Disagree: {disagree.sum()} ({disagree.sum()/len(agreement_analysis)*100:.1f}%)")
    
    # Show examples of disagreement for the first 20 cases
    if disagree.sum() > 0:
        print(f"\nFirst 10 disagreement cases:")
        disagreement_cases = agreement_analysis[disagree].head(10)
        for idx, row in disagreement_cases.iterrows():
            date_str = idx.strftime('%Y-%m-%d')
            print(f"  {date_str}: PGO={row['pgo']}, BB={row['bb']}, Combined={row['combined']}")
    
    # Test 5: Recent signal analysis  
    print(f"\n" + "="*60)
    print("TEST 5: Recent Signal Analysis (Last 20 days)")
    print("="*60)
    
    # Create comprehensive analysis DataFrame
    analysis_df = btc_df.copy()
    analysis_df['bb_position_pct'] = combined_df['bb_position_pct']
    analysis_df['bb_signal'] = combined_df['bb_signal']
    analysis_df['pgo_value'] = combined_df['pgo_value']
    analysis_df['pgo_signal'] = combined_df['pgo_signal']
    analysis_df['combined_signal'] = combined_df['combined_signal']
    
    # Show last 20 days
    recent_analysis = analysis_df[['close', 'bb_position_pct', 'bb_signal', 'pgo_value', 'pgo_signal', 'combined_signal']].tail(20)
    print(f"Last 20 days analysis:")
    recent_analysis_display = recent_analysis.copy()
    recent_analysis_display.index = recent_analysis_display.index.strftime('%Y-%m-%d')
    print(recent_analysis_display.round(2))
    
    # Test 6: Current signal status
    print(f"\n" + "="*60)
    print("TEST 6: Current Signal Status")
    print("="*60)
    
    current_bb_signal = combined_df['bb_signal'].iloc[-1]
    current_pgo_signal = combined_df['pgo_signal'].iloc[-1]
    current_combined_signal = combined_df['combined_signal'].iloc[-1]
    current_bb_position = combined_df['bb_position_pct'].iloc[-1]
    current_pgo_value = combined_df['pgo_value'].iloc[-1]
    current_price = btc_df['close'].iloc[-1]
    current_date = btc_df.index[-1].strftime('%Y-%m-%d')
    
    print(f"Current Date: {current_date}")
    print(f"Current BTC Price: ${current_price:,.2f}")
    print(f"Current BB Position: {current_bb_position:.1f}%")
    print(f"Current BB Signal: {current_bb_signal} ({'Long' if current_bb_signal == 1 else 'Short' if current_bb_signal == -1 else 'Neutral'})")
    print(f"Current PGO Value: {current_pgo_value:.2f}")
    print(f"Current PGO Signal: {current_pgo_signal} ({'Long' if current_pgo_signal == 1 else 'Short' if current_pgo_signal == -1 else 'Neutral'})")
    print(f"Current Combined Signal: {current_combined_signal} ({'Long' if current_combined_signal == 1 else 'Short' if current_combined_signal == -1 else 'Neutral'})")
    
    # Test 7: Signal transitions analysis
    print(f"\n" + "="*60)
    print("TEST 7: Signal Transitions Analysis")
    print("="*60)
    
    # Find signal changes
    bb_changes = combined_df['bb_signal'].diff().fillna(0)
    pgo_changes = combined_df['pgo_signal'].diff().fillna(0)
    combined_changes = combined_df['combined_signal'].diff().fillna(0)
    
    bb_transitions = bb_changes[bb_changes != 0].index
    pgo_transitions = pgo_changes[pgo_changes != 0].index
    combined_transitions = combined_changes[combined_changes != 0].index
    
    print(f"Signal Transitions:")
    print(f"  Bollinger Band: {len(bb_transitions)} transitions")
    print(f"  PGO: {len(pgo_transitions)} transitions")
    print(f"  Combined (Equal Weight): {len(combined_transitions)} transitions")
    
    print(f"\nRecent Combined Signal transitions (last 5):")
    for idx in combined_transitions[-5:]:
        date_str = idx.strftime('%Y-%m-%d')
        signal_before = combined_df['combined_signal'].shift(1).loc[idx]
        signal_after = combined_df['combined_signal'].loc[idx]
        price = btc_df.loc[idx, 'close']
        bb_pos = combined_df.loc[idx, 'bb_position_pct']
        pgo_val = combined_df.loc[idx, 'pgo_value']
        bb_sig = combined_df.loc[idx, 'bb_signal']
        pgo_sig = combined_df.loc[idx, 'pgo_signal']
        
        print(f"  {date_str}: ${price:,.0f} | Combined: {signal_before} → {signal_after}")
        print(f"    BB: {bb_sig} (pos: {bb_pos:.1f}%), PGO: {pgo_sig} (val: {pgo_val:.2f})")
    
    # Test 8: TradingView cross-check data export
    print(f"\n" + "="*60)
    print("TEST 8: TradingView Cross-Check Data Export")
    print("="*60)
    
    # Export data for TradingView comparison
    export_df = analysis_df[['close', 'bb_position_pct', 'pgo_value', 'bb_signal', 'pgo_signal', 'combined_signal']].copy()
    export_df.index = export_df.index.strftime('%Y-%m-%d')
    
    # Save to CSV for easy comparison
    export_filename = f'btc_signals_tradingview_check_{test_start_date}.csv'
    export_df.to_csv(export_filename)
    print(f"✓ Exported signal data to '{export_filename}' for TradingView comparison")
    
    # Show sample of export data
    print(f"\nSample export data (first 10 rows):")
    print(export_df.head(10).round(2))
    
    # Test 9: Visualization
    print(f"\n" + "="*60)
    print("TEST 9: Creating Comprehensive Visualization")
    print("="*60)
    
    try:
        # Plot the data
        plot_days = min(90, len(analysis_df))  # Show last 90 days
        plot_data = analysis_df.tail(plot_days)
        
        fig, axes = plt.subplots(5, 1, figsize=(16, 20), sharex=True)
        
        # Plot 1: Price with Bollinger Bands
        ax1 = axes[0]
        ax1.plot(plot_data.index, plot_data['close'], label='BTC Close Price', color='black', linewidth=2)
        bb_upper = bb_data['upper_band'].tail(plot_days)
        bb_basis = bb_data['basis'].tail(plot_days)
        bb_lower = bb_data['lower_band'].tail(plot_days)
        ax1.plot(plot_data.index, bb_upper, label='BB Upper', color='red', alpha=0.7)
        ax1.plot(plot_data.index, bb_basis, label='BB Basis (SMA)', color='blue', alpha=0.7)
        ax1.plot(plot_data.index, bb_lower, label='BB Lower', color='green', alpha=0.7)
        ax1.fill_between(plot_data.index, bb_upper, bb_lower, alpha=0.1, color='gray')
        ax1.set_title(f'BTC Price with Bollinger Bands (Length={bb_config["length"]}, Heikin-Ashi={bb_config["use_heikin_ashi"]})')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylabel('Price (USD)')
        
        # Plot 2: BB Position Percentage
        ax2 = axes[1]
        ax2.plot(plot_data.index, plot_data['bb_position_pct'], label='BB Position %', color='purple', linewidth=2)
        ax2.axhline(y=bb_config['long_threshold'], color='green', linestyle='--', alpha=0.8, label=f'Long Threshold ({bb_config["long_threshold"]})')
        ax2.axhline(y=bb_config['short_threshold'], color='red', linestyle='--', alpha=0.8, label=f'Short Threshold ({bb_config["short_threshold"]})')
        ax2.axhline(y=50, color='black', linestyle='-', alpha=0.3, label='Middle (50%)')
        ax2.fill_between(plot_data.index, bb_config['long_threshold'], 100, alpha=0.2, color='green', label='Long Zone')
        ax2.fill_between(plot_data.index, 0, bb_config['short_threshold'], alpha=0.2, color='red', label='Short Zone')
        ax2.set_title('Bollinger Band Position Percentage')
        ax2.set_ylabel('Position %')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)
        
        # Plot 3: PGO Values
        ax3 = axes[2]
        ax3.plot(plot_data.index, plot_data['pgo_value'], label='PGO Value', color='orange', linewidth=2)
        ax3.axhline(y=pgo_config['upper_threshold'], color='green', linestyle='--', alpha=0.8, label=f'PGO Long ({pgo_config["upper_threshold"]})')
        ax3.axhline(y=pgo_config['lower_threshold'], color='red', linestyle='--', alpha=0.8, label=f'PGO Short ({pgo_config["lower_threshold"]})')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3, label='Zero Line')
        ax3.set_title('PGO (Pretty Good Oscillator) Values')
        ax3.set_ylabel('PGO Value')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Individual Signals Comparison
        ax4 = axes[3]
        ax4.plot(plot_data.index, plot_data['bb_signal'], label='BB Signal', color='blue', linewidth=2, marker='o', markersize=4)
        ax4.plot(plot_data.index, plot_data['pgo_signal'], label='PGO Signal', color='orange', linewidth=2, marker='s', markersize=3, alpha=0.7)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax4.set_title('Individual Signals: Bollinger Band vs PGO')
        ax4.set_ylabel('Signal')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(-1.5, 1.5)
        ax4.set_yticks([-1, 0, 1])
        ax4.set_yticklabels(['Short', 'Neutral', 'Long'])
        
        # Plot 5: Combined Equal Weight Signal
        ax5 = axes[4]
        ax5.plot(plot_data.index, plot_data['combined_signal'], label='Combined Signal (Equal Weight)', color='red', linewidth=3, marker='D', markersize=5)
        ax5.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax5.set_title('Combined Equal Weight Signal')
        ax5.set_ylabel('Combined Signal')
        ax5.set_xlabel('Date')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        ax5.set_ylim(-1.5, 1.5)
        ax5.set_yticks([-1, 0, 1])
        ax5.set_yticklabels(['Short', 'Neutral', 'Long'])
        
        # Format x-axis
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        plt.tight_layout()
        
        # Save the plot
        plot_filename = f'bb_equal_weight_analysis_{test_start_date}.png'
        plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
        print(f"✓ Visualization saved as '{plot_filename}'")
        
        # Show the plot
        plt.show()
        
    except Exception as e:
        print(f"ERROR creating visualization: {e}")
    
    print(f"\n" + "="*80)
    print("BOLLINGER BAND EQUAL WEIGHT TEST COMPLETE")
    print(f"Data exported to: {export_filename}")
    print(f"Chart saved as: {plot_filename}")
    print("Ready for TradingView cross-checking!")
    print("="*80)
    
    return analysis_df, combined_df

if __name__ == "__main__":
    test_bb_pine_script_config()