#!/usr/bin/env python
"""
Fix the pandas_ta library to work with newer versions of NumPy.
"""

import os
import sys

def fix_pandas_ta():
    """
    Fix the pandas_ta library to use numpy.nan instead of numpy.NaN.
    """
    try:
        # Get the path to the pandas_ta package
        import pandas_ta
        package_path = os.path.dirname(pandas_ta.__file__)
        
        # Path to the squeeze_pro.py file
        squeeze_pro_path = os.path.join(package_path, 'momentum', 'squeeze_pro.py')
        
        # Check if the file exists
        if not os.path.exists(squeeze_pro_path):
            print(f"Error: File not found: {squeeze_pro_path}")
            return False
        
        # Read the file
        with open(squeeze_pro_path, 'r') as f:
            content = f.read()
        
        # Check if the file contains the problematic import
        if 'from numpy import NaN as npNaN' in content:
            # Replace the import
            content = content.replace('from numpy import NaN as npNaN', 'from numpy import nan as npNaN')
            
            # Write the file back
            with open(squeeze_pro_path, 'w') as f:
                f.write(content)
            
            print(f"Successfully fixed {squeeze_pro_path}")
            return True
        else:
            print(f"No need to fix {squeeze_pro_path}, it's already using the correct import")
            return True
    
    except Exception as e:
        print(f"Error fixing pandas_ta: {e}")
        return False

if __name__ == "__main__":
    if fix_pandas_ta():
        print("pandas_ta fixed successfully!")
        sys.exit(0)
    else:
        print("Failed to fix pandas_ta")
        sys.exit(1)
