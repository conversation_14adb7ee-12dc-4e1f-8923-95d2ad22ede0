#!/usr/bin/env python3
"""
Test script to verify MTPI asset trend detection integration with the strategy system.
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config, save_config
from src.strategy import calculate_daily_scores

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_mtpi_strategy_integration():
    """
    Test MTPI asset trend detection integration with the main strategy system.
    """
    print("=" * 80)
    print("MTPI STRATEGY INTEGRATION TEST")
    print("=" * 80)
    
    # Load configuration
    config = load_config()
    
    # Test parameters
    assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
    timeframe = '1d'
    limit = 50  # Last 50 days for faster testing
    
    print(f"📊 Testing with assets: {assets}")
    print(f"⏰ Timeframe: {timeframe}")
    print(f"📈 Data points: {limit}")
    
    # Fetch data
    print(f"\n🔄 Fetching data...")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=assets,
            timeframe=timeframe,
            limit=limit
        )
        
        if data_dict:
            for asset, df in data_dict.items():
                if not df.empty:
                    print(f"✅ {asset}: {len(df)} candles from {df.index[0]} to {df.index[-1]}")
                else:
                    print(f"❌ {asset}: No data received")
        else:
            print("❌ No data received from fetch_ohlcv_data")
            return
            
    except Exception as e:
        print(f"❌ Error fetching data: {e}")
        return
    
    if not data_dict:
        print("❌ No data available for testing")
        return
    
    print(f"\n✅ Successfully fetched data for {len(data_dict)} assets")
    
    # Test 1: RSI Method (baseline)
    print(f"\n" + "="*50)
    print("TEST 1: RSI METHOD (BASELINE)")
    print("="*50)
    
    # Temporarily set trend method to RSI
    original_trend_method = config.get('settings', {}).get('trend_method', 'RSI')
    config['settings']['trend_method'] = 'RSI'
    save_config(config)
    
    try:
        rsi_scores = calculate_daily_scores(
            data_dict=data_dict,
            debug_matrices=False
        )
        print(f"✅ RSI Method: {rsi_scores.shape[0]} days, {rsi_scores.shape[1]} assets")
        if not rsi_scores.empty:
            latest_rsi = rsi_scores.iloc[-1]
            best_rsi_asset = latest_rsi.idxmax()
            print(f"📊 Latest RSI best asset: {best_rsi_asset} (score: {latest_rsi.max():.2f})")
    except Exception as e:
        print(f"❌ RSI Method failed: {e}")
        rsi_scores = pd.DataFrame()
    
    # Test 2: PGO Method
    print(f"\n" + "="*50)
    print("TEST 2: PGO METHOD")
    print("="*50)
    
    # Set trend method to PGO
    config['settings']['trend_method'] = 'PGO For Loop'
    save_config(config)
    
    try:
        pgo_scores = calculate_daily_scores(
            data_dict=data_dict,
            debug_matrices=False
        )
        print(f"✅ PGO Method: {pgo_scores.shape[0]} days, {pgo_scores.shape[1]} assets")
        if not pgo_scores.empty:
            latest_pgo = pgo_scores.iloc[-1]
            best_pgo_asset = latest_pgo.idxmax()
            print(f"📊 Latest PGO best asset: {best_pgo_asset} (score: {latest_pgo.max():.2f})")
    except Exception as e:
        print(f"❌ PGO Method failed: {e}")
        pgo_scores = pd.DataFrame()
    
    # Test 3: MTPI Method (NEW!)
    print(f"\n" + "="*50)
    print("TEST 3: MTPI METHOD (NEW!)")
    print("="*50)
    
    # Set trend method to MTPI
    config['settings']['trend_method'] = 'MTPI'
    save_config(config)
    
    try:
        mtpi_scores = calculate_daily_scores(
            data_dict=data_dict,
            debug_matrices=False
        )
        print(f"✅ MTPI Method: {mtpi_scores.shape[0]} days, {mtpi_scores.shape[1]} assets")
        if not mtpi_scores.empty:
            latest_mtpi = mtpi_scores.iloc[-1]
            best_mtpi_asset = latest_mtpi.idxmax()
            print(f"📊 Latest MTPI best asset: {best_mtpi_asset} (score: {latest_mtpi.max():.2f})")
    except Exception as e:
        print(f"❌ MTPI Method failed: {e}")
        logging.error(f"MTPI Method error details: {e}", exc_info=True)
        mtpi_scores = pd.DataFrame()
    
    # Restore original trend method
    config['settings']['trend_method'] = original_trend_method
    save_config(config)
    
    # Comparison Analysis
    print(f"\n" + "="*50)
    print("COMPARISON ANALYSIS")
    print("="*50)
    
    methods = []
    if not rsi_scores.empty:
        methods.append(('RSI', rsi_scores))
    if not pgo_scores.empty:
        methods.append(('PGO', pgo_scores))
    if not mtpi_scores.empty:
        methods.append(('MTPI', mtpi_scores))
    
    if len(methods) >= 2:
        print(f"📊 Comparing {len(methods)} methods over the last 5 days:")
        
        # Get common dates for comparison
        common_dates = None
        for method_name, scores in methods:
            if common_dates is None:
                common_dates = scores.index
            else:
                common_dates = common_dates.intersection(scores.index)
        
        if len(common_dates) >= 5:
            recent_dates = sorted(common_dates)[-5:]
            
            print(f"\n📅 Date Range: {recent_dates[0]} to {recent_dates[-1]}")
            print(f"{'Date':<12} {'RSI Best':<12} {'PGO Best':<12} {'MTPI Best':<12}")
            print("-" * 60)
            
            for date in recent_dates:
                row = f"{date.strftime('%Y-%m-%d'):<12}"
                
                for method_name, scores in methods:
                    if date in scores.index:
                        day_scores = scores.loc[date]
                        best_asset = day_scores.idxmax()
                        best_score = day_scores.max()
                        row += f"{best_asset.split('/')[0]:<8}({best_score:.1f})"
                    else:
                        row += f"{'N/A':<12}"
                
                print(row)
        
        # Summary
        print(f"\n🎯 INTEGRATION TEST SUMMARY:")
        print(f"   ✅ RSI Method: {'Working' if not rsi_scores.empty else 'Failed'}")
        print(f"   ✅ PGO Method: {'Working' if not pgo_scores.empty else 'Failed'}")
        print(f"   🚀 MTPI Method: {'Working' if not mtpi_scores.empty else 'Failed'}")
        
        if not mtpi_scores.empty:
            print(f"\n🎉 SUCCESS! MTPI asset trend detection is fully integrated!")
            print(f"   📈 You can now use trend_method: 'MTPI' in your configuration")
            print(f"   🔧 This leverages all your existing MTPI indicators for asset selection")
            print(f"   💪 Should provide more robust trend detection with reduced false positives")
        else:
            print(f"\n❌ MTPI integration needs debugging")
    
    print(f"\n✅ MTPI strategy integration test completed!")

if __name__ == "__main__":
    test_mtpi_strategy_integration()
