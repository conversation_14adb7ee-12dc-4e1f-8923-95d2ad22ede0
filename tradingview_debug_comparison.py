#!/usr/bin/env python3
"""
Comprehensive debugging tool to find exact discrepancies with TradingView.
This will help identify the specific calculation differences.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_smoothing, calculate_atr, calculate_median_supertrend
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config

def debug_step_by_step():
    """Debug each calculation step to find TradingView discrepancies."""
    print("=" * 100)
    print("TRADINGVIEW DEBUGGING - STEP BY STEP COMPARISON")
    print("=" * 100)
    
    # Load config and data
    config = load_mtpi_multi_indicator_config()
    median_config = config.get('median_score', {})
    
    # Parameters
    atr_period = median_config.get('atr_period', 12)
    multiplier = median_config.get('multiplier', 1.45)
    median_length = median_config.get('median_length', 27)
    src_col = median_config.get('src_col', 'high')
    
    print(f"Parameters: ATR={atr_period}, Mult={multiplier}, MedianLen={median_length}, Src={src_col}")
    
    # Fetch data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Data: {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Calculate each component
    print(f"\n" + "=" * 100)
    print("STEP 1: MEDIAN SMOOTHING ANALYSIS")
    print("=" * 100)
    
    smooth1 = calculate_median_smoothing(btc_df, median_length, src_col)
    
    # Test different median calculation methods
    print(f"Testing different median methods for comparison:")
    
    # Test specific dates that might show discrepancies
    test_dates = ['2024-01-27', '2024-02-01', '2024-02-08', '2024-03-19']
    
    for test_date in test_dates:
        if test_date in btc_df.index.strftime('%Y-%m-%d'):
            idx = btc_df.index.get_loc(pd.to_datetime(test_date))
            
            # Get window data
            if idx >= median_length - 1:
                window_data = btc_df[src_col].iloc[idx-median_length+1:idx+1].values
            else:
                window_data = btc_df[src_col].iloc[:idx+1].values
            
            # Different median methods
            pandas_median = pd.Series(window_data).median()
            numpy_median = np.median(window_data)
            
            # Nearest rank method (our implementation)
            sorted_data = np.sort(window_data)
            n = len(sorted_data)
            rank = int(np.ceil(0.5 * n))
            nearest_rank_median = sorted_data[rank - 1]
            
            # Alternative nearest rank (in case of different interpretation)
            rank_alt = int(np.floor(0.5 * n))
            nearest_rank_alt = sorted_data[rank_alt] if rank_alt < n else sorted_data[-1]
            
            our_value = smooth1.iloc[idx]
            
            print(f"\n{test_date} (window size: {len(window_data)}):")
            print(f"  Raw data: {window_data[-5:]}...")  # Last 5 values
            print(f"  Pandas median: {pandas_median:.2f}")
            print(f"  Numpy median: {numpy_median:.2f}")
            print(f"  Nearest rank (ceil): {nearest_rank_median:.2f}")
            print(f"  Nearest rank (floor): {nearest_rank_alt:.2f}")
            print(f"  Our implementation: {our_value:.2f}")
            print(f"  Match nearest rank: {'✅' if abs(our_value - nearest_rank_median) < 0.01 else '❌'}")
    
    print(f"\n" + "=" * 100)
    print("STEP 2: ATR ANALYSIS")
    print("=" * 100)
    
    atr = calculate_atr(btc_df, atr_period)
    
    # Manual ATR calculation for verification
    for test_date in test_dates[:2]:  # Test first 2 dates
        if test_date in btc_df.index.strftime('%Y-%m-%d'):
            idx = btc_df.index.get_loc(pd.to_datetime(test_date))
            
            if idx >= atr_period - 1:
                # Manual ATR calculation
                high_vals = btc_df['high'].iloc[idx-atr_period+1:idx+1]
                low_vals = btc_df['low'].iloc[idx-atr_period+1:idx+1]
                close_vals = btc_df['close'].iloc[idx-atr_period:idx]  # Previous closes
                
                tr1 = high_vals - low_vals
                tr2 = abs(high_vals - close_vals.values)
                tr3 = abs(low_vals - close_vals.values)
                
                true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                manual_atr = true_range.mean()
                our_atr = atr.iloc[idx]
                
                print(f"\n{test_date}:")
                print(f"  Manual ATR: {manual_atr:.2f}")
                print(f"  Our ATR: {our_atr:.2f}")
                print(f"  Match: {'✅' if abs(manual_atr - our_atr) < 0.01 else '❌'}")
    
    print(f"\n" + "=" * 100)
    print("STEP 3: SUPERTREND BANDS ANALYSIS")
    print("=" * 100)
    
    # Calculate bands
    upper_band = smooth1 + (multiplier * atr)
    lower_band = smooth1 - (multiplier * atr)
    
    for test_date in test_dates[:2]:
        if test_date in btc_df.index.strftime('%Y-%m-%d'):
            idx = btc_df.index.get_loc(pd.to_datetime(test_date))
            
            smooth_val = smooth1.iloc[idx]
            atr_val = atr.iloc[idx]
            upper_val = upper_band.iloc[idx]
            lower_val = lower_band.iloc[idx]
            
            # Manual calculation
            manual_upper = smooth_val + (multiplier * atr_val)
            manual_lower = smooth_val - (multiplier * atr_val)
            
            print(f"\n{test_date}:")
            print(f"  Smooth: {smooth_val:.2f}")
            print(f"  ATR: {atr_val:.2f}")
            print(f"  Upper band: {upper_val:.2f} (manual: {manual_upper:.2f})")
            print(f"  Lower band: {lower_val:.2f} (manual: {manual_lower:.2f})")
    
    print(f"\n" + "=" * 100)
    print("STEP 4: SUPERTREND DIRECTION ANALYSIS")
    print("=" * 100)
    
    supertrend_line, direction = calculate_median_supertrend(
        btc_df, atr_period, multiplier, median_length, src_col
    )
    
    # Find direction changes
    direction_changes = direction != direction.shift(1)
    change_indices = direction_changes[direction_changes].index[:10]  # First 10 changes
    
    print(f"First 10 direction changes:")
    for change_date in change_indices:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_dir = direction.iloc[idx-1]
            curr_dir = direction.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            st_line = supertrend_line.iloc[idx]
            
            print(f"  {change_date.strftime('%Y-%m-%d')}: {prev_dir}→{curr_dir}, Close: ${close_price:.2f}, ST: ${st_line:.2f}")
    
    print(f"\n" + "=" * 100)
    print("DEBUGGING SUGGESTIONS")
    print("=" * 100)
    
    print(f"To find the exact discrepancy with TradingView:")
    print(f"")
    print(f"1. VERIFY DATA SOURCE:")
    print(f"   - Check if TradingView and Binance have identical OHLCV data")
    print(f"   - Compare a few specific candles manually")
    print(f"")
    print(f"2. TEST MEDIAN CALCULATION:")
    print(f"   - Pick a specific date from TradingView")
    print(f"   - Manually calculate the median of the last {median_length} high values")
    print(f"   - Compare with our nearest-rank result")
    print(f"")
    print(f"3. TEST ATR CALCULATION:")
    print(f"   - Pick a specific date from TradingView")
    print(f"   - Manually calculate ATR for the last {atr_period} periods")
    print(f"   - Compare with our result")
    print(f"")
    print(f"4. ALTERNATIVE MEDIAN METHODS TO TRY:")
    print(f"   - Excel PERCENTILE.INC vs PERCENTILE.EXC")
    print(f"   - Different rounding methods (floor vs ceil)")
    print(f"   - 1-indexed vs 0-indexed array access")
    print(f"")
    print(f"5. POTENTIAL ISSUES:")
    print(f"   - Time zone differences in daily candle boundaries")
    print(f"   - Different handling of weekends/holidays")
    print(f"   - Floating point precision differences")
    print(f"   - Different ATR calculation methods (SMA vs EMA)")

def create_manual_verification_template():
    """Create a template for manual verification against TradingView."""
    print(f"\n" + "=" * 100)
    print("MANUAL VERIFICATION TEMPLATE")
    print("=" * 100)
    
    print(f"""
STEP-BY-STEP VERIFICATION PROCESS:

1. Pick a specific date from your TradingView chart (e.g., 2024-02-08)

2. In TradingView, add these plots to verify our calculations:
   
   //@version=5
   indicator("Debug Median Supertrend", overlay=true)
   
   // Parameters
   subject1 = 12
   mul1 = 1.45
   slen = 27
   src_me = high
   
   // Step 1: Median smoothing
   smooth1 = ta.percentile_nearest_rank(src_me, slen, 50)
   plot(smooth1, "Smooth1", color.blue)
   
   // Step 2: ATR
   atr_val = ta.atr(subject1)
   
   // Step 3: Bands
   upper_band = smooth1 + mul1 * atr_val
   lower_band = smooth1 - mul1 * atr_val
   plot(upper_band, "Upper", color.red)
   plot(lower_band, "Lower", color.green)
   
   // Debug values
   if barstate.islast
       label.new(bar_index, high, 
           "Smooth: " + str.tostring(smooth1) + "\\n" +
           "ATR: " + str.tostring(atr_val) + "\\n" +
           "Upper: " + str.tostring(upper_band) + "\\n" +
           "Lower: " + str.tostring(lower_band))

3. Compare the plotted values with our calculations

4. If there's a discrepancy, we can narrow down which step is wrong
""")

if __name__ == "__main__":
    debug_step_by_step()
    create_manual_verification_template()
