import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Text,
  Spinner,
  useColorModeValue,
  Flex,
  Select,
  HStack,
  Button,
  Badge,
} from '@chakra-ui/react';
import axios from 'axios';

const LiveAssetChart = ({ symbol, positions, isLoading }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const [timeframe, setTimeframe] = useState('1h');
  const [isLogScale, setIsLogScale] = useState(true);
  const [chartLoading, setChartLoading] = useState(false);
  
  // Create and update chart
  useEffect(() => {
    if (!symbol || isLoading || !chartContainerRef.current) return;
    
    const initChart = async () => {
      setChartLoading(true);
      
      try {
        // Fetch OHLCV data
        const response = await axios.get(`/api/assets/${symbol}/ohlcv`, {
          params: { timeframe, limit: 200 },
        });
        
        const ohlcvData = response.data;
        
        if (!ohlcvData || ohlcvData.length === 0) {
          console.error('No data available for chart');
          setChartLoading(false);
          return;
        }
        
        // Import TradingView's lightweight charts
        const { createChart, CrosshairMode } = await import('lightweight-charts');
        
        // Clean up previous chart if it exists
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
        }
        
        // Determine colors based on color mode
        const chartBgColor = useColorModeValue('#ffffff', '#1A202C');
        const chartTextColor = useColorModeValue('#333333', '#E2E8F0');
        const chartGridColor = useColorModeValue('#E2E8F0', '#4A5568');
        
        // Create chart
        const chart = createChart(chartContainerRef.current, {
          width: chartContainerRef.current.clientWidth,
          height: 400,
          layout: {
            background: { type: 'solid', color: chartBgColor },
            textColor: chartTextColor,
          },
          grid: {
            vertLines: { color: chartGridColor, style: 1, visible: true },
            horzLines: { color: chartGridColor, style: 1, visible: true, opacity: 0.2 },
          },
          rightPriceScale: {
            scaleMargins: {
              top: 0.02,
              bottom: 0.02,
            },
            borderVisible: false,
            mode: isLogScale ? 1 : 0, // 1 = logarithmic, 0 = normal
            autoScale: true,
          },
          timeScale: {
            borderVisible: false,
            timeVisible: true,
            secondsVisible: false,
          },
          crosshair: {
            mode: CrosshairMode.Normal,
          },
        });
        
        // Create candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: '#26a69a',
          downColor: '#ef5350',
          borderUpColor: '#26a69a',
          borderDownColor: '#ef5350',
          wickUpColor: '#26a69a',
          wickDownColor: '#ef5350',
        });
        
        // Format data for candlestick series
        const formattedData = ohlcvData.map(candle => ({
          time: candle.time / 1000, // Convert milliseconds to seconds
          open: candle.open,
          high: candle.high,
          low: candle.low,
          close: candle.close,
        }));
        
        // Set data
        candlestickSeries.setData(formattedData);
        
        // Add volume series
        const volumeSeries = chart.addHistogramSeries({
          color: '#26a69a',
          priceFormat: {
            type: 'volume',
          },
          priceScaleId: '',
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
        });
        
        // Format data for volume series
        const volumeData = ohlcvData.map(candle => ({
          time: candle.time / 1000, // Convert milliseconds to seconds
          value: candle.volume,
          color: candle.close >= candle.open ? '#26a69a55' : '#ef535055',
        }));
        
        // Set volume data
        volumeSeries.setData(volumeData);
        
        // Add position markers if available
        if (positions && positions.length > 0) {
          const position = positions.find(p => p.symbol === symbol);
          
          if (position) {
            // Find the closest time to entry time
            const entryTime = new Date(position.entry_time).getTime() / 1000;
            const closestCandle = formattedData.reduce((prev, curr) => {
              return Math.abs(curr.time - entryTime) < Math.abs(prev.time - entryTime) ? curr : prev;
            });
            
            // Add marker for entry
            candlestickSeries.setMarkers([
              {
                time: closestCandle.time,
                position: 'belowBar',
                color: '#2196F3',
                shape: 'arrowUp',
                text: `Buy @ ${position.entry_price.toFixed(2)}`,
              },
            ]);
          }
        }
        
        // Fit content
        chart.timeScale().fitContent();
        
        // Save chart reference
        chartRef.current = chart;
        
        // Handle resize
        const resizeObserver = new ResizeObserver(entries => {
          if (entries.length === 0 || !chartRef.current) return;
          const { width } = entries[0].contentRect;
          chartRef.current.applyOptions({ width });
        });
        
        resizeObserver.observe(chartContainerRef.current);
        
        // Cleanup function
        return () => {
          resizeObserver.disconnect();
          if (chartRef.current) {
            chartRef.current.remove();
            chartRef.current = null;
          }
        };
      } catch (error) {
        console.error('Error creating chart:', error);
      } finally {
        setChartLoading(false);
      }
    };
    
    initChart();
  }, [symbol, timeframe, isLogScale, isLoading]);
  
  // Toggle scale type
  const toggleScale = () => {
    setIsLogScale(!isLogScale);
  };
  
  // Handle timeframe change
  const handleTimeframeChange = (e) => {
    setTimeframe(e.target.value);
  };
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="bold">
          {symbol || 'Select an Asset'}
          {positions && positions.length > 0 && positions.some(p => p.symbol === symbol) && (
            <Badge colorScheme="green" ml={2}>Position Open</Badge>
          )}
        </Text>
        
        <HStack spacing={2}>
          <Select 
            size="sm" 
            value={timeframe} 
            onChange={handleTimeframeChange}
            width="auto"
          >
            <option value="1m">1 minute</option>
            <option value="5m">5 minutes</option>
            <option value="15m">15 minutes</option>
            <option value="30m">30 minutes</option>
            <option value="1h">1 hour</option>
            <option value="4h">4 hours</option>
            <option value="1d">1 day</option>
          </Select>
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={toggleScale}
          >
            {isLogScale ? 'Log Scale' : 'Linear Scale'}
          </Button>
        </HStack>
      </Flex>
      
      <Box 
        ref={chartContainerRef} 
        height="400px"
        position="relative"
      >
        {(isLoading || chartLoading || !symbol) && (
          <Flex 
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            justify="center"
            align="center"
            bg="rgba(255, 255, 255, 0.7)"
            zIndex="1"
            borderRadius="md"
          >
            {!symbol ? (
              <Text>Select an asset to view chart</Text>
            ) : (
              <Spinner size="xl" />
            )}
          </Flex>
        )}
      </Box>
    </Box>
  );
};

export default LiveAssetChart;
