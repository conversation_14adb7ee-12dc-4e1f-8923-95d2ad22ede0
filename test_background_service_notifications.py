#!/usr/bin/env python3
"""
Test script to verify that background service loads the correct notification configs.
"""

import sys
import os

# Add the src directory to the path
sys.path.append('src')

from src.config_manager import load_notification_config

def test_notification_loading():
    """Test that notification configs are loaded correctly."""
    print("TESTING NOTIFICATION CONFIG LOADING")
    print("=" * 60)
    
    # Test 1: Default config (should use environment variables as fallback)
    print("\n1. Testing default config (config/notifications.json):")
    default_config = load_notification_config()
    default_token = default_config.get('telegram', {}).get('token', 'NOT SET')
    print(f"   Token: {default_token[:20]}...")
    
    # Test 2: Kraken config (should use JSON values, not environment)
    print("\n2. Testing Kraken config:")
    kraken_config = load_notification_config('config/notifications_kraken.json')
    kraken_token = kraken_config.get('telegram', {}).get('token', 'NOT SET')
    kraken_identifier = kraken_config.get('notification_settings', {}).get('exchange_identifier', 'NOT SET')
    print(f"   Token: {kraken_token[:20]}...")
    print(f"   Identifier: {kraken_identifier}")
    
    # Test 3: Bitvavo config (should use JSON values, not environment)
    print("\n3. Testing Bitvavo config:")
    bitvavo_config = load_notification_config('config/notifications_bitvavo.json')
    bitvavo_token = bitvavo_config.get('telegram', {}).get('token', 'NOT SET')
    bitvavo_identifier = bitvavo_config.get('notification_settings', {}).get('exchange_identifier', 'NOT SET')
    print(f"   Token: {bitvavo_token[:20]}...")
    print(f"   Identifier: {bitvavo_identifier}")
    
    # Verify they're different
    print(f"\n4. Verification:")
    print(f"   Default != Kraken: {default_token != kraken_token}")
    print(f"   Default != Bitvavo: {default_token != bitvavo_token}")
    print(f"   Kraken != Bitvavo: {kraken_token != bitvavo_token}")
    
    # Check environment variables
    print(f"\n5. Environment variables:")
    env_token = os.getenv('TELEGRAM_BOT_TOKEN', 'NOT SET')
    print(f"   TELEGRAM_BOT_TOKEN: {env_token[:20]}...")
    
    # Final assessment
    print(f"\n{'='*60}")
    print("ASSESSMENT")
    print(f"{'='*60}")
    
    if (default_token != kraken_token and 
        default_token != bitvavo_token and 
        kraken_token != bitvavo_token):
        print("✅ SUCCESS: All configs use different bot tokens!")
        print("✅ Environment variables are NOT overriding JSON configs!")
        print("\nYour background services will now use the correct bots:")
        print(f"   Kraken: {kraken_token[:20]}... with [{kraken_identifier}]")
        print(f"   Bitvavo: {bitvavo_token[:20]}... with [{bitvavo_identifier}]")
    else:
        print("❌ ISSUE: Some configs are using the same bot token!")
        print("❌ Environment variables might still be overriding JSON configs!")
        
        if default_token == env_token:
            print("   Default config is using environment variable (expected)")
        if kraken_token == env_token:
            print("   ❌ Kraken config is using environment variable (PROBLEM!)")
        if bitvavo_token == env_token:
            print("   ❌ Bitvavo config is using environment variable (PROBLEM!)")

if __name__ == "__main__":
    test_notification_loading()
