#!/usr/bin/env python3
"""
Debug script to check environment variables when run by systemd
"""

import os
import sys
import logging
from datetime import datetime

# Set up basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - DEBUG_ENV - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/debug_env.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def debug_environment():
    """Debug environment variables"""
    logging.info("=== ENVIRONMENT DEBUG START ===")
    
    # Check key environment variables
    env_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID',
        'TELEGRAM_BOT_TOKEN_KRAKEN',
        'TELEGRAM_CHAT_ID_KRAKEN',
        'TELEGRAM_BOT_TOKEN_BITVAVO',
        'TELEGRAM_CHAT_ID_BITVAVO',
        'KRAKEN_API_KEY',
        'KRAKEN_SECRET_KEY',
        'BITVAVO_API_KEY',
        'BITVAVO_SECRET_KEY'
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if 'TOKEN' in var or 'KEY' in var:
                logging.info(f"{var}: SET (first 10 chars: {value[:10]}...)")
            else:
                logging.info(f"{var}: {value}")
        else:
            logging.info(f"{var}: NOT SET")
    
    # Test notification config loading
    logging.info("=== TESTING NOTIFICATION CONFIG ===")
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from src.config_manager import load_notification_config

        # Test Kraken config
        logging.info("--- Testing Kraken Config ---")
        kraken_config = load_notification_config('config/notifications_kraken_liepa.json')
        kraken_telegram = kraken_config.get('telegram', {})

        logging.info(f"Kraken - Telegram enabled: {kraken_telegram.get('enabled')}")

        kraken_token = kraken_telegram.get('token')
        kraken_chat_id = kraken_telegram.get('chat_id')

        if kraken_token:
            logging.info(f"Kraken - Telegram token loaded: {kraken_token[:10]}...")
        else:
            logging.info("Kraken - Telegram token: NOT LOADED")

        if kraken_chat_id:
            logging.info(f"Kraken - Telegram chat_id loaded: {kraken_chat_id}")
        else:
            logging.info("Kraken - Telegram chat_id: NOT LOADED")

        kraken_exchange_id = kraken_config.get('notification_settings', {}).get('exchange_identifier')
        logging.info(f"Kraken - Exchange identifier: {kraken_exchange_id}")

        # Test Bitvavo config
        logging.info("--- Testing Bitvavo Config ---")
        bitvavo_config = load_notification_config('config/notifications_bitvavo_liepa.json')
        bitvavo_telegram = bitvavo_config.get('telegram', {})

        logging.info(f"Bitvavo - Telegram enabled: {bitvavo_telegram.get('enabled')}")

        bitvavo_token = bitvavo_telegram.get('token')
        bitvavo_chat_id = bitvavo_telegram.get('chat_id')

        if bitvavo_token:
            logging.info(f"Bitvavo - Telegram token loaded: {bitvavo_token[:10]}...")
        else:
            logging.info("Bitvavo - Telegram token: NOT LOADED")

        if bitvavo_chat_id:
            logging.info(f"Bitvavo - Telegram chat_id loaded: {bitvavo_chat_id}")
        else:
            logging.info("Bitvavo - Telegram chat_id: NOT LOADED")

        bitvavo_exchange_id = bitvavo_config.get('notification_settings', {}).get('exchange_identifier')
        logging.info(f"Bitvavo - Exchange identifier: {bitvavo_exchange_id}")

    except Exception as e:
        logging.error(f"Error loading notification config: {e}")
        import traceback
        logging.error(traceback.format_exc())
    
    logging.info("=== ENVIRONMENT DEBUG END ===")

if __name__ == "__main__":
    debug_environment()
