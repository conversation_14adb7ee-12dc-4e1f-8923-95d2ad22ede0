"""
Token evaluator module for memecoin discovery.

This module combines various data sources and analysis results to provide
comprehensive token evaluation and scoring.
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Handle numpy import gracefully
try:
    import numpy as np
except ImportError:
    # Fallback implementation for basic numpy functions
    class NumpyFallback:
        @staticmethod
        def mean(data):
            return sum(data) / len(data) if data else 0

        @staticmethod
        def std(data):
            if len(data) <= 1:
                return 0
            mean_val = sum(data) / len(data)
            variance = sum((x - mean_val) ** 2 for x in data) / len(data)
            return variance ** 0.5

    np = NumpyFallback()

@dataclass
class TokenScore:
    """Comprehensive token scoring result."""
    symbol: str
    final_score: float  # 0-100
    confidence: float  # 0-1
    recommendation: str
    component_scores: Dict[str, float]
    risk_level: str
    reasoning: str
    metadata: Dict[str, Any]

class TokenEvaluator:
    """Comprehensive token evaluator combining multiple analysis methods."""
    
    # Scoring weights for different components
    SCORING_WEIGHTS = {
        'ai_analysis': 0.35,      # AI analysis weight
        'social_sentiment': 0.25,  # Social media sentiment
        'engagement_metrics': 0.20, # Social engagement quality
        'technical_indicators': 0.15, # Technical analysis
        'risk_assessment': -0.05   # Risk penalty (negative weight)
    }
    
    # Risk level thresholds
    RISK_THRESHOLDS = {
        'low': 20,
        'medium': 50,
        'high': 75,
        'critical': 90
    }
    
    def __init__(self):
        """Initialize token evaluator."""
        logging.info("Token evaluator initialized")
    
    def evaluate_token(self, token_symbol: str, ai_analysis: Dict, 
                      social_data: List[Dict], token_metadata: Dict = None) -> TokenScore:
        """Evaluate a token comprehensively using all available data."""
        try:
            # Calculate component scores
            component_scores = {}
            
            # 1. AI Analysis Score
            component_scores['ai_analysis'] = self._score_ai_analysis(ai_analysis)
            
            # 2. Social Sentiment Score
            component_scores['social_sentiment'] = self._score_social_sentiment(social_data)
            
            # 3. Engagement Metrics Score
            component_scores['engagement_metrics'] = self._score_engagement_metrics(social_data)
            
            # 4. Technical Indicators Score
            component_scores['technical_indicators'] = self._score_technical_indicators(
                ai_analysis.get('technical_indicators', {}), token_metadata
            )
            
            # 5. Risk Assessment Score (penalty)
            component_scores['risk_assessment'] = self._score_risk_assessment(
                ai_analysis, social_data
            )
            
            # Calculate weighted final score
            final_score = self._calculate_weighted_score(component_scores)
            
            # Determine confidence level
            confidence = self._calculate_confidence(ai_analysis, social_data, component_scores)
            
            # Determine recommendation
            recommendation = self._determine_recommendation(final_score, confidence, component_scores)
            
            # Determine risk level
            risk_level = self._determine_risk_level(component_scores['risk_assessment'])
            
            # Generate reasoning
            reasoning = self._generate_reasoning(component_scores, ai_analysis, social_data)
            
            # Compile metadata
            metadata = {
                'evaluation_date': datetime.now().isoformat(),
                'data_sources': {
                    'ai_analysis_available': bool(ai_analysis),
                    'social_mentions_count': len(social_data),
                    'token_metadata_available': bool(token_metadata)
                },
                'scoring_weights': self.SCORING_WEIGHTS.copy()
            }
            
            return TokenScore(
                symbol=token_symbol,
                final_score=final_score,
                confidence=confidence,
                recommendation=recommendation,
                component_scores=component_scores,
                risk_level=risk_level,
                reasoning=reasoning,
                metadata=metadata
            )
            
        except Exception as e:
            logging.error(f"Error evaluating token {token_symbol}: {e}")
            return self._create_fallback_score(token_symbol, str(e))
    
    def _score_ai_analysis(self, ai_analysis: Dict) -> float:
        """Score based on AI analysis results."""
        if not ai_analysis:
            return 50.0  # Neutral score if no AI analysis
        
        try:
            # Get AI overall score (should be 0-100)
            ai_score = ai_analysis.get('overall_score', 50.0)
            
            # Adjust based on AI confidence
            ai_confidence = ai_analysis.get('confidence', 0.5)
            confidence_multiplier = 0.5 + (ai_confidence * 0.5)  # 0.5 to 1.0
            
            # Adjust based on recommendation
            recommendation = ai_analysis.get('recommendation', 'hold')
            rec_multipliers = {
                'strong_buy': 1.2,
                'buy': 1.1,
                'hold': 1.0,
                'avoid': 0.8,
                'scam_risk': 0.3
            }
            rec_multiplier = rec_multipliers.get(recommendation, 1.0)
            
            adjusted_score = ai_score * confidence_multiplier * rec_multiplier
            return min(100.0, max(0.0, adjusted_score))
            
        except Exception as e:
            logging.error(f"Error scoring AI analysis: {e}")
            return 50.0
    
    def _score_social_sentiment(self, social_data: List[Dict]) -> float:
        """Score based on social media sentiment."""
        if not social_data:
            return 40.0  # Slightly negative if no social data
        
        try:
            # Calculate average sentiment
            sentiments = [tweet.get('sentiment_score', 0) for tweet in social_data]
            avg_sentiment = np.mean(sentiments) if sentiments else 0
            
            # Convert from -1,1 scale to 0-100 scale
            sentiment_score = (avg_sentiment + 1) * 50
            
            # Adjust based on sentiment consistency
            sentiment_std = np.std(sentiments) if len(sentiments) > 1 else 0
            consistency_bonus = max(0, 10 - (sentiment_std * 20))  # Bonus for consistent sentiment
            
            # Adjust based on volume of mentions
            volume_multiplier = min(1.2, 1 + (len(social_data) / 100))  # Up to 20% bonus for volume
            
            final_score = (sentiment_score + consistency_bonus) * volume_multiplier
            return min(100.0, max(0.0, final_score))
            
        except Exception as e:
            logging.error(f"Error scoring social sentiment: {e}")
            return 40.0
    
    def _score_engagement_metrics(self, social_data: List[Dict]) -> float:
        """Score based on social media engagement quality."""
        if not social_data:
            return 30.0  # Low score if no engagement data
        
        try:
            total_engagement = 0
            total_followers = 0
            high_engagement_tweets = 0
            
            for tweet in social_data:
                likes = tweet.get('like_count', 0)
                retweets = tweet.get('retweet_count', 0)
                replies = tweet.get('reply_count', 0)
                followers = tweet.get('author_followers', 1)
                
                tweet_engagement = likes + (retweets * 2) + replies
                total_engagement += tweet_engagement
                total_followers += followers
                
                # Check for high engagement tweets
                engagement_rate = tweet_engagement / max(followers, 1)
                if engagement_rate > 0.01:  # 1% engagement rate threshold
                    high_engagement_tweets += 1
            
            # Calculate average engagement rate
            avg_engagement_rate = total_engagement / max(total_followers, 1)
            
            # Base score from engagement rate (log scale)
            base_score = min(80, math.log10(max(avg_engagement_rate * 10000, 1)) * 20)
            
            # Bonus for high engagement tweets
            high_engagement_bonus = min(20, high_engagement_tweets * 5)
            
            # Bonus for engagement diversity (multiple sources)
            unique_authors = len(set(tweet.get('author_username', '') for tweet in social_data))
            diversity_bonus = min(10, unique_authors * 2)
            
            final_score = base_score + high_engagement_bonus + diversity_bonus
            return min(100.0, max(0.0, final_score))
            
        except Exception as e:
            logging.error(f"Error scoring engagement metrics: {e}")
            return 30.0
    
    def _score_technical_indicators(self, technical_data: Dict, token_metadata: Dict = None) -> float:
        """Score based on technical indicators."""
        if not technical_data and not token_metadata:
            return 50.0  # Neutral if no technical data
        
        try:
            score = 50.0  # Start with neutral
            
            # Score momentum if available
            momentum = technical_data.get('momentum', 'neutral')
            momentum_scores = {
                'strong_bullish': 90,
                'bullish': 70,
                'neutral': 50,
                'bearish': 30,
                'strong_bearish': 10
            }
            if momentum in momentum_scores:
                score = (score + momentum_scores[momentum]) / 2
            
            # Score volume analysis
            volume_analysis = technical_data.get('volume_analysis', 'stable')
            volume_scores = {
                'increasing': 75,
                'stable': 50,
                'decreasing': 25
            }
            if volume_analysis in volume_scores:
                score = (score + volume_scores[volume_analysis]) / 2
            
            # Score price action
            price_action = technical_data.get('price_action', 'consolidation')
            price_scores = {
                'breakout': 80,
                'consolidation': 50,
                'decline': 20
            }
            if price_action in price_scores:
                score = (score + price_scores[price_action]) / 2
            
            # Adjust based on token metadata
            if token_metadata:
                # Market cap consideration
                market_cap = token_metadata.get('market_cap', 0)
                if market_cap > 0:
                    if market_cap < 1_000_000:  # Under $1M - higher risk but higher potential
                        score *= 1.1
                    elif market_cap > 100_000_000:  # Over $100M - lower risk but lower potential
                        score *= 0.9
                
                # Volume consideration
                volume_24h = token_metadata.get('volume_24h', 0)
                if volume_24h > 0 and market_cap > 0:
                    volume_ratio = volume_24h / market_cap
                    if volume_ratio > 0.1:  # High volume relative to market cap
                        score *= 1.05
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logging.error(f"Error scoring technical indicators: {e}")
            return 50.0
    
    def _score_risk_assessment(self, ai_analysis: Dict, social_data: List[Dict]) -> float:
        """Calculate risk score (higher = more risky)."""
        try:
            risk_score = 0.0
            
            # AI-identified risk factors
            if ai_analysis:
                risk_factors = ai_analysis.get('risk_factors', [])
                risk_score += len(risk_factors) * 10  # 10 points per risk factor
                
                # Specific high-risk recommendations
                recommendation = ai_analysis.get('recommendation', 'hold')
                if recommendation == 'scam_risk':
                    risk_score += 50
                elif recommendation == 'avoid':
                    risk_score += 30
            
            # Social media risk indicators
            if social_data:
                # Check for bot-like activity patterns
                authors = [tweet.get('author_username', '') for tweet in social_data]
                unique_authors = len(set(authors))
                if len(authors) > 0:
                    author_diversity = unique_authors / len(authors)
                    if author_diversity < 0.3:  # Low author diversity suggests bots
                        risk_score += 20
                
                # Check for suspicious engagement patterns
                for tweet in social_data:
                    engagement_score = tweet.get('engagement_score', 0)
                    if engagement_score > 0.9:  # Suspiciously high engagement
                        risk_score += 5
            
            return min(100.0, max(0.0, risk_score))
            
        except Exception as e:
            logging.error(f"Error calculating risk score: {e}")
            return 50.0  # Medium risk on error
    
    def _calculate_weighted_score(self, component_scores: Dict[str, float]) -> float:
        """Calculate weighted final score from component scores."""
        try:
            weighted_sum = 0.0
            total_weight = 0.0
            
            for component, score in component_scores.items():
                if component in self.SCORING_WEIGHTS:
                    weight = self.SCORING_WEIGHTS[component]
                    weighted_sum += score * weight
                    total_weight += abs(weight)  # Use absolute weight for normalization
            
            if total_weight > 0:
                final_score = weighted_sum / total_weight * 100
                return min(100.0, max(0.0, final_score))
            else:
                return 50.0
                
        except Exception as e:
            logging.error(f"Error calculating weighted score: {e}")
            return 50.0
    
    def _calculate_confidence(self, ai_analysis: Dict, social_data: List[Dict], 
                            component_scores: Dict[str, float]) -> float:
        """Calculate confidence level in the evaluation."""
        try:
            confidence_factors = []
            
            # AI analysis confidence
            if ai_analysis:
                ai_confidence = ai_analysis.get('confidence', 0.5)
                confidence_factors.append(ai_confidence)
            
            # Data volume confidence
            social_data_confidence = min(1.0, len(social_data) / 20)  # Max confidence at 20+ mentions
            confidence_factors.append(social_data_confidence)
            
            # Score consistency confidence
            scores = [score for score in component_scores.values() if score > 0]
            if len(scores) > 1:
                score_std = np.std(scores)
                consistency_confidence = max(0.3, 1.0 - (score_std / 50))  # Lower std = higher confidence
                confidence_factors.append(consistency_confidence)
            
            # Overall confidence is the average of all factors
            if confidence_factors:
                return np.mean(confidence_factors)
            else:
                return 0.5
                
        except Exception as e:
            logging.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _determine_recommendation(self, final_score: float, confidence: float, 
                                component_scores: Dict[str, float]) -> str:
        """Determine investment recommendation based on score and confidence."""
        try:
            # Adjust thresholds based on confidence
            confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 to 1.0
            
            # Check for high risk
            risk_score = component_scores.get('risk_assessment', 0)
            if risk_score > 70:
                return 'avoid'
            elif risk_score > 50:
                return 'hold'  # High risk = conservative recommendation
            
            # Score-based recommendations with confidence adjustment
            adjusted_score = final_score * confidence_multiplier
            
            if adjusted_score >= 80:
                return 'strong_buy'
            elif adjusted_score >= 65:
                return 'buy'
            elif adjusted_score >= 35:
                return 'hold'
            else:
                return 'avoid'
                
        except Exception as e:
            logging.error(f"Error determining recommendation: {e}")
            return 'hold'
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """Determine risk level based on risk score."""
        for level, threshold in self.RISK_THRESHOLDS.items():
            if risk_score <= threshold:
                return level
        return 'critical'
    
    def _generate_reasoning(self, component_scores: Dict[str, float], 
                          ai_analysis: Dict, social_data: List[Dict]) -> str:
        """Generate human-readable reasoning for the evaluation."""
        try:
            reasoning_parts = []
            
            # AI analysis reasoning
            if ai_analysis and ai_analysis.get('reasoning'):
                reasoning_parts.append(f"AI Analysis: {ai_analysis['reasoning'][:200]}...")
            
            # Component score summary
            reasoning_parts.append("Component Scores:")
            for component, score in component_scores.items():
                reasoning_parts.append(f"- {component.replace('_', ' ').title()}: {score:.1f}/100")
            
            # Data summary
            reasoning_parts.append(f"Based on {len(social_data)} social media mentions")
            
            return " ".join(reasoning_parts)
            
        except Exception as e:
            logging.error(f"Error generating reasoning: {e}")
            return "Evaluation completed with limited data."
    
    def _create_fallback_score(self, token_symbol: str, error_msg: str) -> TokenScore:
        """Create fallback score when evaluation fails."""
        return TokenScore(
            symbol=token_symbol,
            final_score=30.0,  # Conservative score on error
            confidence=0.1,
            recommendation='avoid',
            component_scores={},
            risk_level='high',
            reasoning=f"Evaluation failed: {error_msg}",
            metadata={'error': error_msg}
        )
