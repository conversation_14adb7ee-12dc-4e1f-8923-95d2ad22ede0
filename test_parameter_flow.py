#!/usr/bin/env python3
"""
Quick test to verify parameter flow from YAML to visualization script.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.MTPI_signal_handler import load_mtpi_indicators_config

def test_parameter_flow():
    """Test that parameters flow correctly from YAML to functions."""
    print("=" * 60)
    print("PARAMETER FLOW TEST")
    print("=" * 60)
    
    # Load configuration
    config = load_mtpi_indicators_config()
    
    print("\n1. Configuration loaded:")
    print("-" * 30)
    for key, value in config.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
    
    # Test PGO parameters specifically
    print("\n2. PGO Parameters:")
    print("-" * 30)
    pgo_config = config.get('pgo', {})
    
    expected_values = {
        'length': 35,
        'upper_threshold': 1.35,
        'lower_threshold': -0.58  # This should match your YAML!
    }
    
    all_correct = True
    for param, expected in expected_values.items():
        actual = pgo_config.get(param, "MISSING")
        status = "✅" if actual == expected else "❌"
        print(f"  {param}: {actual} (expected: {expected}) {status}")
        if actual != expected:
            all_correct = False
    
    print(f"\n3. Result: {'✅ ALL PARAMETERS CORRECT' if all_correct else '❌ PARAMETER MISMATCH FOUND'}")
    
    return all_correct

if __name__ == '__main__':
    success = test_parameter_flow()
    if success:
        print("\n🎉 Parameter flow test PASSED!")
        print("The visualization script should now use the correct PGO parameters.")
    else:
        print("\n💥 Parameter flow test FAILED!")
        print("There are still parameter mismatches that need to be fixed.")
