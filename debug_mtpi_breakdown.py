#!/usr/bin/env python3
"""
MTPI Indicator Breakdown Script

This script shows the individual indicator scores and the math behind the MTPI calculation.
It will help debug why the MTPI score differs from TradingView.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import logging
from datetime import datetime

from src.MTPI_signal_handler import (
    load_mtpi_multi_indicator_config,
    fetch_mtpi_signal_from_config
)
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.pgo_score import generate_pgo_score_signal
from src.indicators.bb_score import generate_bb_score_signal
from src.indicators.dwma_score import generate_dwma_signal
from src.indicators.median_score import generate_median_score_signal
from src.indicators.aad_score import generate_aad_score_signal
from src.indicators.dynamic_ema_score import generate_dynamic_ema_score_signal
from src.indicators.quantile_dema_score import generate_quantile_dema_score_signal
from src.indicators.advanced_indicators import generate_dema_super_score_signal, generate_dpsd_score_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fetch_current_btc_data():
    """Fetch the latest BTC data for analysis."""
    try:
        # Fetch recent BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=100,
            use_cache=True,
            force_refresh=True,
            max_cache_age_days=0
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            raise Exception("Failed to fetch BTC data")
        
        btc_df = data_dict['BTC/USDT']
        logging.info(f"Loaded {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        return btc_df
        
    except Exception as e:
        logging.error(f"Error fetching BTC data: {e}")
        return None

def calculate_individual_indicator_scores(df, config):
    """Calculate individual indicator scores and return the latest values."""
    try:
        enabled_indicators = config.get('enabled_indicators', [])
        indicator_scores = {}
        
        print(f"\n🔍 CALCULATING INDIVIDUAL INDICATOR SCORES")
        print(f"📅 Latest Date: {df.index[-1]}")
        print(f"💰 BTC Price: ${df['close'].iloc[-1]:,.2f}")
        print(f"🔧 Enabled Indicators: {enabled_indicators}")
        print("=" * 80)
        
        # PGO Score
        if 'pgo' in enabled_indicators:
            pgo_config = config.get('pgo', {})
            pgo_signals = generate_pgo_score_signal(
                df=df,
                length=pgo_config.get('length', 35),
                upper_threshold=pgo_config.get('upper_threshold', 1.35),
                lower_threshold=pgo_config.get('lower_threshold', -0.58),
                skip_warmup=False
            )
            latest_pgo = pgo_signals.iloc[-1]
            indicator_scores['pgo'] = latest_pgo
            print(f"📊 PGO Score: {latest_pgo:>3} (length={pgo_config.get('length', 35)}, upper={pgo_config.get('upper_threshold', 1.35)}, lower={pgo_config.get('lower_threshold', -0.58)})")

        # Bollinger Bands Score
        if 'bollinger_bands' in enabled_indicators:
            bb_config = config.get('bollinger_bands', {})
            bb_signals = generate_bb_score_signal(
                df=df,
                length=bb_config.get('length', 33),
                multiplier=bb_config.get('multiplier', 2.0),
                long_threshold=bb_config.get('long_threshold', 76.0),
                short_threshold=bb_config.get('short_threshold', 31.0),
                src_col=bb_config.get('src_col', 'close'),
                use_heikin_ashi=bb_config.get('use_heikin_ashi', True),
                heikin_src=bb_config.get('heikin_src', 'close')
            )
            latest_bb = bb_signals.iloc[-1]
            indicator_scores['bollinger_bands'] = latest_bb
            print(f"📊 BB Score:  {latest_bb:>3} (length={bb_config.get('length', 33)}, mult={bb_config.get('multiplier', 2.0)}, long_th={bb_config.get('long_threshold', 76.0)}, short_th={bb_config.get('short_threshold', 31.0)})")

        # DWMA Score
        if 'dwma_score' in enabled_indicators:
            dwma_config = config.get('dwma_score', {})
            dwma_signals = generate_dwma_signal(
                df=df,
                smoothing_style=dwma_config.get('smoothing_style', 'Weighted SD'),
                src_col=dwma_config.get('src_col', 'close'),
                length=dwma_config.get('length', 17),
                ma_type=dwma_config.get('ma_type', 'EMA'),
                ma_smooth_length=dwma_config.get('ma_smooth_length', 12),
                sd_length=dwma_config.get('sd_length', 33),
                upper_sd_weight=dwma_config.get('upper_sd_weight', 1.031),
                lower_sd_weight=dwma_config.get('lower_sd_weight', 0.996),
                atr_period=dwma_config.get('atr_period', 12),
                atr_multiplier=dwma_config.get('atr_multiplier', 1.0),
                loop_start=dwma_config.get('loop_start', 1),
                loop_end=dwma_config.get('loop_end', 60),
                long_threshold=dwma_config.get('long_threshold', 30),
                short_threshold=dwma_config.get('short_threshold', 0)
            )
            latest_dwma = dwma_signals.iloc[-1]
            indicator_scores['dwma_score'] = latest_dwma
            print(f"📊 DWMA Score:{latest_dwma:>3} (style={dwma_config.get('smoothing_style', 'Weighted SD')}, length={dwma_config.get('length', 17)})")

        # Median Score
        if 'median_score' in enabled_indicators:
            median_config = config.get('median_score', {})
            median_signals = generate_median_score_signal(
                df=df,
                atr_period=median_config.get('atr_period', 10),
                multiplier=median_config.get('multiplier', 1.55),
                median_length=median_config.get('median_length', 7),
                src_col=median_config.get('src_col', 'close')
            )
            latest_median = median_signals.iloc[-1]
            indicator_scores['median_score'] = latest_median
            print(f"📊 Median Score:{latest_median:>2} (atr_period={median_config.get('atr_period', 10)}, mult={median_config.get('multiplier', 1.55)}, length={median_config.get('median_length', 7)})")

        # DEMA Super Score
        if 'dema_super_score' in enabled_indicators:
            dema_config = config.get('dema_super_score', {})
            dema_signals = generate_dema_super_score_signal(
                df=df,
                atr_period=dema_config.get('atr_period', 19),
                multiplier=dema_config.get('multiplier', 2.8),
                dema_length=dema_config.get('length', 17),
                src_col=dema_config.get('src_col', 'close')
            )
            latest_dema = dema_signals.iloc[-1]
            indicator_scores['dema_super_score'] = latest_dema
            print(f"📊 DEMA Score: {latest_dema:>2} (atr_period={dema_config.get('atr_period', 19)}, mult={dema_config.get('multiplier', 2.8)}, length={dema_config.get('length', 17)})")

        # DPSD Score
        if 'dpsd_score' in enabled_indicators:
            dpsd_config = config.get('dpsd_score', {})
            dpsd_signals = generate_dpsd_score_signal(
                df=df,
                dema_length=dpsd_config.get('dema_length', 9),
                percentile_length=dpsd_config.get('percentile_length', 58),
                sd_length=dpsd_config.get('sd_length', 27),
                ema_length=dpsd_config.get('ema_length', 14),
                percentile_upper=dpsd_config.get('percentile_upper', 60.0),
                percentile_lower=dpsd_config.get('percentile_lower', 45.0),
                src_col=dpsd_config.get('src_col', 'close')
            )
            latest_dpsd = dpsd_signals.iloc[-1]
            indicator_scores['dpsd_score'] = latest_dpsd
            print(f"📊 DPSD Score: {latest_dpsd:>2} (dema_len={dpsd_config.get('dema_length', 9)}, perc_len={dpsd_config.get('percentile_length', 58)})")

        # AAD Score
        if 'aad_score' in enabled_indicators:
            aad_config = config.get('aad_score', {})
            aad_signals = generate_aad_score_signal(
                df=df,
                src_col=aad_config.get('src_col', 'close'),
                length=aad_config.get('length', 22),
                aad_mult=aad_config.get('aad_mult', 1.2),
                avg_type=aad_config.get('avg_type', 'SMA')
            )
            latest_aad = aad_signals.iloc[-1]
            indicator_scores['aad_score'] = latest_aad
            print(f"📊 AAD Score:  {latest_aad:>2} (length={aad_config.get('length', 22)}, mult={aad_config.get('aad_mult', 1.2)}, type={aad_config.get('avg_type', 'SMA')})")

        # Dynamic EMA Score
        if 'dynamic_ema_score' in enabled_indicators:
            dynamic_config = config.get('dynamic_ema_score', {})
            dynamic_signals = generate_dynamic_ema_score_signal(
                df=df,
                median_length=dynamic_config.get('median_length', 9),
                median_src=dynamic_config.get('median_src', 'close'),
                ema_length=dynamic_config.get('ema_length', 12),
                smoothing_style=dynamic_config.get('smoothing_style', 'Weighted SD'),
                sd_length=dynamic_config.get('sd_length', 33),
                upper_sd_weight=dynamic_config.get('upper_sd_weight', 1.017),
                lower_sd_weight=dynamic_config.get('lower_sd_weight', 0.996),
                atr_period=dynamic_config.get('atr_period', 14),
                atr_multiplier=dynamic_config.get('atr_multiplier', 1.2)
            )
            latest_dynamic = dynamic_signals.iloc[-1]
            indicator_scores['dynamic_ema_score'] = latest_dynamic
            print(f"📊 Dynamic Score:{latest_dynamic:>1} (median_len={dynamic_config.get('median_length', 9)}, ema_len={dynamic_config.get('ema_length', 12)})")

        # Quantile DEMA Score
        if 'quantile_dema_score' in enabled_indicators:
            quantile_config = config.get('quantile_dema_score', {})
            quantile_signals = generate_quantile_dema_score_signal(
                df=df,
                dema_length=quantile_config.get('dema_length', 30),
                percentile_filter=quantile_config.get('percentile_filter', 10),
                atr_length=quantile_config.get('atr_length', 14),
                mult_up=quantile_config.get('mult_up', 1.2),
                mult_dn=quantile_config.get('mult_dn', 1.2),
                dema_st_length=quantile_config.get('dema_st_length', 30),
                percentile_length=quantile_config.get('percentile_length', 20),
                sd_length=quantile_config.get('sd_length', 30),
                src_col=quantile_config.get('src_col', 'close')
            )
            latest_quantile = quantile_signals.iloc[-1]
            indicator_scores['quantile_dema_score'] = latest_quantile
            print(f"📊 Quantile Score:{latest_quantile:>1} (dema_len={quantile_config.get('dema_length', 30)}, perc_filter={quantile_config.get('percentile_filter', 10)})")

        return indicator_scores
        
    except Exception as e:
        logging.error(f"Error calculating individual indicator scores: {e}")
        return {}

def show_mtpi_math(indicator_scores, config):
    """Show the MTPI calculation math step by step."""
    try:
        print("\n" + "=" * 80)
        print("🧮 MTPI CALCULATION BREAKDOWN")
        print("=" * 80)
        
        if not indicator_scores:
            print("❌ No indicator scores available")
            return
        
        # Get thresholds
        long_threshold = config.get('long_threshold', 0.1)
        short_threshold = config.get('short_threshold', -0.1)
        
        # Show individual scores
        print("📊 Individual Indicator Scores:")
        total_sum = 0
        for indicator, score in indicator_scores.items():
            print(f"   {indicator:<20}: {score:>3}")
            total_sum += score
        
        print("-" * 40)
        print(f"📈 Sum of all scores: {total_sum}")
        print(f"🔢 Number of indicators: {len(indicator_scores)}")
        
        # Calculate MTPI score (finaltpi = scoreSum / componentCount)
        if len(indicator_scores) > 0:
            mtpi_score = total_sum / len(indicator_scores)
            print(f"🎯 MTPI Score (finaltpi): {total_sum} ÷ {len(indicator_scores)} = {mtpi_score:.6f}")
        else:
            mtpi_score = 0.0
            print(f"🎯 MTPI Score (finaltpi): 0.0 (no indicators)")
        
        # Show thresholds and signal determination
        print(f"\n📊 Thresholds:")
        print(f"   Long Threshold:  {long_threshold}")
        print(f"   Short Threshold: {short_threshold}")
        
        # Determine signal
        if mtpi_score >= long_threshold:
            signal = 1
            signal_text = "🟢 BULLISH"
        elif mtpi_score <= short_threshold:
            signal = -1
            signal_text = "🔴 BEARISH"
        else:
            signal = 0
            signal_text = "🟡 NEUTRAL"
        
        print(f"\n🎯 Final Signal: {signal_text} ({signal})")
        print(f"   Logic: {mtpi_score:.6f} {'≥' if mtpi_score >= long_threshold else '≤' if mtpi_score <= short_threshold else 'between'} threshold")
        
        # Compare with TradingView
        print(f"\n📺 TradingView Comparison:")
        print(f"   Your MTPI Score: {mtpi_score:.6f}")
        print(f"   TradingView:     0.714000 (example)")
        print(f"   Difference:      {abs(mtpi_score - 0.714):.6f}")
        
        return mtpi_score, signal
        
    except Exception as e:
        logging.error(f"Error showing MTPI math: {e}")
        return 0.0, 0

def main():
    """Main function to run the MTPI breakdown analysis."""
    print("🔍 MTPI INDICATOR BREAKDOWN ANALYSIS")
    print("=" * 80)
    
    # Load configuration
    config = load_mtpi_multi_indicator_config()
    if not config:
        print("❌ Failed to load MTPI configuration")
        return
    
    # Fetch BTC data
    btc_df = fetch_current_btc_data()
    if btc_df is None:
        print("❌ Failed to fetch BTC data")
        return
    
    # Calculate individual indicator scores
    indicator_scores = calculate_individual_indicator_scores(btc_df, config)
    
    # Show MTPI calculation math
    mtpi_score, signal = show_mtpi_math(indicator_scores, config)
    
    print(f"\n✅ Analysis complete!")
    print(f"🎯 Final MTPI Score: {mtpi_score:.6f}")
    print(f"📊 Final Signal: {signal}")

if __name__ == "__main__":
    main()
