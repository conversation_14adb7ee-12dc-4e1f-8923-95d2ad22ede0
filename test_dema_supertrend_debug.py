#!/usr/bin/env python3
"""
Debug script for DEMA Supertrend indicator
Compare our implementation with expected TradingView behavior
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import yfinance as yf
from indicators.dema_supertrend import calculate_dema_supertrend_score, calculate_dema_supertrend, calculate_dema

def fetch_test_data():
    """Fetch BTC/USDT data for testing"""
    try:
        # Fetch BTC data
        ticker = "BTC-USD"
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        print(f"Fetching {ticker} data from {start_date.date()} to {end_date.date()}")
        
        data = yf.download(ticker, start=start_date, end=end_date, interval='1d')
        
        if data.empty:
            raise ValueError("No data fetched")
        
        # Rename columns to lowercase
        data.columns = [col.lower() for col in data.columns]
        data = data.reset_index()
        data.columns = [col.lower() for col in data.columns]
        
        print(f"Fetched {len(data)} rows of data")
        print(f"Date range: {data['date'].min()} to {data['date'].max()}")
        print(f"Columns: {list(data.columns)}")
        
        return data
        
    except Exception as e:
        print(f"Error fetching data: {e}")
        return None

def debug_dema_supertrend():
    """Debug DEMA Supertrend calculation step by step"""
    
    # Fetch data
    df = fetch_test_data()
    if df is None:
        return
    
    # Parameters from PineScript
    atr_period = 19      # subject
    multiplier = 2.8     # mul
    dema_length = 17     # demalen
    src_col = 'close'    # src (main price series for DEMA)
    
    print(f"\nDEMA Supertrend Parameters:")
    print(f"ATR Period: {atr_period}")
    print(f"Multiplier: {multiplier}")
    print(f"DEMA Length: {dema_length}")
    print(f"Source: {src_col}")
    
    # Calculate components step by step
    print(f"\nCalculating DEMA Supertrend components...")
    
    # Step 1: DEMA calculation
    src = df[src_col]
    dema = calculate_dema(src, dema_length)
    print(f"DEMA calculated, first 10 values:")
    print(dema.head(10).round(4))
    
    # Step 2: Full calculation
    signal, supertrend_line, direction = calculate_dema_supertrend_score(
        df, 
        atr_period=atr_period,
        multiplier=multiplier, 
        dema_length=dema_length,
        src_col=src_col
    )
    
    # Print recent values for debugging
    print(f"\nRecent values (last 20 rows):")
    debug_df = pd.DataFrame({
        'Date': df['date'].tail(20),
        'Close': df['close'].tail(20).round(2),
        'High': df['high'].tail(20).round(2),
        'DEMA': dema.tail(20).round(4),
        'Supertrend': supertrend_line.tail(20).round(4),
        'Direction': direction.tail(20),
        'Signal': signal.tail(20)
    })
    
    print(debug_df.to_string(index=False))
    
    # Signal statistics
    signal_counts = signal.value_counts().sort_index()
    print(f"\nSignal Distribution:")
    for sig, count in signal_counts.items():
        print(f"  {sig}: {count} ({count/len(signal)*100:.1f}%)")
    
    # Plot results
    plt.figure(figsize=(15, 10))
    
    # Use last 100 days for better visibility
    plot_data = df.tail(100).copy()
    plot_signal = signal.tail(100)
    plot_supertrend = supertrend_line.tail(100)
    plot_direction = direction.tail(100)
    plot_dema = dema.tail(100)
    
    # Main price and supertrend plot
    plt.subplot(3, 1, 1)
    plt.plot(plot_data['date'], plot_data['close'], label='Close Price', color='black', linewidth=1)
    plt.plot(plot_data['date'], plot_dema, label='DEMA', color='blue', linewidth=1, alpha=0.7)
    
    # Color supertrend based on direction
    for i in range(len(plot_data)):
        color = 'green' if plot_direction.iloc[i] == -1 else 'red'
        if i > 0:
            plt.plot(plot_data['date'].iloc[i-1:i+1], plot_supertrend.iloc[i-1:i+1], 
                    color=color, linewidth=2)
    
    plt.title('BTC/USD - DEMA Supertrend Analysis (Last 100 Days)')
    plt.ylabel('Price')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Direction plot
    plt.subplot(3, 1, 2)
    plt.plot(plot_data['date'], plot_direction, label='Direction', color='purple', linewidth=2)
    plt.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    plt.title('Supertrend Direction')
    plt.ylabel('Direction')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Signal plot
    plt.subplot(3, 1, 3)
    plt.plot(plot_data['date'], plot_signal, label='Signal', color='orange', linewidth=2, marker='o', markersize=3)
    plt.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    plt.axhline(y=1, color='green', linestyle='--', alpha=0.5, label='Long')
    plt.axhline(y=-1, color='red', linestyle='--', alpha=0.5, label='Short')
    plt.title('Trading Signals')
    plt.ylabel('Signal')
    plt.xlabel('Date')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.xticks(rotation=45)
    plt.show()
    
    # Check for signal changes
    signal_changes = signal.diff().fillna(0)
    change_indices = signal_changes[signal_changes != 0].index
    
    print(f"\nSignal Changes (last 10):")
    for idx in change_indices[-10:]:
        date = df.loc[idx, 'date']
        old_signal = signal.iloc[idx-1] if idx > 0 else 0
        new_signal = signal.iloc[idx]
        close_price = df.loc[idx, 'close']
        print(f"  {date.strftime('%Y-%m-%d')}: {old_signal} -> {new_signal} (Close: ${close_price:.2f})")

if __name__ == "__main__":
    debug_dema_supertrend()
