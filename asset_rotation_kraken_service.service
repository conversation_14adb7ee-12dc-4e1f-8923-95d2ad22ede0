[Unit]
Description=Asset Rotation Strategy Background Service - Kraken EUR
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy
ExecStart=/home/<USER>/asset_rotation_screener/venv/bin/python /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py --config config/settings_kraken_eur.yaml --notifications config/notifications_kraken.json
Restart=always
RestartSec=5s
StandardOutput=append:/var/log/asset-rotation-kraken.log
StandardError=append:/var/log/asset-rotation-kraken-error.log

[Install]
WantedBy=multi-user.target
