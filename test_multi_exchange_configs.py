#!/usr/bin/env python3
"""
Test script to verify multi-exchange configurations are working correctly.
"""

import sys
import os

# Add the src directory to the path
sys.path.append('src')

from src.config_manager import load_config, get_exchange_credentials

def test_config(config_path: str, exchange_name: str):
    """Test a specific exchange configuration."""
    print(f"\n{'='*50}")
    print(f"TESTING {exchange_name.upper()} CONFIGURATION")
    print(f"{'='*50}")
    
    try:
        # Load config
        config = load_config(config_path)
        
        # Check basic settings
        exchange = config.get('exchange')
        assets = config.get('assets', [])
        trading = config.get('trading', {})
        
        print(f"✅ Config loaded successfully")
        print(f"   Exchange: {exchange}")
        print(f"   Assets: {len(assets)} assets ({assets[:3]}...)")
        print(f"   Trading mode: {trading.get('mode', 'not set')}")
        print(f"   Trading enabled: {trading.get('enabled', False)}")
        print(f"   Initial capital: {trading.get('initial_capital', 'not set')}")
        
        # Check credentials
        try:
            credentials = get_exchange_credentials(exchange)
            has_key = bool(credentials.get('api_key'))
            has_secret = bool(credentials.get('api_secret'))
            
            print(f"   API Key: {'✅ PRESENT' if has_key else '❌ MISSING'}")
            print(f"   API Secret: {'✅ PRESENT' if has_secret else '❌ MISSING'}")
            
            if has_key and has_secret:
                print(f"✅ {exchange_name} is ready for trading!")
            else:
                print(f"⚠️ {exchange_name} missing credentials")
                
        except Exception as e:
            print(f"❌ Credential check failed: {e}")
            
    except Exception as e:
        print(f"❌ Config loading failed: {e}")

def main():
    """Test all exchange configurations."""
    print("MULTI-EXCHANGE CONFIGURATION TEST")
    print("=" * 60)
    
    # Test configurations
    configs = [
        ('config/settings_kraken_eur.yaml', 'Kraken'),
        ('config/settings_bitvavo_eur.yaml', 'Bitvavo'),
        ('config/settings.yaml', 'Binance')
    ]
    
    for config_path, exchange_name in configs:
        if os.path.exists(config_path):
            test_config(config_path, exchange_name)
        else:
            print(f"\n❌ {exchange_name} config not found: {config_path}")
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    print("If all configs show '✅ ready for trading', you can start the services:")
    print("")
    print("# Terminal 1: Kraken")
    print("python background_service.py --config config/settings_kraken_eur.yaml")
    print("")
    print("# Terminal 2: Bitvavo") 
    print("python background_service.py --config config/settings_bitvavo_eur.yaml")
    print("")
    print("Both will run in paper trading mode initially for safety!")

if __name__ == "__main__":
    main()
