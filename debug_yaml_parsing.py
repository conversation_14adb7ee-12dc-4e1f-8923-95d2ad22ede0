#!/usr/bin/env python3
"""
Debug YAML parsing to see exactly what's being loaded.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import yaml
from src.config_manager import load_config

def debug_yaml_parsing():
    """Debug YAML parsing step by step."""
    print("=" * 60)
    print("YAML PARSING DEBUG")
    print("=" * 60)
    
    # 1. Raw YAML file content
    print("\n1. RAW YAML FILE CONTENT:")
    print("-" * 40)
    
    try:
        with open('config/settings.yaml', 'r') as f:
            raw_content = f.read()
        
        # Show the MTPI section
        lines = raw_content.split('\n')
        mtpi_start = None
        for i, line in enumerate(lines):
            if 'mtpi_indicators:' in line:
                mtpi_start = i
                break
        
        if mtpi_start:
            print("MTPI section in YAML:")
            for i in range(mtpi_start, min(mtpi_start + 20, len(lines))):
                print(f"{i+1:3d}: {lines[i]}")
        
    except Exception as e:
        print(f"Error reading YAML file: {e}")
    
    # 2. Parse with yaml.safe_load
    print("\n2. YAML SAFE_LOAD PARSING:")
    print("-" * 40)
    
    try:
        with open('config/settings.yaml', 'r') as f:
            parsed_yaml = yaml.safe_load(f)
        
        settings = parsed_yaml.get('settings', {})
        mtpi_indicators = settings.get('mtpi_indicators', {})
        enabled_indicators = mtpi_indicators.get('enabled_indicators', [])
        
        print(f"Parsed enabled_indicators: {enabled_indicators}")
        print(f"Type: {type(enabled_indicators)}")
        
        if enabled_indicators:
            print("Individual indicators:")
            for i, indicator in enumerate(enabled_indicators):
                print(f"  {i}: '{indicator}' (type: {type(indicator)})")
        
        # Check median_score config
        median_config = mtpi_indicators.get('median_score', {})
        print(f"Median config: {median_config}")
        
    except Exception as e:
        print(f"Error parsing YAML: {e}")
    
    # 3. Use config_manager
    print("\n3. CONFIG_MANAGER LOADING:")
    print("-" * 40)
    
    try:
        config = load_config()
        settings = config.get('settings', {})
        mtpi_indicators = settings.get('mtpi_indicators', {})
        enabled_indicators = mtpi_indicators.get('enabled_indicators', [])
        
        print(f"Config manager enabled_indicators: {enabled_indicators}")
        print(f"Type: {type(enabled_indicators)}")
        
        if enabled_indicators:
            print("Individual indicators:")
            for i, indicator in enumerate(enabled_indicators):
                print(f"  {i}: '{indicator}' (type: {type(indicator)})")
        
        # Check if median_score is in the list
        is_median_enabled = 'median_score' in enabled_indicators
        print(f"Is 'median_score' in enabled_indicators? {is_median_enabled}")
        
        # Check the full mtpi_indicators structure
        print(f"\nFull mtpi_indicators keys: {list(mtpi_indicators.keys())}")
        
    except Exception as e:
        print(f"Error with config manager: {e}")
    
    # 4. Check for whitespace or encoding issues
    print("\n4. WHITESPACE/ENCODING CHECK:")
    print("-" * 40)
    
    if enabled_indicators:
        for indicator in enabled_indicators:
            print(f"Indicator: '{indicator}'")
            print(f"  Length: {len(indicator)}")
            print(f"  Repr: {repr(indicator)}")
            print(f"  Equals 'median_score': {indicator == 'median_score'}")
            print(f"  Stripped equals 'median_score': {indicator.strip() == 'median_score'}")
            print()

if __name__ == "__main__":
    debug_yaml_parsing()
