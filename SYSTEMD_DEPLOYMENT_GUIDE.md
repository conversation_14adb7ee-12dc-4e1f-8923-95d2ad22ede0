# Systemd Service Deployment Guide

This guide explains how to deploy the Kraken and Bitvavo trading services using systemd on your remote cloud server.

## Prerequisites

1. **Server Setup**: Ensure your remote server has:
   - Python 3.8+ installed
   - Virtual environment with all dependencies
   - Your codebase deployed to `/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/`
   - Environment variables configured (API keys, Telegram tokens)

2. **Configuration Files**: Ensure these files are present:
   - `config/settings_kraken_eur.yaml`
   - `config/settings_bitvavo_eur.yaml`
   - `config/notifications_kraken.json`
   - `config/notifications_bitvavo.json`

## Service Files

You now have three systemd service files:

1. `asset_rotation_service.service` - Original Binance service
2. `asset_rotation_kraken_service.service` - New Kraken service
3. `asset_rotation_bitvavo_service.service` - New Bitvavo service

## Deployment Steps

### 1. Copy Service Files to Server

Copy the service files to your server's systemd directory:

```bash
# Copy service files to systemd directory
sudo cp asset_rotation_kraken_service.service /etc/systemd/system/
sudo cp asset_rotation_bitvavo_service.service /etc/systemd/system/

# Set proper permissions
sudo chmod 644 /etc/systemd/system/asset_rotation_kraken_service.service
sudo chmod 644 /etc/systemd/system/asset_rotation_bitvavo_service.service
```

### 2. Reload Systemd and Enable Services

```bash
# Reload systemd to recognize new services
sudo systemctl daemon-reload

# Enable services to start on boot
sudo systemctl enable asset_rotation_kraken_service.service
sudo systemctl enable asset_rotation_bitvavo_service.service

# Check service status
sudo systemctl status asset_rotation_kraken_service.service
sudo systemctl status asset_rotation_bitvavo_service.service
```

### 3. Start the Services

```bash
# Start Kraken service
sudo systemctl start asset_rotation_kraken_service.service

# Start Bitvavo service
sudo systemctl start asset_rotation_bitvavo_service.service
```

### 4. Monitor Services

```bash
# Check if services are running
sudo systemctl status asset_rotation_kraken_service.service
sudo systemctl status asset_rotation_bitvavo_service.service

# View logs
sudo journalctl -u asset_rotation_kraken_service.service -f
sudo journalctl -u asset_rotation_bitvavo_service.service -f

# View application logs
tail -f /var/log/asset-rotation-kraken.log
tail -f /var/log/asset-rotation-kraken-error.log
tail -f /var/log/asset-rotation-bitvavo.log
tail -f /var/log/asset-rotation-bitvavo-error.log
```

## Service Management Commands

### Start/Stop/Restart Services

```bash
# Kraken service
sudo systemctl start asset_rotation_kraken_service.service
sudo systemctl stop asset_rotation_kraken_service.service
sudo systemctl restart asset_rotation_kraken_service.service

# Bitvavo service
sudo systemctl start asset_rotation_bitvavo_service.service
sudo systemctl stop asset_rotation_bitvavo_service.service
sudo systemctl restart asset_rotation_bitvavo_service.service
```

### Enable/Disable Auto-start

```bash
# Enable auto-start on boot
sudo systemctl enable asset_rotation_kraken_service.service
sudo systemctl enable asset_rotation_bitvavo_service.service

# Disable auto-start
sudo systemctl disable asset_rotation_kraken_service.service
sudo systemctl disable asset_rotation_bitvavo_service.service
```

## Running All Three Services

You can now run all three trading services simultaneously:

1. **Binance Service**: `asset_rotation_service.service`
2. **Kraken Service**: `asset_rotation_kraken_service.service`
3. **Bitvavo Service**: `asset_rotation_bitvavo_service.service`

Each service:
- Uses its own configuration file
- Has separate notification settings (different Telegram bots)
- Writes to separate log files
- Can be managed independently

## Troubleshooting

### Check Service Status
```bash
sudo systemctl status asset_rotation_kraken_service.service
sudo systemctl status asset_rotation_bitvavo_service.service
```

### View Recent Logs
```bash
sudo journalctl -u asset_rotation_kraken_service.service --since "1 hour ago"
sudo journalctl -u asset_rotation_bitvavo_service.service --since "1 hour ago"
```

### Restart Failed Services
```bash
sudo systemctl restart asset_rotation_kraken_service.service
sudo systemctl restart asset_rotation_bitvavo_service.service
```

### Check Configuration
Ensure your configuration files have the correct exchange settings:
- `settings_kraken_eur.yaml`: `exchange: kraken`
- `settings_bitvavo_eur.yaml`: `exchange: bitvavo`

## Log File Locations

- **Kraken Logs**: 
  - `/var/log/asset-rotation-kraken.log`
  - `/var/log/asset-rotation-kraken-error.log`
- **Bitvavo Logs**: 
  - `/var/log/asset-rotation-bitvavo.log`
  - `/var/log/asset-rotation-bitvavo-error.log`
- **Binance Logs**: 
  - `/var/log/asset-rotation.log`
  - `/var/log/asset-rotation-error.log`

## Notes

- Each service runs independently with its own process
- Services will automatically restart if they crash (due to `Restart=always`)
- Services will start automatically on server reboot (if enabled)
- Each service uses separate Telegram notification configurations to avoid conflicts
