import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/',
  server: {
    host: true, // Listen on all addresses, including network
    port: 5174, // Updated to match the port that's actually being used
    open: true, // Automatically open the browser when starting
    cors: true, // Enable CORS for all origins
    proxy: {
      // Proxy API requests to the Python backend
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
  // Add history API fallback for SPA routing
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
})
