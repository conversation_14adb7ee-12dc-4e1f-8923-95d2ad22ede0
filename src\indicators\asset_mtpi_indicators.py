# src/indicators/asset_mtpi_indicators.py
"""
Asset MTPI Indicators Module

This module applies MTPI multi-indicator aggregation logic to pairwise asset ratios
for robust trend detection. It leverages all existing MTPI indicator implementations
and aggregation logic.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Tuple, List, Optional

# Import existing MTPI indicators (reuse everything!)
from .pgo_score import generate_pgo_score_signal
from .bb_score import generate_bb_score_signal
from .dwma_score import generate_dwma_signal
from .median_score import generate_median_score_signal
from .dema_supertrend import generate_dema_supertrend_signal
from .dpsd_score import generate_dpsd_signal
from .aad_score import generate_aad_score_signal
from .dynamic_ema_score import generate_dynamic_ema_score_signal
from .quantile_dema_score import generate_quantile_dema_score_signal

# Import existing aggregation logic (reuse!)
from ..MTPI_signal_handler import combine_multi_signals_with_score


def create_ratio_dataframe(price_a: pd.Series, price_b: pd.Series) -> pd.DataFrame:
    """
    Create a ratio DataFrame with OHLCV structure for indicator calculations.
    
    Args:
        price_a: Price series for asset A (numerator)
        price_b: Price series for asset B (denominator)
        
    Returns:
        DataFrame with ratio OHLCV data
    """
    # Align the series
    common_idx = price_a.index.intersection(price_b.index)
    price_a_aligned = price_a.reindex(common_idx)
    price_b_aligned = price_b.reindex(common_idx)
    
    # Calculate ratio
    ratio = price_a_aligned / price_b_aligned
    
    # Create OHLCV structure (simplified - using ratio as all OHLC)
    ratio_df = pd.DataFrame({
        'open': ratio,
        'high': ratio,  # Simplified - could be enhanced with proper OHLC ratio calculation
        'low': ratio,
        'close': ratio,
        'volume': pd.Series(1.0, index=common_idx)  # Dummy volume
    }, index=common_idx)
    
    return ratio_df


def calculate_mtpi_signal_for_ratio(
    price_a: pd.Series,
    price_b: pd.Series,
    mtpi_config: Dict,
    asset_pair_name: str = "RATIO"
) -> pd.Series:
    """
    Calculate MTPI signal for a pairwise asset ratio using multiple indicators.
    
    Args:
        price_a: Price series for asset A (numerator)
        price_b: Price series for asset B (denominator)  
        mtpi_config: MTPI configuration dictionary
        asset_pair_name: Name for logging (e.g., "ETH/BTC")
        
    Returns:
        Series with MTPI signals (1/-1/0)
    """
    try:
        logging.info(f"Calculating MTPI signal for ratio {asset_pair_name}")
        
        # Create ratio DataFrame
        ratio_df = create_ratio_dataframe(price_a, price_b)
        
        if ratio_df.empty:
            logging.warning(f"Empty ratio DataFrame for {asset_pair_name}")
            return pd.Series(0, index=price_a.index, dtype=int)
        
        # Get configuration
        enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo'])
        combination_method = mtpi_config.get('combination_method', 'consensus')
        long_threshold = mtpi_config.get('long_threshold', 0.1)
        short_threshold = mtpi_config.get('short_threshold', -0.1)

        # Enforce odd number of indicators to avoid ties
        if len(enabled_indicators) % 2 == 0:
            logging.warning(f"Asset MTPI: Even number of indicators ({len(enabled_indicators)}) detected. "
                          f"Removing last indicator to avoid ties: {enabled_indicators}")
            enabled_indicators = enabled_indicators[:-1]  # Remove last indicator
            logging.info(f"Asset MTPI: Using {len(enabled_indicators)} indicators: {enabled_indicators}")
        
        # Calculate signals for each enabled indicator
        signals = {}
        
        # PGO Score
        if 'pgo' in enabled_indicators:
            pgo_config = mtpi_config.get('pgo', {})
            try:
                pgo_signal = generate_pgo_score_signal(
                    df=ratio_df,
                    length=pgo_config.get('length', 35),
                    upper_threshold=pgo_config.get('upper_threshold', 1.35),
                    lower_threshold=pgo_config.get('lower_threshold', -0.58),
                    skip_warmup=False
                )
                signals['pgo'] = pgo_signal
                logging.debug(f"PGO signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating PGO for {asset_pair_name}: {e}")
        
        # Bollinger Bands Score
        if 'bollinger_bands' in enabled_indicators:
            bb_config = mtpi_config.get('bollinger_bands', {})
            try:
                bb_signal = generate_bb_score_signal(
                    df=ratio_df,
                    length=bb_config.get('length', 33),
                    multiplier=bb_config.get('multiplier', 2.0),
                    long_threshold=bb_config.get('long_threshold', 76.0),
                    short_threshold=bb_config.get('short_threshold', 31.0),
                    use_heikin_ashi=bb_config.get('use_heikin_ashi', False),
                    heikin_src=bb_config.get('heikin_src', 'close')
                )
                signals['bollinger_bands'] = bb_signal
                logging.debug(f"BB signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating BB for {asset_pair_name}: {e}")
        
        # DWMA Score
        if 'dwma_score' in enabled_indicators:
            dwma_config = mtpi_config.get('dwma_score', {})
            try:
                dwma_signal = generate_dwma_signal(
                    df=ratio_df,
                    smoothing_style=dwma_config.get('smoothing_style', 'Weighted SD'),
                    src_col=dwma_config.get('src_col', 'close'),
                    length=dwma_config.get('length', 17),
                    ma_type=dwma_config.get('ma_type', 'EMA'),
                    ma_smooth_length=dwma_config.get('ma_smooth_length', 12),
                    sd_length=dwma_config.get('sd_length', 33),
                    upper_sd_weight=dwma_config.get('upper_sd_weight', 1.031),
                    lower_sd_weight=dwma_config.get('lower_sd_weight', 0.996),
                    atr_period=dwma_config.get('atr_period', 12),
                    atr_multiplier=dwma_config.get('atr_multiplier', 1.0),
                    loop_start=dwma_config.get('loop_start', 1),
                    loop_end=dwma_config.get('loop_end', 60),
                    long_threshold=dwma_config.get('long_threshold', 30),
                    short_threshold=dwma_config.get('short_threshold', 0)
                )
                signals['dwma_score'] = dwma_signal
                logging.debug(f"DWMA signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating DWMA for {asset_pair_name}: {e}")
        
        # Median Score
        if 'median_score' in enabled_indicators:
            median_config = mtpi_config.get('median_score', {})
            try:
                median_signal = generate_median_score_signal(
                    df=ratio_df,
                    subject1=median_config.get('subject1', 12),
                    mul1=median_config.get('mul1', 1.45),
                    slen=median_config.get('slen', 27),
                    src_me=median_config.get('src_me', 'high')
                )
                signals['median_score'] = median_signal
                logging.debug(f"Median signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating Median for {asset_pair_name}: {e}")

        # DEMA Super Score
        if 'dema_super_score' in enabled_indicators:
            dema_config = mtpi_config.get('dema_super_score', {})
            try:
                dema_signal = generate_dema_supertrend_signal(
                    df=ratio_df,
                    atr_period=dema_config.get('atr_period', 19),
                    multiplier=dema_config.get('multiplier', 2.8),
                    dema_length=dema_config.get('length', 17),
                    src_col=dema_config.get('src_col', 'close')
                )
                signals['dema_super_score'] = dema_signal
                logging.debug(f"DEMA Super signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating DEMA Super for {asset_pair_name}: {e}")

        # DPSD Score
        if 'dpsd_score' in enabled_indicators:
            dpsd_config = mtpi_config.get('dpsd_score', {})
            try:
                dpsd_signal = generate_dpsd_signal(
                    df=ratio_df,
                    dema_length=dpsd_config.get('dema_length', 9),
                    percentile_length=dpsd_config.get('percentile_length', 58),
                    sd_length=dpsd_config.get('sd_length', 27),
                    ema_length=dpsd_config.get('ema_length', 14),
                    pertype=f"{dpsd_config.get('percentile_upper', 60)}/{dpsd_config.get('percentile_lower', 45)}",
                    dema_src=dpsd_config.get('src_col', 'close')
                )
                signals['dpsd_score'] = dpsd_signal
                logging.debug(f"DPSD signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating DPSD for {asset_pair_name}: {e}")

        # AAD Score
        if 'aad_score' in enabled_indicators:
            aad_config = mtpi_config.get('aad_score', {})
            try:
                aad_signal = generate_aad_score_signal(
                    df=ratio_df,
                    src_col=aad_config.get('src_col', 'close'),
                    length=aad_config.get('length', 22),
                    aad_mult=aad_config.get('aad_mult', 1.2),
                    avg_type=aad_config.get('avg_type', 'SMA')
                )
                signals['aad_score'] = aad_signal
                logging.debug(f"AAD signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating AAD for {asset_pair_name}: {e}")

        # Dynamic EMA Score
        if 'dynamic_ema_score' in enabled_indicators:
            dynamic_config = mtpi_config.get('dynamic_ema_score', {})
            try:
                dynamic_signal = generate_dynamic_ema_score_signal(
                    df=ratio_df,
                    median_length=dynamic_config.get('median_length', 9),
                    median_src=dynamic_config.get('median_src', 'close'),
                    ema_length=dynamic_config.get('ema_length', 12),
                    smoothing_style=dynamic_config.get('smoothing_style', 'Weighted SD'),
                    sd_length=dynamic_config.get('sd_length', 33),
                    upper_sd_weight=dynamic_config.get('upper_sd_weight', 1.017),
                    lower_sd_weight=dynamic_config.get('lower_sd_weight', 0.996),
                    atr_period=dynamic_config.get('atr_period', 14),
                    atr_multiplier=dynamic_config.get('atr_multiplier', 1.2)
                )
                signals['dynamic_ema_score'] = dynamic_signal
                logging.debug(f"Dynamic EMA signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating Dynamic EMA for {asset_pair_name}: {e}")

        # Quantile DEMA Score
        if 'quantile_dema_score' in enabled_indicators:
            quantile_config = mtpi_config.get('quantile_dema_score', {})
            try:
                quantile_signal = generate_quantile_dema_score_signal(
                    df=ratio_df,
                    dema_length=quantile_config.get('dema_length', 30),
                    percentile_filter=quantile_config.get('percentile_filter', 10),
                    percentile_length=quantile_config.get('percentile_length', 20),
                    atr_length=quantile_config.get('atr_length', 14),
                    mult_up=quantile_config.get('mult_up', 1.2),
                    mult_dn=quantile_config.get('mult_dn', 1.2),
                    sd_length=quantile_config.get('sd_length', 30),
                    dema_st_length=quantile_config.get('dema_st_length', 30),
                    src_col=quantile_config.get('src_col', 'close')
                )
                signals['quantile_dema_score'] = quantile_signal
                logging.debug(f"Quantile DEMA signal calculated for {asset_pair_name}")
            except Exception as e:
                logging.error(f"Error calculating Quantile DEMA for {asset_pair_name}: {e}")
        
        if not signals:
            logging.warning(f"No valid signals generated for {asset_pair_name}")
            return pd.Series(0, index=ratio_df.index, dtype=int)
        
        # Combine signals using existing MTPI aggregation logic
        if len(signals) == 1:
            # Single indicator - return its signal
            combined_signal_series = list(signals.values())[0]
        else:
            # Multiple indicators - need to aggregate point by point
            combined_signal_series = pd.Series(0, index=ratio_df.index, dtype=int)

            for timestamp in ratio_df.index:
                # Get signals for this timestamp
                timestamp_signals = {}
                for indicator, signal_series in signals.items():
                    if timestamp in signal_series.index:
                        timestamp_signals[indicator] = int(signal_series.loc[timestamp])

                if timestamp_signals:
                    # Use existing aggregation logic
                    combined_signal, _ = combine_multi_signals_with_score(
                        timestamp_signals,
                        combination_method,
                        long_threshold,
                        short_threshold
                    )
                    combined_signal_series.loc[timestamp] = combined_signal

        # Convert MTPI signals (-1/0/1) to PGO-style signals (0/1) for consistency with existing scoring
        # This ensures the comparison matrix works the same way as PGO and RSI methods
        pgo_style_signals = pd.Series(0, index=combined_signal_series.index, dtype=int)
        pgo_style_signals[combined_signal_series == 1] = 1  # Long signals become 1
        pgo_style_signals[combined_signal_series <= 0] = 0  # Short/neutral signals become 0

        logging.info(f"MTPI signal calculated for {asset_pair_name}: {len(pgo_style_signals)} values")
        logging.info(f"Signal distribution: {pgo_style_signals.value_counts().to_dict()}")
        return pgo_style_signals
        
    except Exception as e:
        logging.error(f"Error calculating MTPI signal for {asset_pair_name}: {e}")
        return pd.Series(0, index=price_a.index, dtype=int)


def calculate_daily_mtpi_scores(
    data_dict: Dict[str, pd.DataFrame],
    mtpi_config: Dict,
    close_col: str = 'close',
    debug: bool = True
) -> Tuple[pd.DataFrame, Dict]:
    """
    Calculate daily scores for each asset based on pairwise ratio MTPI comparisons.
    
    This function applies MTPI multi-indicator logic to each pairwise ratio,
    then uses the same comparison matrix approach as the existing PGO method.
    
    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with price data
        mtpi_config: MTPI configuration for asset trend detection
        close_col: The column name for the close price
        debug: Whether to collect detailed debug information
        
    Returns:
        Tuple of (daily_scores_df, debug_data)
    """
    try:
        logging.info("Calculating daily MTPI scores using multi-indicator approach...")
        
        if not data_dict:
            logging.warning("Empty data_dict provided")
            return pd.DataFrame(), {}
        
        # Get asset list and common index
        assets = list(data_dict.keys())
        logging.info(f"Assets for MTPI scoring: {assets}")
        
        # Find common time index across all assets
        common_idx = None
        for symbol, df in data_dict.items():
            if common_idx is None:
                common_idx = df.index
            else:
                common_idx = common_idx.intersection(df.index)
        
        if common_idx.empty:
            logging.warning("No common time index found across assets")
            return pd.DataFrame(), {}
        
        logging.info(f"Common time index: {len(common_idx)} timestamps")
        
        # Initialize daily scores DataFrame
        daily_scores = pd.DataFrame(0.0, index=common_idx, columns=assets)
        
        # Calculate MTPI signals for all pairwise ratios
        signals_dict = {}
        
        for i, asset_a in enumerate(assets):
            for j, asset_b in enumerate(assets):
                if i != j:  # Skip self-comparison
                    pair_key = (asset_a, asset_b)
                    asset_pair_name = f"{asset_a}/{asset_b}"
                    
                    logging.info(f"Calculating MTPI signal for {asset_pair_name}")
                    
                    # Get price series
                    price_a = data_dict[asset_a][close_col]
                    price_b = data_dict[asset_b][close_col]
                    
                    # Calculate MTPI signal for this ratio
                    mtpi_signal = calculate_mtpi_signal_for_ratio(
                        price_a, price_b, mtpi_config, asset_pair_name
                    )
                    
                    signals_dict[pair_key] = mtpi_signal
        
        logging.info(f"Calculated MTPI signals for {len(signals_dict)} asset pairs")
        
        # Create daily scores from MTPI signals (same logic as PGO method)
        for day in common_idx:
            # Initialize comparison matrix for this day
            day_matrix = pd.DataFrame(0.0, index=assets, columns=assets)
            np.fill_diagonal(day_matrix.values, np.nan)  # NaN on diagonal
            
            # Fill the matrix with MTPI signals for this day
            for (asset_a, asset_b), signal_series in signals_dict.items():
                if asset_a in assets and asset_b in assets and day in signal_series.index:
                    day_matrix.loc[asset_a, asset_b] = signal_series.loc[day]
            
            # Calculate scores by summing each row
            scores = day_matrix.sum(axis=1, skipna=True)
            
            # Update the scores DataFrame
            for asset in assets:
                daily_scores.loc[day, asset] = scores[asset]
        
        logging.info(f"Daily MTPI scores calculated: {daily_scores.shape}")
        
        # Prepare debug data if requested
        debug_data = {}
        if debug:
            logging.info("Collecting debug data for MTPI scoring...")
            for day in common_idx[-5:]:  # Last 5 days for debug
                existing_assets = [asset for asset in assets if day in daily_scores.index]
                
                if existing_assets:
                    # Initialize matrix for this day
                    matrix = pd.DataFrame(0.0, index=existing_assets, columns=existing_assets)
                    np.fill_diagonal(matrix.values, np.nan)
                    
                    # Fill the matrix with signals for this day
                    for (asset_a, asset_b), signal_series in signals_dict.items():
                        if asset_a in existing_assets and asset_b in existing_assets and day in signal_series.index:
                            matrix.loc[asset_a, asset_b] = signal_series.loc[day]
                    
                    # Get scores for this day
                    scores = {}
                    for asset in existing_assets:
                        if day in daily_scores.index and asset in daily_scores.columns:
                            scores[asset] = daily_scores.loc[day, asset]
                    
                    # Store debug data
                    debug_data[day] = {
                        'matrix': matrix,
                        'scores': scores,
                        'assets': existing_assets
                    }
        
        return daily_scores, debug_data
        
    except Exception as e:
        logging.error(f"Error in calculate_daily_mtpi_scores: {e}")
        return pd.DataFrame(), {}
