import React from 'react';
import {
  Box,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Divider,
  Flex,
  Spinner,
  useColorModeValue,
  Progress,
} from '@chakra-ui/react';

const PortfolioSummary = ({ tradingStatus, positions, isLoading }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  
  // Calculate total portfolio value
  const calculateTotalValue = () => {
    if (!tradingStatus || !positions) return 0;
    
    // Get base currency balance (e.g., USDT)
    const baseCurrencyBalance = tradingStatus.balances?.USDT || 0;
    
    // Add value of all positions
    const positionsValue = positions?.reduce((total, position) => {
      return total + (position.current_value || 0);
    }, 0) || 0;
    
    return baseCurrencyBalance + positionsValue;
  };
  
  // Calculate daily change
  const calculateDailyChange = () => {
    if (!tradingStatus) return { value: 0, percentage: 0 };
    
    const dailyPnl = tradingStatus.daily_pnl || 0;
    const startValue = calculateTotalValue() - dailyPnl;
    const percentage = startValue > 0 ? (dailyPnl / startValue) * 100 : 0;
    
    return {
      value: dailyPnl,
      percentage: percentage,
    };
  };
  
  // Calculate allocation percentages
  const calculateAllocation = () => {
    if (!tradingStatus || !positions) return [];
    
    const totalValue = calculateTotalValue();
    if (totalValue === 0) return [];
    
    // Create allocation for base currency (e.g., USDT)
    const baseCurrency = {
      symbol: 'USDT',
      value: tradingStatus.balances?.USDT || 0,
      percentage: ((tradingStatus.balances?.USDT || 0) / totalValue) * 100,
    };
    
    // Create allocations for positions
    const positionAllocations = positions.map(position => ({
      symbol: position.symbol.split('/')[0], // Extract base currency from symbol (e.g., BTC from BTC/USDT)
      value: position.current_value || 0,
      percentage: ((position.current_value || 0) / totalValue) * 100,
    }));
    
    return [baseCurrency, ...positionAllocations].filter(item => item.value > 0);
  };
  
  const totalValue = calculateTotalValue();
  const dailyChange = calculateDailyChange();
  const allocations = calculateAllocation();
  
  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  if (isLoading) {
    return (
      <Box 
        bg={bgColor} 
        p={4} 
        borderRadius="lg" 
        boxShadow="sm"
        height="100%"
        display="flex"
        justifyContent="center"
        alignItems="center"
      >
        <Spinner size="xl" />
      </Box>
    );
  }
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
    >
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Portfolio Summary
      </Text>
      
      <StatGroup mb={4}>
        <Stat>
          <StatLabel>Total Value</StatLabel>
          <StatNumber>{formatCurrency(totalValue)}</StatNumber>
          <StatHelpText>
            {dailyChange.value !== 0 && (
              <>
                <StatArrow type={dailyChange.value >= 0 ? 'increase' : 'decrease'} />
                {dailyChange.percentage.toFixed(2)}% today
              </>
            )}
          </StatHelpText>
        </Stat>
        
        <Stat>
          <StatLabel>Available Cash</StatLabel>
          <StatNumber>{formatCurrency(tradingStatus?.balances?.USDT || 0)}</StatNumber>
          <StatHelpText>
            {totalValue > 0 && `${((tradingStatus?.balances?.USDT || 0) / totalValue * 100).toFixed(1)}% of portfolio`}
          </StatHelpText>
        </Stat>
      </StatGroup>
      
      <Divider my={4} />
      
      <Text fontWeight="medium" mb={2}>
        Asset Allocation
      </Text>
      
      {allocations.length > 0 ? (
        <Box>
          {allocations.map((allocation, index) => (
            <Box key={allocation.symbol} mb={index < allocations.length - 1 ? 3 : 0}>
              <Flex justify="space-between" mb={1}>
                <Text fontSize="sm">{allocation.symbol}</Text>
                <Text fontSize="sm" fontWeight="medium">
                  {formatCurrency(allocation.value)} ({allocation.percentage.toFixed(1)}%)
                </Text>
              </Flex>
              <Progress 
                value={allocation.percentage} 
                size="sm" 
                colorScheme={allocation.symbol === 'USDT' ? 'gray' : 'blue'} 
                borderRadius="full"
              />
            </Box>
          ))}
        </Box>
      ) : (
        <Text fontSize="sm" color="gray.500">
          No assets in portfolio
        </Text>
      )}
    </Box>
  );
};

export default PortfolioSummary;
