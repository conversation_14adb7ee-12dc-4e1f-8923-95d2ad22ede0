#!/usr/bin/env python3
"""
Step-by-step debugging of 12h execution logic.
This script traces through the exact same logic as main_program.py
but with extensive debugging output to verify each step.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

def setup_logging():
    """Setup detailed logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def fetch_and_debug_12h_data(symbol='BTC/USDT', start_date='2024-06-01'):
    """Fetch 12h data with debugging."""
    logging.info(f"\n🔍 STEP 1: FETCHING 12H DATA FOR {symbol}")
    
    try:
        from src.data_fetcher import fetch_ohlcv_data
        
        # Calculate start date with warmup (same as main_program.py)
        analysis_start = pd.Timestamp(start_date, tz='UTC')
        warmup_days = 60
        effective_start_date = analysis_start - pd.Timedelta(days=warmup_days)
        start_date_str = effective_start_date.strftime('%Y-%m-%d')
        
        logging.info(f"Analysis start: {analysis_start}")
        logging.info(f"Effective start (with warmup): {effective_start_date}")
        
        # Fetch 12h candles (same parameters as main_program.py)
        twelve_hour_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='12h',
            since=start_date_str,
            use_cache=True  # Use cache for consistency
        )
        
        twelve_hour_data = twelve_hour_data_dict.get(symbol, pd.DataFrame())
        
        if twelve_hour_data.empty:
            logging.error("No 12h data retrieved!")
            return None
            
        logging.info(f"✅ Retrieved {len(twelve_hour_data)} 12h candles")
        logging.info(f"Date range: {twelve_hour_data.index.min()} to {twelve_hour_data.index.max()}")
        
        return twelve_hour_data
        
    except Exception as e:
        logging.error(f"❌ Failed to fetch 12h data: {e}")
        return None

def debug_midnight_candle_filtering(twelve_hour_data):
    """Debug the midnight candle filtering logic."""
    logging.info(f"\n🔍 STEP 2: FILTERING MIDNIGHT CANDLES")
    
    # Show hour distribution
    hours = twelve_hour_data.index.hour
    hour_counts = pd.Series(hours).value_counts().sort_index()
    logging.info("Hour distribution:")
    for hour, count in hour_counts.items():
        logging.info(f"  {hour:02d}:00 UTC: {count} candles")
    
    # Filter midnight candles (same logic as main_program.py)
    midnight_candles = twelve_hour_data[twelve_hour_data.index.hour == 0]
    
    logging.info(f"✅ Filtered to {len(midnight_candles)} midnight candles (hour=0)")
    
    # Show first few midnight candles
    logging.info("First 5 midnight candles:")
    for i in range(min(5, len(midnight_candles))):
        candle = midnight_candles.iloc[i]
        date = midnight_candles.index[i]
        logging.info(f"  {date}: Open=${candle['open']:.2f}, Close=${candle['close']:.2f}")
    
    return midnight_candles

def debug_daily_data_and_returns(symbol='BTC/USDT', start_date='2024-06-01'):
    """Debug daily data loading and return calculation."""
    logging.info(f"\n🔍 STEP 3: LOADING DAILY DATA AND CALCULATING RETURNS")
    
    try:
        from src.data_fetcher import fetch_ohlcv_data
        
        # Fetch daily data (same as main_program.py)
        analysis_start = pd.Timestamp(start_date, tz='UTC')
        warmup_days = 60
        effective_start_date = analysis_start - pd.Timedelta(days=warmup_days)
        start_date_str = effective_start_date.strftime('%Y-%m-%d')
        
        daily_data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[symbol],
            timeframe='1d',
            since=start_date_str,
            use_cache=True
        )
        
        daily_data = daily_data_dict.get(symbol, pd.DataFrame())
        
        if daily_data.empty:
            logging.error("No daily data retrieved!")
            return None
            
        logging.info(f"✅ Retrieved {len(daily_data)} daily candles")
        logging.info(f"Date range: {daily_data.index.min()} to {daily_data.index.max()}")
        
        return daily_data
        
    except Exception as e:
        logging.error(f"❌ Failed to fetch daily data: {e}")
        return None

def step_by_step_return_calculation(daily_data, midnight_candles, num_days=10):
    """Step-by-step return calculation with detailed debugging."""
    logging.info(f"\n🔍 STEP 4: STEP-BY-STEP RETURN CALCULATION")
    
    # Focus on a specific period for detailed analysis
    start_idx = max(0, len(daily_data) - num_days - 5)  # Get recent data
    sample_daily = daily_data.iloc[start_idx:start_idx + num_days]
    
    logging.info(f"Analyzing {len(sample_daily)} days starting from {sample_daily.index[0].date()}")
    
    returns_auto = []
    returns_manual = []
    
    for i in range(1, len(sample_daily)):
        current_date = sample_daily.index[i]
        prev_date = sample_daily.index[i-1]
        
        # Previous day's close (signal price)
        prev_close = sample_daily.iloc[i-1]['close']
        
        # Current day's close (automatic execution)
        current_close = sample_daily.iloc[i]['close']
        
        # Calculate automatic return
        auto_return = (current_close - prev_close) / prev_close
        returns_auto.append(auto_return)
        
        # Find midnight candle for manual execution
        midnight_date = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        logging.info(f"\n📅 {current_date.strftime('%Y-%m-%d')}:")
        logging.info(f"  Signal price (prev close): ${prev_close:.4f}")
        logging.info(f"  Looking for midnight candle: {midnight_date}")
        
        if midnight_date in midnight_candles.index:
            twelve_pm_close = midnight_candles.loc[midnight_date, 'close']
            manual_return = (twelve_pm_close - prev_close) / prev_close
            returns_manual.append(manual_return)
            
            logging.info(f"  ✅ Found midnight candle")
            logging.info(f"  Auto execution (00:00): ${current_close:.4f} → {auto_return:.4f} ({auto_return*100:.2f}%)")
            logging.info(f"  Manual execution (12:00): ${twelve_pm_close:.4f} → {manual_return:.4f} ({manual_return*100:.2f}%)")
            logging.info(f"  Difference: {(manual_return - auto_return)*100:.2f}%")
            
            # Additional validation
            price_diff = abs(twelve_pm_close - current_close)
            logging.info(f"  Price difference (12PM vs 00PM): ${price_diff:.2f}")
            
        else:
            logging.warning(f"  ❌ No midnight candle found for {midnight_date}")
            returns_manual.append(0.0)
    
    # Summary statistics
    logging.info(f"\n📊 SUMMARY STATISTICS:")
    logging.info(f"Average auto return: {np.mean(returns_auto)*100:.2f}%")
    logging.info(f"Average manual return: {np.mean(returns_manual)*100:.2f}%")
    logging.info(f"Average difference: {(np.mean(returns_manual) - np.mean(returns_auto))*100:.2f}%")
    
    return returns_auto, returns_manual

def compare_with_main_program_method(daily_data, symbol='BTC/USDT'):
    """Compare our calculation with main_program.py method."""
    logging.info(f"\n🔍 STEP 5: COMPARING WITH MAIN_PROGRAM.PY METHOD")
    
    try:
        # Import and use the exact same method
        from main_program import AllocationTester
        
        # Create minimal tester
        tester = AllocationTester(
            timeframe='1d',
            analysis_start_date='2024-06-01',
            selected_assets=[symbol],
            n_assets=1,
            use_cache=True,
            execution_timing='manual_12pm'
        )
        
        # Calculate returns using main_program method
        returns_main = tester._calculate_manual_execution_returns(symbol, daily_data)
        
        logging.info(f"✅ Main program method returned {len(returns_main)} values")
        
        # Show last 10 returns
        logging.info("Last 10 returns from main_program method:")
        for i in range(max(0, len(returns_main)-10), len(returns_main)):
            if i > 0:  # Skip first value (always 0)
                date = returns_main.index[i]
                return_val = returns_main.iloc[i]
                logging.info(f"  {date.strftime('%Y-%m-%d')}: {return_val:.4f} ({return_val*100:.2f}%)")
        
        return returns_main
        
    except Exception as e:
        logging.error(f"❌ Failed to compare with main_program method: {e}")
        return None

def validate_data_integrity(twelve_hour_data, daily_data):
    """Validate data integrity and consistency."""
    logging.info(f"\n🔍 STEP 6: DATA INTEGRITY VALIDATION")
    
    # Check for missing data
    twelve_h_dates = set(twelve_hour_data.index.date)
    daily_dates = set(daily_data.index.date)
    
    missing_in_12h = daily_dates - twelve_h_dates
    missing_in_daily = twelve_h_dates - daily_dates
    
    if missing_in_12h:
        logging.warning(f"Dates in daily but not in 12h: {len(missing_in_12h)} dates")
        for date in sorted(list(missing_in_12h))[:5]:
            logging.warning(f"  {date}")
    
    if missing_in_daily:
        logging.warning(f"Dates in 12h but not in daily: {len(missing_in_daily)} dates")
        for date in sorted(list(missing_in_daily))[:5]:
            logging.warning(f"  {date}")
    
    # Check price ranges
    logging.info(f"Daily price range: ${daily_data['close'].min():.2f} - ${daily_data['close'].max():.2f}")
    logging.info(f"12h price range: ${twelve_hour_data['close'].min():.2f} - ${twelve_hour_data['close'].max():.2f}")
    
    # Check for obvious data errors
    daily_outliers = daily_data[(daily_data['close'] < 1000) | (daily_data['close'] > 200000)]
    twelve_h_outliers = twelve_hour_data[(twelve_hour_data['close'] < 1000) | (twelve_hour_data['close'] > 200000)]
    
    if len(daily_outliers) > 0:
        logging.warning(f"Found {len(daily_outliers)} potential outliers in daily data")
    
    if len(twelve_h_outliers) > 0:
        logging.warning(f"Found {len(twelve_h_outliers)} potential outliers in 12h data")

def main():
    """Run complete step-by-step debugging."""
    setup_logging()
    
    logging.info("🚀 STEP-BY-STEP DEBUGGING OF 12H EXECUTION LOGIC")
    logging.info("=" * 70)
    
    symbol = 'BTC/USDT'
    start_date = '2024-06-01'
    
    # Step 1: Fetch 12h data
    twelve_hour_data = fetch_and_debug_12h_data(symbol, start_date)
    if twelve_hour_data is None:
        return
    
    # Step 2: Filter midnight candles
    midnight_candles = debug_midnight_candle_filtering(twelve_hour_data)
    
    # Step 3: Fetch daily data
    daily_data = debug_daily_data_and_returns(symbol, start_date)
    if daily_data is None:
        return
    
    # Step 4: Step-by-step calculation
    returns_auto, returns_manual = step_by_step_return_calculation(daily_data, midnight_candles)
    
    # Step 5: Compare with main program
    returns_main = compare_with_main_program_method(daily_data, symbol)
    
    # Step 6: Data integrity validation
    validate_data_integrity(twelve_hour_data, daily_data)
    
    logging.info("\n" + "=" * 70)
    logging.info("🎯 DEBUGGING COMPLETE")
    logging.info("Review the detailed output above to verify the logic")

if __name__ == "__main__":
    main()
