import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  VStack,
  HS<PERSON>ck,
  Badge,
  useColorModeValue,
  Flex,
  Icon,
} from '@chakra-ui/react';
import { InfoIcon, WarningIcon, CheckCircleIcon, TimeIcon } from '@chakra-ui/icons';

const ActivityLog = ({ tradingStatus, trades }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const [activities, setActivities] = useState([]);
  
  // Generate activity log from trading status and trades
  useEffect(() => {
    const newActivities = [];
    
    // Add system status activities
    if (tradingStatus) {
      // Add trading status activity
      if (tradingStatus.enabled) {
        newActivities.push({
          type: 'system',
          message: tradingStatus.running 
            ? 'Trading system is running' 
            : 'Trading system is enabled but paused',
          timestamp: tradingStatus.last_update_time || new Date().toISOString(),
          status: tradingStatus.running ? 'success' : 'warning',
        });
      } else {
        newActivities.push({
          type: 'system',
          message: 'Trading system is disabled',
          timestamp: tradingStatus.last_update_time || new Date().toISOString(),
          status: 'error',
        });
      }
      
      // Add last execution activity if available
      if (tradingStatus.last_execution_time) {
        newActivities.push({
          type: 'execution',
          message: `Last strategy execution completed`,
          timestamp: tradingStatus.last_execution_time,
          status: 'info',
          details: tradingStatus.last_execution_result 
            ? `Result: ${tradingStatus.last_execution_result.success ? 'Success' : 'Failed'}`
            : undefined,
        });
      }
      
      // Add best asset activity if available
      if (tradingStatus.best_asset) {
        newActivities.push({
          type: 'signal',
          message: `Current best asset: ${tradingStatus.best_asset}`,
          timestamp: tradingStatus.last_update_time || new Date().toISOString(),
          status: 'info',
        });
      }
      
      // Add MTPI signal activity if available
      if (tradingStatus.mtpi_signal !== undefined) {
        newActivities.push({
          type: 'signal',
          message: `MTPI signal: ${tradingStatus.mtpi_signal > 0 ? 'Bullish' : 'Bearish'}`,
          timestamp: tradingStatus.last_update_time || new Date().toISOString(),
          status: tradingStatus.mtpi_signal > 0 ? 'success' : 'warning',
        });
      }
    }
    
    // Add trade activities
    if (trades && trades.length > 0) {
      // Only take the 10 most recent trades
      const recentTrades = trades.slice(0, 10);
      
      recentTrades.forEach(trade => {
        newActivities.push({
          type: 'trade',
          message: `${trade.side.toUpperCase()} ${trade.amount.toFixed(6)} ${trade.symbol.split('/')[0]} @ ${trade.price.toFixed(2)}`,
          timestamp: trade.datetime,
          status: trade.side.toLowerCase() === 'buy' ? 'success' : 'info',
          details: `Cost: $${trade.cost.toFixed(2)}, Fee: $${trade.fee ? trade.fee.cost.toFixed(2) : '0.00'}`,
        });
      });
    }
    
    // Sort activities by timestamp (newest first)
    newActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    setActivities(newActivities);
  }, [tradingStatus, trades]);
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get icon based on status
  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return CheckCircleIcon;
      case 'warning':
        return WarningIcon;
      case 'error':
        return WarningIcon;
      case 'info':
      default:
        return InfoIcon;
    }
  };
  
  // Get color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      case 'info':
      default:
        return 'blue';
    }
  };
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
      maxHeight="400px"
      overflowY="auto"
    >
      {activities.length > 0 ? (
        <VStack spacing={3} align="stretch">
          {activities.map((activity, index) => (
            <Box 
              key={`${activity.timestamp}-${index}`}
              p={3}
              borderRadius="md"
              borderLeft="4px solid"
              borderColor={`${getStatusColor(activity.status)}.500`}
              bg={`${getStatusColor(activity.status)}.50`}
            >
              <HStack spacing={3} align="flex-start">
                <Icon 
                  as={getStatusIcon(activity.status)} 
                  color={`${getStatusColor(activity.status)}.500`}
                  boxSize={5}
                  mt={0.5}
                />
                <Box flex="1">
                  <Flex justify="space-between" align="center" mb={1}>
                    <Text fontWeight="medium">{activity.message}</Text>
                    <Badge 
                      size="sm" 
                      colorScheme={activity.type === 'trade' ? 'purple' : 'gray'}
                    >
                      {activity.type}
                    </Badge>
                  </Flex>
                  {activity.details && (
                    <Text fontSize="sm" color="gray.600" mb={1}>
                      {activity.details}
                    </Text>
                  )}
                  <HStack spacing={1} fontSize="xs" color="gray.500">
                    <TimeIcon boxSize={3} />
                    <Text>{formatDate(activity.timestamp)}</Text>
                  </HStack>
                </Box>
              </HStack>
            </Box>
          ))}
        </VStack>
      ) : (
        <Flex 
          justify="center" 
          align="center" 
          height="200px"
          border="1px dashed"
          borderColor="gray.200"
          borderRadius="md"
        >
          <Text color="gray.500">No activity recorded yet</Text>
        </Flex>
      )}
    </Box>
  );
};

export default ActivityLog;
