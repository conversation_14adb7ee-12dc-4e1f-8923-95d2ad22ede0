#!/usr/bin/env python3
"""
Test script to demonstrate critical portfolio rebalancing edge cases.
"""

def test_swap_proceeds_logic_flaw():
    """
    Test the swap proceeds logic flaw with equal weight assets.
    """
    print("=" * 70)
    print("TESTING SWAP PROCEEDS LOGIC FLAW")
    print("=" * 70)
    
    # Scenario: Equal weight rebalancing
    print("\nSCENARIO: Equal weight portfolio rebalancing")
    print("Current: BTC(50%), ETH(50%)")
    print("Target:  SOL(50%), AAVE(50%)")
    
    # Simulate current logic
    assets_to_sell = ['BTC/USDC', 'ETH/USDC']
    assets_to_buy = ['SOL/USDC', 'AAVE/USDC']
    assets_with_weights = {'SOL/USDC': 0.5, 'AAVE/USDC': 0.5}
    current_portfolio = {'BTC/USDC': 0.5, 'ETH/USDC': 0.5}
    
    # Simulate proceeds from sales
    sale_proceeds = {
        'BTC/USDC': 5000.0,  # $5000 from BTC sale
        'ETH/USDC': 5000.0   # $5000 from ETH sale
    }
    
    print(f"\nSale Proceeds:")
    for asset, proceeds in sale_proceeds.items():
        print(f"  {asset}: ${proceeds:,.2f}")
    
    # Simulate current flawed swap logic
    asset_swaps = {}
    swap_proceeds = {}
    
    print(f"\nCURRENT FLAWED LOGIC:")
    for sold_asset, proceeds in sale_proceeds.items():
        original_weight = current_portfolio.get(sold_asset, 0.0)
        print(f"  Looking for replacement for {sold_asset} (weight: {original_weight:.1%})")
        
        # Find replacement with same weight
        for new_asset in assets_to_buy:
            new_weight = assets_with_weights.get(new_asset, 0.0)
            if abs(new_weight - original_weight) < 0.001:  # Same weight = replacement
                asset_swaps[sold_asset] = new_asset
                swap_proceeds[new_asset] = proceeds
                print(f"    → Assigned ${proceeds:,.2f} to {new_asset}")
                break
    
    print(f"\nRESULT:")
    print(f"  Swap proceeds assigned: {swap_proceeds}")
    
    total_assigned = sum(swap_proceeds.values())
    total_available = sum(sale_proceeds.values())
    unassigned = total_available - total_assigned
    
    print(f"  Total proceeds available: ${total_available:,.2f}")
    print(f"  Total proceeds assigned: ${total_assigned:,.2f}")
    print(f"  Unassigned proceeds: ${unassigned:,.2f}")
    
    if unassigned > 0:
        print(f"  ❌ PROBLEM: ${unassigned:,.2f} in proceeds lost!")
    
    print(f"\nIMPACT:")
    print(f"  - SOL gets full ${swap_proceeds.get('SOL/USDC', 0):,.2f} allocation")
    print(f"  - AAVE gets ${swap_proceeds.get('AAVE/USDC', 0):,.2f} (should get $5000)")
    print(f"  - System will try to buy AAVE with insufficient balance")

def test_portfolio_value_inconsistency():
    """
    Test portfolio value calculation inconsistency.
    """
    print("\n" + "=" * 70)
    print("TESTING PORTFOLIO VALUE CALCULATION INCONSISTENCY")
    print("=" * 70)
    
    print("\nSCENARIO: Portfolio value changes during execution")
    
    # Simulate portfolio positions
    positions = {
        'BTC/USDC': {'amount': 1.0, 'price_start': 50000, 'price_end': 51000},
        'ETH/USDC': {'amount': 10.0, 'price_start': 3000, 'price_end': 2950}
    }
    
    available_balance = 1000.0
    
    print(f"Available balance: ${available_balance:,.2f}")
    print(f"Positions at start:")
    
    total_value_start = available_balance
    for asset, pos in positions.items():
        value = pos['amount'] * pos['price_start']
        total_value_start += value
        print(f"  {asset}: {pos['amount']} units @ ${pos['price_start']:,} = ${value:,.2f}")
    
    print(f"Total portfolio value at start: ${total_value_start:,.2f}")
    
    print(f"\nPositions after price movement:")
    
    total_value_end = available_balance
    for asset, pos in positions.items():
        value = pos['amount'] * pos['price_end']
        total_value_end += value
        change = pos['price_end'] - pos['price_start']
        print(f"  {asset}: {pos['amount']} units @ ${pos['price_end']:,} = ${value:,.2f} ({change:+,})")
    
    print(f"Total portfolio value after price changes: ${total_value_end:,.2f}")
    
    value_change = total_value_end - total_value_start
    print(f"Portfolio value change: ${value_change:+,.2f}")
    
    print(f"\nPROBLEM:")
    print(f"  - Current logic recalculates portfolio value for each asset")
    print(f"  - If prices change during execution, calculations become inconsistent")
    print(f"  - Target allocations based on stale portfolio values")
    print(f"  - Can lead to over/under allocation")

def test_precision_edge_case():
    """
    Test precision-related edge cases.
    """
    print("\n" + "=" * 70)
    print("TESTING PRECISION EDGE CASES")
    print("=" * 70)
    
    print("\nSCENARIO: Small portfolio with minimum order sizes")
    
    portfolio_value = 100.0  # Small $100 portfolio
    target_weights = {'BTC/USDC': 0.6, 'ETH/USDC': 0.4}
    prices = {'BTC/USDC': 50000, 'ETH/USDC': 3000}
    min_order_sizes = {'BTC/USDC': 0.001, 'ETH/USDC': 0.01}  # Minimum order sizes
    
    print(f"Portfolio value: ${portfolio_value:.2f}")
    print(f"Target allocation:")
    
    for asset, weight in target_weights.items():
        target_value = portfolio_value * weight
        target_quantity = target_value / prices[asset]
        min_quantity = min_order_sizes[asset]
        min_value = min_quantity * prices[asset]
        
        print(f"  {asset}:")
        print(f"    Target: {weight:.1%} = ${target_value:.2f}")
        print(f"    Quantity needed: {target_quantity:.6f}")
        print(f"    Minimum quantity: {min_quantity:.6f}")
        print(f"    Minimum value: ${min_value:.2f}")
        
        if target_quantity < min_quantity:
            print(f"    ❌ PROBLEM: Cannot buy - below minimum order size!")
        elif min_value > target_value * 1.1:  # 10% tolerance
            print(f"    ⚠️  WARNING: Minimum order significantly exceeds target")

def test_partial_failure_scenario():
    """
    Test partial failure recovery.
    """
    print("\n" + "=" * 70)
    print("TESTING PARTIAL FAILURE RECOVERY")
    print("=" * 70)
    
    print("\nSCENARIO: Some trades succeed, others fail")
    print("Current: BTC(60%), ETH(40%)")
    print("Target:  SOL(50%), AAVE(30%), TRX(20%)")
    
    # Simulate trade results
    trades = [
        {'asset': 'BTC/USDC', 'action': 'sell', 'success': True, 'proceeds': 6000},
        {'asset': 'ETH/USDC', 'action': 'sell', 'success': True, 'proceeds': 4000},
        {'asset': 'SOL/USDC', 'action': 'buy', 'success': True, 'cost': 5000},
        {'asset': 'AAVE/USDC', 'action': 'buy', 'success': False, 'reason': 'Price feed error'},
        {'asset': 'TRX/USDC', 'action': 'buy', 'success': False, 'reason': 'Insufficient balance'}
    ]
    
    print(f"\nTrade Results:")
    successful_trades = []
    failed_trades = []
    
    for trade in trades:
        status = "✅" if trade['success'] else "❌"
        print(f"  {status} {trade['action'].upper()} {trade['asset']}")
        if trade['success']:
            successful_trades.append(trade)
            if trade['action'] == 'sell':
                print(f"      Proceeds: ${trade['proceeds']:,.2f}")
            else:
                print(f"      Cost: ${trade['cost']:,.2f}")
        else:
            failed_trades.append(trade)
            print(f"      Reason: {trade['reason']}")
    
    print(f"\nCURRENT LOGIC RESULT:")
    print(f"  - Portfolio holds: SOL(100%) - completely unbalanced!")
    print(f"  - ${5000:,.2f} cash sitting unused")
    print(f"  - No rollback mechanism")
    print(f"  - Portfolio state inconsistent with target")
    
    print(f"\nBETTER APPROACH:")
    print(f"  - Detect partial failure")
    print(f"  - Redistribute failed allocations to successful assets")
    print(f"  - Or rollback entire rebalancing")
    print(f"  - Maintain portfolio consistency")

if __name__ == "__main__":
    test_swap_proceeds_logic_flaw()
    test_portfolio_value_inconsistency()
    test_precision_edge_case()
    test_partial_failure_scenario()
    
    print("\n" + "=" * 70)
    print("SUMMARY: Multiple critical issues found that need fixing")
    print("See PORTFOLIO_PERMUTATION_ANALYSIS.md for detailed recommendations")
    print("=" * 70)
