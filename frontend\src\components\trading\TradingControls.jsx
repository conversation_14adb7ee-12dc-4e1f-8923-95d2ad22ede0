import React from 'react';
import {
  <PERSON>,
  Button,
  VStack,
  HStack,
  Text,
  Divider,
  useColorModeValue,
  Tooltip,
} from '@chakra-ui/react';
import {
  RepeatIcon,
  WarningTwoIcon
} from '@chakra-ui/icons';
import { FaPlay, FaStop } from 'react-icons/fa';

const TradingControls = ({
  tradingStatus,
  onStart,
  onStop,
  onExecute,
  onReset,
  isLoading,
  tradingMode,
  selectedAssets = []
}) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const isLiveMode = tradingMode === 'live';
  const isRunning = tradingStatus?.running;
  const isEnabled = tradingStatus?.enabled;

  return (
    <Box
      bg={bgColor}
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      height="100%"
    >
      <Text fontSize="lg" fontWeight="bold" mb={2}>
        Trading Controls
      </Text>

      {/* Selected Assets Display */}
      <Box mb={4} fontSize="sm">
        <Text fontWeight="medium" mb={1}>Selected Assets:</Text>
        <Box
          p={2}
          bg={useColorModeValue('gray.50', 'gray.600')}
          borderRadius="md"
          maxHeight="80px"
          overflowY="auto"
        >
          {selectedAssets.length > 0 ? (
            selectedAssets.map((asset, index) => (
              <Text key={asset} display="inline-block" mr={2} mb={1}>
                {asset}{index < selectedAssets.length - 1 ? ',' : ''}
              </Text>
            ))
          ) : (
            <Text color="red.500">No assets selected</Text>
          )}
        </Box>
      </Box>

      <VStack spacing={4} align="stretch">
        {/* Start/Stop Button */}
        {isEnabled && isRunning ? (
          <Button
            leftIcon={<FaStop />}
            colorScheme="red"
            onClick={onStop}
            isLoading={isLoading}
            width="full"
            size="lg"
          >
            Stop Trading
          </Button>
        ) : (
          <Tooltip label="Will automatically select the strongest asset from your selection">
            <Button
              leftIcon={<FaPlay />}
              colorScheme="green"
              onClick={onStart}
              isLoading={isLoading}
              isDisabled={!tradingStatus || selectedAssets.length === 0}
              width="full"
              size="lg"
            >
              Start Trading with Best Asset
            </Button>
          </Tooltip>
        )}

        {/* Manual Execution Button */}
        <Tooltip label="Execute a single trade with the strongest asset from your selection">
          <Button
            leftIcon={<RepeatIcon />}
            colorScheme="blue"
            onClick={onExecute}
            isLoading={isLoading}
            isDisabled={!tradingStatus || selectedAssets.length === 0}
            width="full"
          >
            Execute Now with Best Asset
          </Button>
        </Tooltip>

        <Divider my={2} />

        {/* Reset Paper Trading Button - Only show in paper mode */}
        {tradingMode === 'paper' && (
          <Tooltip label="Reset paper trading account to initial balance">
            <Button
              leftIcon={<WarningTwoIcon />}
              colorScheme="orange"
              variant="outline"
              onClick={onReset}
              isLoading={isLoading}
              isDisabled={!tradingStatus}
              width="full"
            >
              Reset Paper Account
            </Button>
          </Tooltip>
        )}

        {/* Live Trading Warning */}
        {isLiveMode && (
          <Box
            p={3}
            bg="red.50"
            color="red.800"
            borderRadius="md"
            fontSize="sm"
            border="1px solid"
            borderColor="red.200"
          >
            <Text fontWeight="bold">⚠️ Live Trading Mode</Text>
            <Text mt={1}>
              All trades will use real funds. Please be careful.
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default TradingControls;
