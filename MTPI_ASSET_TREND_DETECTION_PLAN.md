# MTPI Asset Trend Detection Integration Plan

## Overview

This document outlines the plan to integrate MTPI signal aggregation logic as an alternative option for asset trend detection, alongside the existing PGO and RSI methods. The goal is to provide a more robust trend detection system that leverages the multi-indicator approach already implemented for BTC market timing.

## Current Asset Trend Detection System

### Current Methods
1. **RSI Method** (`trend_method: "RSI"`)
   - Uses pairwise RSI comparisons between assets
   - Implemented in `calculate_daily_ratio_scores()`
   - Signal: RSI > 50 = 1, RSI ≤ 50 = 0

2. **PGO Method** (`trend_method: "PGO For Loop"`)
   - Uses pairwise PGO comparisons between assets
   - Implemented in `calculate_daily_pgo_scores()`
   - Signal: PGO crossover logic with thresholds (1.1/-0.58)

### Current Flow
```
Asset Data → Pairwise Comparisons → Signal Matrix → Daily Scores → Best Asset Selection
```

## Proposed MTPI Asset Trend Detection

### New Method: MTPI Multi-Indicator
- **Configuration**: `trend_method: "MTPI"`
- **Approach**: Apply MTPI aggregation logic to each pairwise ratio (ETH/BTC, SOL/BTC, etc.)
- **Benefits**:
  - Reduced false positives through multi-indicator consensus
  - More robust trend detection
  - Consistent methodology with BTC market timing
  - Leverages ALL existing MTPI infrastructure!

### Implementation Architecture

#### 1. Configuration Extension
```yaml
settings:
  trend_method: "MTPI"  # New option alongside "RSI", "PGO For Loop"
  
  # Asset-specific MTPI configuration
  asset_mtpi_indicators:
    enabled_indicators:
      - pgo
      - bollinger_bands
      - dwma_score
      # IMPORTANT: Always use ODD number of indicators (3, 5, 7) to avoid ties!
      # - median_score      # Uncomment for 5 indicators
      # - dema_super_score  # Uncomment for 5 indicators
    combination_method: consensus
    long_threshold: 0.1
    short_threshold: -0.1
    
    # Individual indicator parameters (same as BTC MTPI)
    pgo:
      length: 35
      upper_threshold: 1.35
      lower_threshold: -0.58
    bollinger_bands:
      length: 33
      multiplier: 2.0
      long_threshold: 76.0
      short_threshold: 31.0
    # ... other indicators
```

#### 2. New Implementation Function
```python
def calculate_daily_mtpi_scores(
    data_dict: Dict[str, pd.DataFrame],
    mtpi_config: Dict,
    debug: bool = True
) -> Tuple[pd.DataFrame, Dict]:
    """
    Calculate daily scores using MTPI aggregation for each asset.
    
    Args:
        data_dict: Asset price data
        mtpi_config: MTPI configuration for assets
        debug: Debug flag
        
    Returns:
        Tuple of (daily_scores_df, debug_data)
    """
```

#### 3. Integration Points

**File: `src/strategy.py`**
- Extend `calculate_daily_scores()` to support MTPI method
- Add new condition: `elif trend_method == 'MTPI':`

**File: `src/indicators/asset_mtpi_indicators.py`** (New)
- Implement asset-specific MTPI calculation
- Reuse existing indicator implementations
- Apply same aggregation logic as BTC MTPI

**File: `config/settings.yaml`**
- Add asset MTPI configuration section
- Maintain backward compatibility

## Implementation Steps

### Phase 1: Core Infrastructure
1. **Create asset MTPI module** (`src/indicators/asset_mtpi_indicators.py`)
   - Function to calculate MTPI signals for individual assets
   - Reuse existing indicator implementations from MTPI system
   - Apply same aggregation logic (`combine_multi_signals_with_score`)

2. **Extend strategy.py**
   - Add MTPI trend method support in `calculate_daily_scores()`
   - Import and call new asset MTPI function

3. **Configuration updates**
   - Add asset MTPI configuration to settings.yaml
   - Ensure backward compatibility with existing methods

### Phase 2: Testing & Validation
1. **Create test script** (`test_asset_mtpi_comparison.py`)
   - Compare MTPI vs PGO vs RSI asset scoring
   - Validate signal quality and false positive reduction
   - Performance analysis

2. **Backtesting integration**
   - Test with existing backtesting framework
   - Compare strategy performance across methods

### Phase 3: Optimization & Documentation
1. **Parameter optimization**
   - Fine-tune indicator parameters for asset-level analysis
   - Optimize thresholds and combination methods

2. **Documentation updates**
   - Update configuration documentation
   - Add usage examples and best practices

## Technical Implementation Details

### Asset MTPI Calculation Flow
```
For each pairwise ratio (ETH/BTC, SOL/BTC, etc.):
  1. Calculate individual indicator signals on ratio (PGO, BB, DWMA, etc.)
  2. Aggregate signals using consensus/weighted method (reuse existing logic)
  3. Apply thresholds to generate final ratio signal (1/-1/0)
  4. Use ratio signals in comparison matrix (same as current PGO method)
```

### Signal Aggregation Reuse
- Leverage existing `combine_multi_signals_with_score()` function
- Use same indicator implementations from MTPI system
- Maintain consistent parameter structure

### Backward Compatibility
- Existing RSI and PGO methods remain unchanged
- New MTPI method is additive, not replacing
- Configuration migration path for existing users

## Expected Benefits

1. **Reduced False Positives**: Multi-indicator consensus should filter out noise
2. **Improved Robustness**: Less susceptible to single indicator failures
3. **Consistency**: Same methodology for market timing and asset selection
4. **Flexibility**: Configurable indicator selection and weights

## Risk Considerations

1. **Computational Overhead**: More indicators = more calculations
2. **Parameter Sensitivity**: More parameters to tune and optimize
3. **Complexity**: Increased system complexity for debugging
4. **Warmup Period**: Longer warmup time due to multiple indicators

## Success Metrics

1. **Signal Quality**: Reduced false positive rate vs current methods
2. **Performance**: Improved Sharpe ratio and returns in backtesting
3. **Stability**: More consistent asset selection over time
4. **Robustness**: Better performance across different market conditions

## Next Steps

1. **Approval**: Get user approval for this implementation approach
2. **Development**: Start with Phase 1 implementation
3. **Testing**: Comprehensive testing and validation
4. **Deployment**: Gradual rollout with monitoring

---

This plan provides a structured approach to integrating MTPI logic for asset trend detection while maintaining system stability and backward compatibility.
