# MTPI Signal Debugging

This document summarizes the changes made to help diagnose the discrepancy between the MTPI signal processing in the Python backend and the frontend.

## Problem

Despite our previous changes to pass the raw MTPI signals directly to the frontend, there's still a discrepancy in performance between the Python backend and the frontend. This suggests that either:

1. The signals are not being passed correctly
2. The signals are being interpreted differently
3. The signals are being applied at different timestamps
4. Additional processing is being applied somewhere

## Debugging Approach

We've added detailed logging throughout the codebase to track exactly how the MTPI signals are being generated, processed, and used:

### 1. Backend Logging (Strategy.py)

Added detailed logging to track how the MTPI signals are being used in the strategy:

```python
# Check MTPI signal for the first day
mtpi_allows_trade = True
if use_mtpi_signal and mtpi_signals is not None:
    # Find the last known MTPI signal on or before the first day
    prior_signals = mtpi_signals[mtpi_signals.index <= first_day]
    if not prior_signals.empty:
        latest_signal = prior_signals.iloc[-1]
        latest_signal_time = prior_signals.index[-1]
        mtpi_allows_trade = (latest_signal == 1)
        logging.info(f"STRATEGY INIT: First day {first_day} - MTPI signal from {latest_signal_time} is {latest_signal}, allows trade: {mtpi_allows_trade}")
    else:
        logging.warning(f"No MTPI signal available for {first_day}. Assuming no trade allowed.")
        mtpi_allows_trade = False
```

Also added detailed logging for daily trade decisions:

```python
# Check MTPI signal if enabled
mtpi_allows_trade = True
if use_mtpi_signal and mtpi_signals is not None:
    # Find the last known MTPI signal on or before yesterday
    prior_signals = mtpi_signals[mtpi_signals.index <= yesterday]
    if not prior_signals.empty:
        latest_signal = prior_signals.iloc[-1]
        latest_signal_time = prior_signals.index[-1]
        mtpi_allows_trade = (latest_signal == 1)
        
        # Log detailed information about the MTPI signal decision
        time_diff = yesterday - latest_signal_time
        time_diff_hours = time_diff.total_seconds() / 3600
        
        if mtpi_allows_trade:
            logging.info(f"TRADE DECISION: Day {yesterday} - MTPI signal from {latest_signal_time} ({time_diff_hours:.1f}h old) is {latest_signal}, ALLOWING trade")
        else:
            logging.info(f"TRADE DECISION: Day {yesterday} - MTPI signal from {latest_signal_time} ({time_diff_hours:.1f}h old) is {latest_signal}, NOT allowing trade")
    else:
        logging.warning(f"No MTPI signal available for {yesterday}. Assuming no trade allowed.")
        mtpi_allows_trade = False
```

### 2. MTPI Signal Handler Logging

Added detailed logging to track the distribution of MTPI signals:

```python
# Log the distribution of signals to help debugging
signal_counts = pgo_signals.value_counts()
bullish_count = signal_counts.get(1, 0)
bearish_count = signal_counts.get(-1, 0)
neutral_count = signal_counts.get(0, 0)
total_signals = len(pgo_signals)

bullish_pct = (bullish_count / total_signals * 100) if total_signals > 0 else 0
bearish_pct = (bearish_count / total_signals * 100) if total_signals > 0 else 0
neutral_pct = (neutral_count / total_signals * 100) if total_signals > 0 else 0

logging.info(f"MTPI Signal Distribution: Bullish {bullish_pct:.1f}% ({bullish_count}), "
            f"Bearish {bearish_pct:.1f}% ({bearish_count}), Neutral {neutral_pct:.1f}% ({neutral_count})")
```

Also added logging for the first and last few signals:

```python
# Log the first few and last few signals for debugging
if len(pgo_signals) > 0:
    sample_size = min(5, len(pgo_signals))
    first_signals = pgo_signals.iloc[:sample_size]
    last_signals = pgo_signals.iloc[-sample_size:]
    
    logging.info(f"First {sample_size} MTPI signals:")
    for idx, val in first_signals.items():
        logging.info(f"  {idx}: MTPI={val}")
    
    logging.info(f"Last {sample_size} MTPI signals:")
    for idx, val in last_signals.items():
        logging.info(f"  {idx}: MTPI={val}")
```

And added logging for signal transitions:

```python
# Check for signal transitions (changes from one signal to another)
if len(pgo_signals) > 1:
    transitions = (pgo_signals != pgo_signals.shift(1)).sum()
    logging.info(f"Signal transitions: {transitions} changes detected in {len(pgo_signals)} signals")
    
    # Log the most recent transition
    for i in range(len(pgo_signals)-1, 0, -1):
        if pgo_signals.iloc[i] != pgo_signals.iloc[i-1]:
            from_signal = pgo_signals.iloc[i-1]
            to_signal = pgo_signals.iloc[i]
            transition_time = pgo_signals.index[i]
            logging.info(f"Most recent transition: {from_signal} -> {to_signal} at {transition_time}")
            break
```

### 3. API Server Logging

Added detailed logging to track how the MTPI signals are being passed to the frontend:

```python
# Log the raw signals for debugging
logging.info(f"Sending {len(raw_mtpi_signals)} raw MTPI signals to frontend")

# Log the first few signals with detailed timestamps
if len(raw_mtpi_signals) > 0:
    logging.info("First few MTPI signals with detailed timestamps:")
    for i, signal in enumerate(raw_mtpi_signals[:5]):
        signal_time = pd.Timestamp(signal['time'], unit='s', tz='UTC')
        logging.info(f"  Signal {i+1}: time={signal_time} ({signal['time']}), value={signal['value']}")
    
    # Log the last few signals with detailed timestamps
    logging.info("Last few MTPI signals with detailed timestamps:")
    for i, signal in enumerate(raw_mtpi_signals[-5:]):
        signal_time = pd.Timestamp(signal['time'], unit='s', tz='UTC')
        logging.info(f"  Signal {i+1}: time={signal_time} ({signal['time']}), value={signal['value']}")
    
    # Count signal distribution
    signal_values = [s['value'] for s in raw_mtpi_signals]
    bullish_count = signal_values.count(1)
    bearish_count = signal_values.count(-1)
    neutral_count = signal_values.count(0)
    total_signals = len(signal_values)
    
    bullish_pct = (bullish_count / total_signals * 100) if total_signals > 0 else 0
    bearish_pct = (bearish_count / total_signals * 100) if total_signals > 0 else 0
    neutral_pct = (neutral_count / total_signals * 100) if total_signals > 0 else 0
    
    logging.info(f"API MTPI Signal Distribution: Bullish {bullish_pct:.1f}% ({bullish_count}), "
               f"Bearish {bearish_pct:.1f}% ({bearish_count}), Neutral {neutral_pct:.1f}% ({neutral_count})")
    
    # Check for signal transitions
    transitions = sum(1 for i in range(1, len(signal_values)) if signal_values[i] != signal_values[i-1])
    logging.info(f"API MTPI Signal transitions: {transitions} changes detected in {len(signal_values)} signals")
```

### 4. Frontend Logging

Added detailed logging to track how the frontend is processing the MTPI signals:

```javascript
// Log detailed information about the raw MTPI signals
const rawSignals = equityCurves.metadata.rawMtpiSignals;

// Count signal distribution
const signalCounts = {
  bullish: rawSignals.filter(s => s.value === 1).length,
  neutral: rawSignals.filter(s => s.value === 0).length,
  bearish: rawSignals.filter(s => s.value === -1).length
};

const totalSignals = rawSignals.length;
const bullishPct = (signalCounts.bullish / totalSignals * 100).toFixed(1);
const neutralPct = (signalCounts.neutral / totalSignals * 100).toFixed(1);
const bearishPct = (signalCounts.bearish / totalSignals * 100).toFixed(1);

console.log(`FRONTEND MTPI Signal Distribution: Bullish ${bullishPct}% (${signalCounts.bullish}), ` +
           `Neutral ${neutralPct}% (${signalCounts.neutral}), Bearish ${bearishPct}% (${signalCounts.bearish})`);
```

Also added logging for the first and last few signals:

```javascript
// Log the first few and last few signals
if (rawSignals.length > 0) {
  const firstSignals = rawSignals.slice(0, Math.min(5, rawSignals.length));
  const lastSignals = rawSignals.slice(-Math.min(5, rawSignals.length));
  
  console.log('First few MTPI signals:');
  firstSignals.forEach((signal, i) => {
    const signalTime = new Date(signal.time * 1000);
    console.log(`  Signal ${i+1}: time=${signalTime.toISOString()} (${signal.time}), value=${signal.value}`);
  });
  
  console.log('Last few MTPI signals:');
  lastSignals.forEach((signal, i) => {
    const signalTime = new Date(signal.time * 1000);
    console.log(`  Signal ${i+1}: time=${signalTime.toISOString()} (${signal.time}), value=${signal.value}`);
  });
}
```

And added logging for signal transitions:

```javascript
// Check for signal transitions
let transitions = 0;
for (let i = 1; i < rawSignals.length; i++) {
  if (rawSignals[i].value !== rawSignals[i-1].value) {
    transitions++;
  }
}
console.log(`FRONTEND MTPI Signal transitions: ${transitions} changes detected in ${rawSignals.length} signals`);
```

### 5. Frontend API Client Updates

Updated the frontend API client to properly handle the raw MTPI signals:

```javascript
// Parse MTPI signal data if available
const mtpiSignalPeriods = response.data.mtpiSignalPeriods || [];
const rawMtpiSignals = response.data.rawMtpiSignals || [];
const latestMtpiSignal = response.data.latestMtpiSignal !== undefined ? response.data.latestMtpiSignal : null;

// Log MTPI signal data for debugging
if (rawMtpiSignals.length > 0) {
  console.log(`Received ${rawMtpiSignals.length} raw MTPI signals from API`);
  console.log(`Latest MTPI signal: ${latestMtpiSignal}`);
} else if (mtpiSignalPeriods.length > 0) {
  console.log(`Received ${mtpiSignalPeriods.length} MTPI signal periods from API (legacy format)`);
} else {
  console.log('No MTPI signal data received from API');
}
```

## Next Steps

1. Run the strategy with the detailed logging enabled and check the logs for:
   - MTPI signal distribution (bullish vs bearish percentages)
   - Signal timestamps and alignment with data
   - Signal transitions (when signals change from bullish to bearish or vice versa)

2. Compare the logs between the Python backend and the frontend to identify any discrepancies in:
   - Signal values
   - Signal timestamps
   - Signal interpretation
   - Signal application

3. Look for any additional processing that might be applied to the signals in either the backend or frontend.

4. Check if the signals are being applied at the correct timestamps in both the backend and frontend.

5. Verify that the same timeframe is being used for both the MTPI signals and the data.

By comparing the detailed logs, we should be able to identify exactly where the discrepancy is occurring and fix it.
