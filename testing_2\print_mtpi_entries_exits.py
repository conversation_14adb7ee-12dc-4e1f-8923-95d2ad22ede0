import pandas as pd
import logging
import argparse
from datetime import datetime
from src.MTPI_signal_handler import fetch_historical_mtpi_signals
from src.utils import setup_logging

def print_mtpi_entries_exits(timeframe='1d',
                            length=35,
                            upper_threshold=1.35,
                            lower_threshold=-1.0,
                            analysis_start_date='2023-10-20',
                            skip_warmup=True):
    """
    Print out the dates when the strategy enters and exits the market based on MTPI signals.

    Args:
        timeframe: The timeframe for MTPI signal calculation (e.g., '1d', '4h', '1h')
        length: The period length for PGO calculation
        upper_threshold: The threshold for bullish signals
        lower_threshold: The threshold for bearish signals
        analysis_start_date: Start date for analysis in YYYY-MM-DD format
        skip_warmup: Whether to skip the warmup period when generating signals
    """
    # Set up logging
    setup_logging(level=logging.INFO)

    print(f"\n{'=' * 80}")
    print(f"MTPI SIGNAL ENTRY AND EXIT DATES")
    print(f"Timeframe: {timeframe}, PGO Length: {length}")
    print(f"Thresholds: Upper={upper_threshold}, Lower={lower_threshold}")
    print(f"Analysis Start Date: {analysis_start_date}")
    print(f"{'=' * 80}\n")

    # Convert analysis_start_date to datetime with UTC timezone
    analysis_start = pd.to_datetime(analysis_start_date).tz_localize('UTC')

    # Fetch historical MTPI signals
    print("Fetching historical MTPI signals...")
    mtpi_signals = fetch_historical_mtpi_signals(
        timeframe=timeframe,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        limit=1000,  # Use a large limit to ensure good coverage
        skip_warmup=skip_warmup
    )

    if mtpi_signals is None or mtpi_signals.empty:
        print("Error: Failed to fetch MTPI signals or no signals returned.")
        return

    # Filter signals from the analysis start date
    mtpi_signals = mtpi_signals[mtpi_signals.index >= analysis_start]

    if mtpi_signals.empty:
        print(f"No MTPI signals found after {analysis_start_date}.")
        return

    print(f"\nFound {len(mtpi_signals)} MTPI signals from {mtpi_signals.index[0]} to {mtpi_signals.index[-1]}")

    # Count signal distribution
    signal_counts = mtpi_signals.value_counts()
    print("\nSignal Distribution:")
    for signal, count in signal_counts.items():
        signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
        percentage = (count / len(mtpi_signals)) * 100
        print(f"  {signal_name} ({signal}): {count} occurrences ({percentage:.1f}%)")

    # Find all signal transitions
    transitions = []
    current_signal = mtpi_signals.iloc[0]
    current_start = mtpi_signals.index[0]

    for i in range(1, len(mtpi_signals)):
        if mtpi_signals.iloc[i] != current_signal:
            # Signal changed, record the transition
            transitions.append({
                'from_signal': current_signal,
                'to_signal': mtpi_signals.iloc[i],
                'start_date': current_start,
                'end_date': mtpi_signals.index[i],
                'duration_days': (mtpi_signals.index[i] - current_start).days
            })

            # Update current signal and start date
            current_signal = mtpi_signals.iloc[i]
            current_start = mtpi_signals.index[i]

    # Add the last period
    transitions.append({
        'from_signal': current_signal,
        'to_signal': None,  # Still ongoing
        'start_date': current_start,
        'end_date': mtpi_signals.index[-1],
        'duration_days': (mtpi_signals.index[-1] - current_start).days
    })

    # Print market entries and exits
    print("\nMarket Entry and Exit Dates:")
    print(f"{'Type':<10} {'Date':<20} {'Signal':<10} {'Duration (days)':<15}")
    print(f"{'-'*10} {'-'*20} {'-'*10} {'-'*15}")

    # Print initial state
    initial_signal = mtpi_signals.iloc[0]
    initial_state = "IN MARKET" if initial_signal == 1 else "OUT OF MARKET"
    print(f"{initial_state:<10} {mtpi_signals.index[0].strftime('%Y-%m-%d %H:%M'):<20} {initial_signal:<10} {'Initial state':<15}")

    # Print all transitions
    for t in transitions:
        # Skip the last transition if it's ongoing (to_signal is None)
        if t['to_signal'] is None:
            continue

        if t['from_signal'] == 1 and t['to_signal'] != 1:
            # Exit the market
            print(f"{'EXIT':<10} {t['end_date'].strftime('%Y-%m-%d %H:%M'):<20} {t['to_signal']:<10} {t['duration_days']:<15}")
        elif t['from_signal'] != 1 and t['to_signal'] == 1:
            # Enter the market
            print(f"{'ENTRY':<10} {t['end_date'].strftime('%Y-%m-%d %H:%M'):<20} {t['to_signal']:<10} {'-':<15}")

    # Print periods summary
    print("\nDetailed Market Periods:")
    print(f"{'Period':<10} {'From':<20} {'To':<20} {'Signal':<10} {'Duration (days)':<15} {'Status'}")
    print(f"{'-'*10} {'-'*20} {'-'*20} {'-'*10} {'-'*15} {'-'*10}")

    for i, t in enumerate(transitions):
        period_type = "IN MARKET" if t['from_signal'] == 1 else "OUT MARKET"
        print(f"{i+1:<10} {t['start_date'].strftime('%Y-%m-%d %H:%M'):<20} {t['end_date'].strftime('%Y-%m-%d %H:%M'):<20} {t['from_signal']:<10} {t['duration_days']:<15} {period_type}")

    # Calculate total time in/out of market
    total_days = (mtpi_signals.index[-1] - mtpi_signals.index[0]).days
    in_market_days = sum(t['duration_days'] for t in transitions if t['from_signal'] == 1)
    out_market_days = sum(t['duration_days'] for t in transitions if t['from_signal'] != 1)

    print("\nSummary Statistics:")
    print(f"Total period analyzed: {total_days} days")
    print(f"Time in market: {in_market_days} days ({(in_market_days/total_days)*100:.1f}%)")
    print(f"Time out of market: {out_market_days} days ({(out_market_days/total_days)*100:.1f}%)")
    print(f"Number of entries: {sum(1 for t in transitions if t['from_signal'] != 1 and t['to_signal'] == 1)}")
    print(f"Number of exits: {sum(1 for t in transitions if t['from_signal'] == 1 and t['to_signal'] != 1)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Print MTPI signal entry and exit dates')
    parser.add_argument('--timeframe', default='1d', help='Timeframe for MTPI signal calculation (e.g., 1d, 4h, 1h)')
    parser.add_argument('--length', type=int, default=35, help='Period length for PGO calculation')
    parser.add_argument('--upper', type=float, default=1.35, help='Upper threshold for bullish signals')
    parser.add_argument('--lower', type=float, default=-1.0, help='Lower threshold for bearish signals')
    parser.add_argument('--start-date', default='2023-10-20', help='Start date for analysis (YYYY-MM-DD)')
    parser.add_argument('--no-skip-warmup', action='store_true', help='Do not skip the warmup period')

    args = parser.parse_args()

    print_mtpi_entries_exits(
        timeframe=args.timeframe,
        length=args.length,
        upper_threshold=args.upper,
        lower_threshold=args.lower,
        analysis_start_date=args.start_date,
        skip_warmup=not args.no_skip_warmup
    )
