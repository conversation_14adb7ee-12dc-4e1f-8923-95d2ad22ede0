import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Flex,
  Text,
  Button,
  Spinner,
  Badge,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Card,
  CardBody,
  CardHeader,
  Heading,
} from '@chakra-ui/react';
import { RepeatIcon, WarningIcon } from '@chakra-ui/icons';
import { useQuery, useQueryClient } from 'react-query';
import { initWebSocket, addListener, removeListener } from '../services/websocketService';

// Import components (to be created)
import TradingControls from '../components/trading/TradingControls';
import PortfolioSummary from '../components/trading/PortfolioSummary';
import PositionsList from '../components/trading/PositionsList';
import TradeHistory from '../components/trading/TradeHistory';
import TradingPerformanceMetrics from '../components/trading/TradingPerformanceMetrics';
import LiveAssetChart from '../components/trading/LiveAssetChart';
import LiveStrategyChart from '../components/trading/LiveStrategyChart';
import ActivityLog from '../components/trading/ActivityLog';
import AssetSelector from '../components/AssetSelector';
import TradingSettings from '../components/trading/TradingSettings';

// Import API services
import {
  fetchTradingStatus,
  startTrading,
  stopTrading,
  executeTrading,
  fetchTradingConfig,
  fetchTradingPositions,
  fetchTradingHistory,
  fetchTradingPerformance,
  resetPaperTrading
} from '../services/tradingApi';
import { fetchSettings } from '../services/api';

const LiveTradingDashboard = () => {
  const toast = useToast();
  const queryClient = useQueryClient();
  const [selectedAsset, setSelectedAsset] = useState(null);
  const [selectedAssets, setSelectedAssets] = useState(['BTC/USDT', 'ETH/USDT', 'SOL/USDT']);
  const [refreshInterval, setRefreshInterval] = useState(10000); // 10 seconds
  const { isOpen, onOpen, onClose } = useDisclosure(); // For confirmation modal
  const [actionToConfirm, setActionToConfirm] = useState(null);

  // Fetch trading status
  const {
    data: tradingStatus,
    isLoading: statusLoading,
    error: statusError,
    refetch: refetchStatus
  } = useQuery('tradingStatus', fetchTradingStatus, {
    refetchInterval: refreshInterval,
    onError: (error) => {
      console.error('Error fetching trading status:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch trading status',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  });

  // Fetch trading positions
  const {
    data: positions,
    isLoading: positionsLoading
  } = useQuery('tradingPositions', fetchTradingPositions, {
    refetchInterval: refreshInterval,
    enabled: !!tradingStatus
  });

  // Fetch trading history
  const {
    data: tradeHistory,
    isLoading: historyLoading
  } = useQuery('tradingHistory', fetchTradingHistory, {
    refetchInterval: refreshInterval * 3, // Less frequent updates
    enabled: !!tradingStatus
  });

  // Fetch trading performance
  const {
    data: performance,
    isLoading: performanceLoading
  } = useQuery(['tradingPerformance', selectedAssets], () => fetchTradingPerformance({ selected_assets: selectedAssets }), {
    refetchInterval: refreshInterval * 6, // Even less frequent updates
    enabled: !!tradingStatus
  });

  // Fetch trading configuration
  const {
    data: tradingConfig,
    isLoading: configLoading
  } = useQuery('tradingConfig', fetchTradingConfig);

  // Fetch strategy settings
  const {
    data: strategySettings,
    isLoading: settingsLoading
  } = useQuery('strategySettings', fetchSettings, {
    refetchInterval: refreshInterval * 10, // Very infrequent updates
    onError: (error) => {
      console.error('Error fetching strategy settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch strategy settings',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  });

  // Set the selected asset based on current position
  useEffect(() => {
    if (positions && positions.length > 0) {
      setSelectedAsset(positions[0].symbol);
    } else if (tradingStatus && tradingStatus.best_asset) {
      setSelectedAsset(tradingStatus.best_asset);
    }
  }, [positions, tradingStatus]);

  // Initialize selected assets from strategy settings
  useEffect(() => {
    if (strategySettings && strategySettings.assets && strategySettings.assets.length > 0) {
      setSelectedAssets(strategySettings.assets);
    }
  }, [strategySettings]);

  // Initialize WebSocket connection
  useEffect(() => {
    // Initialize WebSocket
    initWebSocket()
      .then(() => {
        console.log('WebSocket initialized successfully');
        toast({
          title: 'Connected',
          description: 'Real-time trading updates enabled',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .catch((error) => {
        console.error('Failed to initialize WebSocket:', error);
        toast({
          title: 'Connection Error',
          description: 'Failed to enable real-time updates',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      });

    // Set up event listeners
    const handleTradingStatus = (status) => {
      console.log('Received trading status update via WebSocket:', status);
      // Update the local state with the new status
      queryClient.setQueryData('tradingStatus', status);
    };

    const handleTradeExecuted = (data) => {
      console.log('Received trade execution update via WebSocket:', data);
      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['tradingPositions', 'tradingHistory', 'tradingPerformance']);
      // Show toast notification
      toast({
        title: 'Trade Executed',
        description: `Trade executed at ${new Date(data.timestamp).toLocaleTimeString()}`,
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    };

    const handleAccountReset = (data) => {
      console.log('Received account reset notification via WebSocket:', data);
      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['tradingStatus', 'tradingPositions', 'tradingHistory', 'tradingPerformance']);
      // Show toast notification
      toast({
        title: 'Account Reset',
        description: 'Paper trading account has been reset',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    };

    // Add event listeners
    addListener('trading_status', handleTradingStatus);
    addListener('trade_executed', handleTradeExecuted);
    addListener('account_reset', handleAccountReset);

    // Clean up event listeners on unmount
    return () => {
      removeListener('trading_status', handleTradingStatus);
      removeListener('trade_executed', handleTradeExecuted);
      removeListener('account_reset', handleAccountReset);
    };
  }, [queryClient, toast]);

  // Handle start trading
  const handleStartTrading = () => {
    if (selectedAssets.length === 0) {
      toast({
        title: 'No Assets Selected',
        description: 'Please select at least one asset for trading',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setActionToConfirm(() => async () => {
      try {
        // Show loading toast for backtest
        const loadingToastId = toast({
          title: 'Running Quick Backtest',
          description: 'Determining the strongest asset from your selection...',
          status: 'info',
          duration: null,
          isClosable: false,
        });

        // Start trading with auto_select_best_asset enabled
        const result = await startTrading({
          selected_assets: selectedAssets,
          auto_select_best_asset: true
        });

        // Close the loading toast
        toast.close(loadingToastId);

        if (result.success) {
          // Show success message with best asset information if available
          let description = 'Automated trading has been started successfully';

          if (result.backtest_result && result.backtest_result.success) {
            const bestAsset = result.backtest_result.best_asset;
            description = `Trading started with ${bestAsset || 'the strongest asset'} automatically selected`;
          }

          toast({
            title: 'Trading Started',
            description: description,
            status: 'success',
            duration: 5000,
            isClosable: true,
          });

          queryClient.invalidateQueries('tradingStatus');
          queryClient.invalidateQueries(['tradingPositions', 'tradingHistory', 'tradingPerformance']);
        } else {
          throw new Error(result.message || 'Failed to start trading');
        }
      } catch (error) {
        console.error('Error starting trading:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to start trading',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    });
    onOpen();
  };

  // Handle stop trading
  const handleStopTrading = () => {
    setActionToConfirm(() => async () => {
      try {
        const result = await stopTrading();
        if (result.success) {
          toast({
            title: 'Trading Stopped',
            description: 'Automated trading has been stopped successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
          queryClient.invalidateQueries('tradingStatus');
        } else {
          throw new Error(result.message || 'Failed to stop trading');
        }
      } catch (error) {
        console.error('Error stopping trading:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to stop trading',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    });
    onOpen();
  };

  // Handle manual execution
  const handleExecuteTrading = () => {
    if (selectedAssets.length === 0) {
      toast({
        title: 'No Assets Selected',
        description: 'Please select at least one asset for trading',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setActionToConfirm(() => async () => {
      try {
        // Show loading toast for backtest
        const loadingToastId = toast({
          title: 'Running Quick Backtest',
          description: 'Determining the strongest asset from your selection...',
          status: 'info',
          duration: null,
          isClosable: false,
        });

        // Execute trading with auto_select_best_asset enabled
        const result = await executeTrading({
          selected_assets: selectedAssets,
          auto_select_best_asset: true
        });

        // Close the loading toast
        toast.close(loadingToastId);

        if (result.success) {
          // Show success message with best asset information if available
          let description = 'Trading strategy has been executed manually';

          if (result.result && result.result.best_asset) {
            const bestAsset = result.result.best_asset;
            description = `Trading executed with ${bestAsset} automatically selected`;
          }

          toast({
            title: 'Trading Executed',
            description: description,
            status: 'success',
            duration: 5000,
            isClosable: true,
          });

          queryClient.invalidateQueries(['tradingStatus', 'tradingPositions', 'tradingHistory', 'tradingPerformance']);
        } else {
          throw new Error(result.message || 'Failed to execute trading');
        }
      } catch (error) {
        console.error('Error executing trading:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to execute trading',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    });
    onOpen();
  };

  // Handle reset paper trading
  const handleResetPaperTrading = () => {
    setActionToConfirm(() => async () => {
      try {
        const result = await resetPaperTrading();
        if (result.success) {
          toast({
            title: 'Paper Trading Reset',
            description: 'Paper trading account has been reset successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
          queryClient.invalidateQueries(['tradingStatus', 'tradingPositions', 'tradingHistory', 'tradingPerformance']);
        } else {
          throw new Error(result.message || 'Failed to reset paper trading');
        }
      } catch (error) {
        console.error('Error resetting paper trading:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to reset paper trading',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    });
    onOpen();
  };

  // Handle confirmation
  const handleConfirm = () => {
    if (actionToConfirm) {
      actionToConfirm();
      setActionToConfirm(null);
    }
    onClose();
  };

  // Determine trading status badge color and text
  const getTradingStatusBadge = () => {
    if (!tradingStatus) return { color: 'gray', text: 'Unknown' };

    if (!tradingStatus.enabled) return { color: 'red', text: 'Disabled' };

    if (tradingStatus.running) {
      return { color: 'green', text: 'Running' };
    } else {
      return { color: 'yellow', text: 'Enabled but Paused' };
    }
  };

  // Get trading mode badge
  const getTradingModeBadge = () => {
    if (!tradingConfig) return { color: 'gray', text: 'Unknown' };

    if (tradingConfig.mode === 'paper') {
      return { color: 'blue', text: 'Paper Trading' };
    } else {
      return { color: 'orange', text: 'Live Trading' };
    }
  };

  const statusBadge = getTradingStatusBadge();
  const modeBadge = getTradingModeBadge();

  // Handle refresh
  const handleRefresh = () => {
    refetchStatus();
    queryClient.invalidateQueries(['tradingPositions', 'tradingHistory', 'tradingPerformance']);

    toast({
      title: 'Refreshed',
      description: 'Trading data has been refreshed',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box>
      {/* Header */}
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Text fontSize="2xl" fontWeight="bold">Live Trading Dashboard</Text>
          <Flex mt={2}>
            <Badge colorScheme={statusBadge.color} mr={2} p={1}>
              Status: {statusBadge.text}
            </Badge>
            <Badge colorScheme={modeBadge.color} p={1}>
              Mode: {modeBadge.text}
            </Badge>
          </Flex>
        </Box>
        <Button
          leftIcon={<RepeatIcon />}
          colorScheme="blue"
          variant="outline"
          onClick={handleRefresh}
          isLoading={statusLoading}
        >
          Refresh
        </Button>
      </Flex>

      {/* Error Alert */}
      {statusError && (
        <Alert status="error" mb={4}>
          <AlertIcon />
          <AlertTitle mr={2}>Error!</AlertTitle>
          <AlertDescription>
            Failed to connect to trading server. Please check your connection.
          </AlertDescription>
        </Alert>
      )}

      {/* Live Trading Warning for Live Mode */}
      {tradingConfig && tradingConfig.mode === 'live' && (
        <Alert status="warning" mb={4}>
          <AlertIcon />
          <AlertTitle mr={2}>Live Trading Mode!</AlertTitle>
          <AlertDescription>
            You are in LIVE trading mode. All trades will use real funds.
          </AlertDescription>
        </Alert>
      )}

      {/* Asset Selection */}
      <Card mb={4}>
        <CardHeader>
          <Heading size="md">Select Assets for Trading</Heading>
        </CardHeader>
        <CardBody>
          <AssetSelector
            selectedAssets={selectedAssets}
            onAssetsChange={setSelectedAssets}
            defaultAssets={['BTC/USDT', 'ETH/USDT', 'SOL/USDT']}
            maxAssets={40}
          />
        </CardBody>
      </Card>

      {/* Main Content */}
      <Grid templateColumns="repeat(12, 1fr)" gap={4}>
        {/* Trading Controls and Portfolio Summary */}
        <GridItem colSpan={{ base: 12, md: 4 }}>
          <Grid templateRows="auto auto" gap={4}>
            <GridItem>
              <TradingControls
                tradingStatus={tradingStatus}
                onStart={handleStartTrading}
                onStop={handleStopTrading}
                onExecute={handleExecuteTrading}
                onReset={handleResetPaperTrading}
                isLoading={statusLoading}
                tradingMode={tradingConfig?.mode}
                selectedAssets={selectedAssets}
              />
            </GridItem>
            <GridItem>
              <PortfolioSummary
                tradingStatus={tradingStatus}
                positions={positions}
                isLoading={statusLoading || positionsLoading}
              />
            </GridItem>
          </Grid>
        </GridItem>

        {/* Strategy Chart */}
        <GridItem colSpan={{ base: 12, md: 8 }}>
          <LiveStrategyChart
            isLoading={performanceLoading || statusLoading}
            selectedAssets={selectedAssets}
          />
        </GridItem>

        {/* Positions List */}
        <GridItem colSpan={{ base: 12, md: 6 }}>
          <PositionsList
            positions={positions}
            isLoading={positionsLoading}
            onSelectAsset={setSelectedAsset}
            selectedAsset={selectedAsset}
          />
        </GridItem>

        {/* Performance Metrics */}
        <GridItem colSpan={{ base: 12, md: 6 }}>
          <Grid templateRows="auto auto" gap={4}>
            <GridItem>
              <TradingPerformanceMetrics
                performance={performance}
                isLoading={performanceLoading}
              />
            </GridItem>
            <GridItem>
              <TradingSettings
                settings={strategySettings}
                isLoading={settingsLoading}
              />
            </GridItem>
          </Grid>
        </GridItem>



        {/* Tabs for Trade History and Activity Log */}
        <GridItem colSpan={12}>
          <Tabs variant="enclosed" colorScheme="blue">
            <TabList>
              <Tab>Trade History</Tab>
              <Tab>Activity Log</Tab>
            </TabList>
            <TabPanels>
              <TabPanel p={0} pt={4}>
                <TradeHistory
                  trades={tradeHistory}
                  isLoading={historyLoading}
                />
              </TabPanel>
              <TabPanel p={0} pt={4}>
                <ActivityLog
                  tradingStatus={tradingStatus}
                  trades={tradeHistory}
                />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </GridItem>
      </Grid>

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Confirm Action</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            Are you sure you want to perform this action?
            {tradingConfig && tradingConfig.mode === 'live' && (
              <Alert status="warning" mt={4}>
                <AlertIcon />
                <Text>You are in LIVE trading mode. This action will affect real funds.</Text>
              </Alert>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="red" mr={3} onClick={handleConfirm}>
              Confirm
            </Button>
            <Button variant="ghost" onClick={onClose}>Cancel</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default LiveTradingDashboard;
