2025-06-29 13:37:54,740 - root - INFO - Loaded environment variables from .env file
2025-06-29 13:37:55,745 - root - INFO - Loaded 6 trade records from logs/trades\trade_log_20250629.json
2025-06-29 13:37:55,746 - root - INFO - Loaded 3 asset selection records from logs/trades\asset_selection_20250629.json
2025-06-29 13:37:55,746 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-29 13:37:57,064 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:57,072 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:57,073 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:57,083 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:57,083 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:57,090 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:57,091 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-29 13:37:57,114 - root - INFO - Notification configuration loaded successfully.
2025-06-29 13:37:58,184 - root - INFO - Telegram command handlers registered
2025-06-29 13:37:58,185 - root - INFO - Telegram bot polling started
2025-06-29 13:37:58,185 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-29 13:37:58,185 - root - INFO - Telegram notification channel initialized
2025-06-29 13:37:58,187 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-29 13:37:58,188 - root - INFO - Loaded 24 templates from file
2025-06-29 13:37:58,188 - root - INFO - Notification manager initialized with 1 channels
2025-06-29 13:37:58,188 - root - INFO - Notification manager initialized
2025-06-29 13:37:58,188 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-29 13:37:58,188 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-29 13:37:58,188 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-29 13:37:58,189 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-29 13:37:58,193 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-29 13:37:58,194 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250627_214259.json.json
2025-06-29 13:37:58,194 - root - INFO - Recovery manager initialized
2025-06-29 13:37:58,194 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-29 13:37:58,194 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.0025}
2025-06-29 13:37:58,194 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:58,203 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:58,204 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 13:37:58,204 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 13:37:58,204 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 13:37:58,204 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 13:37:58,204 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 13:37:58,204 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 13:37:58,205 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 13:37:58,205 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 13:37:58,205 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:58,214 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:58,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-29 13:37:58,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-29 13:37:58,260 - telegram.ext.Application - INFO - Application started
2025-06-29 13:37:58,639 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-29 13:37:58,698 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 13:37:58,699 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 13:37:58,699 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 13:37:58,699 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 13:37:58,699 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 13:37:58,699 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:58,707 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:59,199 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 13:37:59,200 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:59,210 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:59,210 - root - INFO - Trading executor initialized for bitvavo
2025-06-29 13:37:59,210 - root - INFO - Trading mode: live
2025-06-29 13:37:59,210 - root - INFO - Trading enabled: True
2025-06-29 13:37:59,211 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 13:37:59,211 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 13:37:59,212 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 13:37:59,213 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 13:37:59,213 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:37:59,223 - root - INFO - Configuration loaded successfully.
2025-06-29 13:37:59,532 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-29 13:37:59,711 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 13:37:59,711 - root - INFO - Trading enabled in live mode
2025-06-29 13:38:00,019 - root - INFO - Connected to bitvavo, balance: 177.33 EUR
2025-06-29 13:38:00,022 - root - INFO - Generated run ID: 20250629_133800
2025-06-29 13:38:00,022 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-29 13:38:00,022 - root - INFO - Background service initialized
2025-06-29 13:38:00,023 - root - INFO - Network watchdog started
2025-06-29 13:38:00,023 - root - INFO - Network watchdog started
2025-06-29 13:38:00,027 - root - INFO - Schedule set up for 1d timeframe
2025-06-29 13:38:00,028 - root - INFO - Background service started
2025-06-29 13:38:00,028 - root - INFO - Executing strategy (run #1)...
2025-06-29 13:38:00,029 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-29 13:38:00,029 - root - INFO - No trades recorded today (Max: 5)
2025-06-29 13:38:00,031 - root - INFO - Initialized daily trades counter for 2025-06-29
2025-06-29 13:38:00,033 - root - INFO - Creating snapshot for candle timestamp: 20250629
2025-06-29 13:38:00,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 13:38:06,054 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-29 13:38:06,314 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 13:38:06,315 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-29 13:38:06,315 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-29 13:38:06,315 - root - INFO - Using recent date for performance tracking: 2025-06-22
2025-06-29 13:38:06,317 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-29 13:38:06,367 - root - INFO - Loaded 2140 rows of ETH/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,367 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,368 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,369 - root - INFO - Data is up to date for ETH/USDT
2025-06-29 13:38:06,369 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,401 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,402 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,403 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,403 - root - INFO - Data is up to date for BTC/USDT
2025-06-29 13:38:06,403 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,453 - root - INFO - Loaded 1783 rows of SOL/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,456 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,466 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,467 - root - INFO - Data is up to date for SOL/USDT
2025-06-29 13:38:06,469 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,522 - root - INFO - Loaded 788 rows of SUI/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,522 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,523 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,523 - root - INFO - Data is up to date for SUI/USDT
2025-06-29 13:38:06,535 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,591 - root - INFO - Loaded 2140 rows of XRP/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,598 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,604 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,606 - root - INFO - Data is up to date for XRP/USDT
2025-06-29 13:38:06,623 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,709 - root - INFO - Loaded 1718 rows of AAVE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,711 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,714 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,719 - root - INFO - Data is up to date for AAVE/USDT
2025-06-29 13:38:06,721 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,763 - root - INFO - Loaded 1741 rows of AVAX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,769 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,769 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,770 - root - INFO - Data is up to date for AVAX/USDT
2025-06-29 13:38:06,770 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,819 - root - INFO - Loaded 2140 rows of ADA/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,819 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,820 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,820 - root - INFO - Data is up to date for ADA/USDT
2025-06-29 13:38:06,822 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,854 - root - INFO - Loaded 2140 rows of LINK/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,855 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,864 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,865 - root - INFO - Data is up to date for LINK/USDT
2025-06-29 13:38:06,867 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,905 - root - INFO - Loaded 2140 rows of TRX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,906 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,906 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,906 - root - INFO - Data is up to date for TRX/USDT
2025-06-29 13:38:06,908 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,935 - root - INFO - Loaded 786 rows of PEPE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,936 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,937 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,937 - root - INFO - Data is up to date for PEPE/USDT
2025-06-29 13:38:06,938 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:06,989 - root - INFO - Loaded 2140 rows of DOGE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:06,989 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,989 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:06,989 - root - INFO - Data is up to date for DOGE/USDT
2025-06-29 13:38:06,991 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,033 - root - INFO - Loaded 2140 rows of BNB/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,037 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:07,038 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:07,039 - root - INFO - Data is up to date for BNB/USDT
2025-06-29 13:38:07,040 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,081 - root - INFO - Loaded 1776 rows of DOT/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,083 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:07,086 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 13:38:07,086 - root - INFO - Data is up to date for DOT/USDT
2025-06-29 13:38:07,087 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,101 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-29 13:38:07,101 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-29 13:38:07,102 - root - INFO -   - Number of indicators: 8
2025-06-29 13:38:07,102 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 13:38:07,102 - root - INFO -   - Combination method: consensus
2025-06-29 13:38:07,103 - root - INFO -   - Long threshold: 0.1
2025-06-29 13:38:07,103 - root - INFO -   - Short threshold: -0.1
2025-06-29 13:38:07,104 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 13:38:07,105 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:07,105 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-29 13:38:07,105 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-29 13:38:07,105 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-29 13:38:07,105 - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-06-29 13:38:07,105 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-29 13:38:07,105 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-29 13:38:07,105 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-29 13:38:07,106 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-29 13:38:07,106 - root - INFO - Using provided trend method: PGO For Loop
2025-06-29 13:38:07,106 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:38:07,125 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:07,126 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-29 13:38:07,144 - root - INFO - Configuration saved successfully.
2025-06-29 13:38:07,153 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 13:38:07,153 - root - INFO - Number of trend detection assets: 14
2025-06-29 13:38:07,153 - root - INFO - Selected assets type: <class 'list'>
2025-06-29 13:38:07,153 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:07,153 - root - INFO - Number of trading assets: 14
2025-06-29 13:38:07,153 - root - INFO - Trading assets type: <class 'list'>
2025-06-29 13:38:07,680 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:07,693 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:07,714 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 13:38:07,727 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:07,727 - root - INFO - Execution context: backtesting
2025-06-29 13:38:07,727 - root - INFO - Execution timing: candle_close
2025-06-29 13:38:07,727 - root - INFO - Ratio calculation method: independent
2025-06-29 13:38:07,729 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-29 13:38:07,729 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-29 13:38:07,729 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 13:38:07,730 - root - INFO - MTPI combination method override: consensus
2025-06-29 13:38:07,730 - root - INFO - MTPI long threshold override: 0.1
2025-06-29 13:38:07,730 - root - INFO - MTPI short threshold override: -0.1
2025-06-29 13:38:07,731 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-29 13:38:07,731 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-29 13:38:07,732 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,733 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,734 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,734 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,735 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,735 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,736 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,736 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,737 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,737 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,737 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,738 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,738 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,739 - root - INFO - Loaded metadata for 48 assets
2025-06-29 13:38:07,739 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-29 13:38:07,767 - root - INFO - Loaded 199 rows of ETH/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,770 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,770 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,770 - root - INFO - Loaded 199 rows of ETH/USDT data from cache (after filtering).
2025-06-29 13:38:07,798 - root - INFO - Loaded 199 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,799 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,800 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,800 - root - INFO - Loaded 199 rows of BTC/USDT data from cache (after filtering).
2025-06-29 13:38:07,817 - root - INFO - Loaded 199 rows of SOL/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,818 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,819 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,819 - root - INFO - Loaded 199 rows of SOL/USDT data from cache (after filtering).
2025-06-29 13:38:07,830 - root - INFO - Loaded 199 rows of SUI/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,832 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,833 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,833 - root - INFO - Loaded 199 rows of SUI/USDT data from cache (after filtering).
2025-06-29 13:38:07,855 - root - INFO - Loaded 199 rows of XRP/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,856 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,857 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,857 - root - INFO - Loaded 199 rows of XRP/USDT data from cache (after filtering).
2025-06-29 13:38:07,885 - root - INFO - Loaded 199 rows of AAVE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,886 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,887 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,887 - root - INFO - Loaded 199 rows of AAVE/USDT data from cache (after filtering).
2025-06-29 13:38:07,917 - root - INFO - Loaded 199 rows of AVAX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,919 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,920 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,920 - root - INFO - Loaded 199 rows of AVAX/USDT data from cache (after filtering).
2025-06-29 13:38:07,953 - root - INFO - Loaded 199 rows of ADA/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,955 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,955 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,955 - root - INFO - Loaded 199 rows of ADA/USDT data from cache (after filtering).
2025-06-29 13:38:07,989 - root - INFO - Loaded 199 rows of LINK/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:07,997 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:07,998 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:07,999 - root - INFO - Loaded 199 rows of LINK/USDT data from cache (after filtering).
2025-06-29 13:38:08,031 - root - INFO - Loaded 199 rows of TRX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,033 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:08,033 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,034 - root - INFO - Loaded 199 rows of TRX/USDT data from cache (after filtering).
2025-06-29 13:38:08,044 - root - INFO - Loaded 199 rows of PEPE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,048 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:08,049 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,049 - root - INFO - Loaded 199 rows of PEPE/USDT data from cache (after filtering).
2025-06-29 13:38:08,071 - root - INFO - Loaded 199 rows of DOGE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,073 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:08,073 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,073 - root - INFO - Loaded 199 rows of DOGE/USDT data from cache (after filtering).
2025-06-29 13:38:08,100 - root - INFO - Loaded 199 rows of BNB/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,102 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:08,102 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,102 - root - INFO - Loaded 199 rows of BNB/USDT data from cache (after filtering).
2025-06-29 13:38:08,121 - root - INFO - Loaded 199 rows of DOT/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,122 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 13:38:08,123 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,123 - root - INFO - Loaded 199 rows of DOT/USDT data from cache (after filtering).
2025-06-29 13:38:08,123 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 13:38:08,123 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,123 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,124 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,130 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,131 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,131 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,132 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,132 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,132 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 13:38:08,156 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-29 13:38:08,156 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-29 13:38:08,157 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-29 13:38:08,157 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-29 13:38:08,157 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:08,172 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:08,173 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-29 13:38:08,173 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-29 13:38:08,173 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 13:38:08,173 - root - INFO - Override: combination_method = consensus
2025-06-29 13:38:08,173 - root - INFO - Override: long_threshold = 0.1
2025-06-29 13:38:08,174 - root - INFO - Override: short_threshold = -0.1
2025-06-29 13:38:08,174 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-29 13:38:08,181 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-29 13:38:08,181 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-29 13:38:08,182 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-29 13:38:08,201 - root - INFO - Loaded 259 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:08,201 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:08,201 - root - INFO - Loaded 259 rows of BTC/USDT data from cache (after filtering).
2025-06-29 13:38:08,202 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 13:38:08,202 - root - INFO - Fetched BTC data: 259 candles from 2024-10-13 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:08,202 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-29 13:38:08,235 - root - INFO - Generated PGO Score signals: {-1: 107, 0: 34, 1: 118}
2025-06-29 13:38:08,235 - root - INFO - Generated pgo signals: 259 values
2025-06-29 13:38:08,236 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-29 13:38:08,236 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-29 13:38:08,250 - root - INFO - Generated BB Score signals: {-1: 109, 0: 32, 1: 118}
2025-06-29 13:38:08,250 - root - INFO - Generated Bollinger Band signals: 259 values
2025-06-29 13:38:08,251 - root - INFO - Generated bollinger_bands signals: 259 values
2025-06-29 13:38:08,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:38:08,837 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-29 13:38:08,837 - root - INFO - Generated dwma_score signals: 259 values
2025-06-29 13:38:08,919 - root - INFO - Generated DEMA Supertrend signals
2025-06-29 13:38:08,919 - root - INFO - Signal distribution: {-1: 145, 0: 1, 1: 113}
2025-06-29 13:38:08,919 - root - INFO - Generated DEMA Super Score signals
2025-06-29 13:38:08,919 - root - INFO - Generated dema_super_score signals: 259 values
2025-06-29 13:38:09,043 - root - INFO - Generated DPSD signals
2025-06-29 13:38:09,043 - root - INFO - Signal distribution: {-1: 101, 0: 87, 1: 71}
2025-06-29 13:38:09,043 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-29 13:38:09,044 - root - INFO - Generated dpsd_score signals: 259 values
2025-06-29 13:38:09,053 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-29 13:38:09,054 - root - INFO - Generated AAD Score signals using SMA method
2025-06-29 13:38:09,054 - root - INFO - Generated aad_score signals: 259 values
2025-06-29 13:38:09,149 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-29 13:38:09,149 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-29 13:38:09,149 - root - INFO - Generated dynamic_ema_score signals: 259 values
2025-06-29 13:38:09,285 - root - INFO - Generated quantile_dema_score signals: 259 values
2025-06-29 13:38:09,294 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-29 13:38:09,295 - root - INFO - Signal distribution: {1: 145, -1: 113, 0: 1}
2025-06-29 13:38:09,295 - root - INFO - Generated combined MTPI signals: 259 values using consensus method
2025-06-29 13:38:09,295 - root - INFO - Signal distribution: {1: 145, -1: 113, 0: 1}
2025-06-29 13:38:09,296 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-29 13:38:09,299 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-29 13:38:09,310 - root - INFO - Configuration saved successfully.
2025-06-29 13:38:09,310 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-29 13:38:09,310 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:09,326 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:09,326 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-29 13:38:09,326 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-29 13:38:09,327 - root - INFO - Using ratio calculation method: independent
2025-06-29 13:38:09,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:09,493 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:09,573 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:09,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:09,639 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:09,659 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:09,713 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 13:38:09,713 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:09,759 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 13:38:09,773 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:09,820 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:09,820 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:09,853 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:09,884 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:09,942 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:09,943 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:09,985 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:10,001 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:10,039 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-29 13:38:10,039 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,080 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-29 13:38:10,093 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:10,157 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:10,159 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,219 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:10,244 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:10,302 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:10,302 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,333 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:10,353 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:10,403 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:10,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,467 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:10,481 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:10,533 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:10,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,563 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:10,572 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:10,613 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:10,615 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,653 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:10,687 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:10,734 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,774 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:10,812 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 13:38:10,818 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:10,849 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 13:38:10,864 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:10,914 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:10,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,010 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:11,046 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:11,084 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,173 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:11,221 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:11,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,250 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:11,265 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:11,314 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:11,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,347 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:11,356 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:11,403 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:11,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,440 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:11,454 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:11,498 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 13:38:11,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,523 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 13:38:11,533 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:11,567 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:11,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,609 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:11,628 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:11,688 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:11,689 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,718 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:11,734 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:11,773 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:11,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,803 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:11,815 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:11,839 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:11,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,864 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:11,869 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:11,898 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:11,923 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:12,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,086 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:12,136 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 13:38:12,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,169 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 13:38:12,180 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:12,217 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-29 13:38:12,218 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,252 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-29 13:38:12,260 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:12,295 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,327 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:12,361 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:12,363 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,387 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:12,395 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:12,416 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:12,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,438 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:12,452 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:12,484 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-29 13:38:12,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,503 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-29 13:38:12,509 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:12,535 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:12,536 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,552 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:12,563 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:12,590 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:12,590 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,618 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:12,624 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:12,651 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:12,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,669 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:12,675 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:12,704 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 13:38:12,705 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,731 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 13:38:12,740 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:12,767 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:12,767 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,787 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 13:38:12,795 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:12,814 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:12,814 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,836 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:12,847 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:12,876 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:12,879 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,930 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:12,940 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:12,969 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:12,970 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:12,993 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:13,001 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:13,047 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:13,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,093 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:13,105 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:13,137 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,166 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:13,196 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,222 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:13,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,272 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:13,296 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 13:38:13,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,314 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 13:38:13,321 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:13,347 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,384 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:13,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,440 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:13,476 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:13,477 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,494 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:13,505 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:13,527 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,567 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:13,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,623 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:13,654 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:13,654 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,677 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:13,687 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:13,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,767 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:13,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,844 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:13,877 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 13:38:13,880 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,914 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 13:38:13,923 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:13,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:13,999 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:14,022 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,059 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:14,090 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:14,091 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,113 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:14,122 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:14,155 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,155 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,187 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,195 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:14,230 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 13:38:14,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,254 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 13:38:14,264 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:14,297 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:14,298 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,332 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:14,343 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:14,377 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,429 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,440 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:14,484 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:14,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,510 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:14,519 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:14,550 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,551 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,576 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 13:38:14,586 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:14,606 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,640 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:14,672 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,707 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:14,739 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:14,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,766 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:14,775 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:14,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,850 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:14,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,903 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:14,922 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:14,960 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:14,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,006 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:15,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,108 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:15,161 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:15,162 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,194 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:15,204 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:15,230 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:15,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,251 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:15,256 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:15,282 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:15,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,301 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:15,309 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:15,334 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,421 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:15,466 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,494 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:15,519 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:15,519 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,537 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:15,551 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:15,585 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,617 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:15,651 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:15,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,675 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:15,684 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:15,713 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,745 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:15,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,801 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:15,836 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,869 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:15,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,937 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:15,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:15,997 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:16,031 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 13:38:16,031 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,052 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 13:38:16,064 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:16,087 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,120 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:16,152 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-29 13:38:16,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,172 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-29 13:38:16,180 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:16,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,243 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:16,274 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,306 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:16,335 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,365 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:16,401 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,446 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:16,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,521 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:16,556 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:16,556 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,587 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:16,599 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:16,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,665 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:16,696 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,734 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:16,763 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:16,763 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,783 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:16,791 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:16,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,856 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:16,884 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:16,885 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,913 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:16,922 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:16,962 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:16,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:16,994 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:17,003 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:17,070 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 13:38:17,071 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,112 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 13:38:17,123 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:17,172 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,225 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:17,268 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:17,269 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,300 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:17,317 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:17,351 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:17,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,377 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:17,387 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:17,419 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,447 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:17,471 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:17,472 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,499 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:17,509 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:17,536 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,601 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:17,659 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,701 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:17,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,767 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:17,792 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,832 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:17,870 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,893 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:17,918 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 13:38:17,918 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:17,943 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 13:38:17,950 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:17,976 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,009 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:18,035 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,066 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:18,091 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,120 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:18,145 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:18,146 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,171 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:18,187 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:18,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,258 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:18,299 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,340 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:18,373 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,417 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:18,440 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,472 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:18,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:38:18,502 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,531 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:18,557 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:18,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,585 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:18,592 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:18,621 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,653 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:18,694 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:18,695 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,717 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:18,723 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:18,782 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:18,783 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,820 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:18,829 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:18,867 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:18,869 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,891 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:18,902 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:18,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:18,954 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:18,985 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:18,985 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,011 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:19,020 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:19,041 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:19,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,067 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:19,073 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:19,099 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,126 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:19,155 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,185 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:19,208 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,239 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:19,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,302 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:19,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,369 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:19,412 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,500 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:19,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,580 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:19,625 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,696 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:19,840 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-29 13:38:19,840 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,876 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-29 13:38:19,887 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:19,922 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:19,922 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:19,946 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 13:38:19,954 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:19,983 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:19,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,013 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 13:38:20,026 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:20,054 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-29 13:38:20,054 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,073 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-29 13:38:20,080 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:20,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,146 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:20,182 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,236 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:20,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,327 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:20,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,402 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:20,439 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,482 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:20,516 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,546 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:20,569 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,599 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:20,631 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:20,634 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,670 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:20,683 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:20,717 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:20,719 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,746 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 13:38:20,755 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:20,800 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:20,801 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:20,826 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:20,837 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:20,948 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 13:38:20,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,001 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 13:38:21,008 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:21,045 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 13:38:21,045 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,086 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 13:38:21,097 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:21,140 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:21,142 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,180 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:21,190 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:21,235 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:21,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,256 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 13:38:21,268 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:21,303 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 13:38:21,303 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,330 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 13:38:21,339 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:21,380 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,437 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:21,489 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:21,490 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,557 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:21,576 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:21,614 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,666 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:21,705 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,751 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:21,797 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:21,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,832 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:21,842 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:21,894 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:21,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:21,937 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:21,954 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:22,002 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:22,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,051 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 13:38:22,066 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:22,109 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:22,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,151 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 13:38:22,161 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:22,206 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 13:38:22,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,244 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 13:38:22,256 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:22,304 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:22,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,338 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:22,350 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:22,383 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-29 13:38:22,390 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,469 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-29 13:38:22,485 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:22,533 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:22,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,573 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 13:38:22,589 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:22,640 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:22,643 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,783 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:22,819 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:22,863 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:22,916 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:22,958 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,013 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-29 13:38:23,051 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,089 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-29 13:38:23,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,170 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-29 13:38:23,204 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,250 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-29 13:38:23,282 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:23,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,319 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 13:38:23,330 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-29 13:38:23,362 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:23,363 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,387 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 13:38:23,399 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-29 13:38:23,429 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:23,430 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,454 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 13:38:23,462 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-29 13:38:23,495 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 13:38:23,497 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,521 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 13:38:23,530 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-29 13:38:23,557 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-29 13:38:23,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,594 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-29 13:38:23,610 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-29 13:38:23,682 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:23,684 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,716 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 13:38:23,727 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-29 13:38:23,766 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,809 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-29 13:38:23,853 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:23,853 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,901 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 13:38:23,906 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-29 13:38:23,931 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:23,959 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-29 13:38:23,983 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 13:38:24,017 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-29 13:38:26,881 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-29 13:38:26,881 - root - INFO - Latest MTPI signal is -1
2025-06-29 13:38:26,881 - root - INFO - Latest MTPI signal is -1, will stay out of market during equity curve calculation
2025-06-29 13:38:26,881 - root - INFO - Finished calculating daily scores. DataFrame shape: (199, 14)
2025-06-29 13:38:26,881 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-06-29 13:38:26,884 - root - INFO - Date ranges for each asset:
2025-06-29 13:38:26,884 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,884 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,885 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,886 - root - INFO - Common dates range: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 13:38:26,888 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-28 (139 candles)
2025-06-29 13:38:26,890 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-29 13:38:26,890 - root - INFO -    Execution Method: candle_close
2025-06-29 13:38:26,890 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-29 13:38:26,891 - root - INFO -    Signal generated and executed immediately
2025-06-29 13:38:26,905 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,905 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,905 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,905 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,905 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,905 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,906 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,912 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,915 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,915 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,915 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,915 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,915 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,915 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,916 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,916 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,916 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,916 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,916 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,917 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,917 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,917 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,924 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,928 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,929 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,929 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,929 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,929 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,930 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,931 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,931 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,938 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,940 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,940 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,941 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,941 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,941 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,941 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,941 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,946 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,952 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,952 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,953 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,953 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,953 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,953 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 13:38:26,956 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,962 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,972 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:26,984 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,055 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,082 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,089 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,105 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,120 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,135 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,140 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,151 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,155 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,165 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,172 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,184 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,189 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,201 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,206 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,217 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,224 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,239 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,251 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,257 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,270 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,278 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,287 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,297 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,304 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,315 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,321 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,331 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,337 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,345 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,353 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,364 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,370 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,377 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,386 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,392 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,402 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,407 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,417 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,424 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,437 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,451 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,458 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,469 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,481 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,487 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,497 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,504 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,516 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,522 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,534 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,540 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,553 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,564 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,572 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,586 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,593 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,603 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,614 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,621 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,633 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,641 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,653 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,665 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:27,665 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-29 13:38:27,665 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-29 13:38:27,665 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-29 13:38:27,666 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,666 - root - INFO -    Buying: ['SOL/USDT']
2025-06-29 13:38:27,666 - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-06-29 13:38:27,672 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,672 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-29 13:38:27,672 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-29 13:38:27,672 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-29 13:38:27,672 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,672 - root - INFO -    Selling: ['SOL/USDT']
2025-06-29 13:38:27,672 - root - INFO -    Buying: ['SUI/USDT']
2025-06-29 13:38:27,672 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-29 13:38:27,686 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,691 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,703 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,717 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,731 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,737 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,749 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,763 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,771 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,784 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,790 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,800 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,806 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,820 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,833 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,840 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,841 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-29 13:38:27,841 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-29 13:38:27,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-29 13:38:27,841 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,841 - root - INFO -    Selling: ['SUI/USDT']
2025-06-29 13:38:27,842 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-29 13:38:27,842 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-29 13:38:27,855 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,867 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,874 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,886 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,900 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,909 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,919 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,931 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,939 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,952 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,959 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,959 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-29 13:38:27,959 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-29 13:38:27,960 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-29 13:38:27,960 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,960 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-29 13:38:27,960 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-29 13:38:27,960 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-29 13:38:27,967 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,972 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,972 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-29 13:38:27,972 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-29 13:38:27,972 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-29 13:38:27,972 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,973 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-29 13:38:27,973 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-29 13:38:27,973 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-29 13:38:27,982 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,987 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:27,987 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-29 13:38:27,987 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-29 13:38:27,987 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-29 13:38:27,987 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:27,987 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-29 13:38:27,987 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-29 13:38:27,989 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-29 13:38:27,998 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,004 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,013 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,019 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,026 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,033 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,039 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,046 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,053 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,060 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,068 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,074 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,083 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,092 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,102 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,109 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,117 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,123 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,134 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,140 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,148 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,153 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,161 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,168 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,179 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,186 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,195 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 13:38:28,195 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-29 13:38:28,195 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-29 13:38:28,195 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-29 13:38:28,196 - root - INFO -    Execution Delay: 0 hours
2025-06-29 13:38:28,196 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-29 13:38:28,204 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,215 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,223 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,234 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,240 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,247 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,253 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 13:38:28,286 - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-06-29 13:38:28,286 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-29 13:38:28,286 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-29 13:38:28,287 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-29 13:38:28,287 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-29 13:38:28,287 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-29 13:38:28,287 - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-06-29 13:38:28,287 - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-06-29 13:38:28,288 - root - INFO - Strategy execution completed in 1s
2025-06-29 13:38:28,289 - root - INFO - DEBUG: self.elapsed_time = 1.4064650535583496 seconds
2025-06-29 13:38:28,299 - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-29 13:38:28,299 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-29 13:38:28,299 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-29 13:38:28,300 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-29 13:38:28,300 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-29 13:38:28,301 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-29 13:38:28,303 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,305 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,307 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,309 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,310 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,311 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,313 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,315 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,316 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,317 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,320 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,322 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,323 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,324 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 13:38:28,327 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 199 points
2025-06-29 13:38:28,328 - root - INFO - ETH/USDT B&H total return: -8.48%
2025-06-29 13:38:28,331 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 199 points
2025-06-29 13:38:28,331 - root - INFO - BTC/USDT B&H total return: 10.13%
2025-06-29 13:38:28,333 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 199 points
2025-06-29 13:38:28,334 - root - INFO - SOL/USDT B&H total return: -24.82%
2025-06-29 13:38:28,336 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 199 points
2025-06-29 13:38:28,336 - root - INFO - SUI/USDT B&H total return: -12.88%
2025-06-29 13:38:28,337 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 199 points
2025-06-29 13:38:28,337 - root - INFO - XRP/USDT B&H total return: -9.83%
2025-06-29 13:38:28,339 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 199 points
2025-06-29 13:38:28,340 - root - INFO - AAVE/USDT B&H total return: 2.89%
2025-06-29 13:38:28,342 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 199 points
2025-06-29 13:38:28,342 - root - INFO - AVAX/USDT B&H total return: -30.00%
2025-06-29 13:38:28,345 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 199 points
2025-06-29 13:38:28,346 - root - INFO - ADA/USDT B&H total return: -20.60%
2025-06-29 13:38:28,347 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 199 points
2025-06-29 13:38:28,348 - root - INFO - LINK/USDT B&H total return: -28.81%
2025-06-29 13:38:28,349 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 199 points
2025-06-29 13:38:28,350 - root - INFO - TRX/USDT B&H total return: 11.58%
2025-06-29 13:38:28,351 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 199 points
2025-06-29 13:38:28,351 - root - INFO - PEPE/USDT B&H total return: -0.42%
2025-06-29 13:38:28,353 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 199 points
2025-06-29 13:38:28,354 - root - INFO - DOGE/USDT B&H total return: -35.94%
2025-06-29 13:38:28,356 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 199 points
2025-06-29 13:38:28,356 - root - INFO - BNB/USDT B&H total return: 4.84%
2025-06-29 13:38:28,360 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 199 points
2025-06-29 13:38:28,361 - root - INFO - DOT/USDT B&H total return: -29.68%
2025-06-29 13:38:28,363 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:28,373 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:28,390 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-29 13:38:28,501 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:28,613 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:28,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:38:30,309 - root - INFO - Added ETH/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,311 - root - INFO - Added BTC/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,311 - root - INFO - Added SOL/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added SUI/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added XRP/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added AAVE/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added AVAX/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added ADA/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added LINK/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added TRX/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,312 - root - INFO - Added PEPE/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,313 - root - INFO - Added DOGE/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,313 - root - INFO - Added BNB/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,313 - root - INFO - Added DOT/USDT buy-and-hold curve with 199 points
2025-06-29 13:38:30,313 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-29 13:38:30,313 - root - INFO -   - ETH/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,314 - root - INFO -   - BTC/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,314 - root - INFO -   - SOL/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,314 - root - INFO -   - SUI/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,314 - root - INFO -   - XRP/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,314 - root - INFO -   - AAVE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,315 - root - INFO -   - AVAX/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,315 - root - INFO -   - ADA/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,315 - root - INFO -   - LINK/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,315 - root - INFO -   - TRX/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,315 - root - INFO -   - PEPE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,316 - root - INFO -   - DOGE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,316 - root - INFO -   - BNB/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,316 - root - INFO -   - DOT/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,329 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-29 13:38:30,329 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-29 13:38:30,331 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-29 13:38:30,331 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 13:38:30,338 - root - INFO - Configuration loaded successfully.
2025-06-29 13:38:30,338 - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 13:38:30,338 - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 13:38:30,338 - root - INFO - Combination method: consensus
2025-06-29 13:38:30,338 - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-29 13:38:30,338 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-29 13:38:30,354 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 13:38:30,355 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 13:38:30,355 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (after filtering).
2025-06-29 13:38:30,355 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 13:38:30,356 - root - INFO - Fetched BTC data: 2140 candles from 2019-08-20 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 13:38:30,356 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-29 13:38:30,578 - root - INFO - Generated PGO Score signals: {-1: 992, 0: 34, 1: 1114}
2025-06-29 13:38:30,579 - root - INFO - PGO signal: -1
2025-06-29 13:38:30,579 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-29 13:38:30,654 - root - INFO - Generated BB Score signals: {-1: 993, 0: 33, 1: 1114}
2025-06-29 13:38:30,654 - root - INFO - Bollinger Bands signal: -1
2025-06-29 13:38:34,709 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-29 13:38:34,710 - root - INFO - DWMA Score signal: -1
2025-06-29 13:38:35,348 - root - INFO - Generated DEMA Supertrend signals
2025-06-29 13:38:35,348 - root - INFO - Signal distribution: {-1: 1257, 0: 1, 1: 882}
2025-06-29 13:38:35,348 - root - INFO - Generated DEMA Super Score signals
2025-06-29 13:38:35,349 - root - INFO - DEMA Super Score signal: -1
2025-06-29 13:38:36,389 - root - INFO - Generated DPSD signals
2025-06-29 13:38:36,389 - root - INFO - Signal distribution: {-1: 1083, 0: 87, 1: 970}
2025-06-29 13:38:36,389 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-29 13:38:36,389 - root - INFO - DPSD Score signal: -1
2025-06-29 13:38:36,454 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-29 13:38:36,455 - root - INFO - Generated AAD Score signals using SMA method
2025-06-29 13:38:36,455 - root - INFO - AAD Score signal: -1
2025-06-29 13:38:36,951 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-29 13:38:36,951 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-29 13:38:36,952 - root - INFO - Dynamic EMA Score signal: -1
2025-06-29 13:38:37,869 - root - INFO - Quantile DEMA Score signal: -1
2025-06-29 13:38:37,869 - root - INFO - Individual signals: {'pgo': -1, 'bollinger_bands': -1, 'dwma_score': -1, 'dema_super_score': -1, 'dpsd_score': -1, 'aad_score': -1, 'dynamic_ema_score': -1, 'quantile_dema_score': -1}
2025-06-29 13:38:37,869 - root - INFO - Combined MTPI signal (consensus): -1
2025-06-29 13:38:37,869 - root - INFO - MTPI Score: -1.000000
2025-06-29 13:38:37,869 - root - INFO - Added current MTPI score to results: -1.000000 (using 1d timeframe)
2025-06-29 13:38:37,871 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-29 13:38:37,872 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 6.0)
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 7.0)
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 3.0)
2025-06-29 13:38:37,872 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 4.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 2.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-29 13:38:37,873 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-29 13:38:37,874 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-29 13:38:37,874 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-29 13:38:37,874 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-29 13:38:37,874 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 13:38:37,875 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:37,876 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 13:38:37,880 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250622_run_20250629_133800.csv
2025-06-29 13:38:37,881 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250622_run_20250629_133800.csv
2025-06-29 13:38:37,881 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-29 13:38:37,881 - root - INFO - Results type: <class 'dict'>
2025-06-29 13:38:37,881 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-29 13:38:37,881 - root - INFO - Success flag set to: True
2025-06-29 13:38:37,882 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-29 13:38:37,882 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 199 entries
2025-06-29 13:38:37,882 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-29 13:38:37,882 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 199 entries
2025-06-29 13:38:37,882 - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 259 entries
2025-06-29 13:38:37,882 - root - INFO -   - mtpi_score: <class 'float'>
2025-06-29 13:38:37,882 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 199 entries
2025-06-29 13:38:37,882 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-29 13:38:37,883 - root - INFO -   - metrics_file: <class 'str'>
2025-06-29 13:38:37,883 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-29 13:38:37,883 - root - INFO -   - success: <class 'bool'>
2025-06-29 13:38:37,883 - root - INFO -   - message: <class 'str'>
2025-06-29 13:38:37,883 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-29 13:38:37,883 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-06-29 13:38:37,884 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-24 00:00:00+00:00    
2025-06-25 00:00:00+00:00    
2025-06-26 00:00:00+00:00    
2025-06-27 00:00:00+00:00    
2025-06-28 00:00:00+00:00    
dtype: object
2025-06-29 13:38:37,884 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 13:38:37,884 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:37,884 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-29 13:38:37,884 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-29 13:38:37,884 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 13:38:37,884 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:37,885 - root - INFO - MTPI signal is -1, staying out of the market
2025-06-29 13:38:37,885 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset:  (MTPI signal: -1)
2025-06-29 13:38:37,885 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-29 13:38:37,885 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-29 13:38:37,885 - root - ERROR - [DEBUG] NO TIE - CONFIRMED SELECTION: 
2025-06-29 13:38:37,885 - root - ERROR - [DEBUG] NO TIE - Single winner: 
2025-06-29 13:38:37,902 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-29 13:38:37,902 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 13:38:37,902 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-29 13:38:37,903 - root - INFO - [DEBUG]   - Best asset selected: 
2025-06-29 13:38:37,903 - root - INFO - [DEBUG]   - Assets held: {}
2025-06-29 13:38:37,903 - root - INFO - [DEBUG]   - MTPI signal: -1
2025-06-29 13:38:37,903 - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-06-29 13:38:37,904 - root - INFO - MTPI signal is bearish (-1). Exiting all positions.
2025-06-29 13:38:38,047 - root - INFO - Attempting to exit position for TRX/EUR in live mode
2025-06-29 13:38:38,047 - root - INFO - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-29 13:38:38,047 - root - INFO - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-29 13:38:38,047 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-29 13:38:38,052 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-29 13:38:38,052 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-29 13:38:38,272 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-29 13:38:38,273 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-29 13:38:38,312 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-29 13:38:38,313 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1751197112607, 'datetime': '2025-06-29T11:38:32.607Z', 'high': 0.23621, 'low': 0.23327, 'bid': 0.23473, 'bidVolume': 3239.393485, 'ask': 0.23478, 'askVolume': 32901.741982, 'vwap': 0.2348754961982464, 'open': 0.23587, 'close': 0.23474, 'last': 0.23474, 'previousClose': None, 'change': -0.00113, 'percentage': -0.4790774579217365, 'average': 0.235305, 'baseVolume': 586158.331344, 'quoteVolume': 137674.22892515812, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1751110712607, 'timestamp': 1751197112607, 'open': '0.23587', 'openTimestamp': 1751110813183, 'high': '0.23621', 'low': '0.23327', 'last': '0.23474', 'closeTimestamp': 1751197074494, 'bid': '0.2347300', 'bidSize': '3239.393485', 'ask': '0.2347800', 'askSize': '32901.741982', 'volume': '586158.331344', 'volumeQuote': '137674.22892515813'}, 'indexPrice': None, 'markPrice': None}
2025-06-29 13:38:38,313 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23474
2025-06-29 13:38:38,462 - root - INFO - Current position for TRX/EUR: 84.35307700 units (entry price: 0.00000000)
2025-06-29 13:38:38,465 - root - INFO - Loaded market info for 176 trading pairs
2025-06-29 13:38:38,466 - root - INFO - Potential P&L: 19.80104129 (0.00%)
2025-06-29 13:38:38,466 - root - INFO - Executing live market sell order for TRX/EUR
2025-06-29 13:38:38,503 - root - INFO - Adjusted sell amount for TRX/EUR: 84.35307700 -> 84.35307700
2025-06-29 13:38:38,625 - root - INFO - Created market sell order: TRX/EUR, amount: 84.353077, avg price: 0.23473
2025-06-29 13:38:38,625 - root - INFO - Order fee: 0.05019776421 EUR
2025-06-29 13:38:38,626 - root - INFO - Filled amount: 84.353077
2025-06-29 13:38:38,626 - root - INFO - Filled amount: 84.35307700 TRX
2025-06-29 13:38:38,626 - root - INFO - Order fee: 0.05019776 EUR
2025-06-29 13:38:38,626 - root - INFO - Actual P&L: 19.80019776 (0.00%)
2025-06-29 13:38:38,626 - root - INFO - Successfully exited position: TRX/EUR, amount: 84.35307700, price: 0.23473000
2025-06-29 13:38:38,629 - root - INFO - Trade executed: SELL TRX/EUR, amount=84.35307700, price=0.23473000, filled=84.35307700
2025-06-29 13:38:38,629 - root - INFO -   Fee: 0.05019776 EUR
2025-06-29 13:38:38,630 - root - INFO - Exit all positions result logged to trade log file
2025-06-29 13:38:38,630 - root - INFO - All positions exited successfully
2025-06-29 13:38:38,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:38:38,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 13:38:38,727 - root - INFO - Asset selection logged: 0 assets selected with single_asset allocation
2025-06-29 13:38:38,727 - root - INFO - Asset scores (sorted by score):
2025-06-29 13:38:38,728 - root - INFO -   BTC/EUR: score=13.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,728 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,729 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,729 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,729 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,730 - root - INFO -   SOL/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,730 - root - INFO -   ETH/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,730 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,730 - root - INFO -   AVAX/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,731 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,731 - root - INFO -   SUI/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,731 - root - INFO -   ADA/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,731 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,731 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-29 13:38:38,732 - root - INFO - Rejected assets:
2025-06-29 13:38:38,732 - root - INFO -   BTC/EUR: reason=Failed to trade, score=13.0, rank=1
2025-06-29 13:38:38,732 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 13.0) - Failed to trade
2025-06-29 13:38:38,732 - root - INFO - Asset selection logged with 14 assets scored and 0 assets selected
2025-06-29 13:38:38,732 - root - INFO - Extracted asset scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 13:38:38,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 13:38:38,772 - root - INFO - Strategy execution completed successfully in 38.74 seconds
2025-06-29 13:38:38,776 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-29 13:38:48,679 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:38:58,692 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:08,705 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:18,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:28,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:38,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:48,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:39:58,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:08,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:18,816 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:28,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:38,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:48,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:40:58,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:08,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:18,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:28,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:38,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:48,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:41:58,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:08,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:18,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:28,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:38,990 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:49,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:42:59,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:09,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:19,035 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:29,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:39,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:49,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:43:59,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:09,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:19,117 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:29,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:39,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:49,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:44:59,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:09,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:19,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:29,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:39,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:49,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:45:59,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:09,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:19,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:29,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:39,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:49,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:46:59,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:09,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:19,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:29,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:39,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:49,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:47:59,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:09,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:19,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:29,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:39,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:49,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:48:59,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:09,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:19,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:29,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:39,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:49,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:49:59,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:09,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:19,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:29,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:39,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:49,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:50:59,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:09,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:19,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:29,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:39,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:49,753 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:51:59,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:09,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:19,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:29,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:39,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:49,835 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:52:59,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:09,865 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:19,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:29,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:39,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:49,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:53:59,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:54:09,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:54:19,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:54:29,971 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:54:39,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:54:50,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:00,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:10,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:20,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:30,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:40,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:55:50,108 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:00,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:10,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:20,153 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:30,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:40,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:56:50,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:00,199 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:10,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:20,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:30,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:40,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:57:50,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:00,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:10,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:20,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:30,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:40,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:58:50,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:00,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:10,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:20,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:30,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:40,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 13:59:50,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:00,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:10,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:20,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:30,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:40,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:00:50,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:00,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:10,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:20,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:30,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:40,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:01:50,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:00,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:10,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:20,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:30,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:40,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:02:50,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:00,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:10,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:20,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:30,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:40,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:03:50,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:00,748 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:10,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:20,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:30,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:40,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:04:50,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:00,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:10,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:20,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:30,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:40,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:05:50,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:00,903 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:10,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:20,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:30,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:40,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:06:50,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:00,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:10,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:20,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:31,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:41,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:07:51,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:01,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:11,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:21,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:31,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:41,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:08:51,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:01,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:11,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:21,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:31,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:41,172 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:09:51,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:01,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:11,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:21,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:31,231 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:41,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:10:51,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:01,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:11,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:21,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:31,350 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:41,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:11:51,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:01,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:11,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:21,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:31,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:41,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:12:51,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:01,573 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:11,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:21,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:31,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:41,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:13:51,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:01,656 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:11,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:21,683 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:31,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:41,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:14:51,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:01,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:11,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:21,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:31,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:41,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:15:51,830 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:01,842 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:11,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:21,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:31,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:41,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:16:51,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:01,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:11,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:21,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:31,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:41,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:17:52,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:02,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:12,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:22,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:32,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:42,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:18:52,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:02,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:12,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:22,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:32,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:42,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:19:52,218 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:02,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:12,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:22,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:32,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:42,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:20:52,348 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:02,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:12,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:22,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:32,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:42,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:21:52,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:02,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:12,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:22,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:32,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:42,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:22:52,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:02,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:12,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:22,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:32,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:42,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:23:52,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:02,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:12,625 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:22,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:32,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:42,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:24:52,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:02,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:12,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:22,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:32,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:42,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:25:52,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:26:02,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:26:10,715 - root - INFO - Received signal 2, shutting down...
2025-06-29 14:26:12,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:26:16,693 - root - INFO - Network watchdog stopped
2025-06-29 14:26:16,693 - root - INFO - Network watchdog stopped
2025-06-29 14:26:16,693 - root - INFO - Background service stopped
2025-06-29 14:26:16,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
