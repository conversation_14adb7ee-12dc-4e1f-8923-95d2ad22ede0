#!/usr/bin/env python3
"""
Test script to verify MTPI indicator selection functionality.
This script tests the new command line arguments for selecting MTPI indicators.
"""

import subprocess
import sys
import os

def run_backtest_with_indicators(indicators, combination_method=None, long_threshold=None, short_threshold=None):
    """Run a backtest with specific MTPI indicators."""
    
    # Base command
    cmd = [
        sys.executable, "main_program.py",
        "--assets", "BTC/USDT", "ETH/USDT", "SOL/USDT", "SUI/USDT",
        "--trend-method", "PGO For Loop",
        "--weighted",
        "--weights", "0.8", "0.2",
        "--timeframe", "1d",
        "--analysis-start-date", "2023-10-19",
        "--n-assets", "2"
    ]
    
    # Add MTPI indicator selection
    if indicators:
        cmd.extend(["--mtpi-indicators"] + indicators)
    
    if combination_method:
        cmd.extend(["--mtpi-combination-method", combination_method])
    
    if long_threshold is not None:
        cmd.extend(["--mtpi-long-threshold", str(long_threshold)])
    
    if short_threshold is not None:
        cmd.extend(["--mtpi-short-threshold", str(short_threshold)])
    
    print(f"Running command: {' '.join(cmd)}")
    print("=" * 80)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        print(f"\nReturn code: {result.returncode}")
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Command timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Test different MTPI indicator configurations."""
    
    print("Testing MTPI Indicator Selection Functionality")
    print("=" * 80)
    
    # Test 1: Single indicator (PGO only)
    print("\n1. Testing with PGO indicator only:")
    success1 = run_backtest_with_indicators(["pgo"])
    
    # Test 2: Two indicators with consensus method
    print("\n2. Testing with PGO + Bollinger Bands (consensus):")
    success2 = run_backtest_with_indicators(
        ["pgo", "bollinger_bands"], 
        combination_method="consensus"
    )
    
    # Test 3: Multiple indicators with custom thresholds
    print("\n3. Testing with multiple indicators and custom thresholds:")
    success3 = run_backtest_with_indicators(
        ["pgo", "bollinger_bands", "dwma_score"], 
        combination_method="consensus",
        long_threshold=0.2,
        short_threshold=-0.2
    )
    
    # Test 4: All available indicators
    print("\n4. Testing with all available indicators:")
    all_indicators = [
        "pgo", "bollinger_bands", "dwma_score", "median_score", 
        "dema_super_score", "dpsd_score", "aad_score", 
        "dynamic_ema_score", "quantile_dema_score"
    ]
    success4 = run_backtest_with_indicators(
        all_indicators,
        combination_method="consensus"
    )
    
    # Test 5: Default behavior (no indicator selection - should use YAML config)
    print("\n5. Testing default behavior (no indicator selection):")
    success5 = run_backtest_with_indicators(None)
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY:")
    print(f"1. PGO only: {'✓ PASS' if success1 else '✗ FAIL'}")
    print(f"2. PGO + BB (consensus): {'✓ PASS' if success2 else '✗ FAIL'}")
    print(f"3. Multiple indicators + thresholds: {'✓ PASS' if success3 else '✗ FAIL'}")
    print(f"4. All indicators: {'✓ PASS' if success4 else '✗ FAIL'}")
    print(f"5. Default behavior: {'✓ PASS' if success5 else '✗ FAIL'}")
    
    total_passed = sum([success1, success2, success3, success4, success5])
    print(f"\nOverall: {total_passed}/5 tests passed")
    
    if total_passed == 5:
        print("🎉 All tests passed! MTPI indicator selection is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
