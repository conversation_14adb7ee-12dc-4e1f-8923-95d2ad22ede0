#!/usr/bin/env python3
"""
Advanced Indicators Module - MTPI Components

This module contains advanced technical indicators for the MTPI system:
- DWMA Score (Double Weighted Moving Average) - already implemented in dwma_score.py
- Median Score - already implemented in median_score.py  
- DEMA Super Score (Double Exponential Moving Average Supertrend)

These indicators provide sophisticated trend analysis and signal generation
for the 8-indicator MTPI aggregated signal system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple

# Import MTPI Score indicators (consistent naming)
from .pgo_score import generate_pgo_score_signal, DEFAULT_PGO_SCORE_CONFIG
from .bb_score import generate_bb_score_signal, DEFAULT_BB_SCORE_CONFIG
from .dwma_score import generate_dwma_signal
from .median_score import generate_median_score_signal, DEFAULT_MEDIAN_SCORE_CONFIG
from .dema_supertrend import generate_dema_supertrend_signal, DEFAULT_DEMA_SUPERTREND_CONFIG
from .dpsd_score import generate_dpsd_signal, DEFAULT_DPSD_CONFIG
from .aad_score import generate_aad_score_signal, DEFAULT_AAD_SCORE_CONFIG
from .dynamic_ema_score import generate_dynamic_ema_score_signal, DEFAULT_DYNAMIC_EMA_SCORE_CONFIG

# Define default config for DWMA since it's not exported from the module
DEFAULT_DWMA_SCORE_CONFIG = {
    'smoothing_style': 'ATR',
    'src_col': 'close',
    'length': 2,
    'ma_type': 'EMA',
    'ma_smooth_length': 12,
    'atr_period': 12,
    'atr_multiplier': 1.0
}

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def generate_dema_super_score_signal(df: pd.DataFrame, **kwargs) -> pd.Series:
    """
    Generate DEMA Super Score signal for MTPI integration.

    This is a wrapper function that calls the DEMA Supertrend indicator
    with the appropriate parameters for the MTPI system.

    Args:
        df: DataFrame with OHLCV data
        **kwargs: Parameters for DEMA Supertrend calculation

    Returns:
        Series with signals: 1 (Long), -1 (Short), 0 (Neutral)
    """
    try:
        # Extract parameters with defaults matching the PRD configuration
        atr_period = kwargs.get('atr_period', 19)      # Supertrend length
        multiplier = kwargs.get('multiplier', 2.8)     # Supertrend multiplier
        dema_length = kwargs.get('dema_length', 17)    # DEMA length
        src_col = kwargs.get('src_col', 'close')       # Source column (matches PineScript 'src')

        # Generate DEMA Supertrend signal
        signal = generate_dema_supertrend_signal(
            df=df,
            atr_period=atr_period,
            multiplier=multiplier,
            dema_length=dema_length,
            src_col=src_col
        )

        logging.info(f"Generated DEMA Super Score signals")
        return signal

    except Exception as e:
        logging.error(f"Error generating DEMA Super Score signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def generate_dpsd_score_signal(df: pd.DataFrame, **kwargs) -> pd.Series:
    """
    Generate DPSD (DEMA Percentile Standard Deviation) Score signal for MTPI integration.

    Args:
        df: DataFrame with OHLCV data
        **kwargs: Parameters for DPSD calculation

    Returns:
        Series with signals: 1 (Long), -1 (Short), 0 (Neutral)
    """
    try:
        # Extract parameters with defaults matching user specifications
        dema_length = kwargs.get('dema_length', 9)           # DEMA Length
        percentile_length = kwargs.get('percentile_length', 58)  # Percentile Length
        sd_length = kwargs.get('sd_length', 27)              # SD Length
        ema_length = kwargs.get('ema_length', 14)            # EMA Length
        percentile_upper = kwargs.get('percentile_upper', 60.0)  # Upper percentile
        percentile_lower = kwargs.get('percentile_lower', 45.0)  # Lower percentile
        src_col = kwargs.get('src_col', 'close')             # Source column

        # Convert percentile_upper/percentile_lower to pertype format
        pertype = f"{int(percentile_upper)}/{int(percentile_lower)}"

        # Map src_col to dema_src (DPSD function expects 'dema_src' parameter)
        dema_src = src_col

        # Generate DPSD signal
        signal = generate_dpsd_signal(
            df=df,
            dema_length=dema_length,
            dema_src=dema_src,
            percentile_length=percentile_length,
            pertype=pertype,
            sd_length=sd_length,
            ema_length=ema_length,
            include_ema=True
        )

        logging.info(f"Generated DPSD Score signals with pertype={pertype}")
        return signal

    except Exception as e:
        logging.error(f"Error generating DPSD Score signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

# Registry of MTPI Score indicators
ADVANCED_INDICATORS_REGISTRY = {
    'pgo_score': {
        'function': generate_pgo_score_signal,
        'default_config': DEFAULT_PGO_SCORE_CONFIG,
        'description': 'Price Gap Oscillator Score with crossover detection'
    },
    'bb_score': {
        'function': generate_bb_score_signal,
        'default_config': DEFAULT_BB_SCORE_CONFIG,
        'description': 'Bollinger Bands Score with position percentage and persistence'
    },
    'dwma_score': {
        'function': generate_dwma_signal,
        'default_config': DEFAULT_DWMA_SCORE_CONFIG,
        'description': 'Distance Weighted Moving Average Score with multiple smoothing methods'
    },
    'median_score': {
        'function': generate_median_score_signal,
        'default_config': DEFAULT_MEDIAN_SCORE_CONFIG,
        'description': 'Median Supertrend Score with percentile-based smoothing'
    },
    'dema_super_score': {
        'function': generate_dema_super_score_signal,
        'default_config': DEFAULT_DEMA_SUPERTREND_CONFIG,
        'description': 'DEMA Supertrend Score with double exponential moving average'
    },
    'dpsd_score': {
        'function': generate_dpsd_score_signal,
        'default_config': DEFAULT_DPSD_CONFIG,
        'description': 'DEMA Percentile Standard Deviation Score with percentile analysis'
    },
    'aad_score': {
        'function': generate_aad_score_signal,
        'default_config': DEFAULT_AAD_SCORE_CONFIG,
        'description': 'Average Absolute Deviation Trend Score with volatility-based bands'
    },
    'dynamic_ema_score': {
        'function': generate_dynamic_ema_score_signal,
        'default_config': DEFAULT_DYNAMIC_EMA_SCORE_CONFIG,
        'description': 'Dynamic Median EMA Score with adaptive smoothing and efficiency ratio'
    }
}

def get_advanced_indicator(name: str) -> Optional[Dict[str, Any]]:
    """
    Get advanced indicator configuration by name.
    
    Args:
        name: Name of the indicator
        
    Returns:
        Dictionary with indicator function and configuration, or None if not found
    """
    return ADVANCED_INDICATORS_REGISTRY.get(name)

def list_advanced_indicators() -> list:
    """
    List all available advanced indicators.
    
    Returns:
        List of indicator names
    """
    return list(ADVANCED_INDICATORS_REGISTRY.keys())

def calculate_advanced_indicator_signal(df: pd.DataFrame, 
                                      indicator_name: str, 
                                      **kwargs) -> pd.Series:
    """
    Calculate signal for any advanced indicator.
    
    Args:
        df: DataFrame with OHLCV data
        indicator_name: Name of the indicator to calculate
        **kwargs: Parameters for the indicator
        
    Returns:
        Series with signals: 1 (Long), -1 (Short), 0 (Neutral)
    """
    try:
        indicator_config = get_advanced_indicator(indicator_name)
        if indicator_config is None:
            raise ValueError(f"Unknown advanced indicator: {indicator_name}")
        
        # Get the indicator function
        indicator_function = indicator_config['function']
        
        # Merge default config with provided kwargs
        default_config = indicator_config['default_config'].copy()
        default_config.update(kwargs)
        
        # Calculate signal
        signal = indicator_function(df, **default_config)
        
        logging.info(f"Calculated {indicator_name} signal successfully")
        return signal
        
    except Exception as e:
        logging.error(f"Error calculating {indicator_name} signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

# Default configurations for MTPI integration
DEFAULT_ADVANCED_INDICATORS_CONFIG = {
    'dwma_score': {
        'smoothing_style': 'ATR',
        'src_col': 'close',
        'length': 2,
        'ma_type': 'EMA',
        'ma_smooth_length': 12,
        'atr_period': 12,
        'atr_multiplier': 1.0
    },
    'median_score': {
        'atr_period': 10,
        'multiplier': 1.55,
        'median_length': 7,
        'src_col': 'close'
    },
    'dema_super_score': {
        'atr_period': 19,        # subject - Supertrend Len
        'multiplier': 2.8,       # mul - Supertrend Multiple
        'dema_length': 17,       # demalen - Dema len
        'src_col': 'close'       # src - main price series (matches PineScript)
    },
    'dpsd_score': {
        'dema_length': 9,           # DEMA Length
        'percentile_length': 58,    # Percentile Length
        'sd_length': 27,            # SD Length
        'ema_length': 14,           # EMA Length
        'percentile_upper': 60.0,   # Upper percentile
        'percentile_lower': 45.0,   # Lower percentile
        'src_col': 'close'          # Source column
    },
    'aad_score': {
        'src_col': 'close',         # Source column for AAD calculation
        'length': 22,               # AAD calculation length
        'aad_mult': 1.2,            # AAD multiplier for band calculation
        'avg_type': 'SMA'           # Average type for AAD calculation
    },
    'dynamic_ema_score': {
        'median_length': 9,         # Median calculation length
        'median_src': 'close',      # Source for median calculation
        'ema_length': 12,           # Dynamic EMA calculation length
        'smoothing_style': 'Weighted SD',  # Smoothing style
        'sd_length': 33,            # Standard deviation length
        'upper_sd_weight': 1.017,   # Upper SD weight
        'lower_sd_weight': 0.996,   # Lower SD weight
        'atr_period': 14,           # ATR period
        'atr_multiplier': 1.2       # ATR multiplier
    }
}
