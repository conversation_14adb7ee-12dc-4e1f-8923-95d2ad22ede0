#!/usr/bin/env python
"""
Test script to simulate how the frontend processes API data.
This script:
1. Loads API response data (either from a file or generated)
2. Simulates the frontend processing logic
3. Identifies potential issues in the data transformation

Usage:
    python test_frontend_processing.py [--input-file INPUT_FILE] [--output-dir OUTPUT_DIR]
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('frontend_processing_test')

class FrontendProcessingTester:
    """Test how the frontend processes API data."""

    def __init__(self, output_dir='frontend_test_output'):
        """Initialize the tester."""
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_api_data(self, input_file=None):
        """
        Load API response data from a file or generate it.

        Args:
            input_file: Path to the API response JSON file

        Returns:
            Dict containing the API response data
        """
        if input_file and os.path.exists(input_file):
            logger.info(f"Loading API data from {input_file}...")
            with open(input_file, 'r') as f:
                api_data = json.load(f)
            return api_data

        logger.info("No input file provided or file not found. Generating sample API data...")

        # Try to use the ApiDataStructureTester to generate data
        try:
            from test_api_data_structure import ApiDataStructureTester
            tester = ApiDataStructureTester(output_dir=self.output_dir)
            _, api_data = tester.run_test(run_strategy=False)
            return api_data
        except ImportError:
            logger.warning("Could not import ApiDataStructureTester. Using basic sample data.")

        # Generate basic sample API data
        return self.generate_sample_api_data()

    def generate_sample_api_data(self):
        """
        Generate basic sample API data.

        Returns:
            Dict containing sample API data
        """
        # Generate sample timestamps (one per day for the last 100 days)
        now = datetime.now()
        timestamps = []
        values = []

        for i in range(100):
            date = now - (100 - i) * timedelta(days=1)
            timestamps.append(int(date.timestamp()))
            values.append(10000 * (1 + i * 0.01))  # Simple increasing value

        # Create sample API data structure
        api_data = {
            'curves': {
                'strategy': [{'time': t, 'value': v} for t, v in zip(timestamps, values)]
            },
            'buyHoldCurves': {
                'BTC/USDT': [{'time': t, 'value': v * 0.9} for t, v in zip(timestamps, values)],
                'ETH/USDT': [{'time': t, 'value': v * 0.8} for t, v in zip(timestamps, values)],
                'SOL/USDT': [{'time': t, 'value': v * 1.1} for t, v in zip(timestamps, values)]
            },
            'assetChanges': [
                {'time': timestamps[i], 'asset': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'][i % 3]}
                for i in range(0, 100, 10)  # Change asset every 10 days
            ],
            'metadata': {
                'startDate': (now - 100 * timedelta(days=1)).strftime('%Y-%m-%d'),
                'endDate': now.strftime('%Y-%m-%d'),
                'rawMtpiSignals': [
                    {'time': timestamps[i], 'signal': 1.0 if i % 30 < 25 else -1.0}
                    for i in range(100)
                ]
            },
            'performanceMetrics': {
                'Total Return': {
                    'Strategy': 100.0,
                    'BTC/USDT': 90.0,
                    'ETH/USDT': 80.0,
                    'SOL/USDT': 110.0
                },
                'Sharpe Ratio': {
                    'Strategy': 1.5,
                    'BTC/USDT': 1.2,
                    'ETH/USDT': 1.0,
                    'SOL/USDT': 1.8
                }
            }
        }

        return api_data

    def simulate_frontend_processing(self, api_data):
        """
        Simulate how the frontend would process the API data.

        Args:
            api_data: The API response data

        Returns:
            Dict containing the processed data as it would be used in the frontend
        """
        logger.info("Simulating frontend processing...")

        # Create the frontend data structure
        frontend_data = {
            'curves': {},
            'metadata': api_data.get('metadata', {})
        }

        # Process strategy data
        if 'curves' in api_data and 'strategy' in api_data['curves']:
            strategy_data = api_data['curves']['strategy']
            logger.info(f"Processing strategy data with {len(strategy_data)} points")

            # In the frontend, we might need to ensure the data is sorted by time
            strategy_data.sort(key=lambda x: x.get('time', 0))

            # Add the strategy data to the frontend data
            frontend_data['curves']['strategy'] = strategy_data

        # Process buy and hold curves
        if 'buyHoldCurves' in api_data:
            for asset, data in api_data['buyHoldCurves'].items():
                logger.info(f"Processing {asset} data with {len(data)} points")

                # Sort the data by time
                data.sort(key=lambda x: x.get('time', 0))

                # Add the asset data to the frontend data
                frontend_data['curves'][asset] = data

        # Process asset changes
        if 'assetChanges' in api_data:
            asset_changes = api_data['assetChanges']
            logger.info(f"Processing asset changes with {len(asset_changes)} points")

            # Sort the asset changes by time
            asset_changes.sort(key=lambda x: x.get('time', 0))

            # Add the asset changes to the frontend data
            frontend_data['assetChanges'] = asset_changes

        # Process performance metrics
        if 'performanceMetrics' in api_data:
            frontend_data['performanceMetrics'] = api_data['performanceMetrics']

        # Save frontend data to JSON file
        frontend_file = os.path.join(self.output_dir, 'frontend_processed.json')
        with open(frontend_file, 'w') as f:
            json.dump(frontend_data, f, indent=2)
        logger.info(f"Saved frontend processed data to {frontend_file}")

        return frontend_data

    def analyze_frontend_data(self, frontend_data):
        """
        Analyze the frontend data and identify potential issues.

        Args:
            frontend_data: The processed frontend data
        """
        logger.info("\n=== FRONTEND DATA ANALYSIS ===")

        # Check if we have strategy data
        if 'curves' in frontend_data and 'strategy' in frontend_data['curves']:
            strategy_data = frontend_data['curves']['strategy']
            logger.info(f"Strategy data points: {len(strategy_data)}")

            if strategy_data:
                # Check the structure of the strategy data
                first_point = strategy_data[0]
                last_point = strategy_data[-1]

                logger.info(f"Strategy data point structure: {list(first_point.keys())}")
                logger.info(f"First strategy point: {first_point}")
                logger.info(f"Last strategy point: {last_point}")

                # Check for time gaps
                if len(strategy_data) > 1:
                    times = [point.get('time', 0) for point in strategy_data]
                    time_diffs = [times[i+1] - times[i] for i in range(len(times)-1)]
                    avg_diff = sum(time_diffs) / len(time_diffs)
                    max_diff = max(time_diffs)

                    logger.info(f"Average time between points: {avg_diff} seconds")
                    logger.info(f"Maximum time between points: {max_diff} seconds")

                    # Check for unusually large gaps
                    large_gaps = [(i, time_diffs[i]) for i in range(len(time_diffs)) if time_diffs[i] > 2 * avg_diff]
                    if large_gaps:
                        logger.warning(f"Found {len(large_gaps)} unusually large time gaps:")
                        for i, gap in large_gaps[:5]:  # Show at most 5 gaps
                            gap_start = datetime.fromtimestamp(times[i]).strftime('%Y-%m-%d')
                            gap_end = datetime.fromtimestamp(times[i+1]).strftime('%Y-%m-%d')
                            logger.warning(f"  Gap of {gap} seconds between {gap_start} and {gap_end}")

                # Check for value jumps
                if len(strategy_data) > 1:
                    values = [point.get('value', 0) for point in strategy_data]
                    value_changes = [abs(values[i+1] - values[i]) / values[i] * 100 for i in range(len(values)-1) if values[i] != 0]
                    avg_change = sum(value_changes) / len(value_changes)
                    max_change = max(value_changes)

                    logger.info(f"Average value change: {avg_change:.2f}%")
                    logger.info(f"Maximum value change: {max_change:.2f}%")

                    # Check for unusually large value changes
                    large_changes = [(i, value_changes[i]) for i in range(len(value_changes)) if value_changes[i] > 5 * avg_change]
                    if large_changes:
                        logger.warning(f"Found {len(large_changes)} unusually large value changes:")
                        for i, change in large_changes[:5]:  # Show at most 5 changes
                            change_date = datetime.fromtimestamp(times[i]).strftime('%Y-%m-%d')
                            logger.warning(f"  Change of {change:.2f}% on {change_date}")
        else:
            logger.warning("No strategy data found in frontend data")

        # Check if we have asset data
        assets = [key for key in frontend_data.get('curves', {}).keys() if key != 'strategy']
        if assets:
            logger.info(f"Asset data available for: {assets}")

            for asset in assets:
                asset_data = frontend_data['curves'][asset]
                logger.info(f"{asset} data points: {len(asset_data)}")

                if asset_data:
                    # Check the structure of the asset data
                    first_point = asset_data[0]
                    last_point = asset_data[-1]

                    logger.info(f"{asset} data point structure: {list(first_point.keys())}")
                    logger.info(f"{asset} first point: {first_point}")
                    logger.info(f"{asset} last point: {last_point}")
        else:
            logger.warning("No asset data found in frontend data")

        # Check if we have asset changes
        if 'assetChanges' in frontend_data:
            asset_changes = frontend_data['assetChanges']
            logger.info(f"Asset changes: {len(asset_changes)}")

            if asset_changes:
                # Count occurrences of each asset
                asset_counts = {}
                for change in asset_changes:
                    asset = change.get('asset', '')
                    asset_counts[asset] = asset_counts.get(asset, 0) + 1

                logger.info(f"Asset occurrences: {asset_counts}")
        else:
            logger.warning("No asset changes found in frontend data")

        # Check if we have MTPI signals
        if 'metadata' in frontend_data and 'rawMtpiSignals' in frontend_data['metadata']:
            mtpi_signals = frontend_data['metadata']['rawMtpiSignals']
            logger.info(f"MTPI signals: {len(mtpi_signals)}")

            if mtpi_signals:
                # Count occurrences of each signal value
                signal_counts = {}
                for signal in mtpi_signals:
                    value = signal.get('signal', 0)
                    signal_counts[value] = signal_counts.get(value, 0) + 1

                logger.info(f"MTPI signal values: {signal_counts}")
        else:
            logger.warning("No MTPI signals found in frontend data")

        logger.info("===============================\n")

    def run_test(self, input_file=None):
        """
        Run the complete frontend processing test.

        Args:
            input_file: Path to the API response JSON file

        Returns:
            Tuple of (api_data, frontend_data)
        """
        # Step 1: Load API data
        api_data = self.load_api_data(input_file)

        # Step 2: Simulate frontend processing
        frontend_data = self.simulate_frontend_processing(api_data)

        # Step 3: Analyze frontend data
        self.analyze_frontend_data(frontend_data)

        return api_data, frontend_data

def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(description='Test frontend processing of API data')
    parser.add_argument('--input-file', type=str, help='Path to the API response JSON file')
    parser.add_argument('--output-dir', type=str, default='frontend_test_output',
                        help='Directory to save output files')
    args = parser.parse_args()

    tester = FrontendProcessingTester(output_dir=args.output_dir)
    api_data, frontend_data = tester.run_test(input_file=args.input_file)

    logger.info("Frontend processing test completed successfully!")

    # Return data for potential further analysis
    return api_data, frontend_data

if __name__ == '__main__':
    main()
