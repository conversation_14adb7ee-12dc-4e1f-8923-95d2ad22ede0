#!/usr/bin/env python3

import pandas as pd

# Test dictionary order preservation in pandas Series.to_dict()
print("Testing dictionary order preservation...")

# Create a dictionary with specific order (BTC before TRX)
original_dict = {
    'ETH/EUR': 8.0, 
    'BTC/EUR': 12.0, 
    'SOL/EUR': 6.0, 
    'SUI/EUR': 2.0, 
    'XRP/EUR': 9.0, 
    'AAVE/EUR': 9.0, 
    'AVAX/EUR': 3.0, 
    'ADA/EUR': 3.0, 
    'LINK/EUR': 6.0, 
    'TRX/EUR': 12.0, 
    'PEPE/EUR': 0.0, 
    'DOGE/EUR': 3.0, 
    'BNB/EUR': 11.0, 
    'DOT/EUR': 1.0
}

print(f"Original dict keys order: {list(original_dict.keys())}")

# Create a pandas Series from this dictionary
series = pd.Series(original_dict)
print(f"Series index order: {list(series.index)}")

# Convert back to dictionary
converted_dict = series.to_dict()
print(f"Converted dict keys order: {list(converted_dict.keys())}")

# Check if BTC comes before TRX
btc_pos = list(converted_dict.keys()).index('BTC/EUR')
trx_pos = list(converted_dict.keys()).index('TRX/EUR')
print(f"BTC position: {btc_pos}, TRX position: {trx_pos}")
print(f"BTC comes before TRX: {btc_pos < trx_pos}")

# Test tie-breaking logic
max_score = max(converted_dict.values())
print(f"Max score: {max_score}")

tied_assets = [asset for asset, score in converted_dict.items() if score == max_score]
print(f"Tied assets: {tied_assets}")
print(f"First tied asset (winner): {tied_assets[0]}")
