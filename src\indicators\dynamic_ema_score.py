#!/usr/bin/env python3
"""
Dynamic Median EMA Score Indicator - MTPI Component #8

Based on TradingView PineScript implementation:
- Uses median smoothing with percentile rank at 50%
- Applies Dynamic EMA with efficiency ratio and adaptive smoothing
- Two smoothing styles: ATR and Weighted SD
- Parameters: Median Length=9, EMA Length=12, Source=close

Signal Logic:
- ATR Style: Long/Short based on crossover/crossunder with ATR bands
- Weighted SD Style: Long/Short based on crossover/crossunder with weighted SD bands
- Maintains state persistence for signal generation
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_percentile_median(src: pd.Series, length: int = 9) -> pd.Series:
    """
    Calculate median using percentile rank at 50%.
    Matches PineScript: ta.percentile_nearest_rank(s_me, len, 50)
    
    Args:
        src: Source data series
        length: Period for median calculation
        
    Returns:
        Median-smoothed series
    """
    try:
        result = pd.Series(index=src.index, dtype=float)
        
        for i in range(len(src)):
            if i < length - 1:
                # For initial values, use available data
                window_data = src.iloc[:i+1].values
            else:
                # Use full window
                window_data = src.iloc[i-length+1:i+1].values
            
            # Sort the window data
            sorted_data = np.sort(window_data)
            n = len(sorted_data)
            
            # Calculate nearest-rank for 50th percentile (median)
            # Formula: rank = ceil(P/100 * N) where P=50 for median
            rank = int(np.ceil(0.5 * n))
            
            # Convert to 0-indexed and get value
            median_value = sorted_data[rank - 1]
            result.iloc[i] = median_value
        
        return result
        
    except Exception as e:
        logging.error(f"Error calculating percentile median: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_dynamic_ema(source: pd.Series, length: int = 12) -> pd.Series:
    """
    Calculate Dynamic EMA with efficiency ratio and adaptive smoothing.
    Matches PineScript DynamicEMA function.
    
    Args:
        source: Source data series
        length: EMA length
        
    Returns:
        Dynamic EMA series
    """
    try:
        # Constants from PineScript
        fast_end = 2 / (2 + 1)  # 2 / (2 + 1) = 0.6667
        slow_end = 2 / (30 + 1)  # 2 / (30 + 1) = 0.0645
        
        # Initialize result series
        result = pd.Series(index=source.index, dtype=float)
        
        # Calculate RMA (Running Moving Average) - equivalent to EMA with alpha = 1/length
        rma = source.ewm(alpha=1.0/length, adjust=False).mean()
        
        for i in range(length, len(source)):
            # Calculate change over the length period
            change = abs(source.iloc[i] - source.iloc[i - length])
            
            # Calculate volatility as sum of absolute changes over length period
            volatility = 0.0
            for j in range(1, length + 1):
                if i - j >= 0:
                    volatility += abs(source.iloc[i - j + 1] - source.iloc[i - j])
            
            # Calculate efficiency ratio
            efficiency_ratio = change / volatility if volatility > 0 else 0
            
            # Calculate smooth factor
            smooth_factor = (efficiency_ratio * (fast_end - slow_end) + slow_end) ** 2
            
            # Calculate Dynamic EMA
            # ta.rma(source, length) + smooth_factor * (source - ta.rma(source, length))
            dynamic_ema = rma.iloc[i] + smooth_factor * (source.iloc[i] - rma.iloc[i])
            result.iloc[i] = dynamic_ema
        
        # Fill initial values with RMA
        for i in range(min(length, len(source))):
            result.iloc[i] = rma.iloc[i]
        
        return result
        
    except Exception as e:
        logging.error(f"Error calculating Dynamic EMA: {e}")
        return pd.Series(index=source.index, dtype=float)

def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range.
    
    Args:
        df: DataFrame with OHLCV data
        period: Period for ATR calculation
        
    Returns:
        ATR series
    """
    try:
        high = df['high']
        low = df['low'] 
        close = df['close']
        
        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # ATR is the moving average of True Range
        atr = true_range.rolling(window=period, min_periods=1).mean()
        
        return atr
        
    except Exception as e:
        logging.error(f"Error calculating ATR: {e}")
        return pd.Series(index=df.index, dtype=float)

def calculate_dynamic_ema_score(df: pd.DataFrame,
                               median_length: int = 9,
                               median_src: str = 'close',
                               ema_length: int = 12,
                               smoothing_style: str = 'Weighted SD',
                               # Weighted SD parameters
                               sd_length: int = 33,
                               upper_sd_weight: float = 1.017,
                               lower_sd_weight: float = 0.996,
                               # ATR parameters
                               atr_period: int = 14,
                               atr_multiplier: float = 1.2) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate Dynamic Median EMA Score based on the provided PineScript implementation.
    
    Args:
        df: DataFrame with OHLCV data
        median_length: Length for median calculation
        median_src: Source column for median calculation
        ema_length: Length for Dynamic EMA calculation
        smoothing_style: 'ATR' or 'Weighted SD'
        sd_length: Standard deviation length
        upper_sd_weight: Upper SD weight
        lower_sd_weight: Lower SD weight
        atr_period: ATR period
        atr_multiplier: ATR multiplier
        
    Returns:
        Tuple of (signal, dwmas, upper_band, lower_band)
    """
    try:
        # Step 1: Calculate median smoothing
        src = df[median_src]
        subject_d = calculate_percentile_median(src, median_length)
        
        # Step 2: Calculate Dynamic EMA
        dwmas_d = calculate_dynamic_ema(subject_d, ema_length)
        
        # Step 3: Calculate bands based on smoothing style
        if smoothing_style == 'ATR':
            # ATR style bands
            atr = calculate_atr(df, atr_period)
            m1 = atr_multiplier * atr  # M1 = ATRs(Per,Mult)
            
            upper_band = dwmas_d + m1  # atrlD
            lower_band = dwmas_d - m1  # atrsD
            
        else:  # Weighted SD
            # Weighted SD style bands
            sd_d = dwmas_d.rolling(window=sd_length, min_periods=1).std()
            
            upper_band = (dwmas_d + sd_d) * upper_sd_weight  # sdlD
            lower_band = (dwmas_d - sd_d) * lower_sd_weight  # sdsD
        
        # Step 4: Generate signals based on crossovers
        close_price = df['close']
        signal = pd.Series(0, index=df.index, dtype=int)
        
        # Calculate crossovers
        close_prev = close_price.shift(1)
        upper_prev = upper_band.shift(1)
        lower_prev = lower_band.shift(1)
        
        # Crossover detection
        crossover_upper = (close_prev <= upper_prev) & (close_price > upper_band)  # L1D or L2D
        crossunder_lower = (close_prev >= lower_prev) & (close_price < lower_band)  # S1D or S2D
        
        # Apply signal logic with state persistence (matching PineScript var logic)
        current_signal = 0
        for i in range(len(df)):
            if crossover_upper.iloc[i] and not crossunder_lower.iloc[i]:
                current_signal = 1  # Long signal
            elif crossunder_lower.iloc[i]:
                current_signal = -1  # Short signal
            # Keep previous signal if no crossover
            
            signal.iloc[i] = current_signal
        
        logging.info(f"Calculated Dynamic EMA Score with {smoothing_style} style")
        
        return signal, dwmas_d, upper_band, lower_band
        
    except Exception as e:
        logging.error(f"Error calculating Dynamic EMA score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float))

def generate_dynamic_ema_score_signal(df: pd.DataFrame,
                                     median_length: int = 9,
                                     median_src: str = 'close',
                                     ema_length: int = 12,
                                     smoothing_style: str = 'Weighted SD',
                                     # Weighted SD parameters
                                     sd_length: int = 33,
                                     upper_sd_weight: float = 1.017,
                                     lower_sd_weight: float = 0.996,
                                     # ATR parameters
                                     atr_period: int = 14,
                                     atr_multiplier: float = 1.2) -> pd.Series:
    """
    Generate Dynamic EMA Score binary signal for MTPI integration.

    Args:
        df: DataFrame with OHLCV data
        median_length: Length for median calculation
        median_src: Source column for median calculation
        ema_length: Length for Dynamic EMA calculation
        smoothing_style: 'ATR' or 'Weighted SD'
        sd_length: Standard deviation length
        upper_sd_weight: Upper SD weight
        lower_sd_weight: Lower SD weight
        atr_period: ATR period
        atr_multiplier: ATR multiplier

    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        signal, _, _, _ = calculate_dynamic_ema_score(
            df=df,
            median_length=median_length,
            median_src=median_src,
            ema_length=ema_length,
            smoothing_style=smoothing_style,
            sd_length=sd_length,
            upper_sd_weight=upper_sd_weight,
            lower_sd_weight=lower_sd_weight,
            atr_period=atr_period,
            atr_multiplier=atr_multiplier
        )

        logging.info(f"Generated Dynamic EMA Score signals using {smoothing_style} method")
        return signal

    except Exception as e:
        logging.error(f"Error generating Dynamic EMA Score signals: {e}")
        return pd.Series(0, index=df.index, dtype=int)

# Default configuration for MTPI integration
DEFAULT_DYNAMIC_EMA_SCORE_CONFIG = {
    'median_length': 9,
    'median_src': 'close',
    'ema_length': 12,
    'smoothing_style': 'Weighted SD',
    'sd_length': 33,
    'upper_sd_weight': 1.017,
    'lower_sd_weight': 0.996,
    'atr_period': 14,
    'atr_multiplier': 1.2
}

# Export the main function for MTPI integration
__all__ = ['generate_dynamic_ema_score_signal', 'calculate_dynamic_ema_score', 'DEFAULT_DYNAMIC_EMA_SCORE_CONFIG']
