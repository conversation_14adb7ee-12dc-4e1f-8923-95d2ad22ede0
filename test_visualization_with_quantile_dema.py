#!/usr/bin/env python3
"""
Test script to verify the visualization works with the new Quantile DEMA Trend indicator.

This script:
1. Tests the configuration loading with quantile_dema_score
2. Tests the visualization script with the new indicator
3. Verifies the indicator is properly integrated
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config
from src.config_manager import load_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_config_loading():
    """Test that the configuration loads correctly with quantile_dema_score."""
    print("=" * 60)
    print("TESTING CONFIGURATION LOADING")
    print("=" * 60)
    
    try:
        # Test basic config loading
        print("Loading basic configuration...")
        basic_config = load_config()
        print(f"✅ Basic config loaded successfully")
        
        # Test MTPI multi-indicator config loading
        print("\nLoading MTPI multi-indicator configuration...")
        mtpi_config = load_mtpi_multi_indicator_config()
        print(f"✅ MTPI config loaded successfully")
        
        # Check enabled indicators
        enabled_indicators = mtpi_config.get('enabled_indicators', [])
        print(f"\nEnabled indicators: {enabled_indicators}")
        
        # Check if quantile_dema_score is configured
        if 'quantile_dema_score' in mtpi_config:
            print("✅ quantile_dema_score configuration found:")
            quantile_config = mtpi_config['quantile_dema_score']
            for key, value in quantile_config.items():
                print(f"   {key}: {value}")
        else:
            print("❌ quantile_dema_score configuration not found")
            return False
        
        # Check if it's enabled
        if 'quantile_dema_score' in enabled_indicators:
            print("✅ quantile_dema_score is enabled in the configuration")
        else:
            print("ℹ️  quantile_dema_score is not enabled (check YAML file)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indicator_import():
    """Test that the quantile_dema_score indicator can be imported and used."""
    print("\n" + "=" * 60)
    print("TESTING INDICATOR IMPORT")
    print("=" * 60)
    
    try:
        # Test importing the indicator
        print("Importing quantile_dema_score indicator...")
        from src.indicators.quantile_dema_score import generate_quantile_dema_score_signal
        print("✅ Successfully imported generate_quantile_dema_score_signal")
        
        # Test importing from visualization script
        print("Testing import in visualization script...")
        from visualize_mtpi_signals import calculate_mtpi_signals_series
        print("✅ Successfully imported visualization functions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_integration():
    """Test that the visualization script works with the new indicator."""
    print("\n" + "=" * 60)
    print("TESTING VISUALIZATION INTEGRATION")
    print("=" * 60)
    
    try:
        # Import visualization functions
        from visualize_mtpi_signals import fetch_btc_data_with_signals
        
        print("Testing data fetching with signals...")
        
        # Test with a small dataset
        btc_df, signals_data, combined_signal, config = fetch_btc_data_with_signals(
            timeframe='1d',
            limit=50,  # Small dataset for testing
            start_date=None
        )
        
        if btc_df is None:
            print("❌ Failed to fetch data")
            return False
        
        print(f"✅ Successfully fetched {len(btc_df)} candles")
        print(f"✅ Combined signal: {combined_signal}")
        
        if signals_data is not None:
            signals_series, scores_series = signals_data
            print(f"✅ Generated {len(signals_series)} signal points")
            print(f"✅ Generated {len(scores_series)} score points")
        else:
            print("❌ Failed to generate signals")
            return False
        
        # Check configuration
        enabled_indicators = config.get('enabled_indicators', [])
        print(f"✅ Enabled indicators in visualization: {enabled_indicators}")
        
        if 'quantile_dema_score' in enabled_indicators:
            print("✅ quantile_dema_score is active in visualization")
        else:
            print("ℹ️  quantile_dema_score is not enabled in current configuration")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing visualization integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_current_config():
    """Show the current configuration for reference."""
    print("\n" + "=" * 60)
    print("CURRENT CONFIGURATION SUMMARY")
    print("=" * 60)
    
    try:
        config = load_mtpi_multi_indicator_config()
        
        print("Enabled indicators:")
        enabled = config.get('enabled_indicators', [])
        for i, indicator in enumerate(enabled, 1):
            print(f"  {i}. {indicator}")
        
        print(f"\nCombination method: {config.get('combination_method', 'N/A')}")
        print(f"Long threshold: {config.get('long_threshold', 'N/A')}")
        print(f"Short threshold: {config.get('short_threshold', 'N/A')}")
        
        if 'quantile_dema_score' in config:
            print("\nQuantile DEMA Score parameters:")
            qd_config = config['quantile_dema_score']
            for key, value in qd_config.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"Error showing configuration: {e}")

def main():
    """Run all tests."""
    print("🚀 Testing Quantile DEMA Trend Indicator Integration")
    print("=" * 80)
    
    results = []
    
    # Test 1: Configuration loading
    results.append(test_config_loading())
    
    # Test 2: Indicator import
    results.append(test_indicator_import())
    
    # Test 3: Visualization integration
    results.append(test_visualization_integration())
    
    # Show current configuration
    show_current_config()
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Quantile DEMA Trend indicator is properly integrated.")
        print("\n📋 Next steps:")
        print("1. Enable quantile_dema_score in config/settings.yaml if desired")
        print("2. Run: python visualize_mtpi_signals.py")
        print("3. Check the generated visualization")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
