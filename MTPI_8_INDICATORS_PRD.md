# MTPI 8-Indicator Aggregated Signal System - Product Requirements Document (PRD)

## 1. Executive Summary

### 1.1 Project Overview
Enhance the existing MTPI (Market Trend Position Indicator) signal system by expanding from the current 2 indicators (PGO and Bollinger Bands) to a comprehensive 8-indicator aggregated signal system. This will provide more robust market trend detection and reduce whipsaws through signal consensus mechanisms.

### 1.2 Current State
- **Existing Indicators**: PGO (Price Gap Oscillator) and Bollinger Bands
- **Signal Generation**: Individual and combined signals using various combination methods (consensus, primary, equal_weight)
- **Integration**: Fully integrated with asset rotation strategy and backtesting system
- **Configuration**: Flexible parameter configuration through YAML files

### 1.3 Objectives
- Add 6 additional reliable technical indicators
- Create configurable indicator selection system
- Maintain backward compatibility with existing strategies
- Implement robust signal aggregation methods
- Preserve existing signal path to strategies and backtesting

## 2. Technical Requirements

### 2.1 Selected 8 Indicators to Implement

#### 2.1.1 Existing Indicators (Enhanced)
1. **PGO (Price Gap Oscillator)** (Already implemented)
   - Parameters: `length`, `upper_threshold`, `lower_threshold`
   - Signal: Binary (1/-1/0) based on crossover detection

2. **Bollinger Bands** (Already implemented)
   - Parameters: `length`, `multiplier`, `long_threshold`, `short_threshold`
   - Signal: Binary based on position between bands

#### 2.1.2 Technical Analysis Indicators
3. **DWMA Score (Double Weighted Moving Average)**
   - Parameters: `length`, `threshold`
   - Signal: Binary based on price position relative to DWMA

4. **Median Score**
   - Parameters: `length`, `threshold`
   - Signal: Binary based on price position relative to median

5. **DEMA Super Score (Double Exponential Moving Average)**
   - Parameters: `length`, `multiplier`, `threshold`
   - Signal: Binary based on DEMA trend and momentum

#### 2.1.3 Advanced Momentum Indicators
6. **DPSD Score (Directional Price Strength Divergence)**
   - Parameters: `length`, `smoothing`, `threshold`
   - Signal: Binary based on price strength and divergence analysis

7. **AAD Trend Score (Absolute Average Deviation)**
   - Parameters: `length`, `deviation_threshold`
   - Signal: Binary based on price deviation from average

8. **Dynamic EMA Score**
   - Parameters: `fast_length`, `slow_length`, `adaptive_factor`
   - Signal: Binary based on adaptive EMA crossover and momentum

### 2.2 Configuration System

#### 2.2.1 Indicator Selection Configuration
```yaml
mtpi_indicators:
  enabled_indicators:
    - pgo
    - bollinger_bands
    - dwma_score
    - median_score
    - dema_super_score
    - dpsd_score
    - aad_trend_score
    - dynamic_ema_score

  # Individual indicator parameters
  pgo:
    length: 35
    upper_threshold: 1.35
    lower_threshold: -1.0

  bollinger_bands:
    length: 33
    multiplier: 2.0
    long_threshold: 76.0
    short_threshold: 31.0
    use_heikin_ashi: true
    heikin_src: 'close'

  dwma_score:
    length: 21
    threshold: 0.0
    smoothing_factor: 2.0

  median_score:
    length: 20
    threshold: 0.0
    deviation_multiplier: 1.5

  dema_super_score:
    length: 21
    multiplier: 2.0
    threshold: 0.0
    momentum_factor: 1.2

  dpsd_score:
    length: 14
    smoothing: 3
    threshold: 0.0
    strength_factor: 1.0

  aad_trend_score:
    length: 20
    deviation_threshold: 1.5
    trend_sensitivity: 1.0

  dynamic_ema_score:
    fast_length: 12
    slow_length: 26
    adaptive_factor: 2.0
    momentum_threshold: 0.0
```

#### 2.2.2 Signal Aggregation Configuration
```yaml
mtpi_aggregation:
  method: 'weighted_consensus'  # Options: consensus, weighted_consensus, majority, equal_weight
  
  # Weights for weighted_consensus method
  indicator_weights:
    pgo: 1.5
    bollinger_bands: 1.3
    dwma_score: 1.2
    median_score: 1.0
    dema_super_score: 1.4
    dpsd_score: 1.1
    aad_trend_score: 1.0
    dynamic_ema_score: 1.3
  
  # Consensus thresholds
  consensus_threshold: 0.6  # 60% of indicators must agree
  strong_consensus_threshold: 0.8  # 80% for strong signals
  
  # Minimum indicators required
  min_indicators_required: 3
```

### 2.3 Implementation Architecture

#### 2.3.1 File Structure
```
src/
├── indicators/
│   ├── __init__.py
│   ├── base_indicators.py (existing)
│   ├── custom_indicators.py (existing)
│   ├── advanced_indicators.py (new - DWMA, Median, DEMA Super)
│   ├── momentum_indicators.py (new - DPSD, Dynamic EMA)
│   ├── trend_indicators.py (new - AAD Trend)
│   └── indicator_registry.py (new)
├── MTPI_signal_handler.py (enhanced)
└── config_manager.py (enhanced)
```

#### 2.3.2 Core Classes and Functions

##### 2.3.2.1 Indicator Registry System
```python
class IndicatorRegistry:
    """Registry for all available MTPI indicators"""
    
    def register_indicator(self, name: str, calculator_func: callable, 
                          signal_generator_func: callable, default_params: dict)
    
    def get_indicator(self, name: str) -> IndicatorConfig
    
    def list_available_indicators(self) -> List[str]
    
    def validate_indicator_config(self, name: str, params: dict) -> bool
```

##### ******* Enhanced Signal Handler
```python
def calculate_aggregated_mtpi_signal(
    df: pd.DataFrame,
    enabled_indicators: List[str],
    indicator_configs: Dict[str, dict],
    aggregation_method: str = 'weighted_consensus',
    aggregation_params: dict = None
) -> pd.Series:
    """Calculate aggregated MTPI signal from multiple indicators"""

def fetch_multi_indicator_mtpi_signal(
    timeframe: str = '1d',
    enabled_indicators: List[str] = None,
    limit: int = 100,
    skip_warmup: bool = False
) -> Optional[int]:
    """Fetch and calculate multi-indicator MTPI signal"""
```

## 3. Implementation Plan

### 3.1 Phase 1: Core Infrastructure (Week 1)
- [ ] Create new indicator modules (momentum, trend, volume)
- [ ] Implement IndicatorRegistry system
- [ ] Enhance configuration management for multi-indicator setup
- [ ] Update MTPI_signal_handler.py with aggregation framework

### 3.2 Phase 2: Advanced Indicators Implementation (Week 2-3)
- [ ] Implement DWMA Score (Double Weighted Moving Average)
- [ ] Implement Median Score
- [ ] Implement DEMA Super Score (Double Exponential Moving Average)
- [ ] Implement DPSD Score (Directional Price Strength Divergence)
- [ ] Implement AAD Trend Score (Absolute Average Deviation)
- [ ] Implement Dynamic EMA Score

### 3.3 Phase 3: Integration & Testing (Week 4)
- [ ] Create comprehensive unit tests for all new indicators
- [ ] Implement signal aggregation methods
- [ ] Create indicator validation and testing framework
- [ ] Performance optimization and caching

### 3.4 Phase 4: Integration & Validation (Week 5)
- [ ] Integrate with existing strategy system
- [ ] Backward compatibility testing
- [ ] Performance benchmarking
- [ ] Documentation and examples

## 4. Signal Aggregation Methods

### 4.1 Consensus Method
- Requires majority agreement (>50%) for signal generation
- Neutral when no consensus reached

### 4.2 Weighted Consensus Method
- Each indicator has configurable weight
- Weighted average determines final signal
- Thresholds for long/short/neutral signals

### 4.3 Majority Method
- Simple majority vote
- Ties result in neutral signal

### 4.4 Strong Consensus Method
- Requires supermajority (configurable, default 75%)
- More conservative approach to reduce false signals

## 5. Testing Strategy

### 5.1 Individual Indicator Testing
- Unit tests for each indicator calculation
- Comparison with TradingView/PineScript implementations
- Edge case handling (insufficient data, NaN values)

### 5.2 Signal Aggregation Testing
- Test all aggregation methods
- Validate signal consistency
- Performance impact assessment

### 5.3 Integration Testing
- Backward compatibility with existing strategies
- End-to-end signal flow testing
- Backtesting validation

### 5.4 Performance Testing
- Calculation speed benchmarks
- Memory usage optimization
- Scalability testing with multiple timeframes

## 6. Configuration Examples

### 6.1 Conservative Setup (High Consensus)
```yaml
mtpi_indicators:
  enabled_indicators: [pgo, bollinger_bands, dema_super_score, dynamic_ema_score, aad_trend_score]
mtpi_aggregation:
  method: 'strong_consensus'
  consensus_threshold: 0.8
```

### 6.2 Aggressive Setup (Lower Threshold)
```yaml
mtpi_indicators:
  enabled_indicators: [pgo, bollinger_bands, dwma_score, median_score, dema_super_score, dpsd_score, aad_trend_score, dynamic_ema_score]
mtpi_aggregation:
  method: 'majority'
  consensus_threshold: 0.5
```

### 6.3 Weighted Setup (Custom Weights)
```yaml
mtpi_indicators:
  enabled_indicators: [pgo, bollinger_bands, dema_super_score, dynamic_ema_score]
mtpi_aggregation:
  method: 'weighted_consensus'
  indicator_weights:
    pgo: 2.0
    bollinger_bands: 1.5
    dema_super_score: 1.8
    dynamic_ema_score: 1.2
```

## 7. Success Criteria

### 7.1 Technical Success Criteria
- [ ] All 8 indicators implemented and tested
- [ ] Signal aggregation working correctly
- [ ] Backward compatibility maintained
- [ ] Performance impact < 20% increase in calculation time
- [ ] Configuration system fully functional

### 7.2 Quality Criteria
- [ ] 95%+ test coverage for new code
- [ ] All indicators match reference implementations
- [ ] Documentation complete and accurate
- [ ] No breaking changes to existing API

### 7.3 Business Success Criteria
- [ ] Reduced whipsaw signals in backtesting
- [ ] Improved risk-adjusted returns
- [ ] Configurable system allows strategy optimization
- [ ] System ready for production deployment

## 8. Risk Mitigation

### 8.1 Technical Risks
- **Risk**: Performance degradation with 8 indicators
- **Mitigation**: Implement caching and optimize calculations

- **Risk**: Configuration complexity
- **Mitigation**: Provide preset configurations and validation

### 8.2 Integration Risks
- **Risk**: Breaking existing strategies
- **Mitigation**: Maintain backward compatibility, extensive testing

- **Risk**: Signal quality degradation
- **Mitigation**: Individual indicator validation, A/B testing

## 9. Future Enhancements

### 9.1 Advanced Features
- Machine learning-based indicator weighting
- Dynamic threshold adjustment
- Multi-timeframe signal aggregation
- Custom indicator plugin system

### 9.2 Monitoring and Analytics
- Signal performance tracking
- Indicator contribution analysis
- Real-time signal quality metrics
- Alert system for signal anomalies

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Author**: Asset Rotation Strategy Team  
**Review Status**: Draft
