#!/usr/bin/env python3
"""
Comprehensive Test Runner
=========================

This script runs all the PGO and scoring mechanism tests in sequence
to thoroughly investigate the discrepancy between test_allocation.py 
and allocation_report.py implementations.
"""

import os
import sys
import subprocess
import logging
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTestRunner:
    """Run all tests and collect results"""
    
    def __init__(self):
        self.test_files = [
            'test_pgo_methods.py',
            'test_pgo_comparison.py', 
            'test_scoring_mechanism.py'
        ]
        
        self.results = {}
        
    def run_test(self, test_file):
        """Run a single test file"""
        logger.info(f"=== RUNNING {test_file} ===")
        
        try:
            # Run the test
            result = subprocess.run(
                [sys.executable, test_file],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            self.results[test_file] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
            if result.returncode == 0:
                logger.info(f"✓ {test_file} completed successfully")
            else:
                logger.error(f"✗ {test_file} failed with return code {result.returncode}")
                logger.error(f"STDERR: {result.stderr}")
                
            # Log key output
            if result.stdout:
                logger.info(f"Key output from {test_file}:")
                # Show last 20 lines of output
                lines = result.stdout.split('\n')
                for line in lines[-20:]:
                    if line.strip():
                        logger.info(f"  {line}")
                        
        except subprocess.TimeoutExpired:
            logger.error(f"✗ {test_file} timed out after 5 minutes")
            self.results[test_file] = {
                'returncode': -1,
                'stdout': '',
                'stderr': 'Test timed out',
                'success': False
            }
            
        except Exception as e:
            logger.error(f"✗ {test_file} failed with exception: {e}")
            self.results[test_file] = {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }
            
    def run_all_tests(self):
        """Run all tests in sequence"""
        logger.info("=== STARTING COMPREHENSIVE TEST SUITE ===")
        logger.info(f"Running {len(self.test_files)} test files...")
        
        for test_file in self.test_files:
            if os.path.exists(test_file):
                self.run_test(test_file)
            else:
                logger.error(f"Test file {test_file} not found!")
                self.results[test_file] = {
                    'returncode': -1,
                    'stdout': '',
                    'stderr': 'File not found',
                    'success': False
                }
                
    def generate_summary_report(self):
        """Generate a summary report of all test results"""
        logger.info("=== GENERATING SUMMARY REPORT ===")
        
        successful_tests = [test for test, result in self.results.items() if result['success']]
        failed_tests = [test for test, result in self.results.items() if not result['success']]
        
        logger.info(f"Test Results Summary:")
        logger.info(f"  Total tests: {len(self.test_files)}")
        logger.info(f"  Successful: {len(successful_tests)}")
        logger.info(f"  Failed: {len(failed_tests)}")
        
        if successful_tests:
            logger.info(f"  Successful tests:")
            for test in successful_tests:
                logger.info(f"    ✓ {test}")
                
        if failed_tests:
            logger.info(f"  Failed tests:")
            for test in failed_tests:
                logger.info(f"    ✗ {test}")
                
        # Save detailed report
        self.save_detailed_report()
        
    def save_detailed_report(self):
        """Save detailed test report to file"""
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w') as f:
            f.write("COMPREHENSIVE TEST REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now()}\n\n")
            
            for test_file, result in self.results.items():
                f.write(f"\n{'='*20} {test_file} {'='*20}\n")
                f.write(f"Success: {result['success']}\n")
                f.write(f"Return Code: {result['returncode']}\n")
                
                if result['stdout']:
                    f.write(f"\nSTDOUT:\n{result['stdout']}\n")
                    
                if result['stderr']:
                    f.write(f"\nSTDERR:\n{result['stderr']}\n")
                    
        logger.info(f"Detailed report saved to: {report_file}")
        
    def collect_generated_files(self):
        """Collect and summarize all files generated by tests"""
        logger.info("=== COLLECTING GENERATED FILES ===")
        
        # List of expected output files
        expected_files = [
            'pgo_comparison_test.log',
            'pgo_comparison_detailed.csv',
            'scoring_mechanism_test.log',
            'detailed_tester_scores.csv',
            'detailed_reporter_scores.csv',
            'detailed_score_comparison.csv',
            'pgo_methods_test.log',
            'tester_calculate_scores.py',
            'reporter_calculate_scores.py',
            'minimal_test_data.csv'
        ]
        
        found_files = []
        missing_files = []
        
        for file_name in expected_files:
            if os.path.exists(file_name):
                found_files.append(file_name)
                file_size = os.path.getsize(file_name)
                logger.info(f"  ✓ {file_name} ({file_size} bytes)")
            else:
                missing_files.append(file_name)
                
        if missing_files:
            logger.warning(f"Missing expected files:")
            for file_name in missing_files:
                logger.warning(f"  ✗ {file_name}")
                
        # Look for any additional generated files
        current_files = [f for f in os.listdir('.') if f.endswith(('.csv', '.log', '.py', '.txt'))]
        additional_files = [f for f in current_files if f not in expected_files and f not in self.test_files]
        
        if additional_files:
            logger.info(f"Additional generated files:")
            for file_name in additional_files:
                if os.path.getsize(file_name) > 0:  # Only show non-empty files
                    logger.info(f"  + {file_name}")
                    
    def analyze_key_findings(self):
        """Analyze key findings from all tests"""
        logger.info("=== ANALYZING KEY FINDINGS ===")
        
        # Check if we have the detailed score comparison
        if os.path.exists('detailed_score_comparison.csv'):
            try:
                import pandas as pd
                comparison_df = pd.read_csv('detailed_score_comparison.csv')
                
                # Find rows with significant differences
                diff_columns = [col for col in comparison_df.columns if col.endswith('_diff')]
                
                if diff_columns:
                    max_diffs = comparison_df[diff_columns].max()
                    logger.info(f"Maximum differences found:")
                    for col in diff_columns:
                        asset = col.replace('_diff', '')
                        logger.info(f"  {asset}: {max_diffs[col]:.6f}")
                        
                    # Find first significant difference
                    for _, row in comparison_df.iterrows():
                        max_row_diff = max([row[col] for col in diff_columns if not pd.isna(row[col])])
                        if max_row_diff > 0.01:
                            logger.warning(f"First significant difference on: {row['date']}")
                            logger.warning(f"Maximum difference: {max_row_diff:.6f}")
                            break
                            
            except Exception as e:
                logger.error(f"Error analyzing score comparison: {e}")
                
        # Check for source code differences
        if os.path.exists('tester_calculate_scores.py') and os.path.exists('reporter_calculate_scores.py'):
            try:
                with open('tester_calculate_scores.py', 'r') as f:
                    tester_source = f.read()
                with open('reporter_calculate_scores.py', 'r') as f:
                    reporter_source = f.read()
                    
                if tester_source == reporter_source:
                    logger.info("✓ calculate_scores source code is identical")
                else:
                    logger.warning("⚠️  calculate_scores source code differs!")
                    logger.warning("Check tester_calculate_scores.py vs reporter_calculate_scores.py")
                    
            except Exception as e:
                logger.error(f"Error comparing source code: {e}")

def main():
    """Main execution"""
    logger.info("Starting comprehensive test suite for PGO discrepancy investigation")
    
    runner = ComprehensiveTestRunner()
    
    try:
        # Run all tests
        runner.run_all_tests()
        
        # Generate summary
        runner.generate_summary_report()
        
        # Collect generated files
        runner.collect_generated_files()
        
        # Analyze findings
        runner.analyze_key_findings()
        
        logger.info("=== COMPREHENSIVE TEST SUITE COMPLETED ===")
        logger.info("Check the generated files for detailed analysis:")
        logger.info("  - comprehensive_test_results.log (this log)")
        logger.info("  - test_report_*.txt (detailed report)")
        logger.info("  - Various CSV and source code files")
        
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
