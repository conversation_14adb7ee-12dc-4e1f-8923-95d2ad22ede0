#!/usr/bin/env python3
"""
Test DPSD Implementation Against TradingView Logic

This script tests our DPSD implementation with the exact parameters from TradingView:
- DEMA Length: 6
- DEMA Source: high
- Percentile Length: 56
- Percentile Type: 60/45
- SD Length: 27
- EMA Length: 75
- Include EMA: True

Usage Examples:
    # Test with last 300 days (default)
    python test_dpsd_tradingview.py

    # Test with last 500 days
    python test_dpsd_tradingview.py --days-back 500

    # Test from specific start date
    python test_dpsd_tradingview.py --start-date 2024-01-01

    # Test from specific date (will fetch up to 1000 days from that date)
    python test_dpsd_tradingview.py --start-date 2023-06-01
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from indicators.dpsd_score import (
    calculate_dpsd_score,
    generate_dpsd_signal,
    plot_dpsd_analysis,
    DEFAULT_DPSD_CONFIG
)

def test_dpsd_tradingview_params(start_date=None, days_back=300):
    """
    Test DPSD with exact TradingView parameters.

    Args:
        start_date: Starting date in 'YYYY-MM-DD' format (optional)
        days_back: Number of days to fetch if start_date not specified
    """

    print("🔍 Testing DPSD with TradingView Parameters")
    print("=" * 50)

    try:
        # Fetch BTC data
        if start_date:
            print(f"📊 Fetching BTC/USDT data from {start_date}...")

            data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=['BTC/USDT'],
                timeframe='1d',
                since=start_date,  # Pass date string directly
                limit=1000,  # Get more data when using since parameter
                ensure_data_since=True,  # Ensure we get data from the specified date
                force_refresh=True  # Force refresh to avoid cache issues
            )
        else:
            print(f"📊 Fetching BTC/USDT data (last {days_back} days)...")
            data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=['BTC/USDT'],
                timeframe='1d',
                limit=days_back
            )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return
            
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Fetched {len(btc_df)} candles")
        print(f"📅 Date range: {btc_df.index[0].strftime('%Y-%m-%d')} to {btc_df.index[-1].strftime('%Y-%m-%d')}")

        # Verify the start date if specified
        if start_date:
            actual_start = btc_df.index[0].strftime('%Y-%m-%d')
            if actual_start != start_date:
                print(f"⚠️  Note: Requested start date {start_date}, but actual data starts from {actual_start}")
            else:
                print(f"✅ Data starts from requested date: {start_date}")
        
        # Test with exact TradingView parameters
        print("\n🎯 Testing with TradingView parameters:")
        tv_params = {
            'dema_length': 50,
            'dema_src': 'close',
            'percentile_length': 58,
            'pertype': '60/45',
            'sd_length': 27,
            'ema_length': 14,
            'include_ema': True
        }
        
        for key, value in tv_params.items():
            print(f"  {key}: {value}")
        
        # Calculate DPSD components
        print("\n🔧 Calculating DPSD components...")
        trend_signal, PT, EMA_line, T_direction, signal = calculate_dpsd_score(
            df=btc_df,
            **tv_params
        )
        
        # Show recent values
        print("\n📈 Recent Values (last 10 days):")
        recent_data = btc_df.tail(10)
        recent_trend = trend_signal.tail(10)
        recent_PT = PT.tail(10)
        recent_EMA = EMA_line.tail(10)
        recent_T = T_direction.tail(10)
        
        print("Date       | Close    | High     | Trend | T | PT      | EMA     ")
        print("-" * 70)
        for i in range(len(recent_data)):
            date = recent_data.index[i].strftime('%Y-%m-%d')
            close = recent_data['close'].iloc[i]
            high = recent_data['high'].iloc[i]
            trend = recent_trend.iloc[i]
            t_val = recent_T.iloc[i]
            pt_val = recent_PT.iloc[i]
            ema_val = recent_EMA.iloc[i]
            
            print(f"{date} | {close:8.2f} | {high:8.2f} | {trend:5d} | {t_val:1d} | {pt_val:7.2f} | {ema_val:7.2f}")
        
        # Signal statistics
        signal_counts = signal.value_counts().sort_index()
        print(f"\n📊 Signal Distribution:")
        for sig_val, count in signal_counts.items():
            sig_name = {-1: "SHORT", 0: "NEUTRAL", 1: "LONG"}[sig_val]
            percentage = (count / len(signal)) * 100
            print(f"  {sig_name}: {count} ({percentage:.1f}%)")
        
        # Current status
        latest_signal = signal.iloc[-1]
        latest_PT = PT.iloc[-1]
        latest_EMA = EMA_line.iloc[-1]
        latest_T = T_direction.iloc[-1]
        latest_price = btc_df['close'].iloc[-1]
        latest_date = btc_df.index[-1].strftime('%Y-%m-%d')
        
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        
        print(f"\n🎯 Current Status ({latest_date}):")
        print(f"  Signal: {signal_name} ({latest_signal})")
        print(f"  T Direction: {latest_T}")
        print(f"  PT Value: {latest_PT:.2f}")
        print(f"  EMA Value: {latest_EMA:.2f}")
        print(f"  BTC Price: ${latest_price:.2f}")
        
        # Test different percentile variants
        print("\n🔄 Testing different percentile variants:")
        variants = ['60/40', '60/45', '55/40', '55/45']
        
        for variant in variants:
            variant_signal = generate_dpsd_signal(
                df=btc_df,
                dema_length=6,
                dema_src='high',
                percentile_length=56,
                pertype=variant,
                sd_length=27,
                ema_length=75,
                include_ema=True
            )
            
            variant_counts = variant_signal.value_counts().sort_index()
            latest_variant = variant_signal.iloc[-1]
            variant_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_variant]
            
            print(f"  {variant}: {dict(variant_counts)} -> Current: {variant_name}")
        
        # Create visualization
        print(f"\n📊 Creating DPSD visualization...")

        # Determine how many days to show in the plot
        if start_date:
            # If start date specified, show all data from that date
            plot_days = 0  # 0 means show all data
            print(f"📊 Showing all data from {start_date} ({len(btc_df)} days)")
        else:
            # If no start date, show last 100 days
            plot_days = 100
            print(f"📊 Showing last {plot_days} days")

        plot_dpsd_analysis(
            df=btc_df,
            **tv_params,
            days_to_show=plot_days,
            save_path="dpsd_tradingview_test.png"
        )
        
        print(f"\n✅ DPSD test completed successfully!")
        print(f"📁 Plot saved as: dpsd_tradingview_test.png")
        
    except Exception as e:
        print(f"❌ Error in DPSD test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Test DPSD implementation with TradingView parameters')
    parser.add_argument('--start-date', type=str, help='Start date in YYYY-MM-DD format (e.g., 2024-01-01)')
    parser.add_argument('--days-back', type=int, default=300, help='Number of days to fetch (default: 300)')

    args = parser.parse_args()

    if args.start_date:
        print(f"📅 Using start date: {args.start_date}")
        test_dpsd_tradingview_params(start_date=args.start_date)
    else:
        print(f"📅 Using last {args.days_back} days")
        test_dpsd_tradingview_params(days_back=args.days_back)
