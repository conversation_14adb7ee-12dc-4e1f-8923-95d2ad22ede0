#!/usr/bin/env python3
"""
Debug script to verify Median Score signal logic is correct.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score

def debug_median_signals():
    """Debug Median Score signals to verify they make sense."""
    print("=" * 60)
    print("Median Score Signal Logic Verification")
    print("=" * 60)
    
    # Fetch recent BTC data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-05-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Analyzing {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Calculate Median Score
    tv_params = {
        'atr_period': 10,
        'multiplier': 1.55,
        'median_length': 7,
        'src_col': 'close'
    }
    
    signal, supertrend_line, direction = calculate_median_score(df=btc_df, **tv_params)
    
    # Find signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"\nFound {len(change_indices)} signal changes")
    print("\nSignal Change Analysis:")
    print("=" * 80)
    
    for i, change_date in enumerate(change_indices[-8:]):  # Last 8 changes
        idx = btc_df.index.get_loc(change_date)
        
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            
            close_price = btc_df['close'].iloc[idx]
            prev_close = btc_df['close'].iloc[idx-1]
            
            supertrend_value = supertrend_line.iloc[idx]
            direction_value = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            
            price_change = ((close_price - prev_close) / prev_close) * 100
            
            print(f"\nDate: {change_date.strftime('%Y-%m-%d')}")
            print(f"Signal: {prev_signal} → {curr_signal}")
            print(f"Price: ${prev_close:.2f} → ${close_price:.2f} ({price_change:+.2f}%)")
            print(f"Supertrend: ${supertrend_value:.2f}")
            print(f"Direction: {prev_direction} → {direction_value}")
            
            # Check logic
            if curr_signal == 1:  # Long signal
                print(f"LONG: Price vs Supertrend: ${close_price:.2f} vs ${supertrend_value:.2f}")
                if close_price > supertrend_value:
                    print("✅ CORRECT: Long signal with price above Supertrend")
                else:
                    print("❌ INCORRECT: Long signal but price below Supertrend")
            elif curr_signal == -1:  # Short signal
                print(f"SHORT: Price vs Supertrend: ${close_price:.2f} vs ${supertrend_value:.2f}")
                if close_price < supertrend_value:
                    print("✅ CORRECT: Short signal with price below Supertrend")
                else:
                    print("❌ INCORRECT: Short signal but price above Supertrend")
    
    # Current status
    current_price = btc_df['close'].iloc[-1]
    current_signal = signal.iloc[-1]
    current_supertrend = supertrend_line.iloc[-1]
    current_direction = direction.iloc[-1]
    
    print(f"\n" + "=" * 80)
    print("Current Market Status:")
    print("=" * 80)
    print(f"Current Price: ${current_price:.2f}")
    print(f"Current Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Supertrend: ${current_supertrend:.2f}")
    print(f"Direction: {current_direction}")
    print(f"Price vs Supertrend: {((current_price - current_supertrend) / current_supertrend * 100):+.2f}%")
    
    # Logic verification
    if current_signal == 1 and current_price > current_supertrend:
        print("✅ LOGIC CHECK: Bullish signal with price above Supertrend - CORRECT")
    elif current_signal == -1 and current_price < current_supertrend:
        print("✅ LOGIC CHECK: Bearish signal with price below Supertrend - CORRECT")
    elif current_signal == 1 and current_price < current_supertrend:
        print("❌ LOGIC ERROR: Bullish signal but price below Supertrend - INCORRECT")
    elif current_signal == -1 and current_price > current_supertrend:
        print("❌ LOGIC ERROR: Bearish signal but price above Supertrend - INCORRECT")
    else:
        print("⚠️  NEUTRAL: No clear signal")
    
    # Recent trend analysis
    print(f"\n" + "=" * 80)
    print("Recent Trend Analysis (Last 10 days):")
    print("=" * 80)
    
    recent_data = pd.DataFrame({
        'Date': btc_df.index[-10:].strftime('%Y-%m-%d'),
        'Close': btc_df['close'].iloc[-10:].round(2),
        'Signal': signal.iloc[-10:],
        'Supertrend': supertrend_line.iloc[-10:].round(2),
        'Direction': direction.iloc[-10:]
    })
    
    print(recent_data.to_string(index=False))

if __name__ == "__main__":
    debug_median_signals()
