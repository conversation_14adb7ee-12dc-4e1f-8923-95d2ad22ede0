#!/usr/bin/env python
"""
Test script for the append_to_cache functionality.

This script tests the new append_to_cache function that allows for more efficient
data caching by appending new data to existing files instead of reading and rewriting
the entire file.

It now includes a test for appending to actual BTC, ETH, and SOL CSV files.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import shutil

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the data cache module
from src.data_cache import (
    append_to_cache,
    load_from_cache,
    cleanup_cache_file,
    save_to_cache,
    get_cache_path
)
from src.utils import setup_logging

def test_append_functionality():
    """Test the append functionality for cache files."""
    setup_logging(level=logging.INFO)

    # Test parameters
    exchange_id = 'test_exchange'
    symbol = 'TEST/USDT'
    timeframe = '1m'

    # Create a test directory
    cache_dir = 'test_cache'
    os.makedirs(cache_dir, exist_ok=True)

    # Create initial test data
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(minutes=i) for i in range(10)]

    initial_data = pd.DataFrame({
        'open': np.random.rand(10) * 100,
        'high': np.random.rand(10) * 100,
        'low': np.random.rand(10) * 100,
        'close': np.random.rand(10) * 100,
        'volume': np.random.rand(10) * 10
    }, index=dates)

    # Save initial data
    logging.info("Saving initial data...")
    append_to_cache(initial_data, exchange_id, symbol, timeframe, cache_dir)

    # Load the data to verify
    loaded_data = load_from_cache(exchange_id, symbol, timeframe, cache_dir=cache_dir)
    logging.info(f"Loaded initial data: {len(loaded_data)} rows")

    # Create new data to append
    new_start_date = start_date + timedelta(minutes=10)
    new_dates = [new_start_date + timedelta(minutes=i) for i in range(5)]

    new_data = pd.DataFrame({
        'open': np.random.rand(5) * 100,
        'high': np.random.rand(5) * 100,
        'low': np.random.rand(5) * 100,
        'close': np.random.rand(5) * 100,
        'volume': np.random.rand(5) * 10
    }, index=new_dates)

    # Append new data
    logging.info("Appending new data...")
    append_to_cache(new_data, exchange_id, symbol, timeframe, cache_dir)

    # Load the data again to verify
    loaded_data_after_append = load_from_cache(exchange_id, symbol, timeframe, cache_dir=cache_dir)
    logging.info(f"Loaded data after append: {len(loaded_data_after_append)} rows")

    # Verify the data was appended correctly
    expected_rows = len(initial_data) + len(new_data)
    actual_rows = len(loaded_data_after_append)

    if actual_rows == expected_rows:
        logging.info(f"✅ Test passed: Expected {expected_rows} rows, got {actual_rows}")
    else:
        logging.error(f"❌ Test failed: Expected {expected_rows} rows, got {actual_rows}")

    # Test cleanup
    logging.info("Testing cleanup...")
    cleanup_cache_file(exchange_id, symbol, timeframe, cache_dir)

    # Load the data after cleanup
    loaded_data_after_cleanup = load_from_cache(exchange_id, symbol, timeframe, cache_dir=cache_dir)
    logging.info(f"Loaded data after cleanup: {len(loaded_data_after_cleanup)} rows")

    # Verify the cleanup didn't lose any data
    if len(loaded_data_after_cleanup) == expected_rows:
        logging.info(f"✅ Cleanup test passed: Expected {expected_rows} rows, got {len(loaded_data_after_cleanup)}")
    else:
        logging.error(f"❌ Cleanup test failed: Expected {expected_rows} rows, got {len(loaded_data_after_cleanup)}")

    # Test duplicate handling
    logging.info("Testing duplicate handling...")

    # Create data with some duplicates
    duplicate_dates = dates[-3:] + new_dates[:3]  # Overlap of 3 dates
    duplicate_data = pd.DataFrame({
        'open': np.random.rand(len(duplicate_dates)) * 100,
        'high': np.random.rand(len(duplicate_dates)) * 100,
        'low': np.random.rand(len(duplicate_dates)) * 100,
        'close': np.random.rand(len(duplicate_dates)) * 100,
        'volume': np.random.rand(len(duplicate_dates)) * 10
    }, index=duplicate_dates)

    # Append data with duplicates
    append_to_cache(duplicate_data, exchange_id, symbol, timeframe, cache_dir)

    # Load the data after appending duplicates
    loaded_data_with_duplicates = load_from_cache(exchange_id, symbol, timeframe, cache_dir=cache_dir)

    # Run cleanup to remove duplicates
    cleanup_cache_file(exchange_id, symbol, timeframe, cache_dir)

    # Load the data after cleanup
    loaded_data_after_duplicate_cleanup = load_from_cache(exchange_id, symbol, timeframe, cache_dir=cache_dir)

    # Verify the cleanup removed duplicates
    unique_dates = set(dates + new_dates + duplicate_dates)
    expected_unique_rows = len(unique_dates)

    if len(loaded_data_after_duplicate_cleanup) == expected_unique_rows:
        logging.info(f"✅ Duplicate handling test passed: Expected {expected_unique_rows} rows, got {len(loaded_data_after_duplicate_cleanup)}")
    else:
        logging.error(f"❌ Duplicate handling test failed: Expected {expected_unique_rows} rows, got {len(loaded_data_after_duplicate_cleanup)}")

    # Clean up test files
    logging.info("Cleaning up test files...")
    shutil.rmtree(cache_dir)
    logging.info("Test completed and test files removed")

def test_append_to_real_files():
    """Test appending to real BTC, ETH, and SOL CSV files with real data from the exchange."""
    setup_logging(level=logging.INFO)

    print("\n" + "=" * 50)
    print("TESTING APPEND TO REAL CACHE FILES WITH REAL DATA")
    print("=" * 50)

    # Import necessary modules for fetching real data
    from src.incremental_fetcher import fetch_incremental_data

    # Test parameters
    exchange_id = 'binance'
    symbols = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC']
    timeframe = '1m'
    state_name = 'test_append_to_cache'

    # Create backup of original files
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"

        if os.path.exists(cache_path):
            print(f"Creating backup of {cache_path} to {backup_path}")
            shutil.copy2(cache_path, backup_path)

    # Also backup the state file if it exists
    state_file_path = f"data/state/{state_name}.json"
    state_backup_path = f"{state_file_path}.bak"
    if os.path.exists(state_file_path):
        print(f"Creating backup of state file {state_file_path} to {state_backup_path}")
        shutil.copy2(state_file_path, state_backup_path)

    try:
        # For each symbol, load the current data before fetching
        for symbol in symbols:
            print(f"\nTesting append for {symbol}...")

            # Load current data
            current_data = load_from_cache(exchange_id, symbol, timeframe)
            if current_data is None or current_data.empty:
                print(f"No data found for {symbol}, skipping...")
                continue

            print(f"Loaded {len(current_data)} rows for {symbol}")
            print(f"Last timestamp: {current_data.index.max()}")

        # Create a state file with timestamps a few minutes earlier than the actual last timestamps
        # to simulate having new data to fetch
        from src.state_manager import save_state

        # Create a state with timestamps 10 minutes earlier
        state = {'data_timestamps': {exchange_id: {timeframe: {}}}}
        for symbol in symbols:
            current_data = load_from_cache(exchange_id, symbol, timeframe)
            if current_data is not None and not current_data.empty:
                last_timestamp = current_data.index.max()
                # Set the state timestamp to 10 minutes earlier
                earlier_timestamp = last_timestamp - pd.Timedelta(minutes=10)
                state['data_timestamps'][exchange_id][timeframe][symbol] = earlier_timestamp.isoformat()
                print(f"Setting state timestamp for {symbol} to {earlier_timestamp} (10 minutes earlier than actual)")

        # Save the state
        save_state(state, state_name)
        print(f"Created state file with timestamps 10 minutes earlier than actual")

        # Use the incremental fetcher to fetch and append new data
        print("\nFetching incremental data for all symbols...")
        data_dict = fetch_incremental_data(
            exchange_id=exchange_id,
            symbols=symbols,
            timeframe=timeframe,
            state_name=state_name,
            use_cache=True
        )

        if data_dict:
            print(f"\nSuccessfully fetched incremental data for {len(data_dict)} symbols:")
            for symbol, df in data_dict.items():
                if not df.empty:
                    print(f"  - {symbol}: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")

                    # Load the data again to verify
                    loaded_data_after_append = load_from_cache(exchange_id, symbol, timeframe)
                    print(f"  - Loaded {len(loaded_data_after_append)} rows after append")
                    print(f"  - New last timestamp: {loaded_data_after_append.index.max()}")

                    # Show the last few rows of the updated file
                    print(f"\nLast 5 rows of the updated file for {symbol}:")
                    print(loaded_data_after_append.tail(5))
        else:
            print("No new data was fetched")

    finally:
        # Restore original files from backups
        print("\nRestoring original files from backups...")
        for symbol in symbols:
            symbol_file = symbol.replace('/', '_')
            cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
            backup_path = f"{cache_path}.bak"

            if os.path.exists(backup_path):
                print(f"Restoring {cache_path} from {backup_path}")
                shutil.copy2(backup_path, cache_path)
                os.remove(backup_path)

        # Restore state file if it was backed up
        if os.path.exists(state_backup_path):
            print(f"Restoring state file {state_file_path} from {state_backup_path}")
            if os.path.exists(state_file_path):
                os.remove(state_file_path)
            shutil.copy2(state_backup_path, state_file_path)
            os.remove(state_backup_path)

    print("\nTest completed and original files restored")

if __name__ == "__main__":
    print("=" * 50)
    print("TESTING APPEND TO CACHE FUNCTIONALITY")
    print("=" * 50)

    # Run the original test
    test_append_functionality()

    # Run the test on real files
    test_append_to_real_files()

    print("=" * 50)
    print("ALL TESTS COMPLETED")
    print("=" * 50)
