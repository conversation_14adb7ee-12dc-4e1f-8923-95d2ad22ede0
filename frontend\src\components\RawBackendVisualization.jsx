import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON><PERSON>, ColorType, CrosshairMode } from 'lightweight-charts';
import axios from 'axios';
import './TradingViewDashboard.css';

// This component directly plots the raw backend data without ANY processing
const RawBackendVisualization = () => {
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const seriesRef = useRef({});
  const resizeObserverRef = useRef(null);
  const [isLogScale, setIsLogScale] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  // Fetch data directly from the backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching raw data from backend...');
        const response = await axios.get('/api/equity');
        console.log('Raw backend data received:', response.data);
        setData(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Create chart when data is available
  useEffect(() => {
    if (!data || !chartContainerRef.current) return;

    // Define asset colors
    const assetColors = {
      'BTC/USDT': '#f7931a', // Bitcoin orange
      'ETH/USDT': '#627eea', // Ethereum blue
      'SOL/USDT': '#00ffbd', // Solana green
      'out-of-market': '#999999' // Gray for out-of-market periods
    };

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 500,
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#333333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      rightPriceScale: {
        mode: isLogScale ? 1 : 0, // 1 = logarithmic, 0 = linear
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
    });

    chartRef.current = chart;

    // Add series for each curve
    const series = {};

    // Add buy-and-hold series first (so they appear behind the strategy line)
    const assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'];

    assets.forEach(asset => {
      if (data.curves && data.curves[asset] && data.curves[asset].length > 0) {
        console.log(`Adding ${asset} series with ${data.curves[asset].length} data points`);
        series[asset] = chart.addLineSeries({
          color: assetColors[asset] ? assetColors[asset] + '80' : '#99999980', // Add 50% transparency
          lineWidth: 1,
          lineStyle: 0, // 0 = solid line (no dashes)
          title: `${asset} B&H`,
        });
        series[asset].setData(data.curves[asset]);
      } else {
        console.log(`No data for ${asset} or empty array`);
      }
    });

    // Add strategy series as a single continuous line WITHOUT ANY PROCESSING
    if (data.curves && data.curves.strategy && data.curves.strategy.length > 0) {
      console.log(`Adding raw strategy series with ${data.curves.strategy.length} data points`);

      // Create a single continuous line series for the strategy
      const strategySeries = chart.addLineSeries({
        lineWidth: 3,
        title: 'Strategy (Raw Backend Data)',
        lastValueVisible: true,
        priceLineVisible: true,
        color: '#0d6efd', // Default blue color
        lineType: 0, // 0 = solid line (no dashes)
        lineStyle: 0, // 0 = solid line (no dashes)
      });

      // Log the structure of the first few data points to see what's included
      console.log('First few strategy data points from backend:');
      for (let i = 0; i < Math.min(5, data.curves.strategy.length); i++) {
        console.log(`Point ${i}:`, data.curves.strategy[i]);
      }

      // Check if the data already includes color information
      const hasColorInfo = data.curves.strategy.length > 0 && data.curves.strategy[0].color !== undefined;
      console.log(`Raw strategy data ${hasColorInfo ? 'includes' : 'does not include'} color information`);

      // Set the data directly without any processing
      strategySeries.setData(data.curves.strategy);

      console.log('Raw strategy data set without any processing');

      // Also log asset changes information
      if (data.assetChanges && data.assetChanges.length > 0) {
        console.log(`Backend includes ${data.assetChanges.length} asset changes:`);

        // Sort asset changes by date
        const sortedAssetChanges = [...data.assetChanges].sort((a, b) => a.date - b.date);

        // Log the first few and last few asset changes
        console.log('First 5 asset changes:');
        for (let i = 0; i < Math.min(5, sortedAssetChanges.length); i++) {
          const change = sortedAssetChanges[i];
          console.log(`  ${i+1}. ${new Date(change.date * 1000).toISOString().split('T')[0]}: ${change.asset || 'out-of-market'}`);
        }

        if (sortedAssetChanges.length > 10) {
          console.log('Last 5 asset changes:');
          for (let i = Math.max(0, sortedAssetChanges.length - 5); i < sortedAssetChanges.length; i++) {
            const change = sortedAssetChanges[i];
            console.log(`  ${i+1}. ${new Date(change.date * 1000).toISOString().split('T')[0]}: ${change.asset || 'out-of-market'}`);
          }
        }
      } else {
        console.log('Backend does not include asset changes information');
      }
    } else {
      console.log('No data for strategy or empty array');
    }

    // Fit content
    chart.timeScale().fitContent();

    // Save references
    chartRef.current = chart;
    seriesRef.current = series;

    // Set up resize observer
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    resizeObserverRef.current = new ResizeObserver(entries => {
      if (chartRef.current && entries.length > 0) {
        const { width } = entries[0].contentRect;
        chartRef.current.applyOptions({ width });
        chartRef.current.timeScale().fitContent();
      }
    });

    resizeObserverRef.current.observe(chartContainerRef.current);

    // Cleanup function
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [data, isLogScale]);

  // Function to toggle between log and linear scale
  const toggleScale = () => {
    setIsLogScale(!isLogScale);
  };

  if (loading) {
    return <div className="loading">Loading chart data...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="chart-container">
      <h2 style={{ textAlign: 'center', marginBottom: '10px' }}>Raw Backend Data Visualization (No Processing)</h2>
      <div className="chart-controls">
        <button
          className={`scale-toggle-btn ${isLogScale ? 'active' : ''}`}
          onClick={toggleScale}
        >
          {isLogScale ? 'Log Scale' : 'Linear Scale'}
        </button>
      </div>
      <div ref={chartContainerRef} className="chart" style={{ height: '500px' }}></div>
      <div className="legend">
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#0d6efd' }}></div>
          <span>Strategy (Raw Backend Data)</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#f7931a', opacity: 0.5 }}></div>
          <span>BTC/USDT B&H</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#627eea', opacity: 0.5 }}></div>
          <span>ETH/USDT B&H</span>
        </div>
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#00ffbd', opacity: 0.5 }}></div>
          <span>SOL/USDT B&H</span>
        </div>
      </div>
    </div>
  );
};

export default RawBackendVisualization;
