@echo off
echo Starting preload of all USDT pairs from Binance...
echo This may take some time depending on the number of pairs and date range.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
ETH/USDT BTC/USDT SUI/USDT SOL/USDT FDUSD/USDT ^
PEPE/USDT TRUMP/USDT XRP/USDT DOGE/USDT WIF/USDT ^
EUR/USDT BNB/USDT ADA/USDT WLD/USDT ENA/USDT ^
CETUS/USDT AVAX/USDT FET/USDT RUNE/USDT HBAR/USDT ^
LINK/USDT NEIRO/USDT APT/USDT LTC/USDT S/USDT ^
VIRTUAL/USDT TAO/USDT BONK/USDT INJ/USDT CRV/USDT ^
ARB/USDT AAVE/USDT COOKIE/USDT PNUT/USDT SEI/USDT

echo.
echo Batch 1/6 completed. Starting batch 2...
echo.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
NEAR/USDT RENDER/USDT TIA/USDT BCH/USDT TRX/USDT ^
JUP/USDT ONDO/USDT MUBARAK/USDT PENGU/USDT UNI/USDT ^
EIGEN/USDT DOT/USDT GALA/USDT ATOM/USDT KAITO/USDT ^
FLOKI/USDT SHIB/USDT BOME/USDT OP/USDT PYTH/USDT ^
OM/USDT ALGO/USDT XLM/USDT LAYER/USDT BIO/USDT

echo.
echo Batch 2/6 completed. Starting batch 3...
echo.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
USUAL/USDT WCT/USDT BERA/USDT ZRO/USDT RAY/USDT ^
KERNEL/USDT STX/USDT LDO/USDT AIXBT/USDT BABY/USDT ^
PENDLE/USDT ARKM/USDT IO/USDT PEOPLE/USDT ETHFI/USDT ^
FIL/USDT ICP/USDT POL/USDT TURBO/USDT SAGA/USDT ^
CAKE/USDT CGPT/USDT ANIME/USDT AR/USDT VANRY/USDT

echo.
echo Batch 3/6 completed. Starting batch 4...
echo.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
ORDI/USDT EGLD/USDT 1000SATS/USDT MANTA/USDT TON/USDT ^
RSR/USDT NIL/USDT NOT/USDT DYDX/USDT ZK/USDT ^
VET/USDT ACT/USDT BEAMX/USDT W/USDT PHA/USDT ^
TST/USDT STRK/USDT NEO/USDT JTO/USDT RARE/USDT ^
CFX/USDT EOS/USDT ORCA/USDT ENS/USDT APE/USDT

echo.
echo Batch 4/6 completed. Starting batch 5...
echo.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
COW/USDT CHZ/USDT THETA/USDT BB/USDT BROCCOLI714/USDT ^
TUT/USDT IOTA/USDT MKR/USDT BMT/USDT SAND/USDT ^
1000CAT/USDT FORM/USDT SHELL/USDT OSMO/USDT QNT/USDT ^
SYN/USDT REZ/USDT ALT/USDT PIXEL/USDT MEME/USDT ^
VANA/USDT DOGS/USDT PARTI/USDT CVC/USDT MOVE/USDT

echo.
echo Batch 5/6 completed. Starting final batch...
echo.

python preload_data.py preload --timeframe 1d --since 2020-01-01 --assets ^
XTZ/USDT 1MBABYDOGE/USDT GUN/USDT CKB/USDT OMNI/USDT ^
THE/USDT RED/USDT BLUR/USDT SLF/USDT HMSTR/USDT ^
CATI/USDT BANANA/USDT TRB/USDT AUCTION/USDT HIVE/USDT ^
TNSR/USDT ONT/USDT BIGTIME/USDT 1000CHEEMS/USDT RPL/USDT ^
YGG/USDT ZEN/USDT HEI/USDT ACX/USDT BANANAS31/USDT ^
API3/USDT EPIC/USDT CHESS/USDT VELODROME/USDT KAIA/USDT ^
STEEM/USDT GPS/USDT UTK/USDT TLM/USDT GMX/USDT ^
T/USDT JUV/USDT IDEX/USDT DF/USDT

echo.
echo All USDT pairs have been preloaded!
echo.
echo Preload completed for all 174 USDT trading pairs from Binance since 2020-01-01.
pause