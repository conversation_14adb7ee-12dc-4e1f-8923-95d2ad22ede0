#!/usr/bin/env python3
"""
Debug script to test date fetching functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
import pandas as pd

def test_date_fetching():
    """Test different date fetching approaches."""
    
    print("🔍 Testing Date Fetching Functionality")
    print("=" * 50)
    
    # Test 1: Default fetch (last 100 days)
    print("\n📊 Test 1: Default fetch (last 100 days)")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=100
        )
        
        if 'BTC/USDT' in data_dict:
            df = data_dict['BTC/USDT']
            print(f"✅ Fetched {len(df)} candles")
            print(f"📅 Date range: {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}")
        else:
            print("❌ No data returned")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Fetch with specific start date (string format)
    print("\n📊 Test 2: Fetch from 2024-01-01 (string)")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since='2024-01-01',
            limit=1000,
            ensure_data_since=True,
            force_refresh=True,
            use_cache=False  # Disable cache completely
        )
        
        if 'BTC/USDT' in data_dict:
            df = data_dict['BTC/USDT']
            print(f"✅ Fetched {len(df)} candles")
            print(f"📅 Date range: {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}")
            
            # Check if it actually starts from 2024-01-01
            actual_start = df.index[0].strftime('%Y-%m-%d')
            if actual_start == '2024-01-01':
                print("✅ Perfect! Data starts exactly from requested date")
            elif df.index[0] <= pd.to_datetime('2024-01-01'):
                print(f"✅ Good! Data starts from {actual_start}, which includes the requested date")
            else:
                print(f"⚠️  Data starts from {actual_start}, which is after the requested 2024-01-01")
        else:
            print("❌ No data returned")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Fetch with timestamp
    print("\n📊 Test 3: Fetch from 2024-01-01 (timestamp)")
    try:
        # Convert to timestamp
        start_dt = pd.to_datetime('2024-01-01')
        since_timestamp = int(start_dt.timestamp() * 1000)
        
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=since_timestamp,
            limit=1000,
            ensure_data_since=True,
            force_refresh=True,
            use_cache=False
        )
        
        if 'BTC/USDT' in data_dict:
            df = data_dict['BTC/USDT']
            print(f"✅ Fetched {len(df)} candles")
            print(f"📅 Date range: {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}")
        else:
            print("❌ No data returned")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Different date
    print("\n📊 Test 4: Fetch from 2023-06-01")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since='2023-06-01',
            limit=500,
            ensure_data_since=True,
            force_refresh=True,
            use_cache=False
        )
        
        if 'BTC/USDT' in data_dict:
            df = data_dict['BTC/USDT']
            print(f"✅ Fetched {len(df)} candles")
            print(f"📅 Date range: {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}")
        else:
            print("❌ No data returned")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_date_fetching()
