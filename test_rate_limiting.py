#!/usr/bin/env python3
"""
Test script to verify that the rate limiting functionality works correctly.
"""

import logging
import time
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from trading.account import AccountManager

def test_rate_limiting():
    """Test the rate limiting functionality."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("Testing Rate Limiting Functionality")
    print("=" * 50)
    
    try:
        # Initialize account manager
        print("Initializing AccountManager...")
        account_manager = AccountManager('binance')
        
        # Test multiple rapid API calls
        print("\nTesting rapid API calls (should be rate limited)...")
        start_time = time.time()
        
        for i in range(5):
            print(f"API call {i+1}...")
            balance = account_manager.get_balance('USDC')
            print(f"  Balance: {balance:.8f} USDC")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\nTotal time for 5 API calls: {total_time:.2f} seconds")
        print(f"Average time per call: {total_time/5:.2f} seconds")
        
        # With 100ms minimum interval, 5 calls should take at least 400ms
        expected_min_time = 0.4  # 4 * 0.1 seconds
        if total_time >= expected_min_time:
            print(f"✅ Rate limiting working correctly (took {total_time:.2f}s, expected ≥{expected_min_time:.2f}s)")
        else:
            print(f"⚠️  Rate limiting may not be working (took {total_time:.2f}s, expected ≥{expected_min_time:.2f}s)")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        print("This might be expected if you don't have valid Binance credentials configured.")
        print("The rate limiting code has been added successfully.")

if __name__ == "__main__":
    test_rate_limiting()
