#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Different PGO Lengths to Match TradingView

This script tests various PGO lengths to find which one produces
a PGO value closest to TradingView's -0.52 for AAVE/TRX.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.indicators.ratio_indicators import calculate_ratio_pgo_with_signal

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce log noise

def test_pgo_lengths():
    """Test different PGO lengths to find the one matching TradingView."""
    
    print("Testing Different PGO Lengths to Match TradingView")
    print("=" * 60)
    
    # Target value from TradingView
    target_pgo = -0.52
    
    # Load data
    config = load_config()
    data_start_date = '2025-02-10'
    assets = ['AAVE/USDT', 'TRX/USDT']
    
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=assets,
        timeframe='1d',
        since=data_start_date,
        use_cache=True
    )
    
    if not data_dict or 'AAVE/USDT' not in data_dict or 'TRX/USDT' not in data_dict:
        print("ERROR: Failed to fetch data")
        return
    
    # Get price series
    aave_price = data_dict['AAVE/USDT']['close']
    trx_price = data_dict['TRX/USDT']['close']
    
    print(f"Target TradingView PGO: {target_pgo}")
    print(f"Data period: {aave_price.index[0]} to {aave_price.index[-1]}")
    print(f"Total data points: {len(aave_price)}")
    
    # Test different PGO lengths
    test_lengths = [5, 10, 14, 15, 20, 21, 25, 30, 35, 40, 45, 50]
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    results = []
    
    print(f"\n" + "=" * 80)
    print("PGO LENGTH TESTING RESULTS")
    print("=" * 80)
    print(f"{'Length':<8} {'PGO Value':<12} {'Difference':<12} {'Signal':<8} {'Status'}")
    print("-" * 80)
    
    for length in test_lengths:
        try:
            # Calculate PGO with this length
            _, _, signal_values = calculate_ratio_pgo_with_signal(
                aave_price, trx_price, length, upper_threshold, lower_threshold
            )
            
            # Get the latest PGO value
            if len(signal_values) > 0:
                # We need to get the actual PGO value, not just the signal
                # Let's calculate it manually
                ratio = aave_price / trx_price
                
                # Create ratio DataFrame
                ratio_df = pd.DataFrame({
                    'open': ratio,
                    'high': ratio,
                    'low': ratio,
                    'close': ratio,
                    'volume': pd.Series([1000] * len(ratio), index=ratio.index)
                })
                
                # Calculate PGO components
                from src.indicators.base_indicators import calculate_sma, calculate_atr
                sma = calculate_sma(ratio_df, length=length, close_col='close')
                atr = calculate_atr(ratio_df, length=length)
                
                # Calculate PGO
                pgo_values = (ratio - sma) / atr
                latest_pgo = pgo_values.iloc[-1]
                
                # Get signal
                latest_signal = signal_values.iloc[-1]
                
                # Calculate difference from target
                difference = abs(latest_pgo - target_pgo)
                
                # Determine status
                if latest_pgo >= upper_threshold:
                    status = "LONG"
                elif latest_pgo <= lower_threshold:
                    status = "SHORT"
                else:
                    status = "NEUTRAL"
                
                results.append({
                    'length': length,
                    'pgo': latest_pgo,
                    'difference': difference,
                    'signal': latest_signal,
                    'status': status
                })
                
                print(f"{length:<8} {latest_pgo:<12.6f} {difference:<12.6f} {latest_signal:<8.0f} {status}")
            
        except Exception as e:
            print(f"{length:<8} ERROR: {str(e)[:50]}")
    
    # Find the best match
    if results:
        best_match = min(results, key=lambda x: x['difference'])
        
        print(f"\n" + "=" * 80)
        print("BEST MATCH ANALYSIS")
        print("=" * 80)
        print(f"Best matching PGO length: {best_match['length']}")
        print(f"Calculated PGO value: {best_match['pgo']:.6f}")
        print(f"Target TradingView PGO: {target_pgo:.6f}")
        print(f"Difference: {best_match['difference']:.6f}")
        print(f"Signal: {best_match['signal']:.0f}")
        print(f"Status: {best_match['status']}")
        
        if best_match['difference'] < 0.1:
            print(f"\n✅ EXCELLENT MATCH! Length {best_match['length']} produces a very close result.")
            print(f"   Consider using PGO length = {best_match['length']} to match TradingView.")
        elif best_match['difference'] < 0.2:
            print(f"\n✅ GOOD MATCH! Length {best_match['length']} produces a reasonably close result.")
        else:
            print(f"\n⚠️  No close match found. The closest is length {best_match['length']}.")
            print(f"   This suggests TradingView might be using different parameters or data.")
    
    # Test some additional lengths around the best match if needed
    if results and best_match['difference'] > 0.05:
        print(f"\n" + "=" * 80)
        print("FINE-TUNING AROUND BEST MATCH")
        print("=" * 80)
        
        best_length = best_match['length']
        fine_tune_lengths = [best_length - 2, best_length - 1, best_length + 1, best_length + 2]
        fine_tune_lengths = [l for l in fine_tune_lengths if l > 0 and l not in test_lengths]
        
        if fine_tune_lengths:
            print(f"Testing additional lengths around {best_length}: {fine_tune_lengths}")
            print(f"{'Length':<8} {'PGO Value':<12} {'Difference':<12} {'Signal':<8} {'Status'}")
            print("-" * 60)
            
            for length in fine_tune_lengths:
                try:
                    # Calculate PGO with this length
                    ratio = aave_price / trx_price
                    ratio_df = pd.DataFrame({
                        'open': ratio, 'high': ratio, 'low': ratio, 'close': ratio,
                        'volume': pd.Series([1000] * len(ratio), index=ratio.index)
                    })
                    
                    from src.indicators.base_indicators import calculate_sma, calculate_atr
                    sma = calculate_sma(ratio_df, length=length, close_col='close')
                    atr = calculate_atr(ratio_df, length=length)
                    pgo_values = (ratio - sma) / atr
                    latest_pgo = pgo_values.iloc[-1]
                    
                    difference = abs(latest_pgo - target_pgo)
                    
                    if latest_pgo >= upper_threshold:
                        status = "LONG"
                    elif latest_pgo <= lower_threshold:
                        status = "SHORT"
                    else:
                        status = "NEUTRAL"
                    
                    print(f"{length:<8} {latest_pgo:<12.6f} {difference:<12.6f} {'N/A':<8} {status}")
                    
                    if difference < best_match['difference']:
                        print(f"   ⭐ NEW BEST MATCH!")
                        
                except Exception as e:
                    print(f"{length:<8} ERROR: {str(e)[:50]}")

if __name__ == "__main__":
    test_pgo_lengths()
