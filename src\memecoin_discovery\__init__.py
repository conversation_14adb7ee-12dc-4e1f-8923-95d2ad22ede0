"""
Memecoin Discovery Module

This module implements the memecoin discovery system that leverages Twitter data scraping
and AI analysis to identify promising tokens before they experience significant price increases.

Components:
- database.py: Database schema and operations for storing discovered tokens
- twitter_scraper.py: Twitter API integration and data scraping
- ai_analyzer.py: OpenAI API integration for token analysis and scoring
- discovery_engine.py: Main orchestrator for the discovery process
- token_evaluator.py: Token evaluation and scoring algorithms
- integration.py: Integration with existing Asset Rotation Strategy
"""

__version__ = "1.0.0"
__author__ = "Asset Rotation Strategy Team"

# Import main classes for easy access
from .discovery_engine import MemecoinDiscoveryEngine
from .database import DiscoveryDatabase
from .ai_analyzer import AITokenAnalyzer
from .twitter_scraper import Twitter<PERSON>craper
from .token_evaluator import TokenEvaluator

__all__ = [
    'MemecoinDiscoveryEngine',
    'DiscoveryDatabase', 
    'AITokenAnalyzer',
    'TwitterScraper',
    'TokenEvaluator'
]
