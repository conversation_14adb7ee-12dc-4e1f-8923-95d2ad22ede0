#!/usr/bin/env python3
"""
Scoring Mechanism Comparison Test
=================================

This script specifically tests the asset scoring mechanism differences between:
1. test_allocation.py (AllocationTester)
2. allocation_report.py (AllocationReporter)

Focus on the exact point where scores start to diverge (around 2023-08-26).
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import both implementations
from test_allocation import AllocationTester
from allocation_report import AllocationReporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scoring_mechanism_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScoringMechanismTester:
    """Test the scoring mechanism differences"""
    
    def __init__(self):
        self.test_params = {
            'timeframe': '1d',
            'mtpi_timeframe': '1d',
            'analysis_start_date': '2023-08-20',  # Start earlier to catch the divergence
            'n_assets': 1,
            'transaction_fee_rate': 0.001,
            'selected_assets': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'DOT/USDT', 'BNB/USDT'],
            'use_cache': True,
            'initial_capital': 1.0,
            'wait_for_confirmed_signals': True,
            'use_mtpi': False,  # Disable MTPI to focus on scoring
            'use_weighted_allocation': False,
            'weights': None
        }
        
        # Critical dates where divergence starts
        self.critical_dates = [
            '2023-08-25',
            '2023-08-26', 
            '2023-08-27',
            '2023-08-28',
            '2023-08-29',
            '2023-08-30'
        ]
        
    def setup_implementations(self):
        """Initialize both implementations"""
        logger.info("Setting up implementations for scoring test...")
        
        # Force trend method to be consistent
        trend_method = 'PGO For Loop'
        
        # Create tester instance
        self.tester = AllocationTester(
            timeframe=self.test_params['timeframe'],
            mtpi_timeframe=self.test_params['mtpi_timeframe'],
            analysis_start_date=self.test_params['analysis_start_date'],
            n_assets=self.test_params['n_assets'],
            transaction_fee_rate=self.test_params['transaction_fee_rate'],
            selected_assets=self.test_params['selected_assets'],
            use_cache=self.test_params['use_cache'],
            initial_capital=self.test_params['initial_capital'],
            wait_for_confirmed_signals=self.test_params['wait_for_confirmed_signals'],
            use_mtpi=self.test_params['use_mtpi'],
            use_weighted_allocation=self.test_params['use_weighted_allocation'],
            weights=self.test_params['weights']
        )
        
        # Create reporter instance
        self.reporter = AllocationReporter(
            timeframe=self.test_params['timeframe'],
            mtpi_timeframe=self.test_params['mtpi_timeframe'],
            analysis_start_date=self.test_params['analysis_start_date'],
            n_assets=self.test_params['n_assets'],
            transaction_fee_rate=self.test_params['transaction_fee_rate'],
            selected_assets=self.test_params['selected_assets'],
            use_cache=self.test_params['use_cache'],
            initial_capital=self.test_params['initial_capital'],
            wait_for_confirmed_signals=self.test_params['wait_for_confirmed_signals'],
            use_mtpi=self.test_params['use_mtpi'],
            use_weighted_allocation=self.test_params['use_weighted_allocation'],
            weights=self.test_params['weights']
        )
        
        # Set trend method explicitly
        if hasattr(self.tester, 'trend_method'):
            self.tester.trend_method = trend_method
        if hasattr(self.reporter, 'trend_method'):
            self.reporter.trend_method = trend_method
            
        logger.info(f"Both implementations set up with trend_method: {trend_method}")
        
    def test_scoring_step_by_step(self):
        """Test the scoring mechanism step by step"""
        logger.info("=== TESTING SCORING MECHANISM STEP BY STEP ===")
        
        # Fetch data from both
        logger.info("Fetching data from both implementations...")
        tester_data = self.tester.fetch_data()
        reporter_data = self.reporter.fetch_data()
        
        # Calculate scores from both
        logger.info("Calculating scores from AllocationTester...")
        try:
            tester_scores = self.tester.calculate_scores(tester_data, trend_method='PGO For Loop')
            logger.info(f"Tester scores shape: {tester_scores.shape}")
        except Exception as e:
            logger.error(f"Failed to calculate tester scores: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return
            
        logger.info("Calculating scores from AllocationReporter...")
        try:
            reporter_scores = self.reporter.calculate_scores(reporter_data, trend_method='PGO For Loop')
            logger.info(f"Reporter scores shape: {reporter_scores.shape}")
        except Exception as e:
            logger.error(f"Failed to calculate reporter scores: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return
            
        # Compare scores around critical dates
        self.compare_scores_at_critical_dates(tester_scores, reporter_scores)
        
        # Find first divergence point
        self.find_first_divergence(tester_scores, reporter_scores)
        
        # Save detailed comparison
        self.save_detailed_score_comparison(tester_scores, reporter_scores)
        
    def compare_scores_at_critical_dates(self, tester_scores, reporter_scores):
        """Compare scores at critical dates where divergence starts"""
        logger.info("=== COMPARING SCORES AT CRITICAL DATES ===")
        
        for date_str in self.critical_dates:
            try:
                date = pd.Timestamp(date_str)
                
                if date in tester_scores.index and date in reporter_scores.index:
                    logger.info(f"\nScores on {date_str}:")
                    
                    tester_row = tester_scores.loc[date]
                    reporter_row = reporter_scores.loc[date]
                    
                    logger.info("  Tester scores:")
                    for asset in tester_scores.columns:
                        logger.info(f"    {asset}: {tester_row[asset]:.1f}")
                        
                    logger.info("  Reporter scores:")
                    for asset in reporter_scores.columns:
                        logger.info(f"    {asset}: {reporter_row[asset]:.1f}")
                        
                    # Check for differences
                    differences = {}
                    for asset in tester_scores.columns:
                        if asset in reporter_scores.columns:
                            diff = abs(tester_row[asset] - reporter_row[asset])
                            if diff > 0.01:  # Significant difference
                                differences[asset] = diff
                                
                    if differences:
                        logger.warning(f"  Significant differences found:")
                        for asset, diff in differences.items():
                            logger.warning(f"    {asset}: difference = {diff:.3f}")
                    else:
                        logger.info("  ✓ Scores are identical")
                        
                else:
                    logger.warning(f"Date {date_str} not found in one or both score datasets")
                    
            except Exception as e:
                logger.error(f"Error comparing scores for {date_str}: {e}")
                
    def find_first_divergence(self, tester_scores, reporter_scores):
        """Find the exact first point where scores diverge"""
        logger.info("=== FINDING FIRST DIVERGENCE POINT ===")
        
        # Align the dataframes
        common_dates = tester_scores.index.intersection(reporter_scores.index)
        common_assets = tester_scores.columns.intersection(reporter_scores.columns)
        
        logger.info(f"Comparing {len(common_dates)} dates and {len(common_assets)} assets")
        
        first_divergence_date = None
        
        for date in common_dates:
            tester_row = tester_scores.loc[date, common_assets]
            reporter_row = reporter_scores.loc[date, common_assets]
            
            # Check if any scores differ significantly
            max_diff = abs(tester_row - reporter_row).max()
            
            if max_diff > 0.01:  # Significant difference threshold
                first_divergence_date = date
                logger.warning(f"First divergence detected on: {date}")
                logger.warning(f"Maximum difference: {max_diff:.6f}")
                
                # Show detailed differences
                for asset in common_assets:
                    diff = abs(tester_row[asset] - reporter_row[asset])
                    if diff > 0.01:
                        logger.warning(f"  {asset}: Tester={tester_row[asset]:.3f}, Reporter={reporter_row[asset]:.3f}, Diff={diff:.3f}")
                break
                
        if first_divergence_date is None:
            logger.info("No significant divergence found in the tested period")
        else:
            # Analyze the day before divergence
            prev_dates = common_dates[common_dates < first_divergence_date]
            if len(prev_dates) > 0:
                prev_date = prev_dates[-1]
                logger.info(f"\nScores on previous day ({prev_date}):")
                
                tester_prev = tester_scores.loc[prev_date, common_assets]
                reporter_prev = reporter_scores.loc[prev_date, common_assets]
                
                for asset in common_assets:
                    logger.info(f"  {asset}: Tester={tester_prev[asset]:.3f}, Reporter={reporter_prev[asset]:.3f}")
                    
    def save_detailed_score_comparison(self, tester_scores, reporter_scores):
        """Save detailed score comparison to files"""
        logger.info("=== SAVING DETAILED SCORE COMPARISON ===")
        
        # Save individual score files
        tester_scores.to_csv('detailed_tester_scores.csv')
        reporter_scores.to_csv('detailed_reporter_scores.csv')
        
        # Create side-by-side comparison
        comparison_data = []
        
        common_dates = tester_scores.index.intersection(reporter_scores.index)
        common_assets = tester_scores.columns.intersection(reporter_scores.columns)
        
        for date in common_dates:
            row_data = {'date': date}
            
            for asset in common_assets:
                tester_score = tester_scores.loc[date, asset]
                reporter_score = reporter_scores.loc[date, asset]
                difference = abs(tester_score - reporter_score)
                
                row_data[f'{asset}_tester'] = tester_score
                row_data[f'{asset}_reporter'] = reporter_score
                row_data[f'{asset}_diff'] = difference
                
            comparison_data.append(row_data)
            
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv('detailed_score_comparison.csv', index=False)
        
        logger.info("Detailed comparison files saved:")
        logger.info("  - detailed_tester_scores.csv")
        logger.info("  - detailed_reporter_scores.csv") 
        logger.info("  - detailed_score_comparison.csv")
        
    def test_individual_pgo_signals(self):
        """Test individual PGO signal calculations for each asset pair"""
        logger.info("=== TESTING INDIVIDUAL PGO SIGNALS ===")
        
        # Get data
        tester_data = self.tester.fetch_data()
        reporter_data = self.reporter.fetch_data()
        
        assets = self.test_params['selected_assets']
        
        # Test all asset pairs for PGO signals
        for i, asset1 in enumerate(assets):
            for j, asset2 in enumerate(assets):
                if i < j:  # Avoid duplicates
                    logger.info(f"\nTesting PGO signal for {asset1}/{asset2}...")
                    
                    try:
                        # Try to get PGO signals from both implementations
                        self.compare_pgo_signal_for_pair(asset1, asset2, tester_data, reporter_data)
                    except Exception as e:
                        logger.error(f"Failed to compare PGO for {asset1}/{asset2}: {e}")

    def compare_pgo_signal_for_pair(self, asset1, asset2, tester_data, reporter_data):
        """Compare PGO signal calculation for a specific asset pair"""
        
        if asset1 not in tester_data or asset2 not in tester_data:
            logger.warning(f"Assets {asset1} or {asset2} not in tester data")
            return
            
        if asset1 not in reporter_data or asset2 not in reporter_data:
            logger.warning(f"Assets {asset1} or {asset2} not in reporter data")
            return
            
        # Calculate ratio data
        tester_ratio = tester_data[asset1]['close'] / tester_data[asset2]['close']
        reporter_ratio = reporter_data[asset1]['close'] / reporter_data[asset2]['close']
        
        # Check if ratios are identical
        ratio_diff = abs(tester_ratio - reporter_ratio).max()
        logger.info(f"  Max ratio difference: {ratio_diff:.10f}")
        
        if ratio_diff > 1e-10:
            logger.warning(f"  Ratio data differs between implementations!")
            
        # Try to calculate PGO on the ratio
        # This would need access to the actual PGO calculation methods

def main():
    """Main test execution"""
    logger.info("=== STARTING SCORING MECHANISM TEST ===")
    
    tester = ScoringMechanismTester()
    
    try:
        # Setup implementations
        tester.setup_implementations()
        
        # Test scoring step by step
        tester.test_scoring_step_by_step()
        
        # Test individual PGO signals
        tester.test_individual_pgo_signals()
        
        logger.info("=== SCORING MECHANISM TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
