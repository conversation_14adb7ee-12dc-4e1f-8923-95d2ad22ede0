#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BTC/SOL Ratio with RSI Visualization

This script fetches BTC/USDT and SOL/USDT data, calculates their price ratio,
and plots the ratio along with its RSI indicator.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import matplotlib.dates as mdates
from datetime import datetime
import ccxt
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fetch_ohlcv_data(exchange_id, symbols, timeframe='1d', since=None, limit=None):
    """
    Fetches historical OHLCV data for multiple symbols from a specified exchange.
    """
    all_data = {}
    exchange_class = getattr(ccxt, exchange_id, None)

    if not exchange_class:
        logging.error(f"Exchange '{exchange_id}' not found in ccxt.")
        return all_data

    # Configure exchange
    exchange = exchange_class({
        'enableRateLimit': True,
    })

    # Convert since to timestamp if provided
    since_timestamp = None
    if since:
        if isinstance(since, str):
            try:
                since_dt = pd.to_datetime(since, utc=True)
                since_timestamp = int(since_dt.timestamp() * 1000)
            except:
                logging.error(f"Failed to parse 'since' date string: {since}")
                return all_data
        elif isinstance(since, datetime):
            since_timestamp = int(since.timestamp() * 1000)
        elif isinstance(since, (int, float)):
            since_timestamp = int(since)

    # Fetch data for each symbol
    for symbol in symbols:
        try:
            logging.info(f"Fetching data for {symbol} on {exchange_id} (Timeframe: {timeframe}, Since: {since})")

            # Fetch OHLCV data
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since_timestamp, limit=limit)

            if not ohlcv:
                logging.warning(f"No data returned for {symbol}. Skipping.")
                continue

            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)

            # Set timestamp as index
            df.set_index('timestamp', inplace=True)

            all_data[symbol] = df
            logging.info(f"Successfully fetched {len(df)} candles for {symbol}")

        except Exception as e:
            logging.error(f"Error fetching {symbol} from {exchange_id}: {e}")
            continue

    return all_data

def calculate_rsi(series, length=14):
    """
    Calculate Relative Strength Index (RSI) for a series
    """
    # Calculate price changes
    delta = series.diff()

    # Separate gains and losses
    gains = delta.copy()
    losses = delta.copy()
    gains[gains < 0] = 0
    losses[losses > 0] = 0
    losses = abs(losses)

    # Calculate average gains and losses
    avg_gain = gains.rolling(window=length).mean()
    avg_loss = losses.rolling(window=length).mean()

    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi

def main():
    # Configuration
    exchange_id = 'binance'
    symbols = ['BTC/USDT', 'SOL/USDT']
    timeframe = '1d'
    start_date = '2023-10-20'
    rsi_length = 14
    ma_length = 14  # Moving average length for RSI
    output_file = 'btc_sol_ratio_rsi.png'

    # Fetch data
    data_dict = fetch_ohlcv_data(
        exchange_id=exchange_id,
        symbols=symbols,
        timeframe=timeframe,
        since=start_date
    )

    if len(data_dict) < 2:
        logging.error("Failed to fetch data for both symbols. Exiting.")
        return

    # Extract data for BTC and SOL
    btc_data = data_dict['BTC/USDT']
    sol_data = data_dict['SOL/USDT']

    # Ensure both DataFrames have the same index
    common_idx = btc_data.index.intersection(sol_data.index)
    btc_data = btc_data.loc[common_idx]
    sol_data = sol_data.loc[common_idx]

    # Calculate BTC/SOL ratio
    ratio = btc_data['close'] / sol_data['close']
    ratio.name = 'BTC/SOL Ratio'

    # Calculate RSI of the ratio
    rsi = calculate_rsi(ratio, length=rsi_length)

    # Calculate moving average of RSI
    rsi_ma = rsi.rolling(window=ma_length).mean()

    # Create figure with subplots
    fig = plt.figure(figsize=(14, 10))
    gs = gridspec.GridSpec(2, 1, height_ratios=[2, 1])

    # Plot ratio
    ax1 = plt.subplot(gs[0])
    ax1.plot(ratio.index, ratio, label='BTC/SOL Ratio', color='blue')
    ax1.set_title('BTC/SOL Price Ratio')
    ax1.set_ylabel('Ratio')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # Plot RSI
    ax2 = plt.subplot(gs[1])
    ax2.plot(rsi.index, rsi, label=f'RSI({rsi_length})', color='purple')
    ax2.plot(rsi_ma.index, rsi_ma, label=f'RSI MA({ma_length})', color='orange', linestyle='--')
    ax2.axhline(y=70, color='red', linestyle='--', alpha=0.5)
    ax2.axhline(y=30, color='green', linestyle='--', alpha=0.5)
    ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.5)
    ax2.set_title(f'RSI of BTC/SOL Ratio')
    ax2.set_ylabel('RSI')
    ax2.set_ylim(0, 100)
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # Format x-axis dates
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.MonthLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()

    # Save the plot (don't display interactively)
    plt.savefig(output_file)
    logging.info(f"Saved plot to {output_file}")

    # Comment out plt.show() to avoid hanging
    # plt.show()

if __name__ == "__main__":
    main()
