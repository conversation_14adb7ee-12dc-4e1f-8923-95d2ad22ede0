#!/usr/bin/env python3
"""
Real test script to verify the asset swap logic works with actual paper trading.
This uses real prices, real paper trading system, and real trade execution.
"""

import sys
import os
import logging
import json
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_paper_trading_test():
    """Set up a controlled paper trading test environment."""
    from src.trading.executor import TradingExecutor
    
    # Initialize the trading executor in paper mode
    executor = TradingExecutor()
    
    # Ensure we're in paper trading mode
    executor.set_trading_mode('paper')
    
    # Reset paper trading to start fresh
    executor.reset_paper_trading()
    
    return executor

def test_real_asset_swap():
    """Test asset swap with real paper trading system."""
    print("=" * 70)
    print("REAL ASSET SWAP TEST - USING ACTUAL PAPER TRADING")
    print("=" * 70)
    
    try:
        # Set up executor
        executor = setup_paper_trading_test()
        
        print("\n1. INITIAL SETUP:")
        print("   - Reset paper trading account")
        print("   - Starting with fresh 10,000 USDC balance")
        
        # Get initial status
        status = executor.get_trading_status()
        initial_balance = status['balance'].get('USDC', 0)
        print(f"   - Initial balance: {initial_balance:.2f} USDC")
        
        print("\n2. STEP 1 - CREATE INITIAL POSITIONS:")
        print("   Setting up initial portfolio: 20% AAVE, 80% AIXBT")
        
        # Create initial allocation
        initial_allocation = {
            'AAVE/USDC': 0.2,   # 20% = 2000 USDC
            'AIXBT/USDC': 0.8   # 80% = 8000 USDC
        }
        
        # Execute initial allocation
        result1 = executor.execute_multi_asset_strategy(initial_allocation)
        print(f"   Initial allocation result: {result1.get('success', False)}")
        
        if not result1.get('success', False):
            print("   ❌ Failed to create initial positions!")
            return False
        
        # Show initial positions
        status_after_initial = executor.get_trading_status()
        positions = status_after_initial['positions']
        balance_after_initial = status_after_initial['balance'].get('USDC', 0)
        
        print(f"   Remaining balance: {balance_after_initial:.2f} USDC")
        print("   Positions created:")
        
        aave_position_value = 0
        aixbt_position_value = 0
        
        for symbol, position in positions.items():
            amount = position.get('amount', 0)
            price = executor.get_current_price(symbol)
            value = amount * price if price else 0
            print(f"     {symbol}: {amount:.6f} units @ {price:.4f} = {value:.2f} USDC")
            
            if 'AAVE' in symbol:
                aave_position_value = value
            elif 'AIXBT' in symbol:
                aixbt_position_value = value
        
        print(f"   Total AAVE value: {aave_position_value:.2f} USDC")
        print(f"   Total AIXBT value: {aixbt_position_value:.2f} USDC")
        
        print("\n3. STEP 2 - EXECUTE ASSET SWAP:")
        print("   Swapping AIXBT -> ETH (keeping AAVE)")
        print("   New allocation: 20% AAVE, 80% ETH")
        
        # Record AIXBT value before swap
        aixbt_value_before_swap = aixbt_position_value
        
        # Create new allocation (swap AIXBT for ETH)
        swap_allocation = {
            'AAVE/USDC': 0.2,   # Keep AAVE at 20%
            'ETH/USDC': 0.8     # Replace AIXBT with ETH at 80%
        }
        
        # Execute the swap
        result2 = executor.execute_multi_asset_strategy(swap_allocation)
        print(f"   Swap execution result: {result2.get('success', False)}")
        
        if not result2.get('success', False):
            print("   ❌ Failed to execute swap!")
            return False
        
        print("\n4. ANALYZING SWAP RESULTS:")
        
        # Analyze the trades
        trades = result2.get('trades', [])
        sell_trades = [t for t in trades if t.get('side') == 'sell']
        buy_trades = [t for t in trades if t.get('side') == 'buy']
        
        aixbt_proceeds = 0
        eth_purchase_value = 0
        
        print("   Trades executed:")
        for trade in trades:
            symbol = trade.get('symbol', '')
            side = trade.get('side', '')
            amount = trade.get('amount', 0)
            price = trade.get('price', 0)
            value = amount * price
            
            if side == 'sell' and 'AIXBT' in symbol:
                aixbt_proceeds = trade.get('proceeds', value)
                print(f"     SELL {symbol}: {amount:.6f} units @ {price:.4f} = {aixbt_proceeds:.2f} USDC proceeds")
            elif side == 'buy' and 'ETH' in symbol:
                eth_purchase_value = value
                print(f"     BUY {symbol}: {amount:.6f} units @ {price:.2f} = {value:.2f} USDC spent")
        
        print(f"\n5. SWAP ANALYSIS:")
        print(f"   AIXBT original value: {aixbt_value_before_swap:.2f} USDC")
        print(f"   AIXBT sale proceeds: {aixbt_proceeds:.2f} USDC")
        print(f"   ETH purchase value: {eth_purchase_value:.2f} USDC")
        
        # Calculate how much of the proceeds were used
        if aixbt_proceeds > 0:
            proceeds_used_pct = (eth_purchase_value / aixbt_proceeds) * 100
            print(f"   Proceeds utilization: {proceeds_used_pct:.1f}%")
            
            # Check if the fix worked
            if proceeds_used_pct > 95:  # Allow for small fees
                print("   ✅ SUCCESS: Full proceeds used for ETH purchase!")
                success = True
            else:
                print(f"   ❌ ISSUE: Only {proceeds_used_pct:.1f}% of proceeds used!")
                print(f"   ❌ Expected: ~100%, Got: {proceeds_used_pct:.1f}%")
                success = False
        else:
            print("   ❌ ERROR: No proceeds recorded from AIXBT sale!")
            success = False
        
        # Show final positions
        final_status = executor.get_trading_status()
        final_positions = final_status['positions']
        final_balance = final_status['balance'].get('USDC', 0)
        
        print(f"\n6. FINAL PORTFOLIO:")
        print(f"   Remaining balance: {final_balance:.2f} USDC")
        print("   Final positions:")
        
        total_portfolio_value = final_balance
        for symbol, position in final_positions.items():
            amount = position.get('amount', 0)
            price = executor.get_current_price(symbol)
            value = amount * price if price else 0
            total_portfolio_value += value
            print(f"     {symbol}: {amount:.6f} units @ {price:.4f} = {value:.2f} USDC")
        
        print(f"   Total portfolio value: {total_portfolio_value:.2f} USDC")
        
        return success
        
    except Exception as e:
        print(f"\n❌ TEST FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_allocation_new_positions():
    """Test weight-based allocation for completely new positions."""
    print("\n" + "=" * 70)
    print("TESTING WEIGHT-BASED ALLOCATION (NEW POSITIONS)")
    print("=" * 70)
    
    try:
        # Set up fresh executor
        executor = setup_paper_trading_test()
        
        print("\n1. STARTING WITH EMPTY PORTFOLIO:")
        status = executor.get_trading_status()
        initial_balance = status['balance'].get('USDC', 0)
        print(f"   Initial balance: {initial_balance:.2f} USDC")
        print("   No existing positions")
        
        print("\n2. CREATING NEW POSITIONS WITH WEIGHTS:")
        print("   Target: 30% AAVE, 70% ETH")
        
        # Create allocation for new positions
        new_allocation = {
            'AAVE/USDC': 0.3,   # 30% = 3000 USDC
            'ETH/USDC': 0.7     # 70% = 7000 USDC
        }
        
        # Execute allocation
        result = executor.execute_multi_asset_strategy(new_allocation)
        print(f"   Execution result: {result.get('success', False)}")
        
        if not result.get('success', False):
            print("   ❌ Failed to create positions!")
            return False
        
        print("\n3. ANALYZING WEIGHT-BASED ALLOCATION:")
        
        # Check final positions
        final_status = executor.get_trading_status()
        final_positions = final_status['positions']
        final_balance = final_status['balance'].get('USDC', 0)
        
        print("   Positions created:")
        success = True
        
        for symbol, position in final_positions.items():
            amount = position.get('amount', 0)
            price = executor.get_current_price(symbol)
            value = amount * price if price else 0
            expected_value = new_allocation.get(symbol, 0) * initial_balance
            
            print(f"     {symbol}: {value:.2f} USDC (expected: {expected_value:.2f} USDC)")
            
            # Check if allocation is correct (allow 5% tolerance for fees)
            if abs(value - expected_value) / expected_value > 0.05:
                print(f"     ❌ Allocation error: {abs(value - expected_value):.2f} USDC difference")
                success = False
            else:
                print(f"     ✅ Correct allocation")
        
        return success
        
    except Exception as e:
        print(f"\n❌ TEST FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting REAL Asset Swap Logic Tests...")
    print("This will use actual paper trading with real prices!")
    
    # Test 1: Asset swap logic
    print("\n🔄 Running asset swap test...")
    swap_success = test_real_asset_swap()
    
    # Test 2: Weight-based allocation
    print("\n🔄 Running weight allocation test...")
    weight_success = test_weight_allocation_new_positions()
    
    print("\n" + "=" * 70)
    print("FINAL TEST RESULTS")
    print("=" * 70)
    print(f"Asset Swap Test: {'✅ PASSED' if swap_success else '❌ FAILED'}")
    print(f"Weight Allocation Test: {'✅ PASSED' if weight_success else '❌ FAILED'}")
    
    if swap_success and weight_success:
        print("\n🎉 ALL TESTS PASSED! The swap logic is working correctly.")
    else:
        print("\n⚠️  SOME TESTS FAILED! Check the output above for details.")
