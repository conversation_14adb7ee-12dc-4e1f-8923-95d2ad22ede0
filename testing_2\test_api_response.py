#!/usr/bin/env python
"""
Test script to verify the API response structure.
This script sends a request to the /api/run-strategy endpoint and checks the response structure.
"""

import requests
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_api_response():
    """Test the API response structure."""
    # API endpoint
    url = "http://localhost:5001/api/run-strategy"
    
    # Request parameters
    params = {
        "use_mtpi_signal": True,
        "mtpi_timeframe": "1d",
        "timeframe": "1d",
        "analysis_start_date": "2023-10-20",
        "transaction_fee_rate": 0.001,
        "selected_assets": ["BTC/USDT", "ETH/USDT", "SOL/USDT"],
        "n_assets": 1
    }
    
    try:
        # Send the request
        logging.info(f"Sending request to {url} with parameters: {params}")
        response = requests.post(url, json=params)
        
        # Check the response status code
        logging.info(f"Response status code: {response.status_code}")
        
        # Parse the response JSON
        try:
            data = response.json()
            logging.info(f"Response JSON keys: {data.keys()}")
            
            # Check if the success flag is present
            if 'success' in data:
                logging.info(f"Success flag: {data['success']}")
            else:
                logging.error("Success flag not found in response")
            
            # Check if the message is present
            if 'message' in data:
                logging.info(f"Message: {data['message']}")
            else:
                logging.error("Message not found in response")
            
            # Check if there's an error
            if 'error' in data:
                logging.error(f"Error: {data['error']}")
            
            # Return the response data
            return data
        except json.JSONDecodeError:
            logging.error("Failed to parse response JSON")
            logging.error(f"Response text: {response.text}")
            return None
    except Exception as e:
        logging.error(f"Error sending request: {e}")
        return None

if __name__ == "__main__":
    test_api_response()
