import React from 'react';
import './TradingViewDashboard.css';
import { ASSET_COLORS } from '../utils/assetColors';

const ChartLegend = ({ availableAssets, getAssetColor }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h2>Chart Legend</h2>
      </div>
      <div className="card-body">
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color" style={{ background: `linear-gradient(90deg, ${ASSET_COLORS['BTC/USDT']} 0%, ${ASSET_COLORS['ETH/USDT']} 25%, ${ASSET_COLORS['SOL/USDT']} 50%, ${ASSET_COLORS['SUI/USDT']} 75%, ${ASSET_COLORS['out-of-market']} 100%)` }}></div>
            <span>Strategy (colored by held asset)</span>
          </div>
          {availableAssets.map(asset => (
            <div className="legend-item" key={asset}>
              <div className="legend-color" style={{ backgroundColor: getAssetColor(asset), opacity: 0.5 }}></div>
              <span>{asset} B&H</span>
            </div>
          ))}
          <div className="legend-item">
            <div className="legend-color" style={{ backgroundColor: ASSET_COLORS['out-of-market'] }}></div>
            <span>Out of Market</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartLegend;
