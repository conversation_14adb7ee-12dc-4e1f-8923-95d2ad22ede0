#!/usr/bin/env python3
"""
Debug script to understand candle timestamp interpretation.
"""

import logging
import pandas as pd
from datetime import datetime, timezone, timedelta
from src.data_fetcher import fetch_ohlcv_data

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_candle_timestamps():
    """Debug how candle timestamps work."""
    
    print("\n" + "="*80)
    print("DEBUGGING CANDLE TIMESTAMP INTERPRETATION")
    print("="*80)
    
    # Test parameters
    exchange_id = 'binance'
    symbol = 'AAVE/USDT'
    timeframe = '1d'
    
    print(f"\nFetching last 5 candles for {symbol}")
    print(f"Current time: {datetime.now(timezone.utc)}")
    
    try:
        # First, let's see what raw data we get from the exchange
        import ccxt
        exchange = ccxt.binance()
        raw_candles = exchange.fetch_ohlcv(symbol, timeframe, limit=5)

        print("\nRAW DATA FROM EXCHANGE (last 5 candles):")
        print("-" * 60)
        for i, candle in enumerate(raw_candles):
            timestamp_ms, open_price, high, low, close, volume = candle
            timestamp = pd.to_datetime(timestamp_ms, unit='ms', utc=True)
            print(f"Raw candle {i+1}: {timestamp} -> Close: {close:.2f}")

        # Now fetch processed data with filtering
        data = fetch_ohlcv_data(
            exchange_id=exchange_id,
            symbols=[symbol],
            timeframe=timeframe,
            limit=5,
            force_refresh=True
        )
        
        if symbol in data and not data[symbol].empty:
            df = data[symbol]
            
            print(f"\nFetched {len(df)} candles")
            print("\nLast 5 candles with detailed timestamp analysis:")
            print("-" * 80)
            
            for i, (timestamp, row) in enumerate(df.tail(5).iterrows()):
                # Calculate what trading day this represents
                trading_day = timestamp.date() - timedelta(days=1)
                
                print(f"Candle {i+1}:")
                print(f"  Timestamp: {timestamp} ({timestamp.date()})")
                print(f"  Represents trading day: {trading_day}")
                print(f"  Close price: {row['close']:.2f}")
                print(f"  Volume: {row['volume']:.2f}")
                
                # Check if this is today's incomplete candle
                current_date = datetime.now(timezone.utc).date()
                if timestamp.date() == current_date:
                    print(f"  ⚠️  This is TODAY'S candle (incomplete)")
                elif timestamp.date() == current_date - timedelta(days=1):
                    print(f"  ✅ This is YESTERDAY'S complete candle")
                else:
                    print(f"  📅 This is a historical candle")
                print()
            
            # Show the interpretation
            print("INTERPRETATION:")
            print("-" * 40)
            last_timestamp = df.index.max()
            last_date = last_timestamp.date()
            current_date = datetime.now(timezone.utc).date()
            
            if last_date == current_date:
                trading_day = last_date - timedelta(days=1)
                print(f"Last candle timestamp: {last_timestamp}")
                print(f"This represents the COMPLETE trading day: {trading_day}")
                print(f"The candle closed at: {last_timestamp} (00:00 UTC)")
                print("✅ This is the last COMPLETE candle we should use")
            else:
                print(f"Last candle is from: {last_date}")
                print("This appears to be historical data")
                
        else:
            print("No data returned")
            
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*80)
    print("CONCLUSION")
    print("="*80)
    print("Daily candles work as follows:")
    print("- Timestamp 2025-06-22 00:00:00 = Close of June 21st trading day")
    print("- Timestamp 2025-06-23 00:00:00 = Close of June 22nd trading day")
    print("- We should include candles up to current day 00:00 UTC")
    print("- We should NOT filter out 'today's' 00:00 candle - it's yesterday's close!")

if __name__ == "__main__":
    debug_candle_timestamps()
