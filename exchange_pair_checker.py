#!/usr/bin/env python3
"""
Exchange Pair Availability Checker

This script checks the availability of USDC/USDT pairs across multiple exchanges
and saves the results to a CSV file for comparison.

Supported exchanges: OKX, Bybit, Kraken, Bitvavo
"""
import ccxt
import pandas as pd
import time
import logging
from typing import Dict, List, Set, Optional, Tuple
import argparse
from pathlib import Path
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExchangePairChecker:
    def __init__(self, min_age_days: int = 35):
        self.min_age_days = min_age_days
        self.exchanges = {
            'okx': ccxt.okx({'sandbox': False, 'enableRateLimit': True}),
            'bybit': ccxt.bybit({'sandbox': False, 'enableRateLimit': True}),
            'kraken': ccxt.kraken({'sandbox': False, 'enableRateLimit': True}),
            'bitvavo': ccxt.bitvavo({'sandbox': False, 'enableRateLimit': True})
        }
        
        # Tokens from the file (extracted from the pairs)
        self.tokens = [
            'ETH', 'BTC', 'SUI', 'SOL', 'FDUSD', 'PEPE', 'TRUMP', 'XRP', 'DOGE', 'WIF',
            'EUR', 'BNB', 'ADA', 'WLD', 'ENA', 'CETUS', 'AVAX', 'FET', 'RUNE', 'HBAR',
            'LINK', 'NEIRO', 'APT', 'LTC', 'S', 'VIRTUAL', 'TAO', 'BONK', 'INJ', 'CRV',
            'ARB', 'AAVE', 'COOKIE', 'PNUT', 'SEI', 'NEAR', 'RENDER', 'TIA', 'BCH', 'TRX',
            'JUP', 'ONDO', 'MUBARAK', 'PENGU', 'UNI', 'EIGEN', 'DOT', 'GALA', 'ATOM', 'KAITO',
            'FLOKI', 'SHIB', 'BOME', 'OP', 'PYTH', 'OM', 'ALGO', 'XLM', 'LAYER', 'BIO',
            'USUAL', 'WCT', 'BERA', 'ZRO', 'RAY', 'KERNEL', 'STX', 'LDO', 'AIXBT', 'BABY',
            'PENDLE', 'ARKM', 'IO', 'PEOPLE', 'ETHFI', 'FIL', 'ICP', 'POL', 'TURBO', 'SAGA',
            'CAKE', 'CGPT', 'ANIME', 'AR', 'VANRY', 'ORDI', 'EGLD', '1000SATS', 'MANTA', 'TON',
            'RSR', 'NIL', 'NOT', 'DYDX', 'ZK', 'VET', 'ACT', 'BEAMX', 'W', 'PHA',
            'TST', 'STRK', 'NEO', 'JTO', 'RARE', 'CFX', 'EOS', 'ORCA', 'ENS', 'APE',
            'COW', 'CHZ', 'THETA', 'BB', 'BROCCOLI714', 'TUT', 'IOTA', 'MKR', 'BMT', 'SAND',
            '1000CAT', 'FORM', 'SHELL', 'OSMO', 'QNT', 'SYN', 'REZ', 'ALT', 'PIXEL', 'MEME',
            'VANA', 'DOGS', 'PARTI', 'CVC', 'MOVE', 'XTZ', '1MBABYDOGE', 'GUN', 'CKB', 'OMNI',
            'THE', 'RED', 'BLUR', 'SLF', 'HMSTR', 'CATI', 'BANANA', 'TRB', 'AUCTION', 'HIVE',
            'TNSR', 'ONT', 'BIGTIME', '1000CHEEMS', 'RPL', 'YGG', 'ZEN', 'HEI', 'ACX', 'BANANAS31',
            'API3', 'EPIC', 'CHESS', 'VELODROME', 'KAIA', 'STEEM', 'GPS', 'UTK', 'TLM', 'GMX',
            'T', 'JUV', 'IDEX', 'DF'
        ]
        
        self.quote_currencies = ['USDC', 'USDT']
        self.results = []
        self.token_ages = {}  # Cache for token ages

    def load_markets_for_exchange(self, exchange_name: str, exchange_obj) -> Dict[str, any]:
        """Load markets for a specific exchange with error handling."""
        try:
            logger.info(f"Loading markets for {exchange_name}...")
            markets = exchange_obj.load_markets()
            logger.info(f"Successfully loaded {len(markets)} markets for {exchange_name}")
            return markets
        except Exception as e:
            logger.error(f"Failed to load markets for {exchange_name}: {str(e)}")
            return {}



    def check_data_availability(
        self,
        exchange: ccxt.Exchange,
        symbol: str,
        min_days: int = None,
        timeframe: str = '1d'
    ) -> Tuple[bool, int]:
        """
        Check if a trading pair has at least min_days of historical data.

        Args:
            exchange: The CCXT exchange instance
            symbol: The trading pair symbol
            min_days: Minimum number of days of data required (default: self.min_age_days)
            timeframe: The timeframe to check ('1d' for daily)

        Returns:
            Tuple of (has_enough_data, days_available)
        """
        if min_days is None:
            min_days = self.min_age_days

        try:
            # Calculate the start timestamp (min_days ago from now)
            now = datetime.now()
            since = int((now - timedelta(days=min_days)).timestamp() * 1000)

            # Fetch the OHLCV data
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=min_days + 5)

            # Check if we have enough data
            days_available = len(ohlcv)
            has_enough_data = days_available >= min_days

            return has_enough_data, days_available
        except Exception as e:
            logger.debug(f"Could not fetch history for {symbol}: {e}")
            return False, 0

    def check_pair_availability_and_age(self, exchange_name: str, exchange_obj, token: str, quote: str) -> Dict:
        """
        Check if a specific pair is available on the exchange AND has sufficient trading history.

        Returns:
            Dict with keys: 'available', 'has_enough_data', 'days_available', 'symbol_used'
        """
        result = {
            'available': False,
            'has_enough_data': False,
            'days_available': 0,
            'symbol_used': None
        }

        possible_symbols = [
            f"{token}/{quote}",
            f"{token}{quote}",
            f"{token}_{quote}",
            f"{token}-{quote}"
        ]

        # First check if the symbol exists in markets
        try:
            markets = exchange_obj.load_markets()
        except Exception as e:
            logger.debug(f"Could not load markets for {exchange_name}: {e}")
            return result

        found_symbol = None
        for symbol in possible_symbols:
            if symbol in markets:
                found_symbol = symbol
                break

        # Also check if the symbol exists with different formatting
        if not found_symbol:
            target_symbol = f"{token}{quote}".upper()
            for market_symbol in markets.keys():
                if market_symbol.replace('/', '').replace('-', '').replace('_', '').upper() == target_symbol:
                    found_symbol = market_symbol
                    break

        if found_symbol:
            result['available'] = True
            result['symbol_used'] = found_symbol

            # Now check if it has enough trading history
            has_enough_data, days_available = self.check_data_availability(
                exchange_obj, found_symbol, self.min_age_days
            )

            result['has_enough_data'] = has_enough_data
            result['days_available'] = days_available

        return result

    def check_all_pairs(self):
        """Check all token pairs across all exchanges."""
        logger.info("Starting pair availability and age verification check across all exchanges...")
        logger.info(f"Checking for pairs with at least {self.min_age_days} days of trading history...")

        # Check each token against each quote currency on each exchange
        total_checks = len(self.tokens) * len(self.quote_currencies) * len(self.exchanges)
        current_check = 0

        for token in self.tokens:
            for quote in self.quote_currencies:
                row_data = {
                    'Token': token,
                    'Quote': quote,
                    'Pair': f"{token}/{quote}"
                }

                for exchange_name, exchange_obj in self.exchanges.items():
                    current_check += 1
                    if current_check % 25 == 0:
                        logger.info(f"Progress: {current_check}/{total_checks} checks completed")

                    # Check both availability and age in one call
                    result = self.check_pair_availability_and_age(exchange_name, exchange_obj, token, quote)

                    # Store results
                    row_data[f"{exchange_name.upper()}_available"] = result['available']
                    row_data[f"{exchange_name.upper()}_has_enough_data"] = result['has_enough_data']
                    row_data[f"{exchange_name.upper()}_days_available"] = result['days_available']

                    if result['available'] and result['has_enough_data']:
                        logger.debug(f"{token}/{quote} on {exchange_name.upper()}: ✓ Available with {result['days_available']} days of data")
                    elif result['available'] and not result['has_enough_data']:
                        logger.debug(f"{token}/{quote} on {exchange_name.upper()}: ⚠ Available but only {result['days_available']} days of data (need {self.min_age_days})")
                    else:
                        logger.debug(f"{token}/{quote} on {exchange_name.upper()}: ✗ Not available")

                self.results.append(row_data)
        
        logger.info(f"Completed all {total_checks} pair availability checks")

    def save_results(self, output_file: str = "exchange_pair_availability.csv"):
        """Save results to CSV file."""
        if not self.results:
            logger.warning("No results to save")
            return
        
        df = pd.DataFrame(self.results)
        
        # Add summary columns
        exchange_cols = [col for col in df.columns if col.endswith('_available')]
        exchange_data_cols = [col for col in df.columns if col.endswith('_has_enough_data')]

        df['Total_Exchanges_Available'] = df[exchange_cols].sum(axis=1)
        df['Total_Exchanges_With_Enough_Data'] = df[exchange_data_cols].sum(axis=1)

        df['Available_On'] = df.apply(
            lambda row: ', '.join([col.replace('_available', '') for col in exchange_cols if row[col]]),
            axis=1
        )

        df['Available_With_Enough_Data_On'] = df.apply(
            lambda row: ', '.join([col.replace('_has_enough_data', '') for col in exchange_data_cols if row[col]]),
            axis=1
        )
        
        # Sort by token name and quote currency
        df = df.sort_values(['Token', 'Quote'])
        
        # Save to CSV
        df.to_csv(output_file, index=False)
        logger.info(f"Results saved to {output_file}")
        
        # Print summary statistics
        self.print_summary(df)

    def print_summary(self, df: pd.DataFrame):
        """Print summary statistics."""
        logger.info("\n" + "="*60)
        logger.info("SUMMARY STATISTICS")
        logger.info("="*60)
        
        total_pairs = len(df)
        logger.info(f"Total pairs checked: {total_pairs}")
        
        for exchange in ['OKX', 'BYBIT', 'KRAKEN', 'BITVAVO']:
            available_col = f"{exchange}_available"
            data_col = f"{exchange}_has_enough_data"

            if available_col in df.columns and data_col in df.columns:
                available_count = df[available_col].sum()
                data_count = df[data_col].sum()
                available_pct = (available_count / total_pairs) * 100
                data_pct = (data_count / total_pairs) * 100

                logger.info(f"{exchange}: {available_count}/{total_pairs} pairs available ({available_pct:.1f}%), "
                           f"{data_count}/{total_pairs} with {self.min_age_days}+ days data ({data_pct:.1f}%)")
        
        # Most supported tokens (with enough data)
        logger.info(f"\nTop 10 tokens with most {self.min_age_days}+ day trading pairs:")
        token_support = df.groupby('Token')['Total_Exchanges_With_Enough_Data'].sum().sort_values(ascending=False).head(10)
        for token, support_count in token_support.items():
            logger.info(f"  {token}: {support_count} exchange-quote combinations with {self.min_age_days}+ days data")

        # USDC vs USDT comparison
        usdc_pairs_available = df[df['Quote'] == 'USDC']['Total_Exchanges_Available'].sum()
        usdt_pairs_available = df[df['Quote'] == 'USDT']['Total_Exchanges_Available'].sum()
        usdc_pairs_with_data = df[df['Quote'] == 'USDC']['Total_Exchanges_With_Enough_Data'].sum()
        usdt_pairs_with_data = df[df['Quote'] == 'USDT']['Total_Exchanges_With_Enough_Data'].sum()

        logger.info(f"\nUSDC pairs - Available: {usdc_pairs_available}, With {self.min_age_days}+ days data: {usdc_pairs_with_data}")
        logger.info(f"USDT pairs - Available: {usdt_pairs_available}, With {self.min_age_days}+ days data: {usdt_pairs_with_data}")

def main():
    parser = argparse.ArgumentParser(description='Check cryptocurrency pair availability across exchanges')
    parser.add_argument('--output', '-o', default='exchange_pair_availability.csv',
                        help='Output CSV file name (default: exchange_pair_availability.csv)')
    parser.add_argument('--tokens', nargs='+',
                        help='Specific tokens to check (default: all tokens from file)')
    parser.add_argument('--exchanges', nargs='+', choices=['okx', 'bybit', 'kraken', 'bitvavo'],
                        help='Specific exchanges to check (default: all)')
    parser.add_argument('--min-age-days', type=int, default=35,
                        help='Minimum days of trading history required (default: 35)')

    args = parser.parse_args()

    checker = ExchangePairChecker(min_age_days=args.min_age_days)
    
    # Override tokens if specified
    if args.tokens:
        checker.tokens = [token.upper() for token in args.tokens]
        logger.info(f"Checking specific tokens: {checker.tokens}")

    # Override exchanges if specified
    if args.exchanges:
        checker.exchanges = {name: checker.exchanges[name] for name in args.exchanges if name in checker.exchanges}
        logger.info(f"Checking specific exchanges: {list(checker.exchanges.keys())}")



    try:
        checker.check_all_pairs()
        checker.save_results(args.output)
        logger.info("Pair availability check completed successfully!")
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        raise

if __name__ == "__main__":
    main()
