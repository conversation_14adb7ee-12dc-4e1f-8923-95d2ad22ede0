#!/usr/bin/env python3
"""
Print all signal changes for median score indicator to compare with TradingView.
This will help identify exact discrepancies.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config

def print_all_median_signals(start_date='2024-01-01'):
    """Print all median score signal changes for comparison with TradingView."""
    print("=" * 100)
    print("COMPLETE MEDIAN SCORE SIGNAL HISTORY")
    print("PineScript Parameters: subject1=12, mul1=1.45, slen=27, src_me=high")
    print("=" * 100)
    
    # Load configuration
    config = load_mtpi_multi_indicator_config()
    median_config = config.get('median_score', {})
    
    print(f"\nConfiguration:")
    print(f"  atr_period (subject1): {median_config.get('atr_period', 12)}")
    print(f"  multiplier (mul1): {median_config.get('multiplier', 1.45)}")
    print(f"  median_length (slen): {median_config.get('median_length', 27)}")
    print(f"  src_col (src_me): {median_config.get('src_col', 'high')}")
    
    # Fetch data
    print(f"\nFetching BTC/USDT data from {start_date}...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since=start_date
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Calculate median score
    signal, supertrend_line, direction = calculate_median_score(
        btc_df,
        atr_period=median_config.get('atr_period', 12),
        multiplier=median_config.get('multiplier', 1.45),
        median_length=median_config.get('median_length', 27),
        src_col=median_config.get('src_col', 'high')
    )
    
    # Find all signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"\n" + "=" * 100)
    print(f"FOUND {len(change_indices)} SIGNAL CHANGES")
    print("=" * 100)
    
    # Print header
    print(f"{'Date':<12} {'Signal':<8} {'Price':<10} {'High':<10} {'Low':<10} {'Direction':<10} {'Supertrend':<12} {'Logic':<15}")
    print("-" * 100)
    
    # Print all signal changes
    for i, change_date in enumerate(change_indices):
        idx = btc_df.index.get_loc(change_date)
        
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            
            # Price data
            close_price = btc_df['close'].iloc[idx]
            high_price = btc_df['high'].iloc[idx]
            low_price = btc_df['low'].iloc[idx]
            
            # Technical data
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1] if idx > 0 else direction_val
            supertrend_val = supertrend_line.iloc[idx]
            
            # Signal logic
            signal_change = f"{prev_signal}→{curr_signal}"
            signal_type = "LONG" if curr_signal == 1 else "SHORT" if curr_signal == -1 else "NEUTRAL"
            direction_change = f"{prev_direction}→{direction_val}"
            
            # Logic verification
            if curr_signal == 1 and direction_val == -1:
                logic = "✅ L1 (crossunder)"
            elif curr_signal == -1 and direction_val == 1:
                logic = "✅ S1 (crossover)"
            else:
                logic = "❌ Logic error"
            
            print(f"{change_date.strftime('%Y-%m-%d'):<12} "
                  f"{signal_change:<8} "
                  f"${close_price:<9.2f} "
                  f"${high_price:<9.2f} "
                  f"${low_price:<9.2f} "
                  f"{direction_change:<10} "
                  f"${supertrend_val:<11.2f} "
                  f"{logic:<15}")
    
    # Print detailed analysis for recent signals
    print(f"\n" + "=" * 100)
    print("DETAILED ANALYSIS - LAST 10 SIGNAL CHANGES")
    print("=" * 100)
    
    for change_date in change_indices[-10:]:
        idx = btc_df.index.get_loc(change_date)
        
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            
            close_price = btc_df['close'].iloc[idx]
            high_price = btc_df['high'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            supertrend_val = supertrend_line.iloc[idx]
            
            signal_type = "LONG" if curr_signal == 1 else "SHORT" if curr_signal == -1 else "NEUTRAL"
            price_vs_st = ((close_price - supertrend_val) / supertrend_val) * 100
            
            print(f"\n📅 {change_date.strftime('%Y-%m-%d')} ({change_date.strftime('%A')})")
            print(f"   Signal Change: {prev_signal} → {curr_signal} ({signal_type})")
            print(f"   Direction Change: {prev_direction} → {direction_val}")
            print(f"   Price Data:")
            print(f"     Close: ${close_price:,.2f}")
            print(f"     High:  ${high_price:,.2f}")
            print(f"     Supertrend: ${supertrend_val:,.2f}")
            print(f"     Price vs ST: {price_vs_st:+.2f}%")
            
            # PineScript logic explanation
            if curr_signal == 1:
                print(f"   📈 LONG Logic: ta.crossunder(d1, 0) → L1=true → medianScore := 1")
                print(f"      Direction crossed under 0 (from {prev_direction} to {direction_val})")
            elif curr_signal == -1:
                print(f"   📉 SHORT Logic: ta.crossover(d1, 0) → S1=true → medianScore := -1")
                print(f"      Direction crossed over 0 (from {prev_direction} to {direction_val})")
    
    # Current status
    current_signal = signal.iloc[-1]
    current_price = btc_df['close'].iloc[-1]
    current_supertrend = supertrend_line.iloc[-1]
    current_direction = direction.iloc[-1]
    
    print(f"\n" + "=" * 100)
    print("CURRENT STATUS")
    print("=" * 100)
    print(f"Current Date: {btc_df.index[-1].strftime('%Y-%m-%d')}")
    print(f"Current Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Current Price: ${current_price:,.2f}")
    print(f"Current Supertrend: ${current_supertrend:,.2f}")
    print(f"Current Direction: {current_direction}")
    print(f"Price vs Supertrend: {((current_price - current_supertrend) / current_supertrend * 100):+.2f}%")
    
    # Signal statistics
    signal_counts = signal.value_counts().sort_index()
    total_signals = len(signal)
    
    print(f"\n" + "=" * 100)
    print("SIGNAL STATISTICS")
    print("=" * 100)
    print(f"Total candles analyzed: {total_signals}")
    print(f"Total signal changes: {len(change_indices)}")
    
    for sig_val, count in signal_counts.items():
        sig_name = "SHORT" if sig_val == -1 else "NEUTRAL" if sig_val == 0 else "LONG"
        percentage = (count / total_signals) * 100
        print(f"{sig_name} ({sig_val}): {count} candles ({percentage:.1f}%)")
    
    print(f"\n" + "=" * 100)
    print("COPY THIS DATA TO COMPARE WITH TRADINGVIEW")
    print("=" * 100)
    print("Format: Date, Signal_Change, Close_Price, Direction_Change")
    
    for change_date in change_indices:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            
            print(f"{change_date.strftime('%Y-%m-%d')}, {prev_signal}→{curr_signal}, ${close_price:.2f}, {prev_direction}→{direction_val}")

if __name__ == "__main__":
    # You can change the start date here
    print_all_median_signals(start_date='2024-01-01')
