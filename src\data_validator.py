import pandas as pd
import logging
from typing import Dict

def validate_and_fix_timestamp_ordering(data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Validate and fix timestamp ordering for all dataframes.
    
    Ensures all data is in chronological order (oldest to newest) which is
    required for proper time series analysis and strategy backtesting.
    
    Args:
        data_dict: Dictionary of symbol -> DataFrame
        
    Returns:
        Dictionary with all dataframes properly sorted
    """
    fixed_data = {}
    
    for symbol, df in data_dict.items():
        if df.empty:
            fixed_data[symbol] = df
            continue
            
        # Check if timestamps are in ascending order
        is_sorted = df.index.is_monotonic_increasing
        
        if not is_sorted:
            logging.warning(f"Timestamps for {symbol} are not in chronological order. Sorting...")
            
            # Sort by index (timestamp)
            df_sorted = df.sort_index()
            
            # Check for duplicates
            if df_sorted.index.duplicated().any():
                logging.warning(f"Found duplicate timestamps for {symbol}. Removing duplicates...")
                df_sorted = df_sorted[~df_sorted.index.duplicated(keep='first')]
            
            fixed_data[symbol] = df_sorted
            logging.info(f"Fixed timestamp ordering for {symbol}: {len(df_sorted)} rows")
        else:
            fixed_data[symbol] = df
            logging.debug(f"Timestamp ordering OK for {symbol}")
    
    return fixed_data

def validate_data_consistency(data_dict: Dict[str, pd.DataFrame]) -> bool:
    """
    Validate data consistency across all assets.
    
    Args:
        data_dict: Dictionary of symbol -> DataFrame
        
    Returns:
        True if all data passes validation
    """
    issues_found = False
    
    for symbol, df in data_dict.items():
        if df.empty:
            logging.warning(f"Empty dataframe for {symbol}")
            issues_found = True
            continue
            
        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logging.error(f"Missing columns for {symbol}: {missing_cols}")
            issues_found = True
            
        # Check for NaN values
        if df.isnull().any().any():
            nan_count = df.isnull().sum().sum()
            logging.warning(f"Found {nan_count} NaN values in {symbol}")
            
        # Check timestamp ordering
        if not df.index.is_monotonic_increasing:
            logging.error(f"Timestamps not in chronological order for {symbol}")
            issues_found = True
            
        # Check for negative prices
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            if col in df.columns and (df[col] <= 0).any():
                negative_count = (df[col] <= 0).sum()
                logging.error(f"Found {negative_count} negative/zero prices in {col} for {symbol}")
                issues_found = True
                
        # Check OHLC logic (high >= open,close,low and low <= open,close,high)
        if all(col in df.columns for col in price_cols):
            invalid_high = (df['high'] < df[['open', 'close', 'low']].max(axis=1)).any()
            invalid_low = (df['low'] > df[['open', 'close', 'high']].min(axis=1)).any()
            
            if invalid_high or invalid_low:
                logging.error(f"Invalid OHLC relationships found for {symbol}")
                issues_found = True
                
    if not issues_found:
        logging.info("All data passed validation checks")
        
    return not issues_found