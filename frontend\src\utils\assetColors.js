/**
 * Asset Colors Utility
 * 
 * This file contains predefined colors for assets and utility functions for color management.
 * It ensures consistent coloring across the application for up to 40 different assets.
 */

// Predefined colors for common assets - keeping BTC, ETH, SOL as they are
export const ASSET_COLORS = {
  // Keep existing colors for BTC, ETH, SOL
  'BTC/USDT': '#f7931a', // Bitcoin orange
  'ETH/USDT': '#627eea', // Ethereum blue
  'SOL/USDT': '#14F195', // Solana green
  
  // Additional predefined colors for other assets (40 total)
  'LINK/USDT': '#2a5ada', // Chainlink blue
  'DOT/USDT': '#e6007a', // Polkadot pink
  'ADA/USDT': '#0033ad', // Cardano blue
  'AVAX/USDT': '#e84142', // Avalanche red
  'MATIC/USDT': '#8247e5', // Polygon purple
  'XRP/USDT': '#23292f', // XRP black
  'DOGE/USDT': '#c3a634', // <PERSON><PERSON>oin gold
  'SHIB/USDT': '#faa21a', // <PERSON>ba Inu orange
  'UNI/USDT': '#ff007a', // Uniswap pink
  'ATOM/USDT': '#2e3148', // Cosmos dark blue
  'LTC/USDT': '#345d9d', // Litecoin blue
  'SUI/USDT': '#6f4ef2', // SUI purple
  'AAVE/USDT': '#b6509e', // AAVE pink
  'ALGO/USDT': '#000000', // Algorand black
  'APE/USDT': '#0052ff', // ApeCoin blue
  'APT/USDT': '#09d8c4', // Aptos teal
  'ARB/USDT': '#2d374b', // Arbitrum dark blue
  'BCH/USDT': '#8dc351', // Bitcoin Cash green
  'BNB/USDT': '#f3ba2f', // Binance yellow
  'COMP/USDT': '#00d395', // Compound green
  'CRO/USDT': '#103f68', // Cronos blue
  'DAI/USDT': '#f5ac37', // DAI gold
  'DASH/USDT': '#008ce7', // Dash blue
  'EOS/USDT': '#000000', // EOS black
  'ETC/USDT': '#328332', // Ethereum Classic green
  'FIL/USDT': '#0090ff', // Filecoin blue
  'FTM/USDT': '#1969ff', // Fantom blue
  'GALA/USDT': '#00d8ff', // Gala cyan
  'HBAR/USDT': '#222222', // Hedera dark gray
  'ICP/USDT': '#3b00b9', // Internet Computer purple
  'MANA/USDT': '#ff2d55', // Decentraland red
  'NEAR/USDT': '#000000', // NEAR black
  'OP/USDT': '#ff0420', // Optimism red
  'SAND/USDT': '#00aeff', // The Sandbox blue
  'THETA/USDT': '#2ab8e6', // Theta blue
  'TRX/USDT': '#ff0013', // TRON red
  'VET/USDT': '#15bdff', // VeChain blue
  'XLM/USDT': '#14b6e7', // Stellar blue
  'XMR/USDT': '#ff6600', // Monero orange
  'XTZ/USDT': '#2c7df7', // Tezos blue
  'ZEC/USDT': '#ecb244', // Zcash gold
  
  // Special case
  'out-of-market': '#999999' // Gray for out-of-market periods
};

/**
 * Generate a deterministic color for an asset not in the predefined list
 * @param {string} asset - Asset symbol
 * @returns {string} - Hex color code
 */
export const generateAssetColor = (asset) => {
  // Generate a deterministic color based on the asset name
  let hash = 0;
  for (let i = 0; i < asset.length; i++) {
    hash = asset.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Convert to hex color
  let color = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xFF;
    color += ('00' + value.toString(16)).slice(-2);
  }

  return color;
};

/**
 * Get color for an asset, using predefined colors if available or generating one if not
 * @param {string} asset - Asset symbol
 * @returns {string} - Hex color code
 */
export const getAssetColor = (asset) => {
  if (ASSET_COLORS[asset]) {
    return ASSET_COLORS[asset];
  }
  return generateAssetColor(asset);
};

/**
 * Convert hex color to RGB format for CSS variables
 * @param {string} hex - Hex color code
 * @returns {string} - RGB values as "r, g, b" string
 */
export const hexToRgb = (hex) => {
  // Make sure the hex color starts with #
  const normalizedHex = hex.startsWith('#') ? hex : `#${hex}`;
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(normalizedHex);
  return result ?
    `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
    '0, 0, 0';
};
