import React from 'react';
import {
  <PERSON>,
  Text,
  SimpleGrid,
  Badge,
  <PERSON>vider,
  Button,
  useColorModeValue,
  Flex,
  Tooltip,
  Link,
} from '@chakra-ui/react';
import { SettingsIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { Link as RouterLink } from 'react-router-dom';

const TradingSettings = ({ settings, isLoading }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Format transaction fee as percentage
  const formatFeePercentage = (fee) => {
    if (typeof fee !== 'number') return '0.1%';
    return `${(fee * 100).toFixed(2)}%`;
  };

  // Format settings for display
  const getDisplayValue = (key, value) => {
    if (key === 'transaction_fee_rate') {
      return formatFeePercentage(value);
    }

    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }

    if (value === null || value === undefined) {
      return '-';
    }

    return value.toString();
  };

  // Format timeframe for display
  const formatTimeframe = (timeframe) => {
    if (!timeframe) return '1d';

    const timeframeMap = {
      '1m': '1 Minute',
      '5m': '5 Minutes',
      '15m': '15 Minutes',
      '30m': '30 Minutes',
      '1h': '1 Hour',
      '4h': '4 Hours',
      '12h': '12 Hours',
      '1d': '1 Day',
      '1w': '1 Week'
    };

    return timeframeMap[timeframe] || timeframe;
  };

  // Group settings by category
  const groupedSettings = {
    'Asset Rotation': {
      'Timeframe': formatTimeframe(settings?.timeframe || '1d'),
      'Initial Capital': settings?.initial_capital || 10000,
      'Transaction Fee': formatFeePercentage(settings?.transaction_fee_rate || 0.001),
    },
    'MTPI Signal': {
      'Indicator Type': settings?.mtpi_indicator_type || 'PGO',
      'Timeframe': formatTimeframe(settings?.mtpi_timeframe || '1d'),
      'PGO Length': settings?.mtpi_pgo_length || 35,
      'Upper Threshold': settings?.mtpi_upper_threshold || 1.1,
      'Lower Threshold': settings?.mtpi_lower_threshold || -0.58,
    },
    'Trend Detection': {
      'Method': settings?.trend_method || 'RSI',
      'RSI Length': settings?.rsi_length || 14,
      'RSI MA Length': settings?.rsi_ma_length || 14,
      'SuperTrend Factor': settings?.supertrend_factor || 3,
      'CCI Length': settings?.cci_length || 20,
      'DMI Length': settings?.dmi_length || 14,
      'PGO Length': settings?.pgo_length || 35,
      'AAD Period': settings?.aad_period || 14,
    }
  };

  if (isLoading) {
    return (
      <Box
        bg={bgColor}
        p={4}
        borderRadius="lg"
        boxShadow="sm"
        height="100%"
        border="1px solid"
        borderColor={borderColor}
      >
        <Flex justify="space-between" align="center" mb={4}>
          <Text fontSize="lg" fontWeight="bold">
            Strategy Settings
          </Text>
          <Badge colorScheme="blue">Loading...</Badge>
        </Flex>
      </Box>
    );
  }

  return (
    <Box
      bg={bgColor}
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      height="100%"
      border="1px solid"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="bold">
          Strategy Settings
        </Text>
        <Tooltip label="Edit settings">
          <Button
            as={RouterLink}
            to="/settings"
            size="sm"
            leftIcon={<SettingsIcon />}
            colorScheme="blue"
            variant="outline"
          >
            Edit Settings
          </Button>
        </Tooltip>
      </Flex>

      {Object.entries(groupedSettings).map(([category, categorySettings], index) => (
        <Box key={category} mb={index < Object.keys(groupedSettings).length - 1 ? 4 : 0}>
          <Text fontWeight="medium" mb={2} color="blue.500">
            {category}
          </Text>
          <SimpleGrid columns={2} spacing={2} mb={3}>
            {Object.entries(categorySettings).map(([key, value]) => (
              <Box key={key}>
                <Flex justify="space-between">
                  <Text fontSize="sm" color="gray.500">
                    {key}
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {getDisplayValue(key, value)}
                  </Text>
                </Flex>
              </Box>
            ))}
          </SimpleGrid>
          {index < Object.keys(groupedSettings).length - 1 && <Divider />}
        </Box>
      ))}

      <Divider my={3} />

      <Flex justify="center" mt={2}>
        <Link as={RouterLink} to="/backtesting" color="blue.500" fontSize="sm">
          <Flex align="center">
            <Text>View Backtesting Dashboard</Text>
            <ExternalLinkIcon ml={1} />
          </Flex>
        </Link>
      </Flex>
    </Box>
  );
};

export default TradingSettings;
