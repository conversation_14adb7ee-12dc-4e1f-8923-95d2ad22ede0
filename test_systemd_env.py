#!/usr/bin/env python3
"""
Test script to simulate systemd environment loading and verify notification config
"""

import os
import sys
from dotenv import load_dotenv

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_systemd_env_simulation():
    """Simulate how systemd loads environment variables and test notification config"""
    
    print("🔍 Testing Systemd Environment Variable Loading")
    
    # Clear existing environment variables first
    for key in ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID']:
        if key in os.environ:
            del os.environ[key]
    
    print("\n=== Step 1: Simulating systemd EnvironmentFile loading ===")
    # Simulate systemd loading .env.Liepa
    load_dotenv('.env.Liepa', override=True)
    
    telegram_token = os.getenv('TELEGRAM_BOT_TOKEN')
    telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
    
    print(f"TELEGRAM_BOT_TOKEN: {'✅ Set' if telegram_token else '❌ Not set'}")
    print(f"TELEGRAM_CHAT_ID: {'✅ Set' if telegram_chat_id else '❌ Not set'}")
    
    if telegram_token:
        print(f"Token (first 10 chars): {telegram_token[:10]}...")
    if telegram_chat_id:
        print(f"Chat ID: {telegram_chat_id}")
    
    print("\n=== Step 2: Testing notification config loading ===")
    try:
        from src.config_manager import load_notification_config
        
        # Test Liepa's Kraken config
        config = load_notification_config('config/notifications_kraken_liepa.json')
        telegram_config = config.get('telegram', {})
        
        print(f"Notification config loaded successfully")
        print(f"Telegram enabled: {telegram_config.get('enabled')}")
        print(f"Telegram token from config: {'✅ Set' if telegram_config.get('token') else '❌ Empty (will use env)'}")
        print(f"Telegram chat_id from config: {'✅ Set' if telegram_config.get('chat_id') else '❌ Empty (will use env)'}")
        
        # Check if environment variables were loaded into config
        final_token = telegram_config.get('token')
        final_chat_id = telegram_config.get('chat_id')
        
        print(f"\nFinal resolved values:")
        print(f"Token: {'✅ Set' if final_token else '❌ Not set'}")
        print(f"Chat ID: {'✅ Set' if final_chat_id else '❌ Not set'}")
        
        if final_token and final_token == telegram_token:
            print("✅ Token correctly loaded from environment!")
        elif final_token:
            print("⚠️ Token set but doesn't match environment")
        else:
            print("❌ Token not loaded from environment")
            
        if final_chat_id and final_chat_id == telegram_chat_id:
            print("✅ Chat ID correctly loaded from environment!")
        elif final_chat_id:
            print("⚠️ Chat ID set but doesn't match environment")
        else:
            print("❌ Chat ID not loaded from environment")
            
        print(f"\nExchange identifier: {config.get('notification_settings', {}).get('exchange_identifier', 'Not set')}")
        
    except Exception as e:
        print(f"❌ Error loading notification config: {e}")
        import traceback
        traceback.print_exc()

def test_notification_manager():
    """Test the notification manager with environment variables"""
    print("\n=== Step 3: Testing NotificationManager ===")
    
    try:
        from src.config_manager import load_notification_config
        from src.notification.notification_manager import NotificationManager
        
        # Load config
        config = load_notification_config('config/notifications_kraken_liepa.json')
        
        # Create notification manager
        notification_manager = NotificationManager(config)
        
        print("✅ NotificationManager created successfully")
        
        # Check if Telegram channel is initialized
        if 'telegram' in notification_manager.channels:
            telegram_channel = notification_manager.channels['telegram']
            print("✅ Telegram channel initialized")
            print(f"Telegram token (first 10 chars): {telegram_channel.token[:10] if telegram_channel.token else 'None'}...")
            print(f"Telegram chat ID: {telegram_channel.chat_id}")
        else:
            print("❌ Telegram channel not initialized")
            
    except Exception as e:
        print(f"❌ Error testing NotificationManager: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_systemd_env_simulation()
    test_notification_manager()
