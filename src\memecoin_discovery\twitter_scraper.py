"""
Twitter scraper module for memecoin discovery using snscrape.

This module handles Twitter data scraping using snscrape to identify
mentions of new memecoins and potential investment opportunities.
No API keys required!
"""

import os
import re
import logging
import time
import subprocess
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Set, Tuple
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer

# Try to import snscrape
try:
    import snscrape.modules.twitter as sntwitter
    SNSCRAPE_AVAILABLE = True
except (ImportError, AttributeError) as e:
    SNSCRAPE_AVAILABLE = False
    logging.warning(f"snscrape not available: {e}")
    logging.warning("Will use fallback scraping method")

# Fallback imports for alternative scraping
import requests
from bs4 import BeautifulSoup
import urllib.parse

class TwitterScraper:
    """Twitter scraper for memecoin discovery using snscrape."""

    # Keywords to search for memecoin mentions
    MEMECOIN_KEYWORDS = [
        "memecoin", "meme coin", "shitcoin", "altcoin", "new token",
        "moon", "x100", "x1000", "gem", "launch", "presale",
        "pump", "rocket", "diamond hands", "hodl", "ape in",
        "degen", "moonshot", "lambo", "to the moon"
    ]

    # Influential crypto Twitter accounts to monitor
    CRYPTO_INFLUENCERS = [
        "elonmusk", "VitalikButerin", "cz_binance", "justinsuntron",
        "APompliano", "DocumentingBTC", "WhalePanda", "CryptoCobain",
        "CryptoWendyO", "TheCryptoDog", "CryptoBirb", "CryptoCapo_",
        "TechDev_52", "CryptoCred", "CryptoMichNL", "CryptoHamster",
        "CryptoKaleo", "CryptoMessiah", "CryptoSqueeze", "CryptoTony__"
    ]

    # Networks and their common token patterns
    TOKEN_PATTERNS = {
        'ethereum': r'0x[a-fA-F0-9]{40}',
        'solana': r'[1-9A-HJ-NP-Za-km-z]{32,44}',
        'bsc': r'0x[a-fA-F0-9]{40}',
        'polygon': r'0x[a-fA-F0-9]{40}',
        'base': r'0x[a-fA-F0-9]{40}'
    }

    def __init__(self):
        """Initialize Twitter scraper using snscrape (no API keys needed)."""

        # Initialize sentiment analyzer
        self.sentiment_analyzer = SentimentIntensityAnalyzer()

        # Rate limiting to be respectful
        self.last_request_time = 0
        self.min_request_interval = 2.0  # 2 seconds between requests

        # Set scraping method based on availability
        self.use_snscrape = SNSCRAPE_AVAILABLE

        if self.use_snscrape:
            logging.info("Twitter scraper initialized with snscrape")
        else:
            logging.warning("snscrape not available, using fallback method")
            logging.warning("Note: Fallback method has limited functionality")
    
    def _rate_limit_wait(self):
        """Implement basic rate limiting to be respectful."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)

        self.last_request_time = time.time()
    
    def _rate_limit_wait(self):
        """Implement basic rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def search_memecoin_mentions(self, hours_back: int = 24, max_results: int = 100) -> List[Dict]:
        """Search for recent memecoin mentions on Twitter."""
        if self.use_snscrape:
            return self._search_with_snscrape(hours_back, max_results)
        else:
            return self._search_with_fallback(hours_back, max_results)

    def _search_with_snscrape(self, hours_back: int = 24, max_results: int = 100) -> List[Dict]:
        """Search for recent memecoin mentions on Twitter using snscrape."""
        try:
            # Build search queries for different keywords
            all_tweets = []

            # Use top keywords to avoid overwhelming results
            top_keywords = self.MEMECOIN_KEYWORDS[:8]  # Use top 8 keywords

            for keyword in top_keywords:
                try:
                    self._rate_limit_wait()

                    # Calculate date range
                    since_date = (datetime.now() - timedelta(hours=hours_back)).strftime('%Y-%m-%d')

                    # Build search query
                    query = f'"{keyword}" lang:en since:{since_date} -filter:retweets'

                    logging.info(f"Searching for: {query}")

                    # Use snscrape to search
                    tweets_found = 0
                    for i, tweet in enumerate(sntwitter.TwitterSearchScraper(query).get_items()):
                        if tweets_found >= max_results // len(top_keywords):  # Distribute results across keywords
                            break

                        try:
                            # Extract potential token addresses
                            token_addresses = self._extract_token_addresses(tweet.rawContent)

                            # Analyze sentiment
                            sentiment = self._analyze_sentiment(tweet.rawContent)

                            # Extract keywords
                            keywords = self._extract_keywords(tweet.rawContent)

                            tweet_data = {
                                'id': str(tweet.id),
                                'text': tweet.rawContent,
                                'created_at': tweet.date.isoformat(),
                                'author_username': tweet.user.username,
                                'author_display_name': tweet.user.displayname,
                                'author_followers': tweet.user.followersCount,
                                'author_verified': tweet.user.verified,
                                'retweet_count': tweet.retweetCount,
                                'like_count': tweet.likeCount,
                                'reply_count': tweet.replyCount,
                                'quote_count': tweet.quoteCount,
                                'sentiment_score': sentiment['compound'],
                                'sentiment_details': sentiment,
                                'keywords': keywords,
                                'token_addresses': token_addresses,
                                'engagement_score': self._calculate_engagement_score_snscrape(tweet),
                                'search_keyword': keyword
                            }

                            all_tweets.append(tweet_data)
                            tweets_found += 1

                        except Exception as e:
                            logging.error(f"Error processing tweet {tweet.id}: {e}")
                            continue

                    logging.info(f"Found {tweets_found} tweets for keyword '{keyword}'")

                except Exception as e:
                    logging.error(f"Error searching for keyword '{keyword}': {e}")
                    continue

            # Remove duplicates based on tweet ID
            seen_ids = set()
            unique_tweets = []
            for tweet in all_tweets:
                if tweet['id'] not in seen_ids:
                    seen_ids.add(tweet['id'])
                    unique_tweets.append(tweet)

            logging.info(f"Found {len(unique_tweets)} unique memecoin mentions in the last {hours_back} hours")
            return unique_tweets[:max_results]  # Limit to max_results

        except Exception as e:
            logging.error(f"Error searching memecoin mentions: {e}")
            return []

    def _search_with_fallback(self, hours_back: int = 24, max_results: int = 100) -> List[Dict]:
        """Fallback search method when snscrape is not available."""
        logging.warning("Using fallback search method - generating mock data for testing")

        # Generate some mock data for testing purposes
        mock_tweets = []

        mock_data = [
            {
                'text': 'Just found this new memecoin $PEPE that looks promising! 🚀',
                'author_username': 'crypto_trader_1',
                'like_count': 15,
                'retweet_count': 3
            },
            {
                'text': 'New altcoin launch today - $DOGE to the moon! 🌙',
                'author_username': 'memecoin_hunter',
                'like_count': 8,
                'retweet_count': 2
            },
            {
                'text': 'Found a gem token with x100 potential! Contract: 0x123...',
                'author_username': 'defi_analyst',
                'like_count': 25,
                'retweet_count': 7
            }
        ]

        for i, mock in enumerate(mock_data):
            if len(mock_tweets) >= max_results:
                break

            # Analyze sentiment
            sentiment = self._analyze_sentiment(mock['text'])

            # Extract keywords
            keywords = self._extract_keywords(mock['text'])

            # Extract token addresses
            token_addresses = self._extract_token_addresses(mock['text'])

            tweet_data = {
                'id': f'mock_{i}',
                'text': mock['text'],
                'created_at': datetime.now().isoformat(),
                'author_username': mock['author_username'],
                'author_display_name': mock['author_username'],
                'author_followers': 1000,
                'author_verified': False,
                'retweet_count': mock['retweet_count'],
                'like_count': mock['like_count'],
                'reply_count': 1,
                'quote_count': 0,
                'sentiment_score': sentiment['compound'],
                'sentiment_details': sentiment,
                'keywords': keywords,
                'token_addresses': token_addresses,
                'engagement_score': 0.5,
                'search_keyword': 'fallback_mock'
            }

            mock_tweets.append(tweet_data)

        logging.info(f"Generated {len(mock_tweets)} mock tweets for testing")
        return mock_tweets
    
    def monitor_influencer_accounts(self, hours_back: int = 24) -> List[Dict]:
        """Monitor tweets from crypto influencers for memecoin mentions."""
        if self.use_snscrape:
            return self._monitor_with_snscrape(hours_back)
        else:
            return self._monitor_with_fallback(hours_back)

    def _monitor_with_snscrape(self, hours_back: int = 24) -> List[Dict]:
        """Monitor tweets from crypto influencers using snscrape."""
        all_tweets = []

        # Monitor top influencers
        top_influencers = self.CRYPTO_INFLUENCERS[:10]  # Limit to top 10 to avoid being too slow

        for username in top_influencers:
            try:
                self._rate_limit_wait()

                # Calculate date range
                since_date = (datetime.now() - timedelta(hours=hours_back)).strftime('%Y-%m-%d')

                # Build query for this user's tweets
                query = f"from:{username} since:{since_date} -filter:retweets -filter:replies"

                logging.info(f"Monitoring influencer: {username}")

                tweets_found = 0
                for tweet in sntwitter.TwitterSearchScraper(query).get_items():
                    if tweets_found >= 5:  # Limit per influencer
                        break

                    try:
                        # Check if tweet contains memecoin keywords
                        if self._contains_memecoin_keywords(tweet.rawContent):
                            # Extract potential token addresses
                            token_addresses = self._extract_token_addresses(tweet.rawContent)

                            # Analyze sentiment
                            sentiment = self._analyze_sentiment(tweet.rawContent)

                            # Extract keywords
                            keywords = self._extract_keywords(tweet.rawContent)

                            tweet_data = {
                                'id': str(tweet.id),
                                'text': tweet.rawContent,
                                'created_at': tweet.date.isoformat(),
                                'author_username': tweet.user.username,
                                'author_display_name': tweet.user.displayname,
                                'author_followers': tweet.user.followersCount,
                                'author_verified': tweet.user.verified,
                                'retweet_count': tweet.retweetCount,
                                'like_count': tweet.likeCount,
                                'reply_count': tweet.replyCount,
                                'quote_count': tweet.quoteCount,
                                'sentiment_score': sentiment['compound'],
                                'sentiment_details': sentiment,
                                'keywords': keywords,
                                'token_addresses': token_addresses,
                                'influencer_tier': 'high',  # These are all high-tier influencers
                                'engagement_score': self._calculate_engagement_score_snscrape(tweet),
                                'source_type': 'influencer'
                            }

                            all_tweets.append(tweet_data)
                            tweets_found += 1

                    except Exception as e:
                        logging.error(f"Error processing tweet from {username}: {e}")
                        continue

                logging.info(f"Found {tweets_found} memecoin tweets from {username}")

            except Exception as e:
                logging.error(f"Error monitoring influencer {username}: {e}")
                continue

        logging.info(f"Found {len(all_tweets)} influencer tweets mentioning memecoins")
        return all_tweets

    def _monitor_with_fallback(self, hours_back: int = 24) -> List[Dict]:
        """Fallback method for monitoring influencers when snscrape is not available."""
        logging.warning("Using fallback influencer monitoring - generating mock data")

        # Generate mock influencer tweets
        mock_tweets = []

        mock_influencer_data = [
            {
                'text': 'Interesting new DeFi project launching soon. Could be a gem! 💎',
                'author_username': 'elonmusk',
                'like_count': 1500,
                'retweet_count': 300
            },
            {
                'text': 'The future of memecoins is bright. Innovation never stops! 🚀',
                'author_username': 'VitalikButerin',
                'like_count': 800,
                'retweet_count': 150
            }
        ]

        for i, mock in enumerate(mock_influencer_data):
            # Analyze sentiment
            sentiment = self._analyze_sentiment(mock['text'])

            # Extract keywords
            keywords = self._extract_keywords(mock['text'])

            # Extract token addresses
            token_addresses = self._extract_token_addresses(mock['text'])

            tweet_data = {
                'id': f'influencer_mock_{i}',
                'text': mock['text'],
                'created_at': datetime.now().isoformat(),
                'author_username': mock['author_username'],
                'author_display_name': mock['author_username'],
                'author_followers': 50000,
                'author_verified': True,
                'retweet_count': mock['retweet_count'],
                'like_count': mock['like_count'],
                'reply_count': 10,
                'quote_count': 5,
                'sentiment_score': sentiment['compound'],
                'sentiment_details': sentiment,
                'keywords': keywords,
                'token_addresses': token_addresses,
                'influencer_tier': 'high',
                'engagement_score': 0.8,
                'source_type': 'influencer'
            }

            mock_tweets.append(tweet_data)

        logging.info(f"Generated {len(mock_tweets)} mock influencer tweets")
        return mock_tweets

    def _calculate_engagement_score_snscrape(self, tweet) -> float:
        """Calculate engagement score for a tweet from snscrape data."""
        try:
            likes = tweet.likeCount or 0
            retweets = tweet.retweetCount or 0
            replies = tweet.replyCount or 0
            quotes = tweet.quoteCount or 0
            followers = tweet.user.followersCount or 1

            # Calculate engagement rate
            total_engagement = likes + (retweets * 2) + (replies * 1.5) + (quotes * 2)
            engagement_rate = total_engagement / max(followers, 1)

            # Normalize to 0-1 scale (log scale for better distribution)
            import math
            normalized_score = min(1.0, math.log10(max(1, total_engagement)) / 6)

            return normalized_score

        except Exception as e:
            logging.error(f"Error calculating engagement score: {e}")
            return 0.0
    

    
    def _extract_token_addresses(self, text: str) -> Dict[str, List[str]]:
        """Extract potential token contract addresses from tweet text."""
        addresses = {}
        
        for network, pattern in self.TOKEN_PATTERNS.items():
            matches = re.findall(pattern, text)
            if matches:
                addresses[network] = matches
        
        return addresses
    
    def _analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of tweet text."""
        # Use VADER sentiment analyzer
        vader_scores = self.sentiment_analyzer.polarity_scores(text)
        
        # Use TextBlob as backup
        blob = TextBlob(text)
        textblob_polarity = blob.sentiment.polarity
        textblob_subjectivity = blob.sentiment.subjectivity
        
        return {
            'compound': vader_scores['compound'],
            'positive': vader_scores['pos'],
            'negative': vader_scores['neg'],
            'neutral': vader_scores['neu'],
            'textblob_polarity': textblob_polarity,
            'textblob_subjectivity': textblob_subjectivity
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from tweet text."""
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in self.MEMECOIN_KEYWORDS:
            if keyword.lower() in text_lower:
                found_keywords.append(keyword)
        
        # Extract hashtags
        hashtags = re.findall(r'#\w+', text)
        found_keywords.extend(hashtags)
        
        # Extract cashtags (e.g., $BTC)
        cashtags = re.findall(r'\$[A-Z]{2,10}', text.upper())
        found_keywords.extend(cashtags)
        
        return list(set(found_keywords))  # Remove duplicates
    
    def _contains_memecoin_keywords(self, text: str) -> bool:
        """Check if text contains memecoin-related keywords."""
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in self.MEMECOIN_KEYWORDS)
    
    def get_trending_hashtags_snscrape(self) -> List[str]:
        """Get trending crypto-related hashtags using snscrape."""
        try:
            # Search for trending crypto hashtags
            trending_keywords = ['#crypto', '#bitcoin', '#ethereum', '#defi', '#nft']
            crypto_trends = []

            for keyword in trending_keywords:
                try:
                    # Get recent tweets with this hashtag
                    query = f"{keyword} lang:en since:2024-01-01"

                    # Just get a few tweets to see if it's trending
                    tweet_count = 0
                    for tweet in sntwitter.TwitterSearchScraper(query).get_items():
                        if tweet_count >= 5:
                            break
                        tweet_count += 1

                    if tweet_count >= 3:  # If we found at least 3 recent tweets
                        crypto_trends.append(keyword)

                except Exception as e:
                    logging.error(f"Error checking trend for {keyword}: {e}")
                    continue

            return crypto_trends

        except Exception as e:
            logging.error(f"Error getting trending hashtags: {e}")
            return []
