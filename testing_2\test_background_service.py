#!/usr/bin/env python
"""
Test script for the background service.
This script tests the background service components individually.
"""

import os
import sys
import logging
import argparse
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.config_manager import load_config, get_trading_config, load_notification_config
from src.trading.executor import TradingExecutor
from src.trading.account import AccountManager

# Import notification modules
try:
    from src.notification.notification_manager import NotificationManager
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False
    logging.error("Notification modules not available. Please check your installation.")
    sys.exit(1)

def test_notification_system(token=None, chat_id=None):
    """Test the notification system."""
    logging.info("Testing notification system...")
    
    # Load notification config
    config = load_notification_config()
    
    # Override with provided values if any
    if token:
        config['telegram']['token'] = token
    if chat_id:
        config['telegram']['chat_id'] = chat_id
    
    # Make sure Telegram is enabled
    config['telegram']['enabled'] = True
    
    try:
        # Initialize notification manager
        manager = NotificationManager(config)
        
        # Send a test notification
        logging.info("Sending test notification...")
        success = manager.notify(
            'service_status',
            {
                'status': 'testing',
                'mode': 'paper',
                'trading_enabled': 'False'
            }
        )
        
        if success:
            logging.info("✅ Notification sent successfully!")
        else:
            logging.error("❌ Failed to send notification")
        
        # Wait a bit to make sure the notification is sent
        time.sleep(2)
        
        return success
    
    except Exception as e:
        logging.error(f"Error testing notification system: {e}")
        return False

def test_paper_trading():
    """Test the paper trading system."""
    logging.info("Testing paper trading system...")
    
    try:
        # Initialize trading executor
        executor = TradingExecutor('binance')
        
        # Reset paper trading account
        if hasattr(executor, 'paper_trading'):
            executor.paper_trading.reset()
            logging.info("Reset paper trading account to initial balance")
        
        # Get current balance
        if hasattr(executor, 'paper_trading'):
            balance = executor.paper_trading.get_balance('USDT')
            logging.info(f"Paper trading balance: {balance}")
            
            # Test a trade
            symbol = 'BTC/USDT'
            price = 50000.0  # Example price
            
            # Execute a trade
            logging.info(f"Executing paper trade for {symbol} at price {price}...")
            order = executor.paper_trading.create_market_buy_order(
                symbol, 1000.0, price
            )
            
            if order:
                logging.info(f"✅ Paper trade executed: {order}")
            else:
                logging.error("❌ Failed to execute paper trade")
            
            # Get updated balance
            balance = executor.paper_trading.get_balance('USDT')
            logging.info(f"Updated paper trading balance: {balance}")
            
            return True
        else:
            logging.error("Paper trading not available")
            return False
    
    except Exception as e:
        logging.error(f"Error testing paper trading system: {e}")
        return False

def test_strategy_execution():
    """Test the strategy execution."""
    logging.info("Testing strategy execution...")
    
    try:
        # Import test_allocation module
        from test_allocation import run_strategy_for_web
        
        # Run the strategy with minimal parameters
        logging.info("Running strategy with minimal parameters...")
        results = run_strategy_for_web(
            use_mtpi_signal=True,
            mtpi_indicator_type='PGO',
            mtpi_timeframe='1d',
            timeframe='1d',
            analysis_start_date='2023-10-20',
            selected_assets=['BTC/USDT', 'ETH/USDT'],
            n_assets=1,
            use_cache=True,
            force_refresh_cache=False
        )
        
        if results and 'success' in results and results['success']:
            logging.info("✅ Strategy execution successful!")
            
            # Extract key results
            mtpi_signal = None
            best_asset = None
            
            # Get MTPI signal
            if 'mtpi_signals' in results and results['mtpi_signals'] is not None:
                if isinstance(results['mtpi_signals'], dict) and 'latest' in results['mtpi_signals']:
                    mtpi_signal = results['mtpi_signals']['latest']
            
            # Get best asset
            if 'best_asset_series' in results and results['best_asset_series'] is not None:
                if isinstance(results['best_asset_series'], dict) and 'latest' in results['best_asset_series']:
                    best_asset = results['best_asset_series']['latest']
            
            logging.info(f"Latest MTPI signal: {mtpi_signal}")
            logging.info(f"Latest best asset: {best_asset}")
            
            return True
        else:
            error_msg = results.get('error', 'Unknown error') if results else 'No results returned'
            logging.error(f"❌ Strategy execution failed: {error_msg}")
            return False
    
    except Exception as e:
        logging.error(f"Error testing strategy execution: {e}")
        return False

def main():
    """Main entry point for the test script."""
    parser = argparse.ArgumentParser(description='Test Background Service Components')
    parser.add_argument('--test', choices=['all', 'notifications', 'paper_trading', 'strategy'],
                        default='all', help='Which component to test')
    parser.add_argument('--token', help='Telegram bot token')
    parser.add_argument('--chat-id', help='Telegram chat ID')
    args = parser.parse_args()
    
    if args.test == 'all' or args.test == 'notifications':
        test_notification_system(args.token, args.chat_id)
    
    if args.test == 'all' or args.test == 'paper_trading':
        test_paper_trading()
    
    if args.test == 'all' or args.test == 'strategy':
        test_strategy_execution()
    
    logging.info("All tests completed!")

if __name__ == "__main__":
    main()
