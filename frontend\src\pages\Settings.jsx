import React, { useState, useEffect } from 'react'
import {
  Box,
  VStack,
  Heading,
  FormControl,
  FormLabel,
  Select,
  Button,
  SimpleGrid,
  Text,
  useToast,
  Spinner,
  Flex,
  Card,
  CardBody,
  CardHeader,
  Input,
} from '@chakra-ui/react'

import { fetchSettings, updateSettings } from '../services/api'

const Settings = () => {
  const toast = useToast()
  const [formValues, setFormValues] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  // Default settings
  const defaultSettings = {
    trend_method: 'RSI',
    rsi_length: 14,
    rsi_ma_length: 14,
    supertrend_factor: 3,
    cci_length: 20,
    dmi_length: 14,
    pgo_length: 35,
    pgo_upper_threshold: 1.1,
    pgo_lower_threshold: -0.58,
    aad_period: 14,
    mtpi_indicator_type: 'PGO',
    mtpi_pgo_length: 35,
    mtpi_upper_threshold: 1.1,
    mtpi_lower_threshold: -0.58,
    mtpi_timeframe: '1d',
    timeframe: '1d',
    initial_capital: 10000,
    transaction_fee_rate: 0.001
  }

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const data = await fetchSettings()
        console.log('Settings loaded:', data)
        setFormValues(data)
      } catch (error) {
        console.error('Error loading settings:', error)
        // Use default settings if there's an error
        setFormValues(defaultSettings)
        toast({
          title: 'Error loading settings',
          description: 'Using default settings instead',
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, []) // Empty dependency array means this runs once on mount

  // Handle form input changes
  const handleChange = (field, value) => {
    // Simply update the form values with the provided value
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Get default value for a field
  const getDefaultValue = (field) => {
    switch (field) {
      case 'mtpi_upper_threshold': return 1.1
      case 'mtpi_lower_threshold': return -0.58
      case 'mtpi_pgo_length': return 35
      case 'mtpi_timeframe': return '1d'
      case 'timeframe': return '1d'
      case 'pgo_upper_threshold': return 1.1
      case 'pgo_lower_threshold': return -0.58
      case 'rsi_length': return 14
      case 'rsi_ma_length': return 14
      case 'supertrend_factor': return 3
      case 'cci_length': return 20
      case 'dmi_length': return 14
      case 'pgo_length': return 35
      case 'aad_period': return 14
      case 'initial_capital': return 10000
      case 'transaction_fee_rate': return 0.001
      default: return null
    }
  }

  // Super simple numeric field handling with better string support
  const sanitizeNumericField = (field, value) => {
    // If it's already a number, just return it
    if (typeof value === 'number') {
      return value
    }

    // If it's a string, try to convert to number
    if (typeof value === 'string') {
      // Handle empty string
      if (value === '') {
        return getDefaultValue(field)
      }

      // Determine if we should parse as float or int
      const isFloatField = field.includes('threshold') || field.includes('factor')
      const numValue = isFloatField ? parseFloat(value) : parseInt(value)

      if (!isNaN(numValue)) {
        // Apply range validation
        if (field === 'mtpi_upper_threshold' && (numValue < 0.1 || numValue > 5)) {
          return getDefaultValue(field)
        }
        if (field === 'mtpi_lower_threshold' && (numValue > 0 || numValue < -5)) {
          return getDefaultValue(field)
        }
        if (field === 'mtpi_pgo_length' && (numValue < 5 || numValue > 100)) {
          return getDefaultValue(field)
        }
        if (field === 'pgo_upper_threshold' && (numValue < 0.1 || numValue > 5)) {
          return getDefaultValue(field)
        }
        if (field === 'pgo_lower_threshold' && (numValue > 0 || numValue < -5)) {
          return getDefaultValue(field)
        }

        return numValue
      }
    }

    // If all else fails, use default
    return getDefaultValue(field)
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      // Create a copy of formValues to sanitize
      const sanitizedValues = { ...formValues }

      // Ensure all numeric fields are properly converted to numbers
      const numericFields = [
        'mtpi_upper_threshold',
        'mtpi_lower_threshold',
        'mtpi_pgo_length',
        'pgo_upper_threshold',
        'pgo_lower_threshold',
        'rsi_length',
        'rsi_ma_length',
        'supertrend_factor',
        'cci_length',
        'dmi_length',
        'pgo_length',
        'aad_period',
        'initial_capital',
        'transaction_fee_rate'
      ]

      // Sanitize all numeric fields
      numericFields.forEach(field => {
        if (field in sanitizedValues) {
          sanitizedValues[field] = sanitizeNumericField(field, sanitizedValues[field])
        } else {
          sanitizedValues[field] = getDefaultValue(field)
        }
      })

      console.log('Submitting settings:', sanitizedValues)

      // Save settings to backend
      const result = await updateSettings(sanitizedValues)
      console.log('Settings saved:', result)

      // Update form values with the returned settings
      if (result && result.settings) {
        setFormValues(result.settings)
      }

      toast({
        title: 'Settings saved',
        description: 'Your settings have been saved successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error) {
      console.error('Error saving settings:', error)

      let errorMessage = 'An error occurred while saving settings'
      if (error.response && error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        }
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: 'Error saving settings',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsSaving(false)
    }
  }



  // Show loading state
  if (isLoading) {
    return (
      <Flex justify="center" align="center" h="500px">
        <Spinner size="xl" color="blue.500" />
      </Flex>
    )
  }

  // Safety check - if formValues is still null despite our fallback
  if (!formValues) {
    setFormValues(defaultSettings)
    return (
      <Flex justify="center" align="center" h="500px">
        <Spinner size="xl" color="blue.500" />
      </Flex>
    )
  }

  return (
    <Box>
      <Heading size="lg" mb={3}>
        Dashboard Settings
      </Heading>

      <Box p={4} bg="blue.50" borderRadius="md" mb={6}>
        <Text fontSize="md">
          <strong>Note:</strong> Currently, all assets are sourced from Binance. Additional exchanges will be supported in future updates.
        </Text>
      </Box>

      <form onSubmit={handleSubmit}>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
          {/* Asset Rotation Timeframe Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">Asset Rotation Timeframe</Heading>
              <Text fontSize="sm" color="gray.500" mt={1}>
                Timeframe settings for asset rotation strategy
              </Text>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>Asset Rotation Timeframe</FormLabel>
                  <Select
                    value={formValues.timeframe || '1d'}
                    onChange={(e) => handleChange('timeframe', e.target.value)}
                  >
                    <option value="1m">1 Minute</option>
                    <option value="5m">5 Minutes</option>
                    <option value="15m">15 Minutes</option>
                    <option value="30m">30 Minutes</option>
                    <option value="1h">1 Hour</option>
                    <option value="4h">4 Hours</option>
                    <option value="12h">12 Hours</option>
                    <option value="1d">1 Day</option>
                    <option value="1w">1 Week</option>
                  </Select>
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Timeframe for asset rotation strategy execution
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel>Initial Capital</FormLabel>
                  <Input
                    type="text"
                    value={formValues.initial_capital ?? 10000}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === '' || /^[0-9]*$/.test(value)) {
                        handleChange('initial_capital', value);
                      }
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (value !== '') {
                        const numValue = parseInt(value);
                        if (!isNaN(numValue)) {
                          handleChange('initial_capital', numValue);
                        } else {
                          handleChange('initial_capital', 10000);
                        }
                      }
                    }}
                  />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Starting capital for backtesting and live trading
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel>Transaction Fee (%)</FormLabel>
                  <Input
                    type="text"
                    value={(formValues.transaction_fee_rate ?? 0.001) * 100}
                    onChange={(e) => {
                      const value = e.target.value;

                      // Accept any input that could potentially be part of a valid decimal
                      // This includes digits and decimal points
                      if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                        // Store as decimal (e.g., 0.1% is stored as 0.001)
                        handleChange('transaction_fee_rate', value === '' ? '' : parseFloat(value) / 100);
                      }
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;

                      // When the field loses focus, try to convert to a number
                      if (value !== '') {
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue)) {
                          // Store as decimal (e.g., 0.1% is stored as 0.001)
                          handleChange('transaction_fee_rate', numValue / 100);
                        } else {
                          // If it's not a valid number, revert to default
                          handleChange('transaction_fee_rate', 0.001);
                        }
                      }
                    }}
                  />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Enter a percentage between 0 and 10 (e.g., 0.1 for 0.1%)
                  </Text>
                </FormControl>
              </VStack>
            </CardBody>
          </Card>

          {/* Asset Trend Detection Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">Asset Trend Detection</Heading>
              <Text fontSize="sm" color="gray.500" mt={1}>
                Settings for detecting trends between assets
              </Text>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>Trend Classification Method</FormLabel>
                  <Select
                    value={formValues.trend_method || 'RSI'}
                    onChange={(e) => handleChange('trend_method', e.target.value)}
                  >
                    <option value="RSI">RSI</option>
                    <option value="CCI">CCI</option>
                    <option value="SuperTrend">SuperTrend</option>
                    <option value="DMI">DMI</option>
                    <option value="PGO For Loop">PGO For Loop</option>
                    <option value="AAD Trend">AAD Trend</option>
                    <option value="All">All</option>
                  </Select>
                </FormControl>

                {/* RSI Parameters */}
                {(formValues.trend_method === 'RSI' || formValues.trend_method === 'All') && (
                  <>
                    <FormControl>
                      <FormLabel>RSI Length</FormLabel>
                      <Input
                        type="text"
                        value={formValues.rsi_length ?? 14}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Accept any input that could potentially be part of a valid integer
                          // This includes only digits
                          if (value === '' || /^[0-9]*$/.test(value)) {
                            handleChange('rsi_length', value);
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;

                          // When the field loses focus, try to convert to a number
                          if (value !== '') {
                            const numValue = parseInt(value);
                            if (!isNaN(numValue)) {
                              handleChange('rsi_length', numValue);
                            } else {
                              // If it's not a valid number, revert to default
                              handleChange('rsi_length', 14);
                            }
                          }
                        }}
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        Enter a whole number between 1 and 100 (e.g., 14)
                      </Text>
                    </FormControl>

                    <FormControl>
                      <FormLabel>RSI MA Length</FormLabel>
                      <Input
                        type="text"
                        value={formValues.rsi_ma_length ?? 14}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Accept any input that could potentially be part of a valid integer
                          // This includes only digits
                          if (value === '' || /^[0-9]*$/.test(value)) {
                            handleChange('rsi_ma_length', value);
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;

                          // When the field loses focus, try to convert to a number
                          if (value !== '') {
                            const numValue = parseInt(value);
                            if (!isNaN(numValue)) {
                              handleChange('rsi_ma_length', numValue);
                            } else {
                              // If it's not a valid number, revert to default
                              handleChange('rsi_ma_length', 14);
                            }
                          }
                        }}
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        Enter a whole number between 1 and 50 (e.g., 14)
                      </Text>
                    </FormControl>
                  </>
                )}

                {/* SuperTrend Parameters */}
                {(formValues.trend_method === 'SuperTrend' || formValues.trend_method === 'All') && (
                  <FormControl>
                    <FormLabel>SuperTrend Factor</FormLabel>
                    <Input
                      type="text"
                      value={formValues.supertrend_factor ?? 3}
                      onChange={(e) => {
                        const value = e.target.value;

                        // Accept any input that could potentially be part of a valid decimal
                        // This includes digits and decimal points
                        if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          handleChange('supertrend_factor', value);
                        }
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;

                        // When the field loses focus, try to convert to a number
                        if (value !== '') {
                          const numValue = parseFloat(value);
                          if (!isNaN(numValue)) {
                            handleChange('supertrend_factor', numValue);
                          } else {
                            // If it's not a valid number, revert to default
                            handleChange('supertrend_factor', 3);
                          }
                        }
                      }}
                    />
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      Enter a decimal value between 0.5 and 10 (e.g., 3.0)
                    </Text>
                  </FormControl>
                )}

                {/* CCI Parameters */}
                {(formValues.trend_method === 'CCI' || formValues.trend_method === 'All') && (
                  <FormControl>
                    <FormLabel>CCI Length</FormLabel>
                    <Input
                      type="text"
                      value={formValues.cci_length ?? 20}
                      onChange={(e) => {
                        const value = e.target.value;

                        // Accept any input that could potentially be part of a valid integer
                        // This includes only digits
                        if (value === '' || /^[0-9]*$/.test(value)) {
                          handleChange('cci_length', value);
                        }
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;

                        // When the field loses focus, try to convert to a number
                        if (value !== '') {
                          const numValue = parseInt(value);
                          if (!isNaN(numValue)) {
                            handleChange('cci_length', numValue);
                          } else {
                            // If it's not a valid number, revert to default
                            handleChange('cci_length', 20);
                          }
                        }
                      }}
                    />
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      Enter a whole number between 5 and 100 (e.g., 20)
                    </Text>
                  </FormControl>
                )}

                {/* DMI Parameters */}
                {(formValues.trend_method === 'DMI' || formValues.trend_method === 'All') && (
                  <FormControl>
                    <FormLabel>DMI Length</FormLabel>
                    <Input
                      type="text"
                      value={formValues.dmi_length ?? 14}
                      onChange={(e) => {
                        const value = e.target.value;

                        // Accept any input that could potentially be part of a valid integer
                        // This includes only digits
                        if (value === '' || /^[0-9]*$/.test(value)) {
                          handleChange('dmi_length', value);
                        }
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;

                        // When the field loses focus, try to convert to a number
                        if (value !== '') {
                          const numValue = parseInt(value);
                          if (!isNaN(numValue)) {
                            handleChange('dmi_length', numValue);
                          } else {
                            // If it's not a valid number, revert to default
                            handleChange('dmi_length', 14);
                          }
                        }
                      }}
                    />
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      Enter a whole number between 5 and 100 (e.g., 14)
                    </Text>
                  </FormControl>
                )}

                {/* PGO For Loop Parameters */}
                {(formValues.trend_method === 'PGO For Loop' || formValues.trend_method === 'All') && (
                  <>
                    <FormControl>
                      <FormLabel>PGO Length</FormLabel>
                      <Input
                        type="text"
                        value={formValues.pgo_length ?? 35}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Accept any input that could potentially be part of a valid integer
                          // This includes only digits
                          if (value === '' || /^[0-9]*$/.test(value)) {
                            handleChange('pgo_length', value);
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;

                          // When the field loses focus, try to convert to a number
                          if (value !== '') {
                            const numValue = parseInt(value);
                            if (!isNaN(numValue)) {
                              handleChange('pgo_length', numValue);
                            } else {
                              // If it's not a valid number, revert to default
                              handleChange('pgo_length', 35);
                            }
                          }
                        }}
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        Enter a whole number between 5 and 100 (e.g., 35)
                      </Text>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Upper Threshold (Bullish)</FormLabel>
                      <Input
                        type="text"
                        value={formValues.pgo_upper_threshold ?? 1.1}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Accept any input that could potentially be part of a valid number
                          // This includes digits and decimal points
                          if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                            handleChange('pgo_upper_threshold', value);
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;

                          // When the field loses focus, try to convert to a number
                          if (value !== '') {
                            const numValue = parseFloat(value);
                            if (!isNaN(numValue)) {
                              handleChange('pgo_upper_threshold', numValue);
                            } else {
                              // If it's not a valid number, revert to default
                              handleChange('pgo_upper_threshold', 1.1);
                            }
                          }
                        }}
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        Enter a positive value between 0.1 and 5 (e.g., 1.1)
                      </Text>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Lower Threshold (Bearish)</FormLabel>
                      <Input
                        type="text"
                        value={formValues.pgo_lower_threshold ?? -0.58}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Accept any input that could potentially be part of a valid negative number
                          // This includes digits, decimal points, and minus signs
                          if (value === '' || /^-?[0-9]*\.?[0-9]*$/.test(value)) {
                            handleChange('pgo_lower_threshold', value);
                          }
                        }}
                        onBlur={(e) => {
                          const value = e.target.value;

                          // When the field loses focus, try to convert to a number
                          if (value !== '') {
                            const numValue = parseFloat(value);
                            if (!isNaN(numValue)) {
                              handleChange('pgo_lower_threshold', numValue);
                            } else {
                              // If it's not a valid number, revert to default
                              handleChange('pgo_lower_threshold', -0.58);
                            }
                          }
                        }}
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        Enter a negative value between -5 and 0 (e.g., -0.58)
                      </Text>
                    </FormControl>
                  </>
                )}

                {/* AAD Trend Parameters */}
                {(formValues.trend_method === 'AAD Trend' || formValues.trend_method === 'All') && (
                  <FormControl>
                    <FormLabel>AAD Period</FormLabel>
                    <Input
                      type="text"
                      value={formValues.aad_period ?? 14}
                      onChange={(e) => {
                        const value = e.target.value;

                        // Accept any input that could potentially be part of a valid integer
                        // This includes only digits
                        if (value === '' || /^[0-9]*$/.test(value)) {
                          handleChange('aad_period', value);
                        }
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;

                        // When the field loses focus, try to convert to a number
                        if (value !== '') {
                          const numValue = parseInt(value);
                          if (!isNaN(numValue)) {
                            handleChange('aad_period', numValue);
                          } else {
                            // If it's not a valid number, revert to default
                            handleChange('aad_period', 14);
                          }
                        }
                      }}
                    />
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      Enter a whole number between 5 and 100 (e.g., 14)
                    </Text>
                  </FormControl>
                )}
              </VStack>
            </CardBody>
          </Card>

          {/* MTPI Signal Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">MTPI Signal Settings</Heading>
              <Text fontSize="sm" color="gray.500" mt={1}>
                Market Trend Prediction Indicator for overall market trend detection
              </Text>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>MTPI Indicator Type</FormLabel>
                  <Select
                    value={formValues.mtpi_indicator_type || 'PGO'}
                    onChange={(e) => handleChange('mtpi_indicator_type', e.target.value)}
                  >
                    <option value="PGO">Pretty Good Oscillator (PGO)</option>
                    {/* Add more MTPI indicator types here as they become available */}
                  </Select>
                </FormControl>

                <FormControl>
                  <FormLabel>MTPI Timeframe</FormLabel>
                  <Select
                    value={formValues.mtpi_timeframe || '1d'}
                    onChange={(e) => handleChange('mtpi_timeframe', e.target.value)}
                  >
                    <option value="1m">1 Minute</option>
                    <option value="5m">5 Minutes</option>
                    <option value="15m">15 Minutes</option>
                    <option value="30m">30 Minutes</option>
                    <option value="1h">1 Hour</option>
                    <option value="4h">4 Hours</option>
                    <option value="12h">12 Hours</option>
                    <option value="1d">1 Day</option>
                    <option value="1w">1 Week</option>
                  </Select>
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Timeframe for MTPI signal calculation
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel>PGO Length</FormLabel>
                  <Input
                    type="text"
                    value={formValues.mtpi_pgo_length ?? 35}
                    onChange={(e) => {
                      const value = e.target.value;

                      // Accept any input that could potentially be part of a valid integer
                      // This includes only digits
                      if (value === '' || /^[0-9]*$/.test(value)) {
                        handleChange('mtpi_pgo_length', value);
                      }
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;

                      // When the field loses focus, try to convert to a number
                      if (value !== '') {
                        const numValue = parseInt(value);
                        if (!isNaN(numValue)) {
                          handleChange('mtpi_pgo_length', numValue);
                        } else {
                          // If it's not a valid number, revert to default
                          handleChange('mtpi_pgo_length', 35);
                        }
                      }
                    }}
                  />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Enter a whole number between 5 and 100 (e.g., 35)
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel>Upper Threshold (Bullish)</FormLabel>
                  <Input
                    type="text"
                    value={formValues.mtpi_upper_threshold ?? 1.1}
                    onChange={(e) => {
                      const value = e.target.value;

                      // Accept any input that could potentially be part of a valid number
                      // This includes digits, decimal points, and minus signs in any order
                      if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                        handleChange('mtpi_upper_threshold', value);
                      }
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;

                      // When the field loses focus, try to convert to a number
                      if (value !== '') {
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue)) {
                          handleChange('mtpi_upper_threshold', numValue);
                        } else {
                          // If it's not a valid number, revert to default
                          handleChange('mtpi_upper_threshold', 1.1);
                        }
                      }
                    }}
                  />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Enter a positive value between 0.1 and 5 (e.g., 1.1)
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel>Lower Threshold (Bearish)</FormLabel>
                  <Input
                    type="text"
                    value={formValues.mtpi_lower_threshold ?? -0.58}
                    onChange={(e) => {
                      const value = e.target.value;

                      // Accept any input that could potentially be part of a valid negative number
                      // This includes digits, decimal points, and minus signs in any order
                      if (value === '' || /^-?[0-9]*\.?[0-9]*$/.test(value)) {
                        handleChange('mtpi_lower_threshold', value);
                      }
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;

                      // When the field loses focus, try to convert to a number
                      if (value !== '') {
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue)) {
                          handleChange('mtpi_lower_threshold', numValue);
                        } else {
                          // If it's not a valid number, revert to default
                          handleChange('mtpi_lower_threshold', -0.58);
                        }
                      }
                    }}
                  />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    Enter a negative value between -5 and 0 (e.g., -0.58)
                  </Text>
                </FormControl>


              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>

        <Flex mt={8} justify="flex-end">
          <Button
            colorScheme="blue"
            type="submit"
            isLoading={isSaving}
          >
            Save Settings
          </Button>
        </Flex>
      </form>
    </Box>
  )
}

export default Settings
