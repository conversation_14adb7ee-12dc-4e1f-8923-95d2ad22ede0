#!/usr/bin/env python3
"""
Test script to verify environment variable loading for different .env files
"""

import os
import sys
from dotenv import load_dotenv

def test_env_file(env_file_path):
    """Test loading environment variables from a specific .env file"""
    print(f"\n=== Testing {env_file_path} ===")
    
    # Clear existing environment variables
    for key in ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID', 'KRAKEN_API_KEY', 'BITVAVO_API_KEY']:
        if key in os.environ:
            del os.environ[key]
    
    # Load the specific .env file
    if os.path.exists(env_file_path):
        load_dotenv(env_file_path, override=True)
        print(f"✅ Loaded {env_file_path}")
        
        # Check key environment variables
        telegram_token = os.getenv('TELEGRAM_BOT_TOKEN')
        telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
        kraken_key = os.getenv('KRAKEN_API_KEY')
        bitvavo_key = os.getenv('BITVAVO_API_KEY')
        
        print(f"TELEGRAM_BOT_TOKEN: {'✅ Set' if telegram_token else '❌ Not set'}")
        print(f"TELEGRAM_CHAT_ID: {'✅ Set' if telegram_chat_id else '❌ Not set'}")
        print(f"KRAKEN_API_KEY: {'✅ Set' if kraken_key else '❌ Not set'}")
        print(f"BITVAVO_API_KEY: {'✅ Set' if bitvavo_key else '❌ Not set'}")
        
        if telegram_token:
            print(f"Telegram Token (first 10 chars): {telegram_token[:10]}...")
        if telegram_chat_id:
            print(f"Telegram Chat ID: {telegram_chat_id}")
            
    else:
        print(f"❌ File not found: {env_file_path}")

def test_notification_config_loading():
    """Test notification config loading with environment variables"""
    print(f"\n=== Testing Notification Config Loading ===")
    
    # Add the parent directory to the path
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        from src.config_manager import load_notification_config
        
        # Test Liepa's Kraken config
        config = load_notification_config('config/notifications_kraken_liepa.json')
        telegram_config = config.get('telegram', {})
        
        print(f"Loaded config: {config}")
        print(f"Telegram enabled: {telegram_config.get('enabled')}")
        print(f"Telegram token: {'✅ Set' if telegram_config.get('token') else '❌ Not set'}")
        print(f"Telegram chat_id: {'✅ Set' if telegram_config.get('chat_id') else '❌ Not set'}")
        
        if telegram_config.get('token'):
            print(f"Token (first 10 chars): {telegram_config.get('token')[:10]}...")
        if telegram_config.get('chat_id'):
            print(f"Chat ID: {telegram_config.get('chat_id')}")
            
    except Exception as e:
        print(f"❌ Error loading notification config: {e}")

if __name__ == "__main__":
    print("🔍 Environment Variable Loading Test")
    
    # Test your .env file
    test_env_file('.env')
    
    # Test friend's .env file
    test_env_file('.env.Liepa')
    
    # Test notification config loading with Liepa's environment
    print(f"\n=== Loading Liepa's Environment ===")
    load_dotenv('.env.Liepa', override=True)
    test_notification_config_loading()
