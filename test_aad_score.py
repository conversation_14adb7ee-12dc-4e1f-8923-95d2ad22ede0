#!/usr/bin/env python3
"""
Test script for AAD Score indicator implementation.

This script tests the AAD Score indicator with BTC/USDT data
and compares the results with the expected PineScript behavior.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.aad_score import generate_aad_score_signal, calculate_aad_score
from src.config_manager import load_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_aad_score_basic():
    """Test basic AAD Score functionality with default parameters."""
    print("\n" + "="*60)
    print("TESTING AAD SCORE INDICATOR")
    print("="*60)
    
    try:
        # Fetch BTC data
        print("📊 Fetching BTC/USDT data...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=100
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return False
        
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Loaded {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Test with default parameters (matching PineScript)
        print("\n🔧 Testing AAD Score with default parameters:")
        print("   - Source: close")
        print("   - Length: 22")
        print("   - AAD Multiplier: 1.2")
        print("   - Average Type: SMA")
        
        # Calculate AAD Score
        signal, avg, avg_p, avg_m = calculate_aad_score(
            df=btc_df,
            src_col='close',
            length=22,
            aad_mult=1.2,
            avg_type='SMA'
        )
        
        # Generate binary signals
        binary_signal = generate_aad_score_signal(
            df=btc_df,
            src_col='close',
            length=22,
            aad_mult=1.2,
            avg_type='SMA'
        )
        
        # Display results
        print(f"\n📈 AAD Score Results (last 10 values):")
        print("-" * 50)
        
        for i in range(max(0, len(btc_df)-10), len(btc_df)):
            date = btc_df.index[i].strftime('%Y-%m-%d')
            close_price = btc_df['close'].iloc[i]
            avg_val = avg.iloc[i] if not pd.isna(avg.iloc[i]) else 0
            upper_band = avg_p.iloc[i] if not pd.isna(avg_p.iloc[i]) else 0
            lower_band = avg_m.iloc[i] if not pd.isna(avg_m.iloc[i]) else 0
            signal_val = signal.iloc[i]
            
            signal_text = "🟢 LONG" if signal_val == 1 else "🔴 SHORT" if signal_val == -1 else "⚪ NEUTRAL"
            
            print(f"{date}: Close={close_price:8.2f} | Avg={avg_val:8.2f} | "
                  f"Upper={upper_band:8.2f} | Lower={lower_band:8.2f} | {signal_text}")
        
        # Summary statistics
        long_signals = (binary_signal == 1).sum()
        short_signals = (binary_signal == -1).sum()
        neutral_signals = (binary_signal == 0).sum()
        
        print(f"\n📊 Signal Summary:")
        print(f"   🟢 Long signals: {long_signals}")
        print(f"   🔴 Short signals: {short_signals}")
        print(f"   ⚪ Neutral signals: {neutral_signals}")
        print(f"   📈 Latest signal: {binary_signal.iloc[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AAD Score: {e}")
        logging.error(f"Error in AAD Score test: {e}")
        return False

def test_aad_score_with_config():
    """Test AAD Score with configuration from YAML."""
    print("\n" + "="*60)
    print("TESTING AAD SCORE WITH YAML CONFIG")
    print("="*60)
    
    try:
        # Load configuration
        config = load_config()
        settings = config.get('settings', {})
        mtpi_indicators = settings.get('mtpi_indicators', {})
        aad_config = mtpi_indicators.get('aad_score', {})
        
        print(f"📋 Loaded AAD config from YAML: {aad_config}")
        
        # Fetch BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=50
        )
        
        btc_df = data_dict['BTC/USDT']
        
        # Test with YAML parameters
        signal = generate_aad_score_signal(
            df=btc_df,
            src_col=aad_config.get('src_col', 'close'),
            length=aad_config.get('length', 22),
            aad_mult=aad_config.get('aad_mult', 1.2),
            avg_type=aad_config.get('avg_type', 'SMA')
        )
        
        print(f"✅ Generated AAD signals using YAML config")
        print(f"📈 Latest signal: {signal.iloc[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AAD Score with config: {e}")
        return False

def test_different_average_types():
    """Test AAD Score with different average types."""
    print("\n" + "="*60)
    print("TESTING AAD SCORE WITH DIFFERENT AVERAGE TYPES")
    print("="*60)
    
    try:
        # Fetch BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=50
        )
        
        btc_df = data_dict['BTC/USDT']
        
        avg_types = ['SMA', 'EMA', 'HMA', 'DEMA', 'TEMA', 'RMA']
        
        for avg_type in avg_types:
            print(f"\n🔧 Testing with {avg_type}...")
            
            signal = generate_aad_score_signal(
                df=btc_df,
                src_col='close',
                length=22,
                aad_mult=1.2,
                avg_type=avg_type
            )
            
            latest_signal = signal.iloc[-1]
            signal_text = "🟢 LONG" if latest_signal == 1 else "🔴 SHORT" if latest_signal == -1 else "⚪ NEUTRAL"
            
            print(f"   {avg_type}: {signal_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing different average types: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 AAD SCORE INDICATOR TEST SUITE")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    test_results.append(("Basic AAD Score Test", test_aad_score_basic()))
    test_results.append(("YAML Config Test", test_aad_score_with_config()))
    test_results.append(("Different Average Types Test", test_different_average_types()))
    
    # Summary
    print("\n" + "="*60)
    print("TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All tests passed! AAD Score indicator is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
