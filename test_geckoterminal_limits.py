#!/usr/bin/env python3
"""
Test script to investigate GeckoTerminal API rate limits.

Based on research: GeckoTerminal has a limit of ~30 requests per minute.
Each token requires 2 requests: 1 for pool search + 1 for OHLCV data.
So we can process ~15 tokens per minute maximum.

This script tests fetching 2 days of 1h data for each memecoin asset
with proper rate limiting to determine optimal request patterns.
"""

import os
import sys
import time
import logging
import requests
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Import GeckoTerminal functions
try:
    from geckoterminal_fetcher import search_pools, get_ohlcv_data, GECKOTERMINAL_API_BASE
    GECKOTERMINAL_AVAILABLE = True
except ImportError:
    GECKOTERMINAL_AVAILABLE = False
    logging.error("GeckoTerminal fetcher not available")
    sys.exit(1)

# Rate limiting constants
REQUESTS_PER_MINUTE = 30
SECONDS_PER_REQUEST = 60 / REQUESTS_PER_MINUTE  # 2 seconds per request
POOL_CACHE_FILE = "pool_cache.json"

# Memecoin assets from the configuration
MEMECOIN_ASSETS = [
    # Format: (network, token_address, name)
    ("solana", "A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump", "FWOG"),
    ("solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "BONK"),
    ("eth", "******************************************", "PEPE"),
    ("solana", "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "WIF"),
    ("solana", "63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9", "GIGA"),
    ("solana", "BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump", "AUTISM"),
    ("base", "******************************************", "TOSHI"),
    ("bsc", "******************************************", "ALTURA"),
    ("solana", "5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUibpump", "SIGMA"),
    ("solana", "8Ki8DpuWNxu9VsS3kQbarsCWMcFGWkzzA8pUPto9zBd5", "LOCKIN"),
    ("solana", "J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr", "SPX"),
    ("base", "******************************************", "SKI"),
    ("solana", "Df6yfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump", "CHILLGUY"),
    ("eth", "******************************************", "APU"),
    ("solana", "9WPTUkh8fKuCnepRWoPYLH3aK9gSjPHFDenBq2X1Czdp", "SELFIE"),
    ("base", "******************************************", "ALIENBASE"),
    ("eth", "******************************************", "DOGEETH"),
    ("solana", "5mbK36SZ7J19An8jFochhQS4of8g6BwUjbeCSxBSoWdp", "MICHI"),
    ("solana", "2JcXacFwt9mVAwBQ5nZkYwCyXQkRcdsYrDXn6hj22SbP", "MINI"),
    ("solana", "GtDZKAqvMZMnti46ZewMiXCa4oXF4bZxwQPoKzXPFxZn", "NUB"),
    ("solana", "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "POPCAT"),
    ("solana", "********************************************", "GIKO"),
    ("solana", "8WnQQRbuEZ3CCDbH5MCVioBbw6o75NKANq9WdPhBDsWo", "COBY"),
    ("solana", "DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2", "AURA"),
    ("base", "******************************************", "COCORO"),
    ("solana", "DPaQfq5sFnoqw2Sh9WMmmASFL9LNu6RdtDqwE1tab2tB", "SKIBIDI"),
    ("solana", "4YK1njyeCkBuXG6phNtidJWKCbBhB659iwGkUJx98P5Z", "DOLAN"),
    ("solana", "69kdRLyP5DTRkpHraaSZAQbWmAwzF9guKjZfzMXzcbAs", "USA"),
    ("eth", "******************************************", "EPIK"),
]

def test_single_asset_fetch(network: str, token_address: str, name: str, delay: float = 1.0) -> Tuple[bool, str, int]:
    """
    Test fetching 2 days of 1h data for a single asset.

    Returns:
        (success, error_message, num_candles)
    """
    try:
        logging.info(f"Testing {name} ({network}:{token_address[:8]}...)")

        # Find pools for this token
        pools = search_pools(network, token_address)

        if not pools:
            return False, f"No pools found for {name}", 0

        # Use the most liquid pool
        pool_address = pools[0]['id']
        logging.info(f"Using pool {pool_address} for {name}")

        # Fetch 2 days of 1h data (48 candles)
        df = get_ohlcv_data(
            network=network,
            pool_address=pool_address,
            timeframe="1h",
            limit=48  # 2 days of hourly data
        )

        if df.empty:
            return False, f"No OHLCV data returned for {name}", 0

        num_candles = len(df)
        logging.info(f"✅ Successfully fetched {num_candles} candles for {name}")

        # Add delay to avoid rate limiting
        time.sleep(delay)

        return True, "", num_candles

    except requests.exceptions.HTTPError as e:
        if "429" in str(e):
            return False, f"Rate limit hit for {name}: {e}", 0
        else:
            return False, f"HTTP error for {name}: {e}", 0
    except Exception as e:
        return False, f"Error fetching {name}: {e}", 0

def run_rate_limit_test(delay_between_requests: float = 1.0) -> Dict:
    """
    Run the rate limit test on all memecoin assets.

    Args:
        delay_between_requests: Delay in seconds between each request

    Returns:
        Dictionary with test results
    """
    results = {
        'successful': [],
        'failed': [],
        'rate_limited': [],
        'total_candles': 0,
        'test_duration': 0
    }

    start_time = time.time()

    logging.info(f"Starting rate limit test with {len(MEMECOIN_ASSETS)} assets")
    logging.info(f"Delay between requests: {delay_between_requests} seconds")
    logging.info("=" * 60)

    for i, (network, token_address, name) in enumerate(MEMECOIN_ASSETS, 1):
        logging.info(f"[{i}/{len(MEMECOIN_ASSETS)}] Testing {name}...")

        success, error_msg, num_candles = test_single_asset_fetch(
            network, token_address, name, delay_between_requests
        )

        if success:
            results['successful'].append({
                'name': name,
                'network': network,
                'token_address': token_address,
                'candles': num_candles
            })
            results['total_candles'] += num_candles
        else:
            failed_entry = {
                'name': name,
                'network': network,
                'token_address': token_address,
                'error': error_msg
            }

            if "rate limit" in error_msg.lower() or "429" in error_msg:
                results['rate_limited'].append(failed_entry)
                logging.error(f"❌ Rate limit hit at asset {i}: {name}")
                # Stop testing when we hit rate limits
                break
            else:
                results['failed'].append(failed_entry)
                logging.error(f"❌ Failed to fetch {name}: {error_msg}")

    results['test_duration'] = time.time() - start_time

    return results

def print_test_summary(results: Dict):
    """Print a summary of the test results."""
    print("\n" + "=" * 60)
    print("GECKOTERMINAL RATE LIMIT TEST SUMMARY")
    print("=" * 60)

    print(f"Test Duration: {results['test_duration']:.1f} seconds")
    print(f"Total Assets Tested: {len(results['successful']) + len(results['failed']) + len(results['rate_limited'])}")
    print(f"Successful Fetches: {len(results['successful'])}")
    print(f"Failed Fetches: {len(results['failed'])}")
    print(f"Rate Limited: {len(results['rate_limited'])}")
    print(f"Total Candles Fetched: {results['total_candles']}")

    if results['successful']:
        print(f"\n✅ SUCCESSFUL ASSETS ({len(results['successful'])}):")
        for asset in results['successful']:
            print(f"  - {asset['name']} ({asset['network']}): {asset['candles']} candles")

    if results['failed']:
        print(f"\n❌ FAILED ASSETS ({len(results['failed'])}):")
        for asset in results['failed']:
            print(f"  - {asset['name']} ({asset['network']}): {asset['error']}")

    if results['rate_limited']:
        print(f"\n🚫 RATE LIMITED ASSETS ({len(results['rate_limited'])}):")
        for asset in results['rate_limited']:
            print(f"  - {asset['name']} ({asset['network']}): {asset['error']}")

    # Calculate success rate
    total_attempted = len(results['successful']) + len(results['failed']) + len(results['rate_limited'])
    if total_attempted > 0:
        success_rate = len(results['successful']) / total_attempted * 100
        print(f"\nSuccess Rate: {success_rate:.1f}%")

        if results['rate_limited']:
            print(f"Rate limit hit after {len(results['successful']) + len(results['failed'])} assets")

def main():
    """Main function to run the rate limit test."""
    if not GECKOTERMINAL_AVAILABLE:
        logging.error("GeckoTerminal fetcher not available. Exiting.")
        return

    print("GeckoTerminal Rate Limit Test")
    print("Testing with 2 days of 1h data per asset")
    print("=" * 60)

    # Test with different delays
    delays_to_test = [0.5, 1.0, 2.0]

    for delay in delays_to_test:
        print(f"\n🧪 Testing with {delay}s delay between requests...")
        results = run_rate_limit_test(delay_between_requests=delay)
        print_test_summary(results)

        # If we successfully fetched all assets, we found a good delay
        if not results['rate_limited'] and len(results['successful']) > 10:
            print(f"\n🎉 Found optimal delay: {delay}s")
            break

        # Wait a bit before trying the next delay
        if delay != delays_to_test[-1]:
            print(f"\nWaiting 30 seconds before next test...")
            time.sleep(30)

if __name__ == "__main__":
    main()
