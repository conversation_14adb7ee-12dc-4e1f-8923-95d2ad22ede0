#!/usr/bin/env python3
"""
Test script to verify the asset swap logic works correctly.
This simulates the scenario where AIXBT is swapped for ETH with proper proceeds handling.
"""

import sys
import os
import logging
from unittest.mock import Mock, patch
from typing import Dict, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_mock_trading_executor():
    """Create a mock trading executor for testing."""
    from src.trading.executor import TradingExecutor
    
    # Mock the dependencies
    mock_executor = TradingExecutor()
    
    # Mock paper trading
    mock_paper_trading = Mock()
    mock_paper_trading.get_balance.return_value = {'USDC': 100.0}  # Start with 100 USDC
    mock_paper_trading.get_positions.return_value = {
        'AAVE/USDC': {'amount': 0.077, 'entry_price': 259.96},  # ~20 USDC position
        'AIXBT/USDC': {'amount': 383.3, 'entry_price': 0.1761}  # ~67.5 USDC position
    }
    
    # Mock successful sell order for AIXBT (returns 67 USDC proceeds)
    mock_sell_order = {
        'id': 'sell_123',
        'filled': 383.3,
        'average_price': 0.175,  # Slightly different price
        'fee': {'cost': 0.067, 'currency': 'USDC', 'rate': 0.001}
    }
    mock_paper_trading.create_market_sell_order.return_value = mock_sell_order
    
    # Mock successful buy order for ETH
    mock_buy_order = {
        'id': 'buy_456',
        'filled': 0.0217,
        'average_price': 2921.7,
        'fee': {'cost': 0.063, 'currency': 'USDC', 'rate': 0.001}
    }
    mock_paper_trading.create_market_buy_order.return_value = mock_buy_order
    
    mock_executor.paper_trading = mock_paper_trading
    
    # Mock other dependencies
    mock_executor.trading_config = {'mode': 'paper', 'transaction_fee_rate': 0.001}
    mock_executor.risk_manager = Mock()
    mock_executor.risk_manager.close_position = Mock()
    mock_executor.risk_manager.register_open_position = Mock()
    
    # Set initial portfolio state (AAVE 0.2, AIXBT 0.8)
    mock_executor.current_portfolio = {
        'AAVE/USDC': 0.2,
        'AIXBT/USDC': 0.8
    }
    
    # Mock price fetching
    def mock_get_current_price(symbol):
        prices = {
            'AAVE/USDC': 259.96,
            'AIXBT/USDC': 0.1761,
            'ETH/USDC': 2921.7
        }
        return prices.get(symbol, 0.0)
    
    mock_executor.get_current_price = mock_get_current_price
    
    return mock_executor

def test_asset_swap_logic():
    """Test the asset swap logic with the scenario: AIXBT -> ETH swap."""
    print("=" * 60)
    print("TESTING ASSET SWAP LOGIC")
    print("=" * 60)
    
    # Create mock executor
    executor = create_mock_trading_executor()
    
    print("\n1. INITIAL STATE:")
    print(f"   Portfolio: {executor.current_portfolio}")
    print(f"   AAVE position: 0.077 units @ 259.96 = ~20 USDC (20%)")
    print(f"   AIXBT position: 383.3 units @ 0.1761 = ~67.5 USDC (80%)")
    print(f"   Available USDC: 100.0")
    
    print("\n2. NEW TARGET ALLOCATION:")
    new_allocation = {
        'AAVE/USDC': 0.2,  # Keep AAVE at 20%
        'ETH/USDC': 0.8    # Replace AIXBT with ETH at 80%
    }
    print(f"   Target: {new_allocation}")
    print(f"   This should trigger: AIXBT -> ETH swap")
    
    print("\n3. EXECUTING STRATEGY...")
    
    # Execute the multi-asset strategy
    result = executor.execute_multi_asset_strategy(new_allocation)
    
    print(f"\n4. EXECUTION RESULTS:")
    print(f"   Success: {result.get('success', False)}")
    print(f"   Number of trades: {len(result.get('trades', []))}")
    
    # Analyze the trades
    trades = result.get('trades', [])
    sell_trades = [t for t in trades if t.get('side') == 'sell']
    buy_trades = [t for t in trades if t.get('side') == 'buy']
    
    print(f"\n5. TRADE ANALYSIS:")
    
    if sell_trades:
        sell_trade = sell_trades[0]
        proceeds = sell_trade.get('proceeds', 0.0)
        print(f"   SELL AIXBT:")
        print(f"     Amount: {sell_trade.get('amount', 0):.4f} units")
        print(f"     Price: {sell_trade.get('price', 0):.4f} USDC")
        print(f"     Proceeds: {proceeds:.2f} USDC")
    
    if buy_trades:
        buy_trade = buy_trades[0]
        symbol = buy_trade.get('symbol')
        amount = buy_trade.get('amount', 0)
        price = buy_trade.get('price', 0)
        value = amount * price
        print(f"   BUY {symbol}:")
        print(f"     Amount: {amount:.6f} units")
        print(f"     Price: {price:.2f} USDC")
        print(f"     Value: {value:.2f} USDC")
        
        # Check if the full proceeds were used
        if sell_trades:
            proceeds = sell_trades[0].get('proceeds', 0.0)
            proceeds_used_pct = (value / proceeds) * 100 if proceeds > 0 else 0
            print(f"     Proceeds used: {proceeds_used_pct:.1f}% of sale proceeds")
            
            if proceeds_used_pct > 95:  # Allow for small fees/slippage
                print(f"   ✅ SUCCESS: Full proceeds used for replacement asset!")
            else:
                print(f"   ❌ ISSUE: Only {proceeds_used_pct:.1f}% of proceeds used!")
    
    print(f"\n6. FINAL PORTFOLIO STATE:")
    print(f"   Portfolio: {executor.current_portfolio}")
    
    return result

def test_weight_based_allocation():
    """Test that weight-based allocation still works for new positions."""
    print("\n" + "=" * 60)
    print("TESTING WEIGHT-BASED ALLOCATION (NEW POSITIONS)")
    print("=" * 60)
    
    # Create mock executor with no existing positions
    executor = create_mock_trading_executor()
    executor.current_portfolio = {}  # Start with empty portfolio
    executor.paper_trading.get_positions.return_value = {}
    executor.paper_trading.get_balance.return_value = {'USDC': 100.0}
    
    print("\n1. INITIAL STATE:")
    print(f"   Portfolio: Empty")
    print(f"   Available USDC: 100.0")
    
    print("\n2. NEW TARGET ALLOCATION:")
    new_allocation = {
        'AAVE/USDC': 0.3,  # 30 USDC
        'ETH/USDC': 0.7    # 70 USDC
    }
    print(f"   Target: {new_allocation}")
    print(f"   Expected: AAVE=30 USDC, ETH=70 USDC")
    
    print("\n3. EXECUTING STRATEGY...")
    result = executor.execute_multi_asset_strategy(new_allocation)
    
    print(f"\n4. EXECUTION RESULTS:")
    print(f"   Success: {result.get('success', False)}")
    
    # Analyze the trades
    trades = result.get('trades', [])
    buy_trades = [t for t in trades if t.get('side') == 'buy']
    
    print(f"\n5. TRADE ANALYSIS:")
    for trade in buy_trades:
        symbol = trade.get('symbol')
        amount = trade.get('amount', 0)
        price = trade.get('price', 0)
        value = amount * price
        expected_value = new_allocation.get(symbol, 0) * 100  # 100 USDC total
        
        print(f"   BUY {symbol}:")
        print(f"     Value: {value:.2f} USDC (expected: {expected_value:.2f} USDC)")
        
        if abs(value - expected_value) < 1.0:  # Allow for small precision differences
            print(f"   ✅ SUCCESS: Correct weight-based allocation!")
        else:
            print(f"   ❌ ISSUE: Incorrect allocation!")
    
    return result

if __name__ == "__main__":
    print("Starting Asset Swap Logic Tests...")
    
    try:
        # Test 1: Asset swap logic
        swap_result = test_asset_swap_logic()
        
        # Test 2: Weight-based allocation for new positions
        weight_result = test_weight_based_allocation()
        
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print(f"Asset Swap Test: {'✅ PASSED' if swap_result.get('success') else '❌ FAILED'}")
        print(f"Weight Allocation Test: {'✅ PASSED' if weight_result.get('success') else '❌ FAILED'}")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
