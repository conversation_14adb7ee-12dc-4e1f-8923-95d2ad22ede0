#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debug AAVE/XRP Signal Discrepancy

This script checks the exact PGO signal for AAVE/XRP ratio to understand
the discrepancy between universal visualizer and asset scoring debugger.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime, timedel<PERSON>

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_ratio_ohlcv(df_a: pd.DataFrame, df_b: pd.DataFrame) -> pd.DataFrame:
    """Create OHLCV data for the ratio between two assets."""
    # Align dataframes by common index
    common_index = df_a.index.intersection(df_b.index)
    df_a_aligned = df_a.reindex(common_index)
    df_b_aligned = df_b.reindex(common_index)

    # Calculate ratio OHLCV
    ratio_df = pd.DataFrame(index=common_index)
    ratio_df['open'] = df_a_aligned['open'] / df_b_aligned['open']
    ratio_df['high'] = df_a_aligned['high'] / df_b_aligned['high']
    ratio_df['low'] = df_a_aligned['low'] / df_b_aligned['low']
    ratio_df['close'] = df_a_aligned['close'] / df_b_aligned['close']
    ratio_df['volume'] = df_a_aligned['volume']  # Use volume from asset A

    return ratio_df.dropna()

def main():
    """Debug AAVE/XRP signal discrepancy."""
    print("AAVE/XRP Signal Debug")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    settings = config.get('settings', {})
    
    # Get PGO parameters from config
    pgo_length = settings.get('pgo_length', 35)
    upper_threshold = settings.get('pgo_upper_threshold', 1.1)
    lower_threshold = settings.get('pgo_lower_threshold', -0.58)
    
    print(f"PGO Parameters: length={pgo_length}, upper={upper_threshold}, lower={lower_threshold}")
    
    # Fetch data for AAVE and XRP
    print("\nFetching data...")
    exchange_id = config.get('exchange', 'binance')
    timeframe = settings.get('timeframe', '1d')
    
    # Calculate warmup period
    warmup_days = 120
    data_start_dt = datetime.now() - timedelta(days=warmup_days + 30)  # Extra buffer
    data_start_date = data_start_dt.strftime('%Y-%m-%d')
    
    data_dict = fetch_ohlcv_data(
        exchange_id=exchange_id,
        symbols=['AAVE/USDT', 'XRP/USDT'],
        timeframe=timeframe,
        since=data_start_date,
        use_cache=True
    )
    
    if not data_dict or 'AAVE/USDT' not in data_dict or 'XRP/USDT' not in data_dict:
        print("ERROR: Failed to fetch data")
        return
    
    print(f"Successfully fetched data for AAVE and XRP")
    print(f"AAVE data: {len(data_dict['AAVE/USDT'])} candles")
    print(f"XRP data: {len(data_dict['XRP/USDT'])} candles")
    
    # Create AAVE/XRP ratio
    print("\nCreating AAVE/XRP ratio...")
    ratio_df = create_ratio_ohlcv(data_dict['AAVE/USDT'], data_dict['XRP/USDT'])
    print(f"Ratio data: {len(ratio_df)} candles")
    print(f"Latest ratio date: {ratio_df.index[-1]}")
    print(f"Latest ratio value: {ratio_df['close'].iloc[-1]:.6f}")
    
    # Calculate PGO values
    print("\nCalculating PGO...")
    pgo_values = calculate_pgo(ratio_df, length=pgo_length)
    
    # Generate PGO signals
    print("Generating PGO signals...")
    pgo_signals = generate_pgo_signal(
        df=ratio_df,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )
    
    # Show last 10 values
    print(f"\nLast 10 PGO values and signals:")
    print("Date                PGO Value    Signal")
    print("-" * 45)
    
    for i in range(max(0, len(pgo_values) - 10), len(pgo_values)):
        date = pgo_values.index[i]
        pgo_val = pgo_values.iloc[i]
        signal = pgo_signals.iloc[i]
        signal_text = "LONG" if signal == 1 else "SHORT" if signal == -1 else "NEUTRAL"
        print(f"{date.strftime('%Y-%m-%d')}    {pgo_val:+8.6f}    {signal:+2d} ({signal_text})")
    
    # Latest values
    latest_pgo = pgo_values.iloc[-1]
    latest_signal = pgo_signals.iloc[-1]
    latest_binary = 1 if latest_signal == 1 else 0
    
    print(f"\n" + "=" * 50)
    print("FINAL RESULTS:")
    print(f"Latest PGO value: {latest_pgo:+.6f}")
    print(f"Latest signal: {latest_signal} ({'LONG' if latest_signal == 1 else 'SHORT' if latest_signal == -1 else 'NEUTRAL'})")
    print(f"Binary score (signal == 1): {latest_binary}")
    print(f"Upper threshold: {upper_threshold}")
    print(f"Lower threshold: {lower_threshold}")
    
    # Check threshold logic
    print(f"\nThreshold Analysis:")
    print(f"PGO > upper_threshold ({latest_pgo:.6f} > {upper_threshold}): {latest_pgo > upper_threshold}")
    print(f"PGO < lower_threshold ({latest_pgo:.6f} < {lower_threshold}): {latest_pgo < lower_threshold}")
    print(f"Between thresholds: {lower_threshold < latest_pgo < upper_threshold}")
    
    # Check signal transitions
    print(f"\nSignal Transitions (last 5):")
    for i in range(max(1, len(pgo_signals) - 5), len(pgo_signals)):
        if pgo_signals.iloc[i] != pgo_signals.iloc[i-1]:
            date = pgo_signals.index[i]
            from_signal = pgo_signals.iloc[i-1]
            to_signal = pgo_signals.iloc[i]
            pgo_val = pgo_values.iloc[i]
            print(f"{date.strftime('%Y-%m-%d')}: {from_signal} → {to_signal} (PGO: {pgo_val:+.6f})")

if __name__ == "__main__":
    main()
