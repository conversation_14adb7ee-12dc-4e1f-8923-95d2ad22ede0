#!/usr/bin/env python3
"""
PGO TradingView Comparison Debug Script

This script helps debug discrepancies between Python PGO implementation 
and TradingView PineScript by:
1. Fetching the same BTC/USDT data
2. Calculating PGO values step by step
3. Showing detailed calculations for verification
4. Comparing with expected TradingView behavior

Parameters to match TradingView:
- Length: 35
- Upper Threshold: 1.1  
- Lower Threshold: -0.58
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from src.data_fetcher import fetch_ohlcv_data
from src.indicators.base_indicators import calculate_sma, calculate_atr
from src.indicators.pgo_score import calculate_pgo, generate_pgo_score_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_pgo_calculation(df, length=35, upper_threshold=1.1, lower_threshold=-0.58, debug_rows=10):
    """
    Debug PGO calculation step by step to identify discrepancies.
    """
    print(f"\n🔍 DEBUGGING PGO CALCULATION")
    print(f"Parameters: length={length}, upper_threshold={upper_threshold}, lower_threshold={lower_threshold}")
    print(f"Data range: {df.index[0]} to {df.index[-1]} ({len(df)} rows)")
    
    # Calculate components
    close_prices = df['close']
    sma_values = calculate_sma(df, length=length, close_col='close')
    atr_values = calculate_atr(df, length=length)
    
    # Calculate PGO manually
    pgo_manual = (close_prices - sma_values) / atr_values
    
    # Calculate using our function
    pgo_function = calculate_pgo(df, length=length, src_col='close')
    
    # Show detailed calculations for recent data
    print(f"\n📊 DETAILED CALCULATIONS (last {debug_rows} rows):")
    print("Date\t\tClose\t\tSMA\t\tATR\t\tPGO\t\tManual_PGO")
    print("-" * 90)
    
    for i in range(max(0, len(df) - debug_rows), len(df)):
        date = df.index[i].strftime('%Y-%m-%d')
        close = close_prices.iloc[i]
        sma = sma_values.iloc[i] if not pd.isna(sma_values.iloc[i]) else "NaN"
        atr = atr_values.iloc[i] if not pd.isna(atr_values.iloc[i]) else "NaN"
        pgo_func = pgo_function.iloc[i] if not pd.isna(pgo_function.iloc[i]) else "NaN"
        pgo_man = pgo_manual.iloc[i] if not pd.isna(pgo_manual.iloc[i]) else "NaN"
        
        print(f"{date}\t{close:.2f}\t\t{sma}\t{atr}\t{pgo_func}\t{pgo_man}")
    
    # Check for differences
    diff = abs(pgo_function - pgo_manual)
    max_diff = diff.max()
    print(f"\n📈 CALCULATION VERIFICATION:")
    print(f"Max difference between function and manual calculation: {max_diff}")
    
    if max_diff > 1e-10:
        print("⚠️  WARNING: Significant differences found in PGO calculation!")
    else:
        print("✅ PGO calculations match between function and manual methods")
    
    return pgo_function, sma_values, atr_values

def debug_signal_generation(df, pgo_values, upper_threshold=1.1, lower_threshold=-0.58, debug_rows=20):
    """
    Debug signal generation step by step.
    """
    print(f"\n🎯 DEBUGGING SIGNAL GENERATION")
    print(f"Thresholds: upper={upper_threshold}, lower={lower_threshold}")
    
    # Generate signals using our function
    signals = generate_pgo_score_signal(
        df, 
        length=35, 
        upper_threshold=upper_threshold, 
        lower_threshold=lower_threshold,
        skip_warmup=False
    )
    
    # Manual signal generation to match PineScript exactly
    manual_signals = pd.Series(0, index=df.index, dtype=int)
    is_long = False
    is_short = False
    
    print(f"\n📊 SIGNAL GENERATION DETAILS (last {debug_rows} rows):")
    print("Date\t\tPGO\t\tis_long\tis_short\tSignal\tManual")
    print("-" * 80)
    
    # Process ALL data points to build proper state history
    for i in range(len(df)):
        pgo_value = pgo_values.iloc[i]

        if not pd.isna(pgo_value):
            # Apply PineScript logic
            if pgo_value > upper_threshold:
                is_long = True
                is_short = False
            elif pgo_value < lower_threshold:
                is_long = False
                is_short = True
            # No else clause - state persists

            # Convert to MTPI format
            if is_long:
                manual_signals.iloc[i] = 1
            elif is_short:
                manual_signals.iloc[i] = -1
            else:
                manual_signals.iloc[i] = 0

    # Now show debug info for last debug_rows
    for i in range(max(0, len(df) - debug_rows), len(df)):
        pgo_value = pgo_values.iloc[i]

        date = df.index[i].strftime('%Y-%m-%d')
        pgo_str = f"{pgo_value:.4f}" if not pd.isna(pgo_value) else "NaN"
        signal_func = signals.iloc[i]
        signal_manual = manual_signals.iloc[i]

        # Show current state for this row
        current_is_long = signal_manual == 1
        current_is_short = signal_manual == -1

        print(f"{date}\t{pgo_str}\t{current_is_long}\t{current_is_short}\t\t{signal_func}\t{signal_manual}")
    
    # Compare signals
    signal_diff = (signals != manual_signals).sum()
    print(f"\n📈 SIGNAL VERIFICATION:")
    print(f"Differences between function and manual signals: {signal_diff}")
    
    if signal_diff > 0:
        print("⚠️  WARNING: Signal generation differences found!")
        diff_indices = signals != manual_signals
        print("Differing dates:")
        for idx in df.index[diff_indices]:
            print(f"  {idx}: function={signals.loc[idx]}, manual={manual_signals.loc[idx]}")
    else:
        print("✅ Signal generation matches between function and manual methods")
    
    return signals, manual_signals

def main():
    """
    Main debug function.
    """
    print("🔧 PGO TRADINGVIEW COMPARISON DEBUG")
    print("=" * 50)
    
    # Parameters matching TradingView
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    # Fetch recent BTC data
    print("📥 Fetching BTC/USDT data...")
    try:
        data_dict = fetch_ohlcv_data(
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=200,  # Get enough data for proper calculation
            exchange_id='binance',
            force_refresh=True,  # Force fresh data to avoid cache issues
            max_cache_age_days=0  # Don't use any cached data
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return
        
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Debug PGO calculation
        pgo_values, sma_values, atr_values = debug_pgo_calculation(
            btc_df, length, upper_threshold, lower_threshold
        )
        
        # Debug signal generation  
        signals, manual_signals = debug_signal_generation(
            btc_df, pgo_values, upper_threshold, lower_threshold
        )
        
        # Summary statistics
        print(f"\n📊 SUMMARY STATISTICS:")
        print(f"PGO Statistics:")
        print(f"  Min: {pgo_values.min():.4f}")
        print(f"  Max: {pgo_values.max():.4f}")
        print(f"  Mean: {pgo_values.mean():.4f}")
        print(f"  Current: {pgo_values.iloc[-1]:.4f}")
        
        signal_counts = signals.value_counts().sort_index()
        print(f"\nSignal Distribution:")
        for signal, count in signal_counts.items():
            signal_name = {-1: "Short", 0: "Neutral", 1: "Long"}.get(signal, str(signal))
            print(f"  {signal_name} ({signal}): {count} ({count/len(signals)*100:.1f}%)")
        
        print(f"\nCurrent Signal: {signals.iloc[-1]}")
        
        # Check recent crossovers
        print(f"\n🔄 RECENT CROSSOVERS:")
        crossovers = []
        for i in range(max(1, len(pgo_values) - 30), len(pgo_values)):
            if not pd.isna(pgo_values.iloc[i-1]) and not pd.isna(pgo_values.iloc[i]):
                prev_pgo = pgo_values.iloc[i-1]
                curr_pgo = pgo_values.iloc[i]
                
                if prev_pgo <= upper_threshold and curr_pgo > upper_threshold:
                    crossovers.append((btc_df.index[i], "Bullish", curr_pgo))
                elif prev_pgo >= lower_threshold and curr_pgo < lower_threshold:
                    crossovers.append((btc_df.index[i], "Bearish", curr_pgo))
        
        if crossovers:
            for date, direction, pgo_val in crossovers[-5:]:  # Show last 5
                print(f"  {date.strftime('%Y-%m-%d')}: {direction} crossover (PGO: {pgo_val:.4f})")
        else:
            print("  No recent crossovers found")
        
        print(f"\n✅ Debug analysis complete!")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        logging.error(f"Debug error: {e}")

if __name__ == "__main__":
    main()
