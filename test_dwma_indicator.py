#!/usr/bin/env python3
"""
Test script for DWMA Score indicator implementation.

This script tests the DWMA indicator against sample data to ensure
it's working correctly before integration into the MTPI system.
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.indicators.dwma_score import generate_dwma_signal, calculate_dwma_score
from src.data_fetcher import fetch_ohlcv_data


def create_sample_data(periods: int = 100) -> pd.DataFrame:
    """Create sample OHLCV data for testing."""
    dates = pd.date_range(start='2023-01-01', periods=periods, freq='D')
    
    # Generate realistic price data with trend and noise
    base_price = 100
    trend = np.linspace(0, 20, periods)
    noise = np.random.normal(0, 2, periods)
    
    close_prices = base_price + trend + noise
    
    # Generate OHLC from close prices
    high_prices = close_prices + np.random.uniform(0.5, 3, periods)
    low_prices = close_prices - np.random.uniform(0.5, 3, periods)
    open_prices = np.roll(close_prices, 1)
    open_prices[0] = close_prices[0]
    
    # Generate volume
    volume = np.random.uniform(1000, 10000, periods)
    
    df = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volume
    }, index=dates)
    
    return df


def test_dwma_basic_functionality():
    """Test basic DWMA functionality with sample data."""
    print("Testing DWMA basic functionality...")
    
    # Create sample data
    df = create_sample_data(100)
    
    # Test different smoothing methods
    methods = ["ATR", "Weighted SD", "For loop"]
    
    for method in methods:
        print(f"\nTesting {method} method:")
        
        try:
            signal = generate_dwma_signal(
                df=df,
                smoothing_style=method,
                length=2,
                ma_smooth_length=12
            )
            
            # Check signal properties
            unique_signals = signal.unique()
            signal_counts = signal.value_counts()
            
            print(f"  Unique signals: {sorted(unique_signals)}")
            print(f"  Signal distribution: {dict(signal_counts)}")
            print(f"  Non-zero signals: {(signal != 0).sum()}/{len(signal)}")
            
            # Check for NaN values
            nan_count = signal.isna().sum()
            print(f"  NaN values: {nan_count}")
            
            if nan_count == 0 and len(unique_signals) > 1:
                print(f"  ✅ {method} method working correctly")
            else:
                print(f"  ❌ {method} method has issues")
                
        except Exception as e:
            print(f"  ❌ Error with {method} method: {e}")


def test_dwma_with_real_data():
    """Test DWMA with real BTC data using TradingView parameters."""
    print("\nTesting DWMA with real BTC data (TradingView parameters)...")

    try:
        # Use a fixed start date of 2023-10-19
        start_date = '2024-09-01'
        end_date = datetime.now().strftime('%Y-%m-%d')
        print(f"Fetching BTC data from {start_date} to {end_date}")
        
        # Fetch real BTC data with a specific start date
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=start_date  # Pass as date string, not timestamp
        )

        if 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return

        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} BTC candles")

        # TradingView parameters - Weighted SD method
        tv_params = {
            'smoothing_style': "Weighted SD",
            'src_col': 'close',
            'length': 17,  # DWA Length
            'ma_type': "EMA",  # Smoothing MA
            'ma_smooth_length': 12,  # MA Smooth
            'sd_length': 33,  # SD Length
            'upper_sd_weight': 1.031,  # Upper SD Weight
            'lower_sd_weight': 0.996,  # Lower SD Weight
        }

        print(f"Using TradingView parameters: {tv_params}")

        # Test DWMA with TradingView Weighted SD method
        signal = generate_dwma_signal(df=btc_df, **tv_params)

        # Analyze results
        signal_changes = (signal != signal.shift(1)).sum()
        latest_signal = signal.iloc[-1]

        print(f"Signal changes: {signal_changes}")
        print(f"Latest signal: {latest_signal}")
        print(f"Signal distribution: {dict(signal.value_counts())}")

        # Show recent signals
        print("\nRecent signals (last 15):")
        recent_data = pd.DataFrame({
            'Date': btc_df.index[-15:].strftime('%Y-%m-%d'),
            'Close': btc_df['close'].iloc[-15:].round(2),
            'Signal': signal.iloc[-15:]
        })
        print(recent_data.to_string(index=False))

        print("✅ Real data test completed successfully")
        return btc_df, signal, tv_params

    except Exception as e:
        print(f"❌ Error with real data test: {e}")
        return None, None, None


def test_dwma_parameters():
    """Test DWMA with different parameter combinations."""
    print("\nTesting DWMA with different parameters...")
    
    df = create_sample_data(100)
    
    # Test different parameter combinations
    test_configs = [
        {"length": 2, "ma_smooth_length": 12, "smoothing_style": "ATR"},
        {"length": 5, "ma_smooth_length": 20, "smoothing_style": "Weighted SD"},
        {"length": 3, "ma_smooth_length": 15, "smoothing_style": "For loop"},
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\nTest config {i+1}: {config}")
        
        try:
            signal = generate_dwma_signal(df=df, **config)
            
            # Basic validation
            assert len(signal) == len(df), "Signal length mismatch"
            assert signal.dtype in [int, 'int64'], "Signal should be integer type"
            assert set(signal.unique()).issubset({-1, 0, 1}), "Invalid signal values"
            
            print(f"  ✅ Config {i+1} passed validation")
            
        except Exception as e:
            print(f"  ❌ Config {i+1} failed: {e}")


def visualize_dwma_tradingview_style(btc_df, signal, tv_params, save_plot: bool = True):
    """Create TradingView-style visualization of DWMA signals."""
    print("\nGenerating TradingView-style DWMA visualization...")

    try:
        # Calculate DWMA signals and components with TradingView parameters
        dwma_signal, dwma, dwmas = calculate_dwma_score(df=btc_df, **tv_params)

        # Create TradingView-style dark theme visualization
        plt.style.use('dark_background')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10), sharex=True,
                                      gridspec_kw={'height_ratios': [3, 1]})

        # Main price chart (top panel)
        # Plot candlestick-style price data
        for i in range(len(btc_df)):
            color = '#00ff88' if btc_df['close'].iloc[i] >= btc_df['open'].iloc[i] else '#ff4444'
            ax1.plot([i, i], [btc_df['low'].iloc[i], btc_df['high'].iloc[i]],
                    color=color, linewidth=1, alpha=0.8)
            ax1.plot([i, i], [btc_df['open'].iloc[i], btc_df['close'].iloc[i]],
                    color=color, linewidth=3, alpha=0.9)

        # Plot DWMA lines (similar to TradingView)
        ax1.plot(range(len(btc_df)), dwmas, color='#00d4ff', linewidth=2,
                label='Enhanced DWMA', alpha=0.9)

        # Add signal markers and connected lines for better visualization
        long_signals = signal == 1
        short_signals = signal == -1

        # For long signals - keep the triangle markers but add connecting lines
        if long_signals.any():
            long_indices = np.where(long_signals)[0]
            ax1.scatter(long_indices, btc_df['close'].iloc[long_indices],
                       color='#00ff88', marker='^', s=100, label='Long Signal',
                       edgecolors='white', linewidth=1, zorder=5)
            
        # For short signals - replace triangles with connecting line and colored circles
        if short_signals.any():
            short_indices = np.where(short_signals)[0]
            # Add a line connecting short signal points
            if len(short_indices) > 1:
                ax1.plot(short_indices, btc_df['close'].iloc[short_indices],
                        color='#ff4444', linestyle='-', linewidth=2, alpha=0.7,
                        label='Short Signal Trend')
            # Add circle markers at each short signal point
            ax1.scatter(short_indices, btc_df['close'].iloc[short_indices],
                       color='#ff4444', marker='o', s=80, 
                       edgecolors='white', linewidth=1, zorder=5,
                       label='Short Signal' if len(short_indices) == 1 else "")

        ax1.set_title('BTC/USDT - Enhanced DWMA Indicator (TradingView Style)',
                     color='white', fontsize=14, fontweight='bold')
        ax1.legend(loc='upper left', framealpha=0.8)
        ax1.grid(True, alpha=0.2, color='gray')
        ax1.set_ylabel('Price (USDT)', color='white')

        # Signal panel (bottom)
        signal_colors = ['#ff4444' if s == -1 else '#00ff88' if s == 1 else '#666666'
                        for s in signal]
        ax2.bar(range(len(signal)), signal, color=signal_colors, alpha=0.8, width=0.8)
        ax2.axhline(y=0, color='white', linestyle='-', alpha=0.5, linewidth=1)
        ax2.set_ylim(-1.5, 1.5)
        ax2.set_ylabel('Signal', color='white')
        ax2.set_xlabel('Days', color='white')
        ax2.grid(True, alpha=0.2, color='gray')

        # Add parameter info
        param_text = f"Weighted SD | Length: {tv_params['length']} | MA: {tv_params['ma_smooth_length']} | SD: {tv_params['sd_length']}"
        ax1.text(0.02, 0.98, param_text, transform=ax1.transAxes,
                color='#cccccc', fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

        # Format x-axis with dates
        date_labels = [btc_df.index[i].strftime('%m/%d') for i in range(0, len(btc_df), max(1, len(btc_df)//10))]
        date_positions = list(range(0, len(btc_df), max(1, len(btc_df)//10)))
        ax2.set_xticks(date_positions)
        ax2.set_xticklabels(date_labels, rotation=45, color='white')

        plt.tight_layout()

        if save_plot:
            plt.savefig('dwma_tradingview_style.png', dpi=300, bbox_inches='tight',
                       facecolor='#1e1e1e', edgecolor='none')
            print("TradingView-style plot saved as 'dwma_tradingview_style.png'")

        plt.show()

        print("✅ TradingView-style visualization completed")

    except Exception as e:
        print(f"❌ TradingView visualization failed: {e}")
        import traceback
        traceback.print_exc()


def test_all_smoothing_methods():
    """Test all three smoothing methods with TradingView parameters."""
    print("\nTesting all smoothing methods with TradingView parameters...")

    try:
        # Use the same fixed start date of 2023-10-19 for consistency
        start_date = '2023-10-19'
        print(f"Fetching BTC data from {start_date} for smoothing methods test")
        
        # Fetch BTC data with specific start date
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            since=start_date  # Pass as date string, not timestamp
        )

        btc_df = data_dict['BTC/USDT']

        # Test configurations for all three methods
        configs = {
            # "ATR": {
            #     'smoothing_style': "ATR",
            #     'length': 17,
            #     'ma_type': "EMA",
            #     'ma_smooth_length': 12,
            #     'atr_period': 6,
            #     'atr_multiplier': 1.0
            # },
            "Weighted SD": {
                'smoothing_style': "Weighted SD",
                'length': 17,
                'ma_type': "EMA",
                'ma_smooth_length': 12,
                'sd_length': 33,
                'upper_sd_weight': 1.031,
                'lower_sd_weight': 0.996
            }
            # "For loop": {
            #     'smoothing_style': "For loop",
            #     'length': 17,
            #     'ma_type': "EMA",
            #     'ma_smooth_length': 12,
            #     'loop_start': 1,
            #     'loop_end': 21,
            #     'long_threshold': 20,
            #     'short_threshold': 0
            # }
        }

        for method, params in configs.items():
            print(f"\n--- Testing {method} method ---")
            signal = generate_dwma_signal(df=btc_df, **params)

            signal_changes = (signal != signal.shift(1)).sum()
            latest_signal = signal.iloc[-1]
            distribution = dict(signal.value_counts())

            print(f"Signal changes: {signal_changes}")
            print(f"Latest signal: {latest_signal}")
            print(f"Distribution: {distribution}")

    except Exception as e:
        print(f"❌ Error testing all methods: {e}")


def main():
    """Run all DWMA tests with focus on TradingView compatibility."""
    print("=" * 70)
    print("DWMA Score Indicator Test Suite - TradingView Compatible")
    print("=" * 70)

    # Run basic tests
    test_dwma_basic_functionality()
    test_dwma_parameters()

    # Test with real BTC data using TradingView parameters
    btc_df, signal, tv_params = test_dwma_with_real_data()

    # Test all smoothing methods
    test_all_smoothing_methods()

    # Create TradingView-style visualization if data is available
    if btc_df is not None and signal is not None:
        try:
            visualize_dwma_tradingview_style(btc_df, signal, tv_params, save_plot=True)
        except Exception as e:
            print(f"Skipping TradingView visualization: {e}")

    print("\n" + "=" * 70)
    print("DWMA Test Suite Completed - Ready for MTPI Integration")
    print("=" * 70)


if __name__ == "__main__":
    main()
