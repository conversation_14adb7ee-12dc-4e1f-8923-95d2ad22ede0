#!/usr/bin/env python3
"""
Debug script to check actual Kraken positions and understand why the system
thinks there are 10/10 open positions when there should be none.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading.account import AccountManager
from src.trading.executor import TradingExecutor
from src.trading.risk_manager import RiskManager
import yaml

def setup_logging():
    """Setup logging for debugging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_config(config_path):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logging.error(f"Error loading config from {config_path}: {e}")
        return None

def debug_kraken_positions():
    """Debug Kraken positions and position counting logic."""
    
    print("=" * 80)
    print("KRAKEN POSITIONS DEBUG SCRIPT")
    print("=" * 80)
    
    config_path = 'config/settings_kraken_eur.yaml'
    
    # Load configuration
    config = load_config(config_path)
    if not config:
        print(f"❌ Failed to load config from {config_path}")
        return
    
    print(f"✅ Config loaded from {config_path}")
    
    try:
        # 1. Check AccountManager positions
        print("\n" + "=" * 50)
        print("1. CHECKING ACCOUNT MANAGER POSITIONS")
        print("=" * 50)
        
        account_manager = AccountManager('kraken', config_path=config_path)
        
        # Get all balances
        all_balances = account_manager.get_all_balances()
        print(f"All non-zero balances: {all_balances}")
        
        # Get open positions (this is what the system uses to count positions)
        open_positions = account_manager.get_open_positions()
        print(f"Open positions count: {len(open_positions)}")
        print(f"Open positions details:")
        for symbol, details in open_positions.items():
            print(f"  {symbol}: {details}")
        
        # 2. Check TradingExecutor position counting
        print("\n" + "=" * 50)
        print("2. CHECKING TRADING EXECUTOR POSITION LOGIC")
        print("=" * 50)
        
        executor = TradingExecutor('kraken', config_path=config_path)
        
        # Get current positions as the executor sees them
        current_positions = executor.account_manager.get_open_positions()
        print(f"Executor sees {len(current_positions)} positions:")
        for symbol, details in current_positions.items():
            print(f"  {symbol}: amount={details.get('amount', 0):.8f}, value={details.get('value_usdt', 0):.2f}")
        
        # 3. Check Risk Manager configuration
        print("\n" + "=" * 50)
        print("3. CHECKING RISK MANAGER CONFIGURATION")
        print("=" * 50)
        
        risk_manager = RiskManager(config.get('risk_management', {}))
        max_positions = config.get('risk_management', {}).get('max_open_positions', 1)
        print(f"Max open positions configured: {max_positions}")
        
        # Test the position check
        can_open_new = risk_manager.check_max_open_positions(len(current_positions))
        print(f"Can open new position: {can_open_new}")
        print(f"Current positions count: {len(current_positions)}")
        
        # 4. Check raw exchange balances
        print("\n" + "=" * 50)
        print("4. CHECKING RAW EXCHANGE BALANCES")
        print("=" * 50)
        
        if account_manager.exchange:
            try:
                raw_balance = account_manager.exchange.fetch_balance()
                print("Raw balance from exchange:")
                print(f"  Free balances: {raw_balance['free']}")
                print(f"  Used balances: {raw_balance['used']}")
                print(f"  Total balances: {raw_balance['total']}")
                
                # Count non-zero free balances (excluding quote currencies)
                quote_currencies = ['USDC', 'USD', 'BUSD', 'USDT', 'EUR']
                non_quote_balances = {
                    currency: amount 
                    for currency, amount in raw_balance['free'].items() 
                    if currency not in quote_currencies and float(amount) > 0
                }
                print(f"  Non-quote currency balances: {non_quote_balances}")
                print(f"  Count of non-quote currencies with balance: {len(non_quote_balances)}")
                
            except Exception as e:
                print(f"❌ Error fetching raw balance: {e}")
        
        # 5. Check if there are any open orders that might be counted as positions
        print("\n" + "=" * 50)
        print("5. CHECKING OPEN ORDERS")
        print("=" * 50)
        
        try:
            if hasattr(executor, 'order_manager') and executor.order_manager:
                open_orders = executor.order_manager.get_open_orders()
                print(f"Open orders count: {len(open_orders)}")
                for order in open_orders:
                    print(f"  Order: {order}")
            else:
                print("No order manager available")
        except Exception as e:
            print(f"❌ Error checking open orders: {e}")
        
        # 6. Summary and recommendations
        print("\n" + "=" * 50)
        print("6. SUMMARY AND DIAGNOSIS")
        print("=" * 50)
        
        print(f"Configuration max positions: {max_positions}")
        print(f"Actual positions detected: {len(current_positions)}")
        print(f"Position limit reached: {len(current_positions) >= max_positions}")
        
        if len(current_positions) >= max_positions and len(current_positions) > 0:
            print("\n🔍 DIAGNOSIS:")
            print("The system is detecting positions based on non-zero balances.")
            print("These might be:")
            print("1. Leftover small amounts from previous trades")
            print("2. Dust balances that are too small to trade")
            print("3. Balances from manual trades or deposits")
            print("\n💡 SOLUTIONS:")
            print("1. Manually sell/withdraw the detected positions")
            print("2. Increase max_open_positions in config")
            print("3. Add minimum balance threshold to ignore dust")
        elif len(current_positions) == 0:
            print("\n✅ No positions detected - the issue might be elsewhere")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    setup_logging()
    debug_kraken_positions()
