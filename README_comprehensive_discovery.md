# Comprehensive Exchange Asset Discovery

This tool discovers **ALL available trading pairs** across multiple cryptocurrency exchanges for USDT, USDC, USD, and EUR quote currencies. Perfect for finding comprehensive alternatives to Binance and discovering new trading opportunities.

## 🎯 What This Script Does

**Discovers EVERYTHING available:**
- Fetches ALL base assets from each exchange
- Checks availability for USDT, USDC, USD, EUR pairs
- Verifies trading history (35+ days by default)
- Creates comprehensive Excel comparison

**vs. the previous script that only checked your predefined token list**

## Supported Exchanges
- **OKX** - 0.08%/0.10% fees, EU-regulated, 350+ cryptos
- **Bybit** - 0.10%/0.10% fees, EU-regulated, 450+ cryptos  
- **Kraken** - 0.25%/0.40% fees, Fully EU-regulated, 230+ cryptos
- **Bitvavo** - 0.15%/0.25% fees, DNB-registered (NL), 200+ cryptos

## Installation

```bash
pip install -r requirements_exchange_checker.txt
```

## Usage

### Basic Usage (Discover everything)
```bash
python comprehensive_exchange_discovery.py
```

### Check specific exchanges only
```bash
python comprehensive_exchange_discovery.py --exchanges okx bybit
```

### Check specific quote currencies only
```bash
python comprehensive_exchange_discovery.py --quotes USDT USDC
```

### Custom trading history requirement
```bash
python comprehensive_exchange_discovery.py --min-age-days 60
```

### Custom output file
```bash
python comprehensive_exchange_discovery.py --output my_discovery.xlsx
```

### Combined options
```bash
python comprehensive_exchange_discovery.py --exchanges okx kraken --quotes USDT USDC --min-age-days 35 --output focused_discovery.xlsx
```

## Output Structure

The script creates an **Excel file with multiple sheets**:

### 📊 **Main Sheets:**
1. **All_Pairs_Comprehensive** - Complete dataset with all combinations
2. **USDT_Pairs** - Only USDT pairs
3. **USDC_Pairs** - Only USDC pairs  
4. **USD_Pairs** - Only USD pairs
5. **EUR_Pairs** - Only EUR pairs
6. **Summary_Statistics** - Key metrics and totals

### 📋 **Columns in Each Sheet:**
- `Base_Asset`: The cryptocurrency (e.g., BTC, ETH, DOGE)
- `Quote_Currency`: USDT, USDC, USD, or EUR
- `Pair`: The trading pair (e.g., BTC/USDT)
- `OKX_available`: Available on OKX (True/False)
- `OKX_has_enough_data`: Has 35+ days trading data on OKX
- `OKX_days_available`: Exact days of data available
- *(Similar columns for BYBIT, KRAKEN, BITVAVO)*
- `Total_Exchanges_Available`: Number of exchanges listing this pair
- `Total_Exchanges_With_Enough_Data`: Number with sufficient trading history
- `Available_On`: List of exchanges where pair is available
- `Available_With_Enough_Data_On`: List of exchanges with mature trading

## 🔍 **Key Features**

### **Complete Discovery:**
- **No predefined lists** - discovers everything available
- **All quote currencies** - USDT, USDC, USD, EUR
- **Cross-exchange comparison** - see what's unique to each exchange
- **Trading maturity verification** - ensures sufficient liquidity history

### **Smart Analysis:**
- **Sorted by popularity** - most widely available pairs first
- **Exchange-specific data** - actual trading history verification
- **Multiple output formats** - separate sheets for easy filtering
- **Comprehensive statistics** - summary metrics for quick insights

### **Practical Benefits:**
- **Discover new opportunities** - find tokens you didn't know existed
- **Identify exchange strengths** - see which exchange has the most pairs
- **Plan migrations** - know exactly what's available where
- **Find alternatives** - discover similar tokens across exchanges

## 📈 **Example Use Cases**

### **Finding Binance Alternatives:**
1. Run the discovery script
2. Filter the Excel for tokens you currently trade on Binance
3. See which alternative exchanges support them
4. Identify backup exchanges for your portfolio

### **Discovering New Opportunities:**
1. Sort by `Total_Exchanges_With_Enough_Data` (descending)
2. Look for widely supported tokens you haven't considered
3. Check specific quote currency sheets (e.g., USDC_Pairs)
4. Find tokens unique to specific exchanges

### **Exchange Comparison:**
1. Check Summary_Statistics sheet
2. Compare total pairs available per exchange
3. See which exchange supports your preferred quote currency best
4. Identify the most comprehensive exchange for your needs

## ⚡ **Performance Notes**

- **Comprehensive but slow**: Checks thousands of combinations
- **Rate limited**: Respects exchange API limits
- **Progress tracking**: Shows completion percentage
- **Resumable**: Can be interrupted and results still saved
- **Memory efficient**: Processes data in chunks

## 🚀 **Expected Results**

You'll discover:
- **1000+ unique base assets** across all exchanges
- **4000+ pair combinations** (asset × quote currency)
- **Exchange-specific gems** - tokens only available on certain exchanges
- **Mature vs new tokens** - which have sufficient trading history
- **Quote currency preferences** - which exchanges favor USDT vs USDC

## 📊 **Sample Output Preview**

| Base_Asset | Quote_Currency | OKX_available | OKX_has_enough_data | BYBIT_available | Total_Exchanges_With_Enough_Data |
|------------|----------------|---------------|---------------------|-----------------|----------------------------------|
| BTC        | USDT           | True          | True                | True            | 4                                |
| ETH        | USDC           | True          | True                | True            | 3                                |
| RARE_TOKEN | USDT           | True          | True                | False           | 1                                |

This gives you the **complete picture** of what's available across all exchanges, not just what you already know about!

## 🔧 **Troubleshooting**

- **Long runtime**: Normal for comprehensive discovery (30-60 minutes)
- **Rate limiting**: Script includes delays to avoid API blocks
- **Memory usage**: Large datasets - ensure sufficient RAM
- **Network issues**: Script will continue from where it left off

Ready to discover everything that's available? 🚀
