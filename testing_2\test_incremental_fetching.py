#!/usr/bin/env python
"""
Test script for incremental data fetching.
"""

import os
import sys
import logging
import time
import json
from datetime import datetime, timedelta

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.incremental_fetcher import fetch_incremental_data
from src.state_manager import load_state, save_state, get_state_file_path
from src.config_manager import load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('incremental_fetching_test.log', mode='w')
    ]
)

def clear_state():
    """Clear the state file to start fresh."""
    state_file_path = get_state_file_path('test_incremental_fetching')
    if os.path.exists(state_file_path):
        os.remove(state_file_path)
        logging.info(f"Removed state file: {state_file_path}")
    else:
        logging.info(f"No state file to remove: {state_file_path}")

def test_incremental_fetching():
    """Test incremental data fetching."""
    # Load configuration
    config = load_config()
    
    # Get settings
    settings = config.get('settings', {})
    exchange_id = config.get('exchange', 'binance')
    timeframe = settings.get('timeframe', '1d')
    assets = settings.get('assets', ['BTC/USDC', 'ETH/USDC', 'SOL/USDC'])
    
    # Clear the state to start fresh
    clear_state()
    
    # First run: Fetch all data
    logging.info("=== FIRST RUN: FETCHING ALL DATA ===")
    start_time = time.time()
    data_dict_1 = fetch_incremental_data(
        exchange_id=exchange_id,
        symbols=assets,
        timeframe=timeframe,
        state_name='test_incremental_fetching',
        use_cache=True,
        force_refresh=False
    )
    elapsed_time_1 = time.time() - start_time
    
    # Log the results
    logging.info(f"First run completed in {elapsed_time_1:.2f} seconds")
    for symbol, df in data_dict_1.items():
        if not df.empty:
            logging.info(f"  - {symbol}: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")
    
    # Wait a moment to ensure timestamps are different
    time.sleep(1)
    
    # Second run: Should only fetch new data
    logging.info("\n=== SECOND RUN: SHOULD ONLY FETCH NEW DATA ===")
    start_time = time.time()
    data_dict_2 = fetch_incremental_data(
        exchange_id=exchange_id,
        symbols=assets,
        timeframe=timeframe,
        state_name='test_incremental_fetching',
        use_cache=True,
        force_refresh=False
    )
    elapsed_time_2 = time.time() - start_time
    
    # Log the results
    logging.info(f"Second run completed in {elapsed_time_2:.2f} seconds")
    for symbol, df in data_dict_2.items():
        if not df.empty:
            logging.info(f"  - {symbol}: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")
    
    # Third run: Force refresh to fetch all data again
    logging.info("\n=== THIRD RUN: FORCE REFRESH TO FETCH ALL DATA AGAIN ===")
    start_time = time.time()
    data_dict_3 = fetch_incremental_data(
        exchange_id=exchange_id,
        symbols=assets,
        timeframe=timeframe,
        state_name='test_incremental_fetching',
        use_cache=True,
        force_refresh=True
    )
    elapsed_time_3 = time.time() - start_time
    
    # Log the results
    logging.info(f"Third run completed in {elapsed_time_3:.2f} seconds")
    for symbol, df in data_dict_3.items():
        if not df.empty:
            logging.info(f"  - {symbol}: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")
    
    # Compare the results
    logging.info("\n=== COMPARISON ===")
    logging.info(f"First run (full fetch): {elapsed_time_1:.2f} seconds")
    logging.info(f"Second run (incremental): {elapsed_time_2:.2f} seconds")
    logging.info(f"Third run (force refresh): {elapsed_time_3:.2f} seconds")
    
    # Calculate the speedup
    if elapsed_time_1 > 0:
        speedup = elapsed_time_1 / elapsed_time_2 if elapsed_time_2 > 0 else float('inf')
        logging.info(f"Speedup from incremental fetching: {speedup:.2f}x")
    
    # Check if the data is the same
    same_data = True
    for symbol in data_dict_1:
        if symbol in data_dict_3:
            if len(data_dict_1[symbol]) != len(data_dict_3[symbol]):
                same_data = False
                logging.warning(f"Different number of candles for {symbol}: {len(data_dict_1[symbol])} vs {len(data_dict_3[symbol])}")
    
    if same_data:
        logging.info("Data from first and third runs is the same (as expected)")
    else:
        logging.warning("Data from first and third runs is different (unexpected)")
    
    # Print the state file
    state_file_path = get_state_file_path('test_incremental_fetching')
    if os.path.exists(state_file_path):
        with open(state_file_path, 'r') as f:
            state = json.load(f)
        logging.info(f"\nState file contents: {json.dumps(state, indent=2)}")
    else:
        logging.warning(f"State file not found: {state_file_path}")
    
    return {
        'first_run_time': elapsed_time_1,
        'second_run_time': elapsed_time_2,
        'third_run_time': elapsed_time_3,
        'speedup': speedup if 'speedup' in locals() else None,
        'same_data': same_data
    }

if __name__ == "__main__":
    print("=== TESTING INCREMENTAL DATA FETCHING ===")
    results = test_incremental_fetching()
    
    print("\n=== RESULTS ===")
    print(f"First run (full fetch): {results['first_run_time']:.2f} seconds")
    print(f"Second run (incremental): {results['second_run_time']:.2f} seconds")
    print(f"Third run (force refresh): {results['third_run_time']:.2f} seconds")
    
    if results['speedup'] is not None:
        print(f"Speedup from incremental fetching: {results['speedup']:.2f}x")
    
    if results['same_data']:
        print("Data from first and third runs is the same (as expected)")
    else:
        print("Data from first and third runs is different (unexpected)")
    
    print("\nSee incremental_fetching_test.log for detailed logs")
