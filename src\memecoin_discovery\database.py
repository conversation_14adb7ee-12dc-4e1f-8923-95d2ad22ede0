"""
Database module for memecoin discovery system.

This module handles all database operations for storing discovered tokens,
analysis results, and tracking performance metrics.
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import os

@dataclass
class DiscoveredToken:
    """Data class representing a discovered token."""
    symbol: str
    name: str
    contract_address: str
    network: str
    discovery_date: datetime
    source: str  # 'twitter', 'manual', etc.
    initial_price: Optional[float] = None
    market_cap: Optional[float] = None
    volume_24h: Optional[float] = None
    twitter_mentions: int = 0
    sentiment_score: Optional[float] = None
    ai_score: Optional[float] = None
    risk_score: Optional[float] = None
    final_score: Optional[float] = None
    status: str = 'discovered'  # 'discovered', 'analyzing', 'approved', 'rejected', 'monitoring'
    metadata: Optional[Dict] = None

@dataclass
class TokenAnalysis:
    """Data class representing AI analysis results for a token."""
    token_symbol: str
    analysis_date: datetime
    sentiment_analysis: Dict
    technical_analysis: Dict
    social_metrics: Dict
    risk_assessment: Dict
    ai_recommendation: str
    confidence_score: float
    reasoning: str

@dataclass
class TokenPerformance:
    """Data class for tracking token performance after discovery."""
    token_symbol: str
    date: datetime
    price: float
    volume_24h: float
    market_cap: Optional[float] = None
    price_change_1d: Optional[float] = None
    price_change_7d: Optional[float] = None
    price_change_30d: Optional[float] = None

class DiscoveryDatabase:
    """Database manager for the memecoin discovery system."""
    
    def __init__(self, db_path: str = "data/memecoin_discovery.db"):
        """Initialize the database connection and create tables if needed."""
        self.db_path = db_path
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        logging.info(f"Discovery database initialized at {db_path}")
    
    def _init_database(self):
        """Create database tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Discovered tokens table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS discovered_tokens (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    name TEXT,
                    contract_address TEXT,
                    network TEXT,
                    discovery_date TIMESTAMP,
                    source TEXT,
                    initial_price REAL,
                    market_cap REAL,
                    volume_24h REAL,
                    twitter_mentions INTEGER DEFAULT 0,
                    sentiment_score REAL,
                    ai_score REAL,
                    risk_score REAL,
                    final_score REAL,
                    status TEXT DEFAULT 'discovered',
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, contract_address, network)
                )
            ''')
            
            # Token analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS token_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_symbol TEXT NOT NULL,
                    analysis_date TIMESTAMP,
                    sentiment_analysis TEXT,
                    technical_analysis TEXT,
                    social_metrics TEXT,
                    risk_assessment TEXT,
                    ai_recommendation TEXT,
                    confidence_score REAL,
                    reasoning TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (token_symbol) REFERENCES discovered_tokens (symbol)
                )
            ''')
            
            # Token performance tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS token_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_symbol TEXT NOT NULL,
                    date TIMESTAMP,
                    price REAL,
                    volume_24h REAL,
                    market_cap REAL,
                    price_change_1d REAL,
                    price_change_7d REAL,
                    price_change_30d REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (token_symbol) REFERENCES discovered_tokens (symbol)
                )
            ''')
            
            # Twitter mentions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS twitter_mentions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token_symbol TEXT,
                    tweet_id TEXT UNIQUE,
                    tweet_text TEXT,
                    author_username TEXT,
                    author_followers INTEGER,
                    retweet_count INTEGER,
                    like_count INTEGER,
                    reply_count INTEGER,
                    tweet_date TIMESTAMP,
                    sentiment_score REAL,
                    keywords TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Discovery performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS discovery_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TIMESTAMP,
                    tokens_discovered INTEGER,
                    tokens_analyzed INTEGER,
                    tokens_approved INTEGER,
                    avg_ai_score REAL,
                    avg_sentiment_score REAL,
                    successful_predictions INTEGER,
                    total_predictions INTEGER,
                    accuracy_rate REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON discovered_tokens (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tokens_status ON discovered_tokens (status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tokens_discovery_date ON discovered_tokens (discovery_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_token ON token_analysis (token_symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_token ON token_performance (token_symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_date ON token_performance (date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mentions_token ON twitter_mentions (token_symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mentions_date ON twitter_mentions (tweet_date)')
            
            conn.commit()
            logging.info("Database tables created/verified successfully")
    
    def add_discovered_token(self, token: DiscoveredToken) -> bool:
        """Add a newly discovered token to the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Convert metadata to JSON string
                metadata_json = json.dumps(token.metadata) if token.metadata else None
                
                cursor.execute('''
                    INSERT OR REPLACE INTO discovered_tokens 
                    (symbol, name, contract_address, network, discovery_date, source,
                     initial_price, market_cap, volume_24h, twitter_mentions,
                     sentiment_score, ai_score, risk_score, final_score, status, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    token.symbol, token.name, token.contract_address, token.network,
                    token.discovery_date, token.source, token.initial_price,
                    token.market_cap, token.volume_24h, token.twitter_mentions,
                    token.sentiment_score, token.ai_score, token.risk_score,
                    token.final_score, token.status, metadata_json
                ))
                
                conn.commit()
                logging.info(f"Added discovered token: {token.symbol}")
                return True
                
        except Exception as e:
            logging.error(f"Error adding discovered token {token.symbol}: {e}")
            return False
    
    def get_discovered_tokens(self, status: Optional[str] = None, 
                            limit: Optional[int] = None,
                            days_back: Optional[int] = None) -> List[DiscoveredToken]:
        """Retrieve discovered tokens with optional filtering."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM discovered_tokens WHERE 1=1"
                params = []
                
                if status:
                    query += " AND status = ?"
                    params.append(status)
                
                if days_back:
                    cutoff_date = datetime.now() - timedelta(days=days_back)
                    query += " AND discovery_date >= ?"
                    params.append(cutoff_date)
                
                query += " ORDER BY discovery_date DESC"
                
                if limit:
                    query += " LIMIT ?"
                    params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                tokens = []
                for row in rows:
                    metadata = json.loads(row[16]) if row[16] else None
                    token = DiscoveredToken(
                        symbol=row[1], name=row[2], contract_address=row[3],
                        network=row[4], discovery_date=datetime.fromisoformat(row[5]),
                        source=row[6], initial_price=row[7], market_cap=row[8],
                        volume_24h=row[9], twitter_mentions=row[10],
                        sentiment_score=row[11], ai_score=row[12],
                        risk_score=row[13], final_score=row[14],
                        status=row[15], metadata=metadata
                    )
                    tokens.append(token)
                
                return tokens
                
        except Exception as e:
            logging.error(f"Error retrieving discovered tokens: {e}")
            return []
    
    def update_token_status(self, symbol: str, status: str) -> bool:
        """Update the status of a discovered token."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE discovered_tokens SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE symbol = ?",
                    (status, symbol)
                )
                conn.commit()
                logging.info(f"Updated token {symbol} status to {status}")
                return True
                
        except Exception as e:
            logging.error(f"Error updating token status: {e}")
            return False
    
    def add_token_analysis(self, analysis: TokenAnalysis) -> bool:
        """Add AI analysis results for a token."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO token_analysis 
                    (token_symbol, analysis_date, sentiment_analysis, technical_analysis,
                     social_metrics, risk_assessment, ai_recommendation, confidence_score, reasoning)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analysis.token_symbol, analysis.analysis_date,
                    json.dumps(analysis.sentiment_analysis),
                    json.dumps(analysis.technical_analysis),
                    json.dumps(analysis.social_metrics),
                    json.dumps(analysis.risk_assessment),
                    analysis.ai_recommendation, analysis.confidence_score,
                    analysis.reasoning
                ))
                
                conn.commit()
                logging.info(f"Added analysis for token: {analysis.token_symbol}")
                return True
                
        except Exception as e:
            logging.error(f"Error adding token analysis: {e}")
            return False
    
    def add_twitter_mention(self, token_symbol: str, tweet_data: Dict) -> bool:
        """Add a Twitter mention for a token."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO twitter_mentions 
                    (token_symbol, tweet_id, tweet_text, author_username, author_followers,
                     retweet_count, like_count, reply_count, tweet_date, sentiment_score, keywords)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    token_symbol, tweet_data.get('id'), tweet_data.get('text'),
                    tweet_data.get('author_username'), tweet_data.get('author_followers'),
                    tweet_data.get('retweet_count', 0), tweet_data.get('like_count', 0),
                    tweet_data.get('reply_count', 0), tweet_data.get('created_at'),
                    tweet_data.get('sentiment_score'), 
                    json.dumps(tweet_data.get('keywords', []))
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logging.error(f"Error adding Twitter mention: {e}")
            return False
    
    def get_token_performance_history(self, symbol: str, days: int = 30) -> List[TokenPerformance]:
        """Get performance history for a token."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                cursor.execute('''
                    SELECT * FROM token_performance 
                    WHERE token_symbol = ? AND date >= ?
                    ORDER BY date DESC
                ''', (symbol, cutoff_date))
                
                rows = cursor.fetchall()
                
                performance_data = []
                for row in rows:
                    perf = TokenPerformance(
                        token_symbol=row[1], date=datetime.fromisoformat(row[2]),
                        price=row[3], volume_24h=row[4], market_cap=row[5],
                        price_change_1d=row[6], price_change_7d=row[7],
                        price_change_30d=row[8]
                    )
                    performance_data.append(perf)
                
                return performance_data
                
        except Exception as e:
            logging.error(f"Error retrieving performance history: {e}")
            return []
    
    def close(self):
        """Close database connection."""
        # SQLite connections are automatically closed when using context managers
        pass
