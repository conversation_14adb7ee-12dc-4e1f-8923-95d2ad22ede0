#!/usr/bin/env python
"""
Test script to analyze the exact API data structure sent from backend to frontend.
This script:
1. Loads data from test_allocation.py (if available)
2. Shows how it's transformed for the API
3. Saves the data at each step for inspection
4. Provides a detailed analysis of the data structure

Usage:
    python test_api_data_structure.py [--run-strategy] [--output-dir OUTPUT_DIR]
"""

import argparse
import json
import logging
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('api_data_structure_test')

class ApiDataStructureTester:
    """Test the API data structure sent from backend to frontend."""

    def __init__(self, output_dir='api_data_test_output'):
        """Initialize the tester."""
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def run_test_allocation(self):
        """
        Run test_allocation.py to generate real data.

        Returns:
            Dict containing strategy results or None if failed
        """
        logger.info("Running test_allocation.py to generate real data...")

        try:
            # Import test_allocation.py
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from test_allocation import run_strategy_for_web

            # Run the strategy with default parameters
            strategy_results = run_strategy_for_web(
                use_mtpi_signal=True,
                mtpi_indicator_type='PGO',
                mtpi_timeframe='1d',
                timeframe='1d',
                analysis_start_date='2023-10-20',
                selected_assets=['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
                n_assets=1,
                use_cache=True
            )

            logger.info("Successfully ran test_allocation.py")
            return strategy_results

        except Exception as e:
            logger.error(f"Error running test_allocation.py: {e}")
            logger.info("Falling back to sample data...")
            return None

    def generate_sample_data(self):
        """
        Generate sample data similar to what test_allocation.py would produce.

        Returns:
            Dict containing strategy results
        """
        logger.info("Generating sample data...")

        # Create sample date range
        start_date = datetime(2023, 10, 20)
        end_date = datetime.now()
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')

        # Generate strategy equity curve
        np.random.seed(42)  # For reproducibility
        initial_value = 10000
        daily_returns = np.random.normal(0.001, 0.02, len(date_range))
        cumulative_returns = np.cumprod(1 + daily_returns)
        strategy_equity = pd.Series(initial_value * cumulative_returns, index=date_range)

        # Generate buy and hold curves for sample assets
        assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        buy_hold_curves = {}

        for i, asset in enumerate(assets):
            np.random.seed(42 + i)
            asset_returns = np.random.normal(0.0008, 0.025, len(date_range))
            asset_cumulative = np.cumprod(1 + asset_returns)
            buy_hold_curves[asset] = pd.Series(initial_value * asset_cumulative, index=date_range)

        # Generate best asset series
        best_asset_series = pd.Series(index=date_range, dtype=object)
        for i, date in enumerate(date_range):
            if i % 30 < 10:
                best_asset_series[date] = assets[0]
            elif i % 30 < 20:
                best_asset_series[date] = assets[1]
            else:
                best_asset_series[date] = assets[2]

        # Generate MTPI signals
        mtpi_signals = pd.Series(index=date_range, dtype=float)
        for i, date in enumerate(date_range):
            if i % 45 < 35:
                mtpi_signals[date] = 1.0
            else:
                mtpi_signals[date] = -1.0

        # Create performance metrics
        performance_metrics = {
            'Total Return': {
                'Strategy': 100 * (strategy_equity.iloc[-1] / strategy_equity.iloc[0] - 1),
                'BTC/USDT': 100 * (buy_hold_curves['BTC/USDT'].iloc[-1] / buy_hold_curves['BTC/USDT'].iloc[0] - 1),
                'ETH/USDT': 100 * (buy_hold_curves['ETH/USDT'].iloc[-1] / buy_hold_curves['ETH/USDT'].iloc[0] - 1),
                'SOL/USDT': 100 * (buy_hold_curves['SOL/USDT'].iloc[-1] / buy_hold_curves['SOL/USDT'].iloc[0] - 1),
            },
            'Sharpe Ratio': {
                'Strategy': 1.5,
                'BTC/USDT': 0.8,
                'ETH/USDT': 1.1,
                'SOL/USDT': 1.3,
            }
        }

        # Combine all data
        sample_data = {
            'strategy_equity': strategy_equity,
            'buy_hold_curves': buy_hold_curves,
            'best_asset_series': best_asset_series,
            'mtpi_signals': mtpi_signals,
            'performance_metrics': performance_metrics
        }

        return sample_data

    def format_for_api_response(self, backend_data):
        """
        Format the backend data as it would be in the API response.

        Args:
            backend_data: The data generated by the backend

        Returns:
            Dict formatted as an API response
        """
        logger.info("Formatting data for API response...")

        # Check if we have pandas Series or DataFrames
        if isinstance(backend_data.get('strategy_equity'), pd.Series):
            # Convert pandas Series to lists of dicts for JSON serialization
            strategy_equity = backend_data['strategy_equity']
            strategy_data = []

            for date, value in strategy_equity.items():
                strategy_data.append({
                    'time': int(date.timestamp()),
                    'value': float(value)
                })

            # Format buy and hold curves
            buy_hold_data = {}
            for asset, curve in backend_data['buy_hold_curves'].items():
                asset_data = []
                for date, value in curve.items():
                    asset_data.append({
                        'time': int(date.timestamp()),
                        'value': float(value)
                    })
                buy_hold_data[asset] = asset_data

            # Format best asset series
            best_asset_data = []
            for date, asset in backend_data['best_asset_series'].items():
                best_asset_data.append({
                    'time': int(date.timestamp()),
                    'asset': asset
                })

            # Format MTPI signals
            mtpi_data = []
            for date, signal in backend_data['mtpi_signals'].items():
                mtpi_data.append({
                    'time': int(date.timestamp()),
                    'signal': float(signal)
                })

            # Create API response structure
            api_response = {
                'curves': {
                    'strategy': strategy_data,
                },
                'buyHoldCurves': buy_hold_data,
                'assetChanges': best_asset_data,
                'metadata': {
                    'startDate': backend_data['strategy_equity'].index.min().strftime('%Y-%m-%d'),
                    'endDate': backend_data['strategy_equity'].index.max().strftime('%Y-%m-%d'),
                    'rawMtpiSignals': mtpi_data
                },
                'performanceMetrics': backend_data['performance_metrics']
            }
        else:
            # Assume the data is already in the format returned by run_strategy_for_web
            api_response = {
                'curves': {
                    'strategy': []
                },
                'buyHoldCurves': {},
                'assetChanges': [],
                'metadata': {
                    'startDate': '',
                    'endDate': '',
                    'rawMtpiSignals': []
                },
                'performanceMetrics': {}
            }

            # Extract strategy equity
            if 'strategy_equity' in backend_data:
                strategy_equity = backend_data['strategy_equity']
                if isinstance(strategy_equity, dict):
                    # Convert dict to list of dicts
                    for date_str, value in strategy_equity.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['curves']['strategy'].append({
                            'time': int(date.timestamp()),
                            'value': float(value)
                        })
                else:
                    logger.warning(f"Unexpected strategy_equity type: {type(strategy_equity)}")

            # Extract buy and hold curves
            if 'buy_hold_curves' in backend_data:
                for asset, curve in backend_data['buy_hold_curves'].items():
                    api_response['buyHoldCurves'][asset] = []
                    if isinstance(curve, dict):
                        for date_str, value in curve.items():
                            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            api_response['buyHoldCurves'][asset].append({
                                'time': int(date.timestamp()),
                                'value': float(value)
                            })

            # Extract best asset series
            if 'best_asset_series' in backend_data:
                best_asset_series = backend_data['best_asset_series']
                if isinstance(best_asset_series, dict):
                    for date_str, asset in best_asset_series.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['assetChanges'].append({
                            'time': int(date.timestamp()),
                            'asset': asset
                        })

            # Extract MTPI signals
            if 'mtpi_signals' in backend_data:
                mtpi_signals = backend_data['mtpi_signals']
                if isinstance(mtpi_signals, dict):
                    for date_str, signal in mtpi_signals.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['metadata']['rawMtpiSignals'].append({
                            'time': int(date.timestamp()),
                            'signal': float(signal)
                        })

            # Extract performance metrics
            if 'performance_metrics' in backend_data:
                api_response['performanceMetrics'] = backend_data['performance_metrics']

            # Extract dates
            if 'strategy_equity' in backend_data and isinstance(backend_data['strategy_equity'], dict):
                dates = list(backend_data['strategy_equity'].keys())
                if dates:
                    api_response['metadata']['startDate'] = min(dates).split('T')[0]
                    api_response['metadata']['endDate'] = max(dates).split('T')[0]

        # Save API response to JSON file
        api_file = os.path.join(self.output_dir, 'api_response.json')
        with open(api_file, 'w') as f:
            json.dump(api_response, f, indent=2)
        logger.info(f"Saved API response to {api_file}")

        return api_response

    def analyze_api_structure(self, api_response):
        """
        Analyze the API response structure and provide detailed information.

        Args:
            api_response: The API response data
        """
        logger.info("\n=== API RESPONSE STRUCTURE ANALYSIS ===")

        # Check top-level keys
        logger.info(f"Top-level keys: {list(api_response.keys())}")

        # Analyze curves data
        if 'curves' in api_response:
            logger.info(f"Curves keys: {list(api_response['curves'].keys())}")

            if 'strategy' in api_response['curves']:
                strategy_data = api_response['curves']['strategy']
                logger.info(f"Strategy data points: {len(strategy_data)}")

                if strategy_data:
                    logger.info(f"Strategy data point structure: {list(strategy_data[0].keys())}")
                    logger.info(f"First strategy point: {strategy_data[0]}")
                    logger.info(f"Last strategy point: {strategy_data[-1]}")

        # Analyze buy and hold curves
        if 'buyHoldCurves' in api_response:
            assets = list(api_response['buyHoldCurves'].keys())
            logger.info(f"Buy and hold assets: {assets}")

            for asset in assets:
                asset_data = api_response['buyHoldCurves'][asset]
                logger.info(f"{asset} data points: {len(asset_data)}")

                if asset_data:
                    logger.info(f"{asset} data point structure: {list(asset_data[0].keys())}")

        # Analyze asset changes
        if 'assetChanges' in api_response:
            asset_changes = api_response['assetChanges']
            logger.info(f"Asset changes data points: {len(asset_changes)}")

            if asset_changes:
                logger.info(f"Asset changes data point structure: {list(asset_changes[0].keys())}")

                # Count occurrences of each asset
                asset_counts = {}
                for change in asset_changes:
                    asset = change.get('asset', '')
                    asset_counts[asset] = asset_counts.get(asset, 0) + 1

                logger.info(f"Asset occurrences: {asset_counts}")

        # Analyze metadata
        if 'metadata' in api_response:
            logger.info(f"Metadata keys: {list(api_response['metadata'].keys())}")

            if 'rawMtpiSignals' in api_response['metadata']:
                mtpi_signals = api_response['metadata']['rawMtpiSignals']
                logger.info(f"MTPI signals data points: {len(mtpi_signals)}")

                if mtpi_signals:
                    logger.info(f"MTPI signals data point structure: {list(mtpi_signals[0].keys())}")

        # Analyze performance metrics
        if 'performanceMetrics' in api_response:
            metrics = api_response['performanceMetrics']
            logger.info(f"Performance metrics keys: {list(metrics.keys())}")

            for metric in metrics:
                logger.info(f"{metric} values: {metrics[metric]}")

        logger.info("=======================================\n")

    def run_test(self, run_strategy=False):
        """
        Run the complete API data structure test.

        Args:
            run_strategy: Whether to run test_allocation.py to generate real data

        Returns:
            Tuple of (backend_data, api_response)
        """
        # Step 1: Get data (either from test_allocation.py or sample data)
        backend_data = None
        if run_strategy:
            backend_data = self.run_test_allocation()

        if backend_data is None:
            backend_data = self.generate_sample_data()

        # Save backend data to JSON file
        backend_file = os.path.join(self.output_dir, 'backend_data.json')
        try:
            # Convert pandas Series to dictionaries for JSON serialization
            backend_data_json = {}
            for key, value in backend_data.items():
                if isinstance(value, pd.Series):
                    # Convert Series to dict with ISO format dates
                    backend_data_json[key] = {date.isoformat(): float(val) if isinstance(val, (int, float)) else str(val)
                                             for date, val in value.items()}
                elif isinstance(value, dict) and all(isinstance(v, pd.Series) for v in value.values()):
                    # Convert dict of Series to nested dict
                    backend_data_json[key] = {k: {date.isoformat(): float(val) if isinstance(val, (int, float)) else str(val)
                                                for date, val in v.items()}
                                             for k, v in value.items()}
                else:
                    backend_data_json[key] = value

            with open(backend_file, 'w') as f:
                json.dump(backend_data_json, f, indent=2)
            logger.info(f"Saved backend data to {backend_file}")
        except Exception as e:
            logger.error(f"Error saving backend data: {e}")

        # Step 2: Format for API response
        api_response = self.format_for_api_response(backend_data)

        # Step 3: Analyze API structure
        self.analyze_api_structure(api_response)

        return backend_data, api_response

def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(description='Test API data structure')
    parser.add_argument('--run-strategy', action='store_true',
                        help='Run test_allocation.py to generate real data')
    parser.add_argument('--output-dir', type=str, default='api_data_test_output',
                        help='Directory to save output files')
    args = parser.parse_args()

    tester = ApiDataStructureTester(output_dir=args.output_dir)
    backend_data, api_response = tester.run_test(run_strategy=args.run_strategy)

    logger.info("API data structure test completed successfully!")

    # Return data for potential further analysis
    return backend_data, api_response

if __name__ == '__main__':
    main()
