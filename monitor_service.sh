#!/bin/bash
# Monitor script for Asset Rotation Strategy Service
# This script checks if the service is running and restarts it if necessary
# Add to crontab: */10 * * * * /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/monitor_service.sh
#
# Note: This script monitors the background_service.py process

# Log file
LOG_FILE="/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/logs/monitor.log"
MAINTENANCE_FILE="/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.maintenance_mode"

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if maintenance mode is enabled
if [ -f "$MAINTENANCE_FILE" ]; then
    log_message "Maintenance mode enabled - skipping monitoring"
    exit 0
fi

# Check if the service is running
if ! systemctl is-active --quiet asset_rotation_service; then
    log_message "Service is not running! Attempting to restart..."

    # Try to restart the service
    systemctl restart asset_rotation_service

    # Check if restart was successful
    if systemctl is-active --quiet asset_rotation_service; then
        log_message "Service successfully restarted"

        # Send Telegram notification if configured
        if [ -f "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env" ]; then
            source "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env"
            if [ -n "$TELEGRAM_BOT_TOKEN" ] && [ -n "$TELEGRAM_CHAT_ID" ]; then
                MESSAGE="⚠️ Asset Rotation Service was down and has been automatically restarted."
                curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
                    -d chat_id="$TELEGRAM_CHAT_ID" \
                    -d text="$MESSAGE" > /dev/null
                log_message "Telegram notification sent"
            fi
        fi
    else
        log_message "Failed to restart service!"

        # Send critical alert via Telegram
        if [ -f "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env" ]; then
            source "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env"
            if [ -n "$TELEGRAM_BOT_TOKEN" ] && [ -n "$TELEGRAM_CHAT_ID" ]; then
                MESSAGE="🚨 CRITICAL: Asset Rotation Service failed to restart! Manual intervention required."
                curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
                    -d chat_id="$TELEGRAM_CHAT_ID" \
                    -d text="$MESSAGE" > /dev/null
                log_message "Critical Telegram alert sent"
            fi
        fi
    fi
else
    # Service is running, log a heartbeat message every hour
    if [ "$(date +%M)" = "00" ]; then
        log_message "Service is running normally (hourly check)"
    fi
fi
