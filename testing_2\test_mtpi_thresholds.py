import pandas as pd
import logging
from src.MTPI_signal_handler import fetch_historical_mtpi_signals

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def compare_mtpi_signals_with_different_thresholds():
    """
    Compare MTPI signals with different threshold values to identify discrepancies.
    """
    # Common parameters
    timeframe = '1d'
    length = 35
    analysis_start_date = '2023-10-20'

    # Test configurations
    configs = [
        {"name": "BTC PGO Visualizer", "upper": 1.1, "lower": -0.58},
        {"name": "MTPI Signal Handler Default", "upper": 1.35, "lower": -1.0},
        {"name": "Main Strategy", "upper": 1.1, "lower": -0.58}
    ]

    print("\n" + "="*80)
    print("COMPARING MTPI SIGNALS WITH DIFFERENT THRESHOLD VALUES")
    print("="*80)

    signals_by_config = {}

    # Fetch signals for each configuration
    for config in configs:
        print(f"\nFetching signals for {config['name']} (Upper={config['upper']}, Lower={config['lower']})...")

        signals = fetch_historical_mtpi_signals(
            timeframe=timeframe,
            length=length,
            upper_threshold=config['upper'],
            lower_threshold=config['lower'],
            limit=1000,
            skip_warmup=True
        )

        if signals is None or signals.empty:
            print(f"No signals found for {config['name']}")
            continue

        # Filter signals from the analysis start date
        signals = signals[signals.index >= pd.to_datetime(analysis_start_date).tz_localize('UTC')]

        if signals.empty:
            print(f"No signals found after {analysis_start_date} for {config['name']}")
            continue

        signals_by_config[config['name']] = signals

        # Print signal distribution
        signal_counts = signals.value_counts()
        print(f"\nSignal Distribution for {config['name']}:")
        for signal, count in signal_counts.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            percentage = (count / len(signals)) * 100
            print(f"  {signal_name} ({signal}): {count} occurrences ({percentage:.1f}%)")

    # Compare transitions between configurations
    print("\n" + "="*80)
    print("COMPARING SIGNAL TRANSITIONS")
    print("="*80)

    # Find all dates where any configuration has a signal change
    all_transition_dates = set()

    for name, signals in signals_by_config.items():
        # Find transitions
        transitions = (signals != signals.shift(1)).astype(int)
        transition_dates = signals.index[transitions == 1]
        all_transition_dates.update(transition_dates)

    # Sort transition dates
    all_transition_dates = sorted(all_transition_dates)

    # Create a comparison table
    print("\nSignal Comparison Table:")
    print(f"{'Date':<20} " + " ".join(f"{name:<25}" for name in signals_by_config.keys()))
    print("-"*20 + " " + "-"*25 * len(signals_by_config))

    # Print signals for each date
    for date in all_transition_dates:
        row = f"{date.strftime('%Y-%m-%d'):<20} "

        for name, signals in signals_by_config.items():
            if date in signals.index:
                signal = signals.loc[date]
                signal_str = "Bullish (1)" if signal == 1 else "Bearish (-1)" if signal == -1 else "Neutral (0)"
                row += f"{signal_str:<25} "
            else:
                row += f"{'N/A':<25} "

        print(row)

    # Find specific discrepancies around January 2024
    print("\n" + "="*80)
    print("DETAILED ANALYSIS OF JANUARY 2024 DISCREPANCY")
    print("="*80)

    # Define the period to analyze
    start_date = pd.to_datetime('2024-01-10').tz_localize('UTC')
    end_date = pd.to_datetime('2024-01-20').tz_localize('UTC')

    print(f"\nSignals from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}:")
    print(f"{'Date':<12} " + " ".join(f"{name:<25}" for name in signals_by_config.keys()))
    print("-"*12 + " " + "-"*25 * len(signals_by_config))

    # Get all dates in the range (already UTC timezone)
    all_dates = pd.date_range(start=start_date, end=end_date, freq='D', tz='UTC')

    for date in all_dates:
        row = f"{date.strftime('%Y-%m-%d'):<12} "

        for name, signals in signals_by_config.items():
            # Find the closest date on or before the current date
            valid_dates = signals.index[signals.index <= date]
            if not valid_dates.empty:
                closest_date = valid_dates[-1]
                signal = signals.loc[closest_date]
                signal_str = f"Bullish (1) [{closest_date.strftime('%m-%d')}]" if signal == 1 else \
                            f"Bearish (-1) [{closest_date.strftime('%m-%d')}]" if signal == -1 else \
                            f"Neutral (0) [{closest_date.strftime('%m-%d')}]"
                row += f"{signal_str:<25} "
            else:
                row += f"{'N/A':<25} "

        print(row)

if __name__ == "__main__":
    compare_mtpi_signals_with_different_thresholds()
