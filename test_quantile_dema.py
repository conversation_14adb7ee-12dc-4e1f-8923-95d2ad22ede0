#!/usr/bin/env python3
"""
Test script for the new Quantile DEMA Trend indicator integration.

This script tests:
1. The quantile_dema_score indicator implementation
2. Integration with the MTPI system
3. Configuration loading from YAML
4. Signal generation
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.indicators.quantile_dema_score import generate_quantile_dema_score_signal, calculate_quantile_dema_score
from src.MTPI_signal_handler import fetch_mtpi_signal_from_config, load_mtpi_multi_indicator_config
from src.data_fetcher import fetch_ohlcv_data

def test_quantile_dema_indicator():
    """Test the Quantile DEMA indicator directly."""
    print("=" * 60)
    print("TESTING QUANTILE DEMA TREND INDICATOR")
    print("=" * 60)
    
    try:
        # Fetch some test data
        print("Fetching BTC/USDT data for testing...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=100
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch test data")
            return False
            
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Fetched {len(btc_df)} candles")
        print(f"   Date range: {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Test the indicator with default parameters
        print("\nTesting Quantile DEMA indicator with default parameters...")
        signal = generate_quantile_dema_score_signal(btc_df)
        
        print(f"✅ Generated signals successfully")
        print(f"   Signal shape: {signal.shape}")
        print(f"   Latest signal: {signal.iloc[-1]}")
        print(f"   Signal distribution: {signal.value_counts().to_dict()}")
        
        # Test with custom parameters (matching config)
        print("\nTesting with configuration parameters...")
        signal_custom = generate_quantile_dema_score_signal(
            btc_df,
            dema_length=30,
            percentile_filter=10,
            atr_length=14,
            mult_up=1.2,
            mult_dn=1.2,
            dema_st_length=30,
            percentile_length=20,
            sd_length=30,
            src_col='close'
        )
        
        print(f"✅ Generated custom signals successfully")
        print(f"   Latest custom signal: {signal_custom.iloc[-1]}")
        
        # Test the full calculation function
        print("\nTesting full calculation function...")
        signal_full, supertrend, direction = calculate_quantile_dema_score(btc_df)
        
        print(f"✅ Full calculation successful")
        print(f"   Signal: {signal_full.iloc[-1]}")
        print(f"   SuperTrend: {supertrend.iloc[-1]:.2f}")
        print(f"   Direction: {direction.iloc[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Quantile DEMA indicator: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mtpi_integration():
    """Test the MTPI system integration."""
    print("\n" + "=" * 60)
    print("TESTING MTPI SYSTEM INTEGRATION")
    print("=" * 60)
    
    try:
        # Test configuration loading
        print("Testing configuration loading...")
        config = load_mtpi_multi_indicator_config()
        
        if 'quantile_dema_score' in config:
            print("✅ Quantile DEMA configuration found in config")
            quantile_config = config['quantile_dema_score']
            print(f"   Configuration: {quantile_config}")
        else:
            print("❌ Quantile DEMA configuration not found in config")
            return False
        
        # Test MTPI signal generation with the new indicator
        print("\nTesting MTPI signal generation with quantile_dema_score enabled...")
        
        # First test with just the new indicator
        signal = fetch_mtpi_signal_from_config(
            timeframe='1d',
            limit=100,
            skip_warmup=False
        )
        
        print(f"✅ MTPI signal generated: {signal}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing MTPI integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test the configuration system."""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION SYSTEM")
    print("=" * 60)
    
    try:
        # Load configuration
        config = load_mtpi_multi_indicator_config()
        
        # Check if quantile_dema_score is in enabled indicators
        enabled_indicators = config.get('enabled_indicators', [])
        print(f"Enabled indicators: {enabled_indicators}")
        
        if 'quantile_dema_score' in enabled_indicators:
            print("✅ quantile_dema_score is enabled in configuration")
        else:
            print("ℹ️  quantile_dema_score is not enabled (this is expected if commented out)")
        
        # Check configuration parameters
        if 'quantile_dema_score' in config:
            quantile_config = config['quantile_dema_score']
            print("✅ Quantile DEMA configuration parameters:")
            for key, value in quantile_config.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Quantile DEMA configuration not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Quantile DEMA Trend Indicator Tests")
    print("=" * 80)
    
    results = []
    
    # Test 1: Direct indicator testing
    results.append(test_quantile_dema_indicator())
    
    # Test 2: Configuration testing
    results.append(test_configuration())
    
    # Test 3: MTPI integration testing
    results.append(test_mtpi_integration())
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Quantile DEMA Trend indicator is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
