#!/usr/bin/env python3
"""
AAD Trend Score Indicator - MTPI Component #7

Based on TradingView PineScript implementation:
- Uses Average Absolute Deviation (AAD) as volatility measure
- Applies AAD-based moving average with upper/lower bands
- Parameters: Length=22, AAD Multiplier=1.2, Average Type=SMA

Signal Logic:
- Long (1): Source crossover above upper band (avg + aad * multiplier)
- Short (-1): Source crossunder below lower band (avg - aad * multiplier)
- Neutral (0): No signal change (maintains previous state)
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple
import pandas_ta as ta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_source_data(df: pd.DataFrame, src: str = 'close') -> pd.Series:
    """
    Get source data based on string specification.
    Matches PineScript calc_srcaad function.
    
    Args:
        df: DataFrame with OHLCV data
        src: Source type string
        
    Returns:
        Series with source data
    """
    try:
        if src == "open":
            return df['open']
        elif src == "high":
            return df['high']
        elif src == "low":
            return df['low']
        elif src == "close":
            return df['close']
        elif src == "oc2":
            return (df['open'] + df['close']) / 2
        elif src == "hl2":
            return (df['high'] + df['low']) / 2
        elif src == "occ3":
            return (df['open'] + df['close'] + df['close']) / 3
        elif src == "hlc3":
            return (df['high'] + df['low'] + df['close']) / 3
        elif src == "ohlc4":
            return (df['open'] + df['high'] + df['low'] + df['close']) / 4
        elif src == "hlcc4":
            return (df['high'] + df['low'] + df['close'] + df['close']) / 4
        else:
            logging.warning(f"Unknown source type '{src}', defaulting to 'close'")
            return df['close']
            
    except Exception as e:
        logging.error(f"Error getting source data for '{src}': {e}")
        return df['close']

def calculate_moving_average(src: pd.Series, length: int, avg_type: str = 'SMA') -> pd.Series:
    """
    Calculate moving average based on type.
    Matches PineScript average type switch statement.
    
    Args:
        src: Source data series
        length: Period length
        avg_type: Average type ('SMA', 'EMA', 'HMA', 'DEMA', 'TEMA', 'RMA', 'FRAMA')
        
    Returns:
        Moving average series
    """
    try:
        if avg_type == 'SMA':
            return src.rolling(window=length, min_periods=1).mean()
        elif avg_type == 'EMA':
            return src.ewm(span=length, adjust=False).mean()
        elif avg_type == 'HMA':
            # Hull Moving Average: HMA = WMA(2*WMA(n/2) - WMA(n), sqrt(n))
            half_length = max(1, length // 2)
            sqrt_length = max(1, int(np.sqrt(length)))
            
            wma_half = src.rolling(window=half_length).apply(
                lambda x: np.average(x, weights=np.arange(1, len(x) + 1)), raw=True
            )
            wma_full = src.rolling(window=length).apply(
                lambda x: np.average(x, weights=np.arange(1, len(x) + 1)), raw=True
            )
            
            hma_src = 2 * wma_half - wma_full
            hma = hma_src.rolling(window=sqrt_length).apply(
                lambda x: np.average(x, weights=np.arange(1, len(x) + 1)), raw=True
            )
            return hma
        elif avg_type == 'DEMA':
            # Double Exponential Moving Average
            ema1 = src.ewm(span=length, adjust=False).mean()
            ema2 = ema1.ewm(span=length, adjust=False).mean()
            return 2 * ema1 - ema2
        elif avg_type == 'TEMA':
            # Triple Exponential Moving Average
            ema1 = src.ewm(span=length, adjust=False).mean()
            ema2 = ema1.ewm(span=length, adjust=False).mean()
            ema3 = ema2.ewm(span=length, adjust=False).mean()
            return 3 * ema1 - 3 * ema2 + ema3
        elif avg_type == 'RMA':
            # Running Moving Average (same as EMA with alpha = 1/length)
            return src.ewm(alpha=1.0/length, adjust=False).mean()
        elif avg_type == 'FRAMA':
            # Fractal Adaptive Moving Average (simplified implementation)
            # For simplicity, using EMA as approximation
            logging.warning("FRAMA not fully implemented, using EMA approximation")
            return src.ewm(span=length, adjust=False).mean()
        else:
            logging.warning(f"Unknown average type '{avg_type}', defaulting to SMA")
            return src.rolling(window=length, min_periods=1).mean()
            
    except Exception as e:
        logging.error(f"Error calculating {avg_type} average: {e}")
        return src.rolling(window=length, min_periods=1).mean()

def calculate_aad(src: pd.Series, length: int, avg_type: str = 'SMA') -> pd.Series:
    """
    Calculate Average Absolute Deviation (AAD).
    Matches PineScript aad function.
    
    Args:
        src: Source data series
        length: Period length
        avg_type: Average type for both the base average and AAD calculation
        
    Returns:
        AAD series
    """
    try:
        # Calculate the base average
        avg = calculate_moving_average(src, length, avg_type)
        
        # Calculate absolute deviations from average
        abs_deviations = np.abs(src - avg)
        
        # Return the SMA of absolute deviations (as per PineScript)
        aad_value = abs_deviations.rolling(window=length, min_periods=1).mean()
        
        return aad_value
        
    except Exception as e:
        logging.error(f"Error calculating AAD: {e}")
        return pd.Series(index=src.index, dtype=float)

def calculate_aad_trend(src: pd.Series, 
                       length: int = 22, 
                       aad_mult: float = 1.2, 
                       avg_type: str = 'SMA') -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate AAD Trend indicator components.
    Matches PineScript AadTrend function.
    
    Args:
        src: Source data series
        length: Period length for AAD and average calculation
        aad_mult: AAD multiplier for band calculation
        avg_type: Average type
        
    Returns:
        Tuple of (direction, avg, avg_p, avg_m)
    """
    try:
        # Calculate AAD (volatility measure)
        aad_value = calculate_aad(src, length, avg_type)
        
        # Calculate the base moving average
        avg = calculate_moving_average(src, length, avg_type)
        
        # Calculate upper and lower bands
        avg_p = avg + (aad_value * aad_mult)  # Upper band
        avg_m = avg - (aad_value * aad_mult)  # Lower band
        
        # Initialize direction series
        direction = pd.Series(0, index=src.index, dtype=int)
        
        # Calculate crossovers for direction changes
        # crossover(src, avg_p): src crosses above avg_p
        # crossunder(src, avg_m): src crosses below avg_m
        
        src_prev = src.shift(1)
        avg_p_prev = avg_p.shift(1)
        avg_m_prev = avg_m.shift(1)
        
        # Crossover: previous was below, current is above
        crossover_up = (src_prev <= avg_p_prev) & (src > avg_p)
        # Crossunder: previous was above, current is below  
        crossunder_down = (src_prev >= avg_m_prev) & (src < avg_m)
        
        # Apply direction logic with state persistence
        current_direction = 0
        for i in range(len(src)):
            if crossover_up.iloc[i]:
                current_direction = 1
            elif crossunder_down.iloc[i]:
                current_direction = -1
            # Keep previous direction if no crossover
            
            direction.iloc[i] = current_direction
        
        return direction, avg, avg_p, avg_m
        
    except Exception as e:
        logging.error(f"Error calculating AAD trend: {e}")
        return (pd.Series(0, index=src.index, dtype=int),
                pd.Series(index=src.index, dtype=float),
                pd.Series(index=src.index, dtype=float),
                pd.Series(index=src.index, dtype=float))

def calculate_aad_score(df: pd.DataFrame,
                       src_col: str = 'close',
                       length: int = 22,
                       aad_mult: float = 1.2,
                       avg_type: str = 'SMA') -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate AAD Score based on the provided PineScript implementation.

    Args:
        df: DataFrame with OHLCV data
        src_col: Source column name
        length: AAD and average calculation length
        aad_mult: AAD multiplier for band calculation
        avg_type: Average type ('SMA', 'EMA', 'HMA', 'DEMA', 'TEMA', 'RMA', 'FRAMA')

    Returns:
        Tuple of (signal, avg, avg_p, avg_m)
    """
    try:
        # Get source data
        src = get_source_data(df, src_col)

        # Calculate AAD trend components
        direction, avg, avg_p, avg_m = calculate_aad_trend(src, length, aad_mult, avg_type)

        # Convert direction to signal format for MTPI
        # Direction: 1 = Long, -1 = Short, 0 = Neutral
        signal = direction.copy()

        logging.info(f"Calculated AAD Score with {avg_type} average, length={length}, multiplier={aad_mult}")

        return signal, avg, avg_p, avg_m

    except Exception as e:
        logging.error(f"Error calculating AAD score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float))

def generate_aad_score_signal(df: pd.DataFrame,
                             src_col: str = 'close',
                             length: int = 22,
                             aad_mult: float = 1.2,
                             avg_type: str = 'SMA') -> pd.Series:
    """
    Generate AAD Score binary signal for MTPI integration.

    Args:
        df: DataFrame with OHLCV data
        src_col: Source column name
        length: AAD and average calculation length
        aad_mult: AAD multiplier for band calculation
        avg_type: Average type

    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        signal, _, _, _ = calculate_aad_score(
            df=df,
            src_col=src_col,
            length=length,
            aad_mult=aad_mult,
            avg_type=avg_type
        )

        logging.info(f"Generated AAD Score signals using {avg_type} method")
        return signal

    except Exception as e:
        logging.error(f"Error generating AAD Score signals: {e}")
        return pd.Series(0, index=df.index, dtype=int)

# Default configuration for MTPI integration
DEFAULT_AAD_SCORE_CONFIG = {
    'src_col': 'close',
    'length': 22,
    'aad_mult': 1.2,
    'avg_type': 'SMA'
}

# Export the main function for MTPI integration
__all__ = ['generate_aad_score_signal', 'calculate_aad_score', 'DEFAULT_AAD_SCORE_CONFIG']
