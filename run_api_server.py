#!/usr/bin/env python
"""
Script to run the API server for the Asset Rotation Dashboard frontend.
"""

import logging
import argparse
from src.api_server import run_api_server

if __name__ == '__main__':
    # Create argument parser
    parser = argparse.ArgumentParser(description='Run API server for Asset Rotation Dashboard')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                       help='Host IP address to bind the server to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5001,
                       help='Port number to run the server on (default: 5001)')
    parser.add_argument('--debug', action='store_true',
                       help='Run server in debug mode')
    parser.add_argument('--analysis-start-date', type=str, default='2023-10-20',
                       help='Start date for the analysis in YYYY-MM-DD format (default: 2023-10-20)')
    parser.add_argument('--save-png', action='store_true',
                       help='Save output visualizations as PNG files in the Backtest_results folder')
    parser.add_argument('--calculate-indicators', action='store_true',
                       help='Calculate indicators during startup (default: False)')

    args = parser.parse_args()

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the API server
    print(f"Starting API server on http://{args.host}:{args.port}")
    print(f"Analysis start date: {args.analysis_start_date}")
    print(f"Calculate indicators during startup: {args.calculate_indicators}")
    run_api_server(
        host=args.host,
        port=args.port,
        debug=args.debug,
        analysis_start_date=args.analysis_start_date,
        save_png=args.save_png,
        skip_initial_indicators=not args.calculate_indicators
    )
