import React, { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import {
  Box,
  Grid,
  GridItem,
  Flex,
  Text,
  Select,
  Button,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react'
import { RepeatIcon } from '@chakra-ui/icons'

// Import components
import Chart from '../components/Chart'
import AssetTable from '../components/AssetTable'
import PerformanceMetrics from '../components/PerformanceMetrics'
import EquityChart from '../components/EquityChart'

// Import API services
import {
  fetchAssets,
  fetchAssetData,
  fetchScores,
  fetchPerformanceMetrics,
  fetchEquityCurves,
} from '../services/api'

const Dashboard = () => {
  const [selectedAsset, setSelectedAsset] = useState('')
  const [timeframe, setTimeframe] = useState('1d')
  const toast = useToast()

  // Fetch assets
  const {
    data: assets,
    isLoading: assetsLoading,
    error: assetsError,
    refetch: refetchAssets,
  } = useQuery('assets', fetchAssets, {
    onSuccess: (data) => {
      if (data.length > 0 && !selectedAsset) {
        setSelectedAsset(data[0].symbol)
      }
    },
  })

  // Fetch asset data for the selected asset
  const {
    data: assetData,
    isLoading: assetDataLoading,
    error: assetDataError,
    refetch: refetchAssetData,
  } = useQuery(
    ['assetData', selectedAsset, timeframe],
    () => fetchAssetData(selectedAsset, timeframe),
    {
      enabled: !!selectedAsset,
    }
  )

  // Fetch scores
  const {
    data: scores,
    isLoading: scoresLoading,
    error: scoresError,
    refetch: refetchScores,
  } = useQuery('scores', fetchScores)

  // Fetch performance metrics
  const {
    data: performanceMetrics,
    isLoading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics,
  } = useQuery('performanceMetrics', fetchPerformanceMetrics)

  // Fetch equity curves
  const {
    data: equityCurves,
    isLoading: equityLoading,
    error: equityError,
    refetch: refetchEquity,
  } = useQuery('equityCurves', fetchEquityCurves)

  // Handle refresh button click
  const handleRefresh = () => {
    refetchAssets()
    refetchAssetData()
    refetchScores()
    refetchMetrics()
    refetchEquity()

    toast({
      title: 'Refreshing data',
      description: 'Fetching the latest data from the server',
      status: 'info',
      duration: 3000,
      isClosable: true,
    })
  }

  // Determine the best asset based on scores
  // The scores object is now a simple flat structure: { "BTC/USDT": 3, "ETH/USDT": 0, ... }
  const bestAsset = scores
    ? Object.entries(scores).reduce(
        (best, [symbol, score]) => (score > best.score ? { symbol, score } : best),
        { symbol: '', score: -Infinity }
      ).symbol
    : null

  // Show error if any of the queries failed
  if (assetsError || assetDataError || scoresError || metricsError || equityError) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        <AlertTitle>Error loading data!</AlertTitle>
        <AlertDescription>
          {assetsError?.message ||
            assetDataError?.message ||
            scoresError?.message ||
            metricsError?.message ||
            equityError?.message ||
            'An unknown error occurred'}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header with controls */}
      <Flex
        justify="space-between"
        align="center"
        mb={4}
        p={4}
        bg="white"
        borderRadius="lg"
        boxShadow="sm"
      >
        <Text fontSize="xl" fontWeight="bold">
          Asset Rotation Dashboard
        </Text>

        <Flex align="center">
          <Select
            value={selectedAsset}
            onChange={(e) => setSelectedAsset(e.target.value)}
            placeholder="Select Asset"
            w="150px"
            mr={4}
            isDisabled={assetsLoading}
          >
            {assets?.map((asset) => (
              <option key={asset.symbol} value={asset.symbol}>
                {asset.symbol}
              </option>
            ))}
          </Select>

          <Select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            w="100px"
            mr={4}
          >
            <option value="1h">1H</option>
            <option value="4h">4H</option>
            <option value="1d">1D</option>
            <option value="1w">1W</option>
          </Select>

          <Button
            leftIcon={<RepeatIcon />}
            onClick={handleRefresh}
            colorScheme="blue"
            isLoading={
              assetsLoading ||
              assetDataLoading ||
              scoresLoading ||
              metricsLoading ||
              equityLoading
            }
          >
            Refresh
          </Button>
        </Flex>
      </Flex>

      {/* Main content */}
      <Grid
        templateColumns={{ base: 'repeat(1, 1fr)', lg: 'repeat(3, 1fr)' }}
        gap={4}
        mb={4}
      >
        {/* Asset Chart */}
        <GridItem colSpan={{ base: 1, lg: 2 }}>
          <Box position="relative" minH="400px">
            {assetDataLoading ? (
              <Flex
                justify="center"
                align="center"
                h="400px"
                bg="white"
                borderRadius="lg"
              >
                <Spinner size="xl" color="blue.500" />
              </Flex>
            ) : (
              assetData && (
                <Chart
                  data={assetData}
                  asset={selectedAsset}
                  height={400}
                />
              )
            )}
          </Box>
        </GridItem>

        {/* Asset Table */}
        <GridItem colSpan={1}>
          {assetsLoading || scoresLoading ? (
            <Flex
              justify="center"
              align="center"
              h="400px"
              bg="white"
              borderRadius="lg"
            >
              <Spinner size="xl" color="blue.500" />
            </Flex>
          ) : (
            assets &&
            scores && (
              <AssetTable
                assets={assets}
                scores={scores}
                bestAsset={bestAsset}
              />
            )
          )}
        </GridItem>
      </Grid>

      {/* Performance Metrics and Equity Chart */}
      <Tabs variant="enclosed" colorScheme="blue" mb={4}>
        <TabList>
          <Tab>Performance Metrics</Tab>
          <Tab>Equity Curves</Tab>
        </TabList>

        <TabPanels>
          <TabPanel p={0} pt={4}>
            {metricsLoading ? (
              <Flex
                justify="center"
                align="center"
                h="300px"
                bg="white"
                borderRadius="lg"
              >
                <Spinner size="xl" color="blue.500" />
              </Flex>
            ) : (
              performanceMetrics && (
                <PerformanceMetrics metrics={performanceMetrics} />
              )
            )}
          </TabPanel>

          <TabPanel p={0} pt={4}>
            {equityLoading ? (
              <Flex
                justify="center"
                align="center"
                h="400px"
                bg="white"
                borderRadius="lg"
              >
                <Spinner size="xl" color="blue.500" />
              </Flex>
            ) : (
              equityCurves && (
                <EquityChart
                  strategyData={equityCurves.curves?.strategy || equityCurves.strategy || []}
                  buyHoldData={equityCurves.buyHold || []}
                  buyHoldCurves={equityCurves.curves || {}}
                  height={400}
                />
              )
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  )
}

export default Dashboard
