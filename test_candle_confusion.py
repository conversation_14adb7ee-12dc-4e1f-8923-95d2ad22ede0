#!/usr/bin/env python3
"""
Test script to demonstrate the candle confusion between incomplete and last complete candle.
"""

import logging
import pandas as pd
from datetime import datetime, timezone, timedelta
from src.data_fetcher import fetch_ohlcv_data
from src.incremental_fetcher import fetch_real_time_data

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_candle_confusion():
    """Test the difference between regular fetch and real-time fetch."""
    
    print("\n" + "="*80)
    print("TESTING CANDLE CONFUSION: INCOMPLETE VS LAST COMPLETE CANDLE")
    print("="*80)
    
    # Test parameters
    exchange_id = 'binance'
    symbol = 'AAVE/USDT'
    timeframe = '1d'
    
    print(f"\nTesting with {symbol} on {exchange_id} (timeframe: {timeframe})")
    print(f"Current time: {datetime.now(timezone.utc)}")
    
    # Test 1: Regular fetch_ohlcv_data (used by visualizer)
    print("\n" + "-"*60)
    print("TEST 1: Regular fetch_ohlcv_data (VISUALIZER APPROACH)")
    print("-"*60)
    
    try:
        regular_data = fetch_ohlcv_data(
            exchange_id=exchange_id,
            symbols=[symbol],
            timeframe=timeframe,
            limit=5,  # Just get last 5 candles
            force_refresh=True
        )
        
        if symbol in regular_data and not regular_data[symbol].empty:
            df_regular = regular_data[symbol]
            print(f"Regular fetch returned {len(df_regular)} candles")
            print(f"Date range: {df_regular.index.min().date()} to {df_regular.index.max().date()}")
            print(f"Last 3 candles:")
            for i, (timestamp, row) in enumerate(df_regular.tail(3).iterrows()):
                print(f"  {i+1}. {timestamp.date()}: Close = {row['close']:.2f}")
        else:
            print("No data returned from regular fetch")
            
    except Exception as e:
        print(f"Error in regular fetch: {e}")
    
    # Test 2: Real-time fetch_real_time_data (used by background service)
    print("\n" + "-"*60)
    print("TEST 2: Real-time fetch_real_time_data (BACKGROUND SERVICE APPROACH)")
    print("-"*60)

    try:
        realtime_data = fetch_real_time_data(
            exchange_id=exchange_id,
            symbols=[symbol],
            timeframe=timeframe,
            force_refresh=False  # Use incremental mode like background service
        )
        
        if symbol in realtime_data and not realtime_data[symbol].empty:
            df_realtime = realtime_data[symbol]
            print(f"Real-time fetch returned {len(df_realtime)} candles")
            print(f"Date range: {df_realtime.index.min().date()} to {df_realtime.index.max().date()}")
            print(f"Last 3 candles:")
            for i, (timestamp, row) in enumerate(df_realtime.tail(3).iterrows()):
                print(f"  {i+1}. {timestamp.date()}: Close = {row['close']:.2f}")
        else:
            print("No data returned from real-time fetch")
            
    except Exception as e:
        print(f"Error in real-time fetch: {e}")
    
    # Test 3: Real-time fetch in incremental mode after cache exists
    print("\n" + "-"*60)
    print("TEST 3: Real-time fetch_real_time_data (INCREMENTAL MODE)")
    print("-"*60)

    try:
        # Now test incremental mode - this should show the issue
        incremental_data = fetch_real_time_data(
            exchange_id=exchange_id,
            symbols=[symbol],
            timeframe=timeframe,
            force_refresh=False  # Incremental mode
        )

        if symbol in incremental_data and not incremental_data[symbol].empty:
            df_incremental = incremental_data[symbol]
            print(f"Incremental fetch returned {len(df_incremental)} candles")
            print(f"Date range: {df_incremental.index.min().date()} to {df_incremental.index.max().date()}")
            print(f"Last 3 candles:")
            for i, (timestamp, row) in enumerate(df_incremental.tail(3).iterrows()):
                print(f"  {i+1}. {timestamp.date()}: Close = {row['close']:.2f}")
        else:
            print("No data returned from incremental fetch")

    except Exception as e:
        print(f"Error in incremental fetch: {e}")

    # Test 4: Compare the results
    print("\n" + "-"*60)
    print("TEST 4: COMPARISON")
    print("-"*60)
    
    try:
        if (symbol in regular_data and not regular_data[symbol].empty and
            symbol in realtime_data and not realtime_data[symbol].empty and
            symbol in incremental_data and not incremental_data[symbol].empty):

            df_regular = regular_data[symbol]
            df_realtime = realtime_data[symbol]
            df_incremental = incremental_data[symbol]

            regular_last = df_regular.index.max()
            realtime_last = df_realtime.index.max()
            incremental_last = df_incremental.index.max()

            print(f"Regular fetch last candle: {regular_last.date()}")
            print(f"Real-time fetch (force_refresh) last candle: {realtime_last.date()}")
            print(f"Real-time fetch (incremental) last candle: {incremental_last.date()}")

            # Show what the real-time logic expects
            current_time = datetime.now()
            expected_last_timestamp = current_time.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)
            expected_last_timestamp = pd.to_datetime(expected_last_timestamp, utc=True)
            print(f"Real-time logic expects data up to: {expected_last_timestamp.date()}")

            if regular_last != incremental_last:
                print("❌ MISMATCH DETECTED between regular and incremental!")
                print(f"   Difference: {(regular_last - incremental_last).days} days")
                print(f"   Regular fetch has data up to: {regular_last.date()}")
                print(f"   Incremental fetch has data up to: {incremental_last.date()}")
            else:
                print("✅ Regular and incremental approaches return the same last candle date")

    except Exception as e:
        print(f"Error in comparison: {e}")
    
    print("\n" + "="*80)
    print("TEST COMPLETE")
    print("="*80)

if __name__ == "__main__":
    test_candle_confusion()
