# debug_mtpi_signal.py
# Script to debug MTPI signal values over time

import pandas as pd
import logging
import argparse
from datetime import datetime, timedelta
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from src.data_fetcher import fetch_ohlcv_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_mtpi_signal(timeframe='1d', length=35, upper_threshold=1.1, lower_threshold=-0.58, limit=200, since=None):
    """
    Fetches BTC data and prints out the MTPI signal values over time.
    
    Args:
        timeframe: The timeframe for the candles (e.g., '1m', '1h', '1d')
        length: The period length for PGO calculation
        upper_threshold: The threshold for long signals
        lower_threshold: The threshold for short signals
        limit: The maximum number of candles to fetch
        since: The start date for fetching data (default: None)
    """
    try:
        # Calculate warm-up period in terms of candles
        warmup_period = length * 2
        
        # If since is provided and we're using a smaller timeframe, adjust to account for warmup period
        if since and (timeframe.endswith('h') or timeframe.endswith('m')):
            # Parse the numeric part of the timeframe (e.g., '4h' -> 4)
            if timeframe.endswith('h'):
                # For hour timeframes
                hours_per_candle = int(timeframe[:-1])
                # Calculate how many days worth of candles we need for warmup
                days_for_warmup = (warmup_period * hours_per_candle) / 24
                # Add a buffer of 1 day to be safe
                days_for_warmup = int(days_for_warmup) + 1
            elif timeframe.endswith('m'):
                # For minute timeframes
                minutes_per_candle = int(timeframe[:-1])
                # Calculate how many days worth of candles we need for warmup
                days_for_warmup = (warmup_period * minutes_per_candle) / (24 * 60)
                # Add a buffer of 1 day to be safe
                days_for_warmup = int(days_for_warmup) + 1
            
            # Create a new since date that's earlier to account for warmup
            since_date = datetime.strptime(since, '%Y-%m-%d')
            adjusted_since_date = since_date - timedelta(days=days_for_warmup)
            adjusted_since = adjusted_since_date.strftime('%Y-%m-%d')
            
            logging.info(f"Adjusted start date from {since} to {adjusted_since} to account for {warmup_period} candle warmup period")
            since = adjusted_since
            
            # Increase limit to ensure we get enough data
            limit = limit + warmup_period
            
        # Fetch BTC data
        logging.info(f"Fetching BTC data for MTPI signal calculation (Timeframe: {timeframe}, Since: {since if since else 'not specified'})")
        
        fetch_params = {
            'exchange_id': 'binance',
            'symbols': ['BTC/USDT'],
            'timeframe': timeframe,
            'limit': limit
        }
        
        if since:
            fetch_params['since'] = since
            
        data_dict = fetch_ohlcv_data(**fetch_params)

        if not data_dict or 'BTC/USDT' not in data_dict:
            logging.error("Failed to fetch BTC data. Cannot calculate MTPI signal.")
            return

        # Get the BTC DataFrame
        btc_df = data_dict['BTC/USDT']
        
        # Calculate raw PGO values
        pgo_raw = calculate_pgo(
            df=btc_df,
            length=length
        )
        
        # Calculate PGO signal with skip_warmup=False to ensure signals from the beginning
        pgo_signal = generate_pgo_signal(
            df=btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False
        )
        
        # Create a DataFrame with date, PGO value, and signal
        result_df = pd.DataFrame({
            'date': btc_df.index,
            'close': btc_df['close'],
            'pgo_value': pgo_raw,
            'mtpi_signal': pgo_signal
        })
        
        # Print the last N days of data
        last_n_days = 30  # Adjust as needed
        print(f"\nLast {last_n_days} rows of MTPI signal data for {timeframe}:")
        print(result_df.tail(last_n_days).to_string())
        
        # Count signal distribution
        signal_counts = result_df['mtpi_signal'].value_counts()
        print(f"\nSignal distribution for {timeframe}:")
        print(f"Bullish (1): {signal_counts.get(1, 0)}")
        print(f"Bearish (-1): {signal_counts.get(-1, 0)}")
        print(f"Neutral (0): {signal_counts.get(0, 0)}")
        
        # Print current thresholds
        print(f"\nCurrent thresholds for {timeframe}: upper={upper_threshold}, lower={lower_threshold}")
        
        # Calculate and print percentiles to help adjust thresholds
        percentiles = [10, 25, 50, 75, 90]
        pgo_percentiles = pgo_raw.dropna().quantile(q=[p/100 for p in percentiles])
        print(f"\nPGO value percentiles for {timeframe} (to help adjust thresholds):")
        for i, p in enumerate(percentiles):
            print(f"{p}th percentile: {pgo_percentiles.iloc[i]:.4f}")
            
        return result_df

    except Exception as e:
        logging.error(f"Error debugging MTPI signal: {e}")
        return None

def compare_timeframes(timeframes=['1d', '4h', '1h', '15m'], since='2023-10-20'):
    """
    Compares MTPI signals across different timeframes.
    
    Args:
        timeframes: List of timeframes to compare
        since: The start date for data fetching
    """
    print("\n===== Comparing MTPI Signals Across Timeframes =====")
    
    # Store latest signals for each timeframe
    latest_signals = {}
    
    for tf in timeframes:
        df = debug_mtpi_signal(timeframe=tf, limit=100, since=since)
        if df is not None:
            latest_signals[tf] = df['mtpi_signal'].iloc[-1]
    
    # Print comparison table
    print("\n----- Latest MTPI Signals by Timeframe -----")
    comparison_df = pd.DataFrame({
        'Timeframe': list(latest_signals.keys()),
        'Latest Signal': list(latest_signals.values())
    })
    print(comparison_df.to_string(index=False))
    
    # Signal agreement analysis
    if len(latest_signals) > 1:
        agreements = sum(1 for sig in latest_signals.values() if sig == list(latest_signals.values())[0])
        agreement_pct = (agreements / len(latest_signals)) * 100
        
        print(f"\nSignal Agreement: {agreement_pct:.1f}% of timeframes show the same signal")
        
        # Count timeframes by signal type
        sig_counts = {
            'Bullish (1)': sum(1 for sig in latest_signals.values() if sig == 1),
            'Bearish (-1)': sum(1 for sig in latest_signals.values() if sig == -1),
            'Neutral (0)': sum(1 for sig in latest_signals.values() if sig == 0)
        }
        
        print("\nTimeframes by Signal Type:")
        for sig_type, count in sig_counts.items():
            print(f"{sig_type}: {count} timeframes ({(count/len(latest_signals))*100:.1f}%)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Debug MTPI signals with different timeframes')
    parser.add_argument('--timeframe', default='1d', help='Single timeframe to debug')
    parser.add_argument('--compare', action='store_true', help='Compare multiple timeframes')
    parser.add_argument('--since', default='2023-10-20', help='Start date for analysis (YYYY-MM-DD)')
    args = parser.parse_args()
    
    if args.compare:
        compare_timeframes(since=args.since)
    else:
        debug_mtpi_signal(timeframe=args.timeframe, since=args.since)
