#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Verify PGO Implementation Against TradingView

This script verifies that our PGO calculation exactly matches TradingView's implementation:
pgo = (PGOsrc - ta.sma(PGOsrc, PGOlength)) / ta.atr(PGOlength)
"""

import pandas as pd
import numpy as np
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.indicators.base_indicators import calculate_sma, calculate_atr

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_tradingview_atr(df: pd.DataFrame, length: int = 14) -> pd.Series:
    """
    Calculate ATR exactly as TradingView does.
    TradingView ATR: RMA of True Range
    True Range = max(high-low, abs(high-close[1]), abs(low-close[1]))
    """
    high = df['high']
    low = df['low']
    close = df['close']
    
    # Calculate True Range
    prev_close = close.shift(1)
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # Calculate RMA (Wilder's smoothing) - this is what TradingView uses
    # RMA formula: alpha = 1/length, rma[i] = alpha * value[i] + (1-alpha) * rma[i-1]
    alpha = 1.0 / length
    atr = pd.Series(index=df.index, dtype=float)
    
    # Initialize first value
    atr.iloc[0] = true_range.iloc[0]
    
    # Calculate RMA
    for i in range(1, len(true_range)):
        if pd.notna(true_range.iloc[i]) and pd.notna(atr.iloc[i-1]):
            atr.iloc[i] = alpha * true_range.iloc[i] + (1 - alpha) * atr.iloc[i-1]
        else:
            atr.iloc[i] = np.nan
    
    return atr

def calculate_tradingview_sma(series: pd.Series, length: int) -> pd.Series:
    """
    Calculate SMA exactly as TradingView does.
    TradingView SMA is a simple rolling mean.
    """
    return series.rolling(window=length, min_periods=length).mean()

def verify_pgo_calculation():
    """Verify PGO calculation against TradingView implementation."""
    
    print("PGO Implementation Verification")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    
    # Fetch AAVE and TRX data
    data_start_date = '2025-02-10'
    assets = ['AAVE/USDT', 'TRX/USDT']
    
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=assets,
        timeframe='1d',
        since=data_start_date,
        use_cache=True
    )
    
    if not data_dict or 'AAVE/USDT' not in data_dict or 'TRX/USDT' not in data_dict:
        print("ERROR: Failed to fetch data")
        return
    
    # Create AAVE/TRX ratio data
    aave_price = data_dict['AAVE/USDT']['close']
    trx_price = data_dict['TRX/USDT']['close']
    ratio = aave_price / trx_price
    
    # Create a DataFrame for the ratio (simulating OHLCV)
    ratio_df = pd.DataFrame({
        'open': ratio,
        'high': ratio,  # Simplified
        'low': ratio,   # Simplified
        'close': ratio,
        'volume': pd.Series([1000] * len(ratio), index=ratio.index)
    })
    
    # Parameters from TradingView (using your default values)
    pgo_length = 35  # You mentioned this in config
    
    print(f"Using PGO Length: {pgo_length}")
    print(f"Data period: {ratio.index[0]} to {ratio.index[-1]}")
    print(f"Total data points: {len(ratio)}")
    
    # Method 1: Our current implementation
    print(f"\n" + "=" * 60)
    print("METHOD 1: Current Implementation")
    print("=" * 60)
    
    our_sma = calculate_sma(ratio_df, length=pgo_length, close_col='close')
    our_atr = calculate_atr(ratio_df, length=pgo_length)
    our_pgo = (ratio - our_sma) / our_atr
    
    # Method 2: TradingView-exact implementation
    print(f"\n" + "=" * 60)
    print("METHOD 2: TradingView-Exact Implementation")
    print("=" * 60)
    
    tv_sma = calculate_tradingview_sma(ratio, pgo_length)
    tv_atr = calculate_tradingview_atr(ratio_df, pgo_length)
    tv_pgo = (ratio - tv_sma) / tv_atr
    
    # Compare the last 10 values
    print(f"\nLast 10 values comparison:")
    print("=" * 80)
    print(f"{'Date':<12} {'Our SMA':<12} {'TV SMA':<12} {'Our ATR':<12} {'TV ATR':<12} {'Our PGO':<12} {'TV PGO':<12}")
    print("-" * 80)
    
    for i in range(-10, 0):
        if i < -len(ratio):
            continue
            
        date = ratio.index[i]
        our_sma_val = our_sma.iloc[i] if not pd.isna(our_sma.iloc[i]) else 0
        tv_sma_val = tv_sma.iloc[i] if not pd.isna(tv_sma.iloc[i]) else 0
        our_atr_val = our_atr.iloc[i] if not pd.isna(our_atr.iloc[i]) else 0
        tv_atr_val = tv_atr.iloc[i] if not pd.isna(tv_atr.iloc[i]) else 0
        our_pgo_val = our_pgo.iloc[i] if not pd.isna(our_pgo.iloc[i]) else 0
        tv_pgo_val = tv_pgo.iloc[i] if not pd.isna(tv_pgo.iloc[i]) else 0
        
        print(f"{date.strftime('%Y-%m-%d'):<12} {our_sma_val:<12.6f} {tv_sma_val:<12.6f} "
              f"{our_atr_val:<12.6f} {tv_atr_val:<12.6f} {our_pgo_val:<12.6f} {tv_pgo_val:<12.6f}")
    
    # Focus on the latest value
    latest_date = ratio.index[-1]
    latest_ratio = ratio.iloc[-1]
    
    print(f"\n" + "=" * 80)
    print("LATEST VALUE DETAILED COMPARISON")
    print("=" * 80)
    print(f"Date: {latest_date.strftime('%Y-%m-%d')}")
    print(f"AAVE/TRX Ratio: {latest_ratio:.6f}")
    
    print(f"\nSMA Comparison:")
    our_sma_latest = our_sma.iloc[-1]
    tv_sma_latest = tv_sma.iloc[-1]
    sma_diff = abs(our_sma_latest - tv_sma_latest)
    print(f"  Our SMA: {our_sma_latest:.6f}")
    print(f"  TV SMA:  {tv_sma_latest:.6f}")
    print(f"  Difference: {sma_diff:.8f}")
    
    print(f"\nATR Comparison:")
    our_atr_latest = our_atr.iloc[-1]
    tv_atr_latest = tv_atr.iloc[-1]
    atr_diff = abs(our_atr_latest - tv_atr_latest)
    print(f"  Our ATR: {our_atr_latest:.6f}")
    print(f"  TV ATR:  {tv_atr_latest:.6f}")
    print(f"  Difference: {atr_diff:.8f}")
    
    print(f"\nPGO Comparison:")
    our_pgo_latest = our_pgo.iloc[-1]
    tv_pgo_latest = tv_pgo.iloc[-1]
    pgo_diff = abs(our_pgo_latest - tv_pgo_latest)
    print(f"  Our PGO: {our_pgo_latest:.6f}")
    print(f"  TV PGO:  {tv_pgo_latest:.6f}")
    print(f"  Difference: {pgo_diff:.8f}")
    
    # Expected TradingView value
    expected_tv_pgo = -0.52
    print(f"\nComparison with your TradingView screenshot:")
    print(f"  Expected TV PGO: {expected_tv_pgo:.6f}")
    print(f"  Our calculated TV PGO: {tv_pgo_latest:.6f}")
    print(f"  Difference: {abs(tv_pgo_latest - expected_tv_pgo):.8f}")
    
    # Identify the main source of discrepancy
    print(f"\n" + "=" * 80)
    print("DISCREPANCY ANALYSIS")
    print("=" * 80)
    
    if sma_diff > 0.001:
        print(f"⚠️  SMA calculation differs significantly: {sma_diff:.8f}")
    else:
        print(f"✅ SMA calculation matches closely: {sma_diff:.8f}")
        
    if atr_diff > 0.001:
        print(f"⚠️  ATR calculation differs significantly: {atr_diff:.8f}")
    else:
        print(f"✅ ATR calculation matches closely: {atr_diff:.8f}")
        
    if pgo_diff > 0.1:
        print(f"⚠️  PGO calculation differs significantly: {pgo_diff:.8f}")
        print("   This suggests a fundamental difference in calculation method.")
    else:
        print(f"✅ PGO calculation matches closely: {pgo_diff:.8f}")

if __name__ == "__main__":
    verify_pgo_calculation()
