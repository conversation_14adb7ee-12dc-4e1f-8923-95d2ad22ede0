import React from 'react';
import {
  Box,
  Text,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Spinner,
  useColorModeValue,
  Flex,
} from '@chakra-ui/react';

const TradingPerformanceMetrics = ({ performance, isLoading }) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  
  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  // Format percentage
  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };
  
  // Format ratio
  const formatRatio = (value) => {
    return value.toFixed(2);
  };
  
  if (isLoading) {
    return (
      <Box 
        bg={bgColor} 
        p={4} 
        borderRadius="lg" 
        boxShadow="sm"
        height="100%"
      >
        <Text fontSize="lg" fontWeight="bold" mb={4}>
          Performance Metrics
        </Text>
        <Flex justify="center" align="center" height="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }
  
  return (
    <Box 
      bg={bgColor} 
      p={4} 
      borderRadius="lg" 
      boxShadow="sm"
      height="100%"
    >
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Performance Metrics
      </Text>
      
      {performance ? (
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
          {/* Total Return */}
          <Stat>
            <StatLabel>Total Return</StatLabel>
            <StatNumber>{formatPercentage(performance.total_return_pct)}</StatNumber>
            <StatHelpText>
              <StatArrow type={performance.total_return_pct >= 0 ? 'increase' : 'decrease'} />
              {formatCurrency(performance.total_return)}
            </StatHelpText>
          </Stat>
          
          {/* Annualized Return */}
          <Stat>
            <StatLabel>Annualized Return</StatLabel>
            <StatNumber>{formatPercentage(performance.annualized_return)}</StatNumber>
          </Stat>
          
          {/* Max Drawdown */}
          <Stat>
            <StatLabel>Max Drawdown</StatLabel>
            <StatNumber>{formatPercentage(performance.max_drawdown)}</StatNumber>
          </Stat>
          
          {/* Sharpe Ratio */}
          <Stat>
            <StatLabel>Sharpe Ratio</StatLabel>
            <StatNumber>{formatRatio(performance.sharpe_ratio)}</StatNumber>
          </Stat>
          
          {/* Win Rate */}
          <Stat>
            <StatLabel>Win Rate</StatLabel>
            <StatNumber>{formatPercentage(performance.win_rate)}</StatNumber>
            <StatHelpText>
              {performance.winning_trades} / {performance.total_trades} trades
            </StatHelpText>
          </Stat>
          
          {/* Profit Factor */}
          <Stat>
            <StatLabel>Profit Factor</StatLabel>
            <StatNumber>{formatRatio(performance.profit_factor)}</StatNumber>
          </Stat>
        </SimpleGrid>
      ) : (
        <Flex 
          justify="center" 
          align="center" 
          height="200px"
          border="1px dashed"
          borderColor="gray.200"
          borderRadius="md"
        >
          <Text color="gray.500">No performance data available</Text>
        </Flex>
      )}
    </Box>
  );
};

export default TradingPerformanceMetrics;
