#!/usr/bin/env python
"""
Test script for network status notifications.
This script tests sending network status notifications to Telegram.
"""

import os
import sys
import logging
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import notification modules
try:
    from src.notification.notification_manager import NotificationManager
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False
    logging.warning("Notification modules not available. Notifications will be disabled.")

def load_notification_config():
    """Load notification configuration."""
    # Try to load from config/notifications.json
    import json
    config_path = "config/notifications.json"

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
            logging.info(f"Loaded notification config from {config_path}")
            return config
    except Exception as e:
        logging.error(f"Failed to load notification config: {e}")
        # Return default config
        return {
            "telegram": {
                "enabled": True,
                "token": "7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig",
                "chat_id": "7358198557",
                "level": "standard"
            }
        }

def test_network_status_notifications():
    """Test network status notifications."""
    if not NOTIFICATIONS_AVAILABLE:
        logging.error("Notification modules not available. Cannot test.")
        return False

    # Load notification config
    config = load_notification_config()

    # Initialize notification manager
    notification_manager = NotificationManager(config)

    # Test connected notification
    logging.info("Sending CONNECTED notification...")
    success1 = notification_manager.notify(
        'network_status',
        {
            'status': '✅ CONNECTED',
            'downtime_str': "",
            'in_critical_window': 'No',
            'missed_executions_str': ""
        }
    )

    # Wait a bit
    time.sleep(2)

    # Test disconnected notification
    logging.info("Sending DISCONNECTED notification...")
    success2 = notification_manager.notify(
        'network_status',
        {
            'status': '❌ DISCONNECTED',
            'downtime_str': "Last execution: 2025-05-21 20:15:00\n",
            'in_critical_window': 'Yes',
            'missed_executions_str': ""
        }
    )

    # Wait a bit
    time.sleep(2)

    # Test restored notification
    logging.info("Sending RESTORED notification...")
    success3 = notification_manager.notify(
        'network_status',
        {
            'status': '✅ RESTORED',
            'downtime_str': "Downtime: 120.5 seconds\n",
            'in_critical_window': 'No',
            'missed_executions_str': "Missed executions: 1\n"
        }
    )

    return success1 and success2 and success3

def main():
    """Main function."""
    print("\n" + "=" * 80)
    print("NETWORK STATUS NOTIFICATION TEST")
    print("=" * 80)

    success = test_network_status_notifications()

    if success:
        logging.info("All notifications sent successfully.")
        print("\nAll notifications sent successfully!")
    else:
        logging.error("Failed to send some notifications.")
        print("\nFailed to send some notifications. Check the logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
