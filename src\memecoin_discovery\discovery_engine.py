"""
Main discovery engine for memecoin discovery system.

This module orchestrates the entire discovery process, from Twitter scraping
to AI analysis and final token evaluation.
"""

import os
import logging
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from .database import DiscoveryDatabase, DiscoveredToken, TokenAnalysis
from .twitter_scraper import TwitterScraper
from .ai_analyzer import AITokenAnalyzer, AIAnalysisResult
from .token_evaluator import TokenEvaluator, TokenScore

class MemecoinDiscoveryEngine:
    """Main orchestrator for the memecoin discovery system."""
    
    def __init__(self, config: Dict = None):
        """Initialize the discovery engine with configuration."""
        self.config = config or {}
        
        # Initialize components
        self.database = DiscoveryDatabase(
            self.config.get('database_path', 'data/memecoin_discovery.db')
        )
        
        # Initialize Twitter scraper (using snscrape - no API keys needed)
        try:
            self.twitter_scraper = TwitterScraper()
            self.twitter_available = True
        except Exception as e:
            logging.warning(f"Twitter scraper initialization failed: {e}")
            logging.warning("Make sure snscrape is installed: pip install snscrape")
            self.twitter_scraper = None
            self.twitter_available = False
        
        # Initialize AI analyzer (will handle missing API key gracefully)
        try:
            self.ai_analyzer = AITokenAnalyzer(
                model=self.config.get('openai_model', 'gpt-4')
            )
            self.ai_available = True
        except Exception as e:
            logging.warning(f"AI analyzer initialization failed: {e}")
            self.ai_analyzer = None
            self.ai_available = False
        
        # Initialize token evaluator
        self.token_evaluator = TokenEvaluator()
        
        # Discovery settings
        self.discovery_settings = self.config.get('discovery', {})
        self.max_tokens_per_run = self.discovery_settings.get('max_tokens_per_run', 50)
        self.min_mentions_threshold = self.discovery_settings.get('min_mentions_threshold', 3)
        self.hours_lookback = self.discovery_settings.get('hours_lookback', 24)
        
        logging.info("Memecoin discovery engine initialized")
        logging.info(f"Twitter available: {self.twitter_available}")
        logging.info(f"AI analysis available: {self.ai_available}")
    
    def run_discovery_cycle(self) -> Dict[str, Any]:
        """Run a complete discovery cycle."""
        start_time = datetime.now()
        logging.info("Starting memecoin discovery cycle")
        
        results = {
            'start_time': start_time.isoformat(),
            'tokens_discovered': 0,
            'tokens_analyzed': 0,
            'tokens_approved': 0,
            'errors': [],
            'top_tokens': []
        }
        
        try:
            # Step 1: Scrape social media for mentions
            social_mentions = self._scrape_social_media()
            logging.info(f"Found {len(social_mentions)} social media mentions")
            
            # Step 2: Extract and deduplicate potential tokens
            potential_tokens = self._extract_potential_tokens(social_mentions)
            logging.info(f"Identified {len(potential_tokens)} potential tokens")
            
            # Step 3: Filter tokens based on minimum criteria
            filtered_tokens = self._filter_tokens(potential_tokens)
            logging.info(f"Filtered to {len(filtered_tokens)} tokens meeting criteria")
            
            # Step 4: Analyze tokens with AI
            analyzed_tokens = self._analyze_tokens_with_ai(filtered_tokens)
            logging.info(f"Completed AI analysis for {len(analyzed_tokens)} tokens")
            
            # Step 5: Evaluate and score tokens
            scored_tokens = self._evaluate_and_score_tokens(analyzed_tokens)
            logging.info(f"Scored {len(scored_tokens)} tokens")
            
            # Step 6: Save results to database
            saved_count = self._save_discovery_results(scored_tokens)
            logging.info(f"Saved {saved_count} tokens to database")
            
            # Step 7: Generate summary
            results.update({
                'tokens_discovered': len(potential_tokens),
                'tokens_analyzed': len(analyzed_tokens),
                'tokens_approved': len([t for t in scored_tokens if t.recommendation in ['buy', 'strong_buy']]),
                'top_tokens': self._get_top_tokens(scored_tokens, limit=10)
            })
            
        except Exception as e:
            error_msg = f"Discovery cycle failed: {str(e)}"
            logging.error(error_msg)
            results['errors'].append(error_msg)
        
        end_time = datetime.now()
        results['end_time'] = end_time.isoformat()
        results['duration_seconds'] = (end_time - start_time).total_seconds()
        
        logging.info(f"Discovery cycle completed in {results['duration_seconds']:.1f} seconds")
        return results
    
    def _scrape_social_media(self) -> List[Dict]:
        """Scrape social media for memecoin mentions."""
        all_mentions = []
        
        if not self.twitter_available:
            logging.warning("Twitter scraping not available, skipping")
            return all_mentions
        
        try:
            # Search for general memecoin mentions
            general_mentions = self.twitter_scraper.search_memecoin_mentions(
                hours_back=self.hours_lookback,
                max_results=100
            )
            all_mentions.extend(general_mentions)
            
            # Monitor influencer accounts
            influencer_mentions = self.twitter_scraper.monitor_influencer_accounts(
                hours_back=self.hours_lookback
            )
            all_mentions.extend(influencer_mentions)
            
            logging.info(f"Scraped {len(all_mentions)} total social media mentions")
            
        except Exception as e:
            logging.error(f"Error scraping social media: {e}")
        
        return all_mentions
    
    def _extract_potential_tokens(self, social_mentions: List[Dict]) -> Dict[str, Dict]:
        """Extract potential tokens from social media mentions."""
        potential_tokens = {}
        
        for mention in social_mentions:
            try:
                # Extract token symbols from text
                symbols = self._extract_token_symbols(mention.get('text', ''))
                
                # Extract contract addresses
                addresses = mention.get('token_addresses', {})
                
                # Process extracted symbols
                for symbol in symbols:
                    if symbol not in potential_tokens:
                        potential_tokens[symbol] = {
                            'symbol': symbol,
                            'mentions': [],
                            'networks': set(),
                            'total_engagement': 0,
                            'sentiment_scores': [],
                            'first_seen': mention.get('created_at'),
                            'contract_addresses': {}
                        }
                    
                    # Add mention data
                    potential_tokens[symbol]['mentions'].append(mention)
                    potential_tokens[symbol]['total_engagement'] += (
                        mention.get('like_count', 0) + mention.get('retweet_count', 0)
                    )
                    potential_tokens[symbol]['sentiment_scores'].append(
                        mention.get('sentiment_score', 0)
                    )
                
                # Process contract addresses
                for network, addr_list in addresses.items():
                    for addr in addr_list:
                        # Try to find associated symbol or create generic one
                        symbol = self._guess_symbol_from_address(addr, mention.get('text', ''))
                        if symbol:
                            if symbol not in potential_tokens:
                                potential_tokens[symbol] = {
                                    'symbol': symbol,
                                    'mentions': [],
                                    'networks': set(),
                                    'total_engagement': 0,
                                    'sentiment_scores': [],
                                    'first_seen': mention.get('created_at'),
                                    'contract_addresses': {}
                                }
                            
                            potential_tokens[symbol]['networks'].add(network)
                            potential_tokens[symbol]['contract_addresses'][network] = addr
                            potential_tokens[symbol]['mentions'].append(mention)
                
            except Exception as e:
                logging.error(f"Error extracting tokens from mention: {e}")
                continue
        
        # Convert sets to lists for JSON serialization
        for token_data in potential_tokens.values():
            token_data['networks'] = list(token_data['networks'])
        
        return potential_tokens
    
    def _extract_token_symbols(self, text: str) -> List[str]:
        """Extract potential token symbols from text."""
        import re
        
        symbols = []
        
        # Extract cashtags (e.g., $BTC, $DOGE)
        cashtags = re.findall(r'\$([A-Z]{2,10})', text.upper())
        symbols.extend(cashtags)
        
        # Extract common token patterns
        # Look for patterns like "SYMBOL token", "SYMBOL coin", etc.
        token_patterns = [
            r'\b([A-Z]{2,10})\s+(?:token|coin|memecoin)\b',
            r'\b(?:token|coin|memecoin)\s+([A-Z]{2,10})\b',
            r'\b([A-Z]{3,10})\s+(?:to the moon|moon|x100|x1000)\b'
        ]
        
        for pattern in token_patterns:
            matches = re.findall(pattern, text.upper())
            symbols.extend(matches)
        
        # Filter out common false positives
        false_positives = {'THE', 'AND', 'FOR', 'YOU', 'ARE', 'NOT', 'BUT', 'CAN', 'ALL', 'NEW', 'GET', 'NOW', 'OUT', 'WAY', 'USE', 'HER', 'HIM', 'HIS', 'SHE', 'HAS', 'HAD'}
        symbols = [s for s in symbols if s not in false_positives and len(s) >= 3]
        
        return list(set(symbols))  # Remove duplicates
    
    def _guess_symbol_from_address(self, address: str, text: str) -> Optional[str]:
        """Try to guess token symbol from contract address and surrounding text."""
        # This is a simplified implementation
        # In practice, you might query blockchain APIs or maintain a database
        
        # Look for symbols near the address in the text
        import re
        
        # Find the position of the address in text
        addr_pos = text.find(address)
        if addr_pos >= 0:
            # Look for symbols in surrounding text (±50 characters)
            start = max(0, addr_pos - 50)
            end = min(len(text), addr_pos + len(address) + 50)
            surrounding_text = text[start:end]
            
            # Extract potential symbols
            symbols = self._extract_token_symbols(surrounding_text)
            if symbols:
                return symbols[0]  # Return first found symbol
        
        # Fallback: generate symbol from address
        return f"TOKEN_{address[-6:].upper()}"
    
    def _filter_tokens(self, potential_tokens: Dict[str, Dict]) -> Dict[str, Dict]:
        """Filter tokens based on minimum criteria."""
        filtered = {}
        
        for symbol, token_data in potential_tokens.items():
            try:
                # Check minimum mentions threshold
                if len(token_data['mentions']) < self.min_mentions_threshold:
                    continue
                
                # Check for minimum engagement
                if token_data['total_engagement'] < 10:  # Minimum 10 total engagements
                    continue
                
                # Check sentiment (avoid overwhelmingly negative tokens)
                if token_data['sentiment_scores']:
                    avg_sentiment = sum(token_data['sentiment_scores']) / len(token_data['sentiment_scores'])
                    if avg_sentiment < -0.5:  # Very negative sentiment
                        continue
                
                # Check for obvious spam patterns
                if self._is_likely_spam(token_data):
                    continue
                
                filtered[symbol] = token_data
                
            except Exception as e:
                logging.error(f"Error filtering token {symbol}: {e}")
                continue
        
        return filtered
    
    def _is_likely_spam(self, token_data: Dict) -> bool:
        """Check if token data shows spam patterns."""
        try:
            mentions = token_data['mentions']
            
            # Check for duplicate content
            texts = [mention.get('text', '') for mention in mentions]
            unique_texts = set(texts)
            if len(unique_texts) / len(texts) < 0.5:  # More than 50% duplicate content
                return True
            
            # Check for bot-like posting patterns
            authors = [mention.get('author_username', '') for mention in mentions]
            unique_authors = set(authors)
            if len(unique_authors) / len(authors) < 0.3:  # Low author diversity
                return True
            
            return False
            
        except Exception as e:
            logging.error(f"Error checking spam patterns: {e}")
            return False
    
    def _analyze_tokens_with_ai(self, filtered_tokens: Dict[str, Dict]) -> List[Dict]:
        """Analyze filtered tokens using AI."""
        analyzed_tokens = []
        
        if not self.ai_available:
            logging.warning("AI analysis not available, skipping")
            # Return tokens with basic analysis
            for symbol, token_data in filtered_tokens.items():
                token_data['ai_analysis'] = {
                    'overall_score': 50.0,
                    'confidence': 0.3,
                    'recommendation': 'hold',
                    'reasoning': 'AI analysis not available'
                }
                analyzed_tokens.append(token_data)
            return analyzed_tokens
        
        # Limit number of tokens to analyze (API cost control)
        tokens_to_analyze = list(filtered_tokens.items())[:self.max_tokens_per_run]
        
        for symbol, token_data in tokens_to_analyze:
            try:
                logging.info(f"Analyzing token: {symbol}")
                
                # Prepare token metadata
                metadata = {
                    'symbol': symbol,
                    'networks': token_data.get('networks', []),
                    'contract_addresses': token_data.get('contract_addresses', {}),
                    'mention_count': len(token_data['mentions']),
                    'total_engagement': token_data['total_engagement']
                }
                
                # Run AI analysis
                ai_result = self.ai_analyzer.analyze_token_from_social_data(
                    symbol, token_data['mentions'], metadata
                )
                
                # Convert AI result to dict
                token_data['ai_analysis'] = {
                    'overall_score': ai_result.overall_score,
                    'confidence': ai_result.confidence,
                    'recommendation': ai_result.recommendation,
                    'reasoning': ai_result.reasoning,
                    'risk_factors': ai_result.risk_factors,
                    'positive_factors': ai_result.positive_factors,
                    'sentiment_analysis': ai_result.sentiment_analysis,
                    'social_metrics_analysis': ai_result.social_metrics_analysis,
                    'technical_indicators': ai_result.technical_indicators
                }
                
                analyzed_tokens.append(token_data)
                
                # Add delay to respect rate limits
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"Error analyzing token {symbol}: {e}")
                # Add token with error status
                token_data['ai_analysis'] = {
                    'overall_score': 30.0,
                    'confidence': 0.1,
                    'recommendation': 'avoid',
                    'reasoning': f'Analysis failed: {str(e)}'
                }
                analyzed_tokens.append(token_data)
        
        return analyzed_tokens
    
    def _evaluate_and_score_tokens(self, analyzed_tokens: List[Dict]) -> List[TokenScore]:
        """Evaluate and score analyzed tokens."""
        scored_tokens = []
        
        for token_data in analyzed_tokens:
            try:
                symbol = token_data['symbol']
                ai_analysis = token_data.get('ai_analysis', {})
                social_data = token_data.get('mentions', [])
                
                # Prepare metadata
                metadata = {
                    'networks': token_data.get('networks', []),
                    'contract_addresses': token_data.get('contract_addresses', {}),
                    'total_engagement': token_data.get('total_engagement', 0),
                    'mention_count': len(social_data)
                }
                
                # Evaluate token
                score_result = self.token_evaluator.evaluate_token(
                    symbol, ai_analysis, social_data, metadata
                )
                
                scored_tokens.append(score_result)
                
            except Exception as e:
                logging.error(f"Error scoring token: {e}")
                continue
        
        # Sort by final score (descending)
        scored_tokens.sort(key=lambda x: x.final_score, reverse=True)
        
        return scored_tokens
    
    def _save_discovery_results(self, scored_tokens: List[TokenScore]) -> int:
        """Save discovery results to database."""
        saved_count = 0
        
        for token_score in scored_tokens:
            try:
                # Create discovered token record
                discovered_token = DiscoveredToken(
                    symbol=token_score.symbol,
                    name=token_score.symbol,  # Use symbol as name for now
                    contract_address=token_score.metadata.get('contract_addresses', {}).get('ethereum', ''),
                    network='ethereum',  # Default to ethereum
                    discovery_date=datetime.now(),
                    source='twitter_ai_discovery',
                    sentiment_score=token_score.component_scores.get('social_sentiment', 0) / 100,
                    ai_score=token_score.component_scores.get('ai_analysis', 0) / 100,
                    risk_score=token_score.component_scores.get('risk_assessment', 0) / 100,
                    final_score=token_score.final_score / 100,
                    status='discovered',
                    metadata=token_score.metadata
                )
                
                # Save to database
                if self.database.add_discovered_token(discovered_token):
                    saved_count += 1
                
            except Exception as e:
                logging.error(f"Error saving token {token_score.symbol}: {e}")
                continue
        
        return saved_count
    
    def _get_top_tokens(self, scored_tokens: List[TokenScore], limit: int = 10) -> List[Dict]:
        """Get top tokens for summary."""
        top_tokens = []
        
        for token in scored_tokens[:limit]:
            top_tokens.append({
                'symbol': token.symbol,
                'score': round(token.final_score, 1),
                'confidence': round(token.confidence, 2),
                'recommendation': token.recommendation,
                'risk_level': token.risk_level
            })
        
        return top_tokens
    
    def get_discovery_summary(self, days: int = 7) -> Dict[str, Any]:
        """Get summary of recent discoveries."""
        try:
            # Get recent discoveries
            recent_tokens = self.database.get_discovered_tokens(days_back=days)
            
            # Calculate summary statistics
            total_discovered = len(recent_tokens)
            approved_tokens = len([t for t in recent_tokens if t.final_score and t.final_score > 0.65])
            avg_score = sum(t.final_score for t in recent_tokens if t.final_score) / max(total_discovered, 1)
            
            return {
                'period_days': days,
                'total_discovered': total_discovered,
                'approved_tokens': approved_tokens,
                'average_score': round(avg_score, 2),
                'top_discoveries': [
                    {
                        'symbol': t.symbol,
                        'score': round(t.final_score, 2) if t.final_score else 0,
                        'discovery_date': t.discovery_date.isoformat(),
                        'status': t.status
                    }
                    for t in sorted(recent_tokens, key=lambda x: x.final_score or 0, reverse=True)[:10]
                ]
            }
            
        except Exception as e:
            logging.error(f"Error generating discovery summary: {e}")
            return {'error': str(e)}
