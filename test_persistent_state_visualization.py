#!/usr/bin/env python3
"""
Test script to verify the persistent state visualization logic.

This script tests that:
1. Neutral signals (0) maintain the last bullish/bearish state
2. Background coloring persists correctly
3. Line coloring follows the same persistent logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_signal_periods_logic():
    """Test the create_signal_periods function with persistent state logic."""
    print("=" * 60)
    print("TESTING PERSISTENT STATE SIGNAL PERIODS")
    print("=" * 60)
    
    try:
        from visualize_mtpi_signals import create_signal_periods
        
        # Create test data
        dates = pd.date_range(start='2024-01-01', periods=10, freq='D')
        test_df = pd.DataFrame({'close': range(100, 110)}, index=dates)
        
        # Test signal sequence: 1, 1, 0, 0, -1, -1, 0, 0, 1, 0
        # Expected persistent: 1, 1, 1, 1, -1, -1, -1, -1, 1, 1
        test_signals = pd.Series([1, 1, 0, 0, -1, -1, 0, 0, 1, 0], index=dates)
        
        print("Original signals:", test_signals.tolist())
        
        # Test the function
        periods = create_signal_periods(test_df, test_signals)
        
        print("Generated periods:")
        for i, (start_idx, end_idx, signal_type) in enumerate(periods):
            start_date = dates[start_idx]
            end_date = dates[end_idx]
            signal_name = "Bullish" if signal_type == 1 else "Bearish"
            print(f"  Period {i+1}: {signal_name} from {start_date.date()} to {end_date.date()} (indices {start_idx}-{end_idx})")
        
        # Verify expected behavior
        # Should have: Bullish (0-3), Bearish (4-7), Bullish (8-9)
        expected_periods = [
            (0, 3, 1),  # Bullish from index 0 to 3
            (4, 7, -1), # Bearish from index 4 to 7  
            (8, 9, 1)   # Bullish from index 8 to 9
        ]
        
        if len(periods) == len(expected_periods):
            print("✅ Correct number of periods generated")
            
            all_correct = True
            for i, ((start, end, signal), (exp_start, exp_end, exp_signal)) in enumerate(zip(periods, expected_periods)):
                if start == exp_start and end == exp_end and signal == exp_signal:
                    print(f"✅ Period {i+1} correct: {start}-{end}, signal {signal}")
                else:
                    print(f"❌ Period {i+1} incorrect: got {start}-{end}, signal {signal}, expected {exp_start}-{exp_end}, signal {exp_signal}")
                    all_correct = False
            
            if all_correct:
                print("✅ All periods match expected behavior!")
                return True
            else:
                print("❌ Some periods don't match expected behavior")
                return False
        else:
            print(f"❌ Wrong number of periods: got {len(periods)}, expected {len(expected_periods)}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing signal periods: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_coloring_logic():
    """Test the line coloring logic with persistent states."""
    print("\n" + "=" * 60)
    print("TESTING PERSISTENT STATE LINE COLORING")
    print("=" * 60)
    
    try:
        # Simulate the line coloring logic from the visualization
        long_threshold = 0.1
        short_threshold = -0.1
        
        # Test scores: above, above, neutral, neutral, below, below, neutral, neutral, above, neutral
        test_scores = [0.2, 0.15, 0.05, -0.05, -0.2, -0.15, -0.05, 0.05, 0.2, 0.05]
        
        print("Test scores:", test_scores)
        print(f"Long threshold: {long_threshold}, Short threshold: {short_threshold}")
        
        # Apply the persistent coloring logic
        line_colors = []
        last_state_color = 'blue'  # Default color
        
        for score in test_scores:
            if score >= long_threshold:
                last_state_color = 'green'
                line_colors.append('green')
            elif score <= short_threshold:
                last_state_color = 'red'
                line_colors.append('red')
            else:
                # Neutral - maintain last state color
                line_colors.append(last_state_color)
        
        print("Generated colors:", line_colors)
        
        # Expected: green, green, green, green, red, red, red, red, green, green
        expected_colors = ['green', 'green', 'green', 'green', 'red', 'red', 'red', 'red', 'green', 'green']
        
        if line_colors == expected_colors:
            print("✅ Line coloring logic works correctly!")
            print("✅ Neutral states maintain the last non-neutral color")
            return True
        else:
            print(f"❌ Line coloring incorrect:")
            print(f"   Got:      {line_colors}")
            print(f"   Expected: {expected_colors}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing line coloring: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_integration():
    """Test that the visualization can be imported and basic functions work."""
    print("\n" + "=" * 60)
    print("TESTING VISUALIZATION INTEGRATION")
    print("=" * 60)
    
    try:
        # Test importing the visualization module
        print("Importing visualization module...")
        import visualize_mtpi_signals
        print("✅ Successfully imported visualization module")
        
        # Test that key functions exist
        functions_to_test = [
            'create_signal_periods',
            'calculate_mtpi_signals_series',
            'fetch_btc_data_with_signals',
            'plot_btc_with_mtpi_signals'
        ]
        
        for func_name in functions_to_test:
            if hasattr(visualize_mtpi_signals, func_name):
                print(f"✅ Function {func_name} exists")
            else:
                print(f"❌ Function {func_name} missing")
                return False
        
        print("✅ All required functions are available")
        return True
        
    except Exception as e:
        print(f"❌ Error testing visualization integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests for the persistent state visualization."""
    print("🎨 Testing Persistent State Visualization Logic")
    print("=" * 80)
    
    results = []
    
    # Test 1: Signal periods logic
    results.append(test_signal_periods_logic())
    
    # Test 2: Line coloring logic
    results.append(test_line_coloring_logic())
    
    # Test 3: Visualization integration
    results.append(test_visualization_integration())
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Persistent state visualization is working correctly.")
        print("\n📋 The visualization now:")
        print("✅ Maintains bullish/bearish state when signal is neutral")
        print("✅ No more orange/neutral coloring")
        print("✅ Background periods persist until actual signal change")
        print("✅ Line coloring follows the same persistent logic")
        print("\n🚀 Ready to use: python visualize_mtpi_signals.py")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
