<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memecoin Discovery Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .score-badge {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
        }
        .score-high { background-color: #28a745; }
        .score-medium { background-color: #ffc107; color: #000; }
        .score-low { background-color: #dc3545; }
        
        .token-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .token-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .discovery-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .refresh-btn {
            transition: transform 0.3s;
        }
        .refresh-btn.spinning {
            transform: rotate(360deg);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-rocket me-2"></i>
                Memecoin Discovery Dashboard
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light me-2" onclick="runDiscovery()">
                    <i class="fas fa-search me-1"></i>
                    Run Discovery
                </button>
                <button class="btn btn-outline-light" onclick="refreshData()">
                    <i class="fas fa-sync-alt refresh-btn" id="refreshIcon"></i>
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            System Status
                        </h5>
                    </div>
                    <div class="card-body" id="systemStatus">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Discovery Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card discovery-stats">
                    <div class="card-body">
                        <div class="row text-center" id="discoverySummary">
                            <div class="col-md-3">
                                <h3 class="mb-0" id="totalDiscovered">-</h3>
                                <p class="mb-0">Total Discovered</p>
                            </div>
                            <div class="col-md-3">
                                <h3 class="mb-0" id="approvedTokens">-</h3>
                                <p class="mb-0">Approved</p>
                            </div>
                            <div class="col-md-3">
                                <h3 class="mb-0" id="averageScore">-</h3>
                                <p class="mb-0">Avg Score</p>
                            </div>
                            <div class="col-md-3">
                                <h3 class="mb-0" id="lastUpdate">-</h3>
                                <p class="mb-0">Last Update</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <label for="statusFilter" class="form-label">Status</label>
                                <select class="form-select" id="statusFilter" onchange="loadTokens()">
                                    <option value="">All Statuses</option>
                                    <option value="discovered">Discovered</option>
                                    <option value="analyzing">Analyzing</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="monitoring">Monitoring</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="daysFilter" class="form-label">Time Period</label>
                                <select class="form-select" id="daysFilter" onchange="loadTokens()">
                                    <option value="1">Last 24 hours</option>
                                    <option value="7" selected>Last 7 days</option>
                                    <option value="30">Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sortBy" class="form-label">Sort By</label>
                                <select class="form-select" id="sortBy" onchange="sortTokens()">
                                    <option value="score">Final Score</option>
                                    <option value="date">Discovery Date</option>
                                    <option value="mentions">Twitter Mentions</option>
                                    <option value="sentiment">Sentiment</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchInput" class="form-label">Search</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="Search tokens..." onkeyup="filterTokens()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tokens Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-coins me-2"></i>
                            Discovered Tokens
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>
                                Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="tokensLoading" class="text-center loading-spinner">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading tokens...</span>
                            </div>
                        </div>
                        <div id="tokensContainer">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Network</th>
                                            <th>Final Score</th>
                                            <th>AI Score</th>
                                            <th>Sentiment</th>
                                            <th>Risk</th>
                                            <th>Mentions</th>
                                            <th>Discovery Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tokensTable">
                                        <!-- Tokens will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Token Detail Modal -->
    <div class="modal fade" id="tokenModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tokenModalTitle">Token Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="tokenModalBody">
                    <!-- Token details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" onclick="updateTokenStatus('approved')">
                        <i class="fas fa-check me-1"></i>
                        Approve
                    </button>
                    <button type="button" class="btn btn-danger" onclick="updateTokenStatus('rejected')">
                        <i class="fas fa-times me-1"></i>
                        Reject
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Discovery Progress Modal -->
    <div class="modal fade" id="discoveryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Running Discovery</h5>
                </div>
                <div class="modal-body text-center">
                    <div class="spinner-border mb-3" role="status">
                        <span class="visually-hidden">Running discovery...</span>
                    </div>
                    <p>Discovering new memecoins...</p>
                    <p class="text-muted">This may take a few minutes.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/dashboard.js"></script>
</body>
</html>
