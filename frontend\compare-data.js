// Simple script to compare backend and frontend data
import axios from 'axios';
import fs from 'fs/promises';

// Format date for better readability
function formatDate(timestamp) {
  return new Date(timestamp * 1000).toISOString().split('T')[0];
}

async function compareData() {
  try {
    console.log('Fetching data from backend API...');
    const response = await axios.get('http://localhost:5001/api/equity');
    const data = response.data;

    console.log('\n=== BACKEND DATA ANALYSIS ===');

    // Check strategy data
    if (data.curves && data.curves.strategy) {
      const strategyData = data.curves.strategy;
      console.log(`Strategy data points: ${strategyData.length}`);

      if (strategyData.length > 0) {
        const firstPoint = strategyData[0];
        const lastPoint = strategyData[strategyData.length - 1];

        console.log(`First point: ${formatDate(firstPoint.time)}, value: ${firstPoint.value}`);
        console.log(`Last point: ${formatDate(lastPoint.time)}, value: ${lastPoint.value}`);

        const totalReturn = ((lastPoint.value / firstPoint.value) - 1) * 100;
        console.log(`Total return: ${totalReturn.toFixed(2)}%`);
      }
    } else {
      console.log('No strategy data found');
    }

    // Check asset changes
    if (data.assetChanges) {
      console.log(`\nAsset changes: ${data.assetChanges.length}`);

      // Log all asset changes
      console.log('\nAll asset changes:');
      data.assetChanges.forEach((change, i) => {
        console.log(`  ${i+1}. ${formatDate(change.date)}: ${change.asset || 'out-of-market'}`);
      });

      // Count asset distribution
      const assetCounts = {};
      data.assetChanges.forEach(change => {
        const asset = change.asset || 'out-of-market';
        assetCounts[asset] = (assetCounts[asset] || 0) + 1;
      });

      console.log('\nAsset distribution:');
      Object.entries(assetCounts).forEach(([asset, count]) => {
        console.log(`  ${asset}: ${count} (${(count/data.assetChanges.length*100).toFixed(1)}%)`);
      });
    } else {
      console.log('No asset changes found');
    }

    // Check MTPI signals
    if (data.metadata && data.metadata.rawMtpiSignals) {
      const mtpiSignals = data.metadata.rawMtpiSignals;
      console.log(`\nMTPI signals: ${mtpiSignals.length}`);

      // Count signal distribution
      const bullish = mtpiSignals.filter(s => s.value === 1).length;
      const neutral = mtpiSignals.filter(s => s.value === 0).length;
      const bearish = mtpiSignals.filter(s => s.value === -1).length;

      console.log(`Signal distribution: Bullish=${bullish} (${(bullish/mtpiSignals.length*100).toFixed(1)}%), Neutral=${neutral} (${(neutral/mtpiSignals.length*100).toFixed(1)}%), Bearish=${bearish} (${(bearish/mtpiSignals.length*100).toFixed(1)}%)`);
    } else {
      console.log('No MTPI signals found');
    }

    // Save the data for further analysis
    await fs.writeFile('backend-data.json', JSON.stringify(data, null, 2));
    console.log('\nBackend data saved to backend-data.json');

    // Now simulate the frontend rendering process
    console.log('\n=== SIMULATING FRONTEND RENDERING ===');

    if (data.curves && data.curves.strategy && data.assetChanges) {
      const strategyData = data.curves.strategy;
      const assetChanges = data.assetChanges;

      // Sort asset changes by date
      const sortedAssetChanges = [...assetChanges].sort((a, b) => a.date - b.date);

      // Create a map of time to asset for quick lookup
      const timeToAssetMap = {};

      // Process all data points to assign the correct asset
      for (let i = 0; i < strategyData.length; i++) {
        const point = strategyData[i];
        const pointTime = point.time;

        // Find the most recent asset change before this point
        let currentAsset = 'out-of-market'; // Default if no asset change is found

        for (let j = sortedAssetChanges.length - 1; j >= 0; j--) {
          if (pointTime >= sortedAssetChanges[j].date) {
            currentAsset = sortedAssetChanges[j].asset || 'out-of-market';
            break; // Found the most recent asset change
          }
        }

        // Store the asset for this time
        timeToAssetMap[pointTime] = currentAsset;
      }

      // Define asset colors for reference
      const assetColors = {
        'BTC/USDT': '#f7931a', // Bitcoin orange
        'ETH/USDT': '#627eea', // Ethereum blue
        'SOL/USDT': '#00ffbd', // Solana green
        'out-of-market': '#999999' // Gray for out-of-market periods
      };

      // Now map the strategy data points with the correct colors
      const coloredData = strategyData.map(point => {
        const asset = timeToAssetMap[point.time] || 'out-of-market';
        let color = assetColors[asset] || '#0d6efd';

        // If the asset is empty string, it means we're out of market
        if (asset === '') {
          color = assetColors['out-of-market'];
        }

        return {
          time: point.time,
          value: point.value,
          asset: asset,
          color: color
        };
      });

      // Count asset distribution in the rendered data
      const assetCounts = {};
      coloredData.forEach(point => {
        const asset = point.asset;
        assetCounts[asset] = (assetCounts[asset] || 0) + 1;
      });

      console.log('\nAsset distribution in rendered data:');
      Object.entries(assetCounts).forEach(([asset, count]) => {
        console.log(`  ${asset}: ${count} (${(count/coloredData.length*100).toFixed(1)}%)`);
      });

      // Check for segments in the rendered data
      console.log('\nRendered segments:');
      let currentAsset = null;
      let segmentStart = null;
      let segments = [];

      coloredData.forEach((point, index) => {
        if (point.asset !== currentAsset || index === coloredData.length - 1) {
          // End of a segment
          if (currentAsset !== null) {
            const segmentEnd = index === coloredData.length - 1 ? point : coloredData[index - 1];
            segments.push({
              asset: currentAsset,
              start: segmentStart.time,
              end: segmentEnd.time,
              startValue: segmentStart.value,
              endValue: segmentEnd.value
            });
          }

          // Start of a new segment
          currentAsset = point.asset;
          segmentStart = point;
        }
      });

      console.log(`Found ${segments.length} segments in the rendered data`);

      // Log the segments
      segments.forEach((segment, i) => {
        console.log(`  ${i+1}. ${formatDate(segment.start)} to ${formatDate(segment.end)}: ${segment.asset}`);
      });

      // Save the rendered data for further analysis
      await fs.writeFile('frontend-rendered-data.json', JSON.stringify({
        coloredData: coloredData.slice(0, 100), // Just save the first 100 points to keep the file size reasonable
        segments: segments
      }, null, 2));
      console.log('\nRendered data saved to frontend-rendered-data.json');
    } else {
      console.log('Missing required data for frontend rendering simulation');
    }

  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the comparison
compareData();
