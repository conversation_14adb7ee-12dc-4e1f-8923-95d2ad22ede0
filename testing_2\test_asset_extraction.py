#!/usr/bin/env python
"""
Test script to debug asset extraction in background_service.py
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('asset_extraction_test.log')
    ]
)

# Import necessary modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.config_manager import load_config
from test_allocation import run_strategy_for_web

def test_asset_extraction():
    """Test the asset extraction logic."""
    logging.info("Starting asset extraction test...")
    
    # Load configuration
    config = load_config()
    settings = config.get('settings', {})
    
    # Log the configuration
    logging.info(f"Configuration loaded: n_assets={settings.get('n_assets', 1)}")
    logging.info(f"Assets: {settings.get('assets', [])}")
    logging.info(f"Use weighted allocation: {settings.get('use_weighted_allocation', False)}")
    
    # Run the strategy
    results = run_strategy_for_web(
        use_mtpi_signal=settings.get('use_mtpi_signal', True),
        mtpi_indicator_type=settings.get('mtpi_indicator_type', 'PGO'),
        mtpi_timeframe=settings.get('mtpi_timeframe', '1d'),
        timeframe=settings.get('timeframe', '1d'),
        analysis_start_date=settings.get('start_date', '2023-10-20'),
        selected_assets=settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']),
        n_assets=settings.get('n_assets', 1),
        use_weighted_allocation=settings.get('use_weighted_allocation', False),
        weights=settings.get('weights', None),
        use_cache=True,
        force_refresh_cache=True  # Always get fresh data
    )
    
    if not results or 'error' in results:
        error_msg = results.get('error', 'Unknown error') if results else 'No results returned'
        logging.error(f"Strategy execution failed: {error_msg}")
        return
    
    # Extract key results
    mtpi_signal = None
    best_asset = None
    assets_held = {}
    
    # Get MTPI signal
    if 'mtpi_signals' in results and results['mtpi_signals'] is not None:
        if isinstance(results['mtpi_signals'], pd.Series):
            mtpi_signal = results['mtpi_signals'].iloc[-1] if not results['mtpi_signals'].empty else None
            logging.info(f"MTPI signal: {mtpi_signal}")
        elif isinstance(results['mtpi_signals'], dict) and 'latest' in results['mtpi_signals']:
            mtpi_signal = results['mtpi_signals']['latest']
            logging.info(f"MTPI signal (from latest): {mtpi_signal}")
    
    # Get best asset and assets held
    if 'best_asset_series' in results and results['best_asset_series'] is not None:
        if isinstance(results['best_asset_series'], pd.Series):
            best_asset_series = results['best_asset_series']
            if not best_asset_series.empty:
                best_asset = best_asset_series.iloc[-1]
                logging.info(f"Best asset: {best_asset}")
                
                # Check if best_asset contains multiple assets (comma-separated)
                if best_asset and ',' in best_asset:
                    # This is a multi-asset strategy result
                    assets_list = [a.strip() for a in best_asset.split(',')]
                    logging.info(f"Multiple assets in best_asset: {assets_list}")
                    
                    # For backward compatibility, set best_asset to the first one
                    best_asset = assets_list[0] if assets_list else None
        elif isinstance(results['best_asset_series'], dict) and 'latest' in results['best_asset_series']:
            best_asset = results['best_asset_series']['latest']
            logging.info(f"Best asset (from latest): {best_asset}")
    
    # Get assets held with weights if available
    if 'assets_held_df' in results and results['assets_held_df'] is not None:
        assets_held_df = results['assets_held_df']
        logging.info(f"Assets held DataFrame columns: {assets_held_df.columns.tolist()}")
        
        if not assets_held_df.empty:
            # Extract assets with non-zero allocation from the last row
            n_assets = settings.get('n_assets', 1)
            use_weighted_allocation = settings.get('use_weighted_allocation', False)
            weights = settings.get('weights', None)
            
            # Debug log the last row of assets_held_df
            last_row = assets_held_df.iloc[-1]
            logging.info(f"Last row of assets_held_df: {last_row}")
            logging.info(f"Last row index: {last_row.index.tolist()}")
            
            # Check if 'assets_held' column exists
            if 'assets_held' in assets_held_df.columns:
                assets_held_last = assets_held_df['assets_held'].iloc[-1]
                logging.info(f"Assets held in last row: {assets_held_last}")
                logging.info(f"Type of assets_held_last: {type(assets_held_last)}")
            else:
                logging.warning(f"No 'assets_held' column found in assets_held_df. Columns: {assets_held_df.columns}")
                
                # Check if asset columns exist directly
                available_assets = settings.get('assets', [])
                logging.info(f"Available assets from config: {available_assets}")
                
                # Check if these assets exist as columns in the dataframe
                asset_columns = [col for col in last_row.index if col in available_assets]
                logging.info(f"Asset columns found in dataframe: {asset_columns}")
                
                if asset_columns:
                    # Get non-zero allocations
                    assets_with_values = {}
                    for asset in asset_columns:
                        value = last_row[asset]
                        assets_with_values[asset] = value
                        logging.info(f"Asset {asset} value: {value}")
                    
                    # Sort assets by value
                    sorted_assets = sorted(assets_with_values.items(), key=lambda x: x[1], reverse=True)
                    logging.info(f"Sorted assets by value: {sorted_assets}")
                    
                    # Get top n assets
                    top_n_assets = sorted_assets[:n_assets]
                    logging.info(f"Top {n_assets} assets: {top_n_assets}")
                    
                    # Create assets_held dictionary
                    if use_weighted_allocation and weights and len(weights) >= len(top_n_assets):
                        # Use the configured weights
                        assets_held = {asset: weights[i] for i, (asset, _) in enumerate(top_n_assets)}
                        logging.info(f"Using weighted allocation: {assets_held}")
                    else:
                        # Equal allocation
                        weight = 1.0 / len(top_n_assets)
                        assets_held = {asset: weight for asset, _ in top_n_assets}
                        logging.info(f"Using equal allocation: {assets_held}")
    
    # Log the final results
    logging.info(f"Final results:")
    logging.info(f"MTPI signal: {mtpi_signal}")
    logging.info(f"Best asset: {best_asset}")
    logging.info(f"Assets held: {assets_held}")

if __name__ == "__main__":
    test_asset_extraction()
