#!/usr/bin/env python3
"""
Debug script to trace the actual strategy execution and see where the difference is lost.
"""

import sys
import os
import pandas as pd
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_program import AllocationTester

# Configure logging to show more details
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_strategy_execution():
    """Debug the actual strategy execution to see where differences are lost."""
    
    print("=" * 80)
    print("DEBUGGING STRATEGY EXECUTION")
    print("=" * 80)
    
    # Simple parameters for debugging
    common_params = {
        'timeframe': '1d',
        'mtpi_timeframe': '1d',
        'analysis_start_date': '2024-06-15',  # Short period for debugging
        'n_assets': 2,  # Need multiple assets for scoring to work
        'transaction_fee_rate': 0.0,  # No fees to simplify
        'selected_assets': ['BTC/USDT', 'ETH/USDT'],  # Multiple assets
        'use_cache': True,
        'initial_capital': 10000,
        'wait_for_confirmed_signals': True,
        'use_mtpi': False,  # Disable MTPI to simplify
        'use_weighted_allocation': False,
        'weights': None,
        'enable_rebalancing': False,
        'rebalance_threshold': 0.05
    }
    
    results = {}
    
    for timing in ['candle_close', 'manual_12pm']:
        print(f"\n{'='*20} {timing.upper()} {'='*20}")
        
        tester = AllocationTester(**common_params, execution_timing=timing)
        tester.fetch_data()
        tester.calculate_scores()  # Need to calculate scores first

        # Run the strategy
        equity_curve, assets_held, mtpi_stats = tester.run_strategy_with_detailed_logging()
        
        # Get the allocation history
        allocation_history = tester.allocation_history
        
        print(f"\nResults for {timing}:")
        print(f"  Initial capital: ${tester.initial_capital:,.2f}")
        print(f"  Final equity: ${equity_curve.iloc[-1]:,.2f}")
        print(f"  Total return: {((equity_curve.iloc[-1] / tester.initial_capital) - 1) * 100:.4f}%")
        print(f"  Number of allocation decisions: {len(allocation_history)}")
        
        # Show first few allocation decisions
        print(f"\nFirst 5 allocation decisions:")
        for i, alloc in enumerate(allocation_history[:5]):
            print(f"  {i+1}. {alloc['date'].date()}: Holdings={alloc['current_holdings']}, Return={alloc['portfolio_return']:.6f}, Equity=${alloc['equity_value']:.2f}")
        
        # Store results
        results[timing] = {
            'equity_curve': equity_curve,
            'final_equity': equity_curve.iloc[-1],
            'total_return': ((equity_curve.iloc[-1] / tester.initial_capital) - 1) * 100,
            'allocation_history': allocation_history
        }
    
    # Compare the results
    print(f"\n{'='*20} COMPARISON {'='*20}")
    auto_return = results['candle_close']['total_return']
    manual_return = results['manual_12pm']['total_return']
    difference = manual_return - auto_return
    
    print(f"Automatic execution return: {auto_return:.6f}%")
    print(f"Manual execution return: {manual_return:.6f}%")
    print(f"Difference: {difference:.6f}%")
    
    if abs(difference) < 0.001:
        print("❌ PROBLEM: Returns are essentially identical!")
        
        # Compare allocation histories day by day
        print("\nComparing allocation decisions day by day:")
        auto_allocs = results['candle_close']['allocation_history']
        manual_allocs = results['manual_12pm']['allocation_history']
        
        for i in range(min(len(auto_allocs), len(manual_allocs), 10)):  # First 10 days
            auto = auto_allocs[i]
            manual = manual_allocs[i]
            
            print(f"  Day {i+1} ({auto['date'].date()}):")
            print(f"    Auto:   Holdings={auto['current_holdings']}, Return={auto['portfolio_return']:.6f}, Equity=${auto['equity_value']:.2f}")
            print(f"    Manual: Holdings={manual['current_holdings']}, Return={manual['portfolio_return']:.6f}, Equity=${manual['equity_value']:.2f}")
            print(f"    Diff:   Return={manual['portfolio_return'] - auto['portfolio_return']:.6f}, Equity=${manual['equity_value'] - auto['equity_value']:.2f}")
            
            if auto['current_holdings'] != manual['current_holdings']:
                print(f"    ⚠️  Different holdings!")
            if abs(manual['portfolio_return'] - auto['portfolio_return']) > 0.001:
                print(f"    ✅ Different returns!")
    else:
        print("✅ SUCCESS: Returns are different!")
    
    return results

if __name__ == "__main__":
    debug_strategy_execution()
