#!/usr/bin/env python3
"""
PGO Signal Generation Comparison Debug Script

This script compares the two different PGO signal generation methods:
1. MTPI_signal_handler.generate_pgo_signal() - State-based with direct comparison
2. indicators.custom_indicators.calculate_pgo_signal() - Crossover-based detection

It analyzes the discrepancies between them to understand the fundamental differences.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import both PGO signal generation methods
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from src.indicators.custom_indicators import calculate_pgo_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_ratio_ohlcv(df_a: pd.DataFrame, df_b: pd.DataFrame) -> pd.DataFrame:
    """Create OHLCV data for the ratio between two assets."""
    # Align dataframes by common index
    common_index = df_a.index.intersection(df_b.index)
    df_a_aligned = df_a.reindex(common_index)
    df_b_aligned = df_b.reindex(common_index)
    
    # Calculate ratio OHLCV
    ratio_df = pd.DataFrame(index=common_index)
    ratio_df['open'] = df_a_aligned['open'] / df_b_aligned['open']
    ratio_df['high'] = df_a_aligned['high'] / df_b_aligned['high']
    ratio_df['low'] = df_a_aligned['low'] / df_b_aligned['low']
    ratio_df['close'] = df_a_aligned['close'] / df_b_aligned['close']
    ratio_df['volume'] = df_a_aligned['volume']  # Use volume from asset A
    
    return ratio_df.dropna()

def compare_pgo_signals(
    ratio_df: pd.DataFrame,
    ratio_name: str,
    pgo_length: int = 35,
    upper_threshold: float = 1.1,
    lower_threshold: float = -0.58
) -> Dict:
    """
    Compare the two PGO signal generation methods and analyze discrepancies.
    
    Returns:
        Dictionary with comparison results and statistics
    """
    print(f"\n" + "="*100)
    print(f"COMPARING PGO SIGNAL METHODS FOR {ratio_name}")
    print("="*100)
    
    # Method 1: MTPI_signal_handler (State-based)
    print("🔄 Method 1: MTPI_signal_handler.generate_pgo_signal() [State-based]")
    pgo_values_mtpi = calculate_pgo(ratio_df, length=pgo_length)
    signal_mtpi = generate_pgo_signal(
        df=ratio_df,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )
    
    # Method 2: custom_indicators (Crossover-based)
    print("🔄 Method 2: custom_indicators.calculate_pgo_signal() [Crossover-based]")

    # First calculate PGO values using the crossover method's internal calculation
    from src.indicators.base_indicators import calculate_sma, calculate_atr

    # Calculate PGO the same way as crossover method
    sma_crossover = calculate_sma(ratio_df, length=pgo_length, close_col='close')
    atr_crossover = calculate_atr(ratio_df, length=pgo_length, high_col='high', low_col='low', close_col='close')
    atr_safe = atr_crossover.replace(0, np.nan)
    pgo_values_crossover = (ratio_df['close'] - sma_crossover) / atr_safe

    signal_crossover = calculate_pgo_signal(
        df=ratio_df,
        length=pgo_length,
        long_threshold=upper_threshold,
        short_threshold=lower_threshold
    )
    
    # Convert signals to binary for comparison (1 for long, 0 for not long)
    binary_mtpi = (signal_mtpi == 1).astype(int)
    binary_crossover = signal_crossover.astype(int)
    
    # Find discrepancies
    discrepancies = binary_mtpi != binary_crossover
    num_discrepancies = discrepancies.sum()
    total_signals = len(binary_mtpi)
    discrepancy_rate = (num_discrepancies / total_signals) * 100
    
    # Analyze signal statistics
    mtpi_long_count = binary_mtpi.sum()
    crossover_long_count = binary_crossover.sum()
    mtpi_long_pct = (mtpi_long_count / total_signals) * 100
    crossover_long_pct = (crossover_long_count / total_signals) * 100
    
    print(f"\n📊 SIGNAL STATISTICS:")
    print(f"Total signals: {total_signals}")
    print(f"MTPI Long signals: {mtpi_long_count} ({mtpi_long_pct:.1f}%)")
    print(f"Crossover Long signals: {crossover_long_count} ({crossover_long_pct:.1f}%)")
    print(f"Discrepancies: {num_discrepancies} ({discrepancy_rate:.1f}%)")
    
    # Check if PGO values are identical
    pgo_diff = (pgo_values_mtpi - pgo_values_crossover).abs()
    max_pgo_diff = pgo_diff.max()
    mean_pgo_diff = pgo_diff.mean()

    print(f"\n🔍 PGO VALUES COMPARISON:")
    print(f"Max PGO difference: {max_pgo_diff:.10f}")
    print(f"Mean PGO difference: {mean_pgo_diff:.10f}")
    if max_pgo_diff < 1e-10:
        print("✅ PGO values are identical between methods")
    else:
        print("⚠️ PGO values differ between methods!")

    # Show last 20 values for detailed comparison
    print(f"\n🔍 LAST 20 SIGNAL COMPARISONS:")
    print(f"{'Date':<12} {'MTPI PGO':<12} {'Cross PGO':<12} {'MTPI':<6} {'Cross':<6} {'Match':<6}")
    print("-" * 80)

    last_20_indices = ratio_df.index[-20:]
    for i, date in enumerate(last_20_indices):
        idx = len(ratio_df) - 20 + i
        pgo_mtpi = pgo_values_mtpi.iloc[idx] if idx < len(pgo_values_mtpi) else np.nan
        pgo_cross = pgo_values_crossover.iloc[idx] if idx < len(pgo_values_crossover) else np.nan
        mtpi_sig = binary_mtpi.iloc[idx] if idx < len(binary_mtpi) else 0
        cross_sig = binary_crossover.iloc[idx] if idx < len(binary_crossover) else 0
        match = "✅" if mtpi_sig == cross_sig else "❌"

        print(f"{date.strftime('%Y-%m-%d'):<12} {pgo_mtpi:+8.4f}    {pgo_cross:+8.4f}     {mtpi_sig:<6} {cross_sig:<6} {match:<6}")
    
    # Find transition points where methods disagree
    print(f"\n🚨 DISCREPANCY ANALYSIS:")
    if num_discrepancies > 0:
        discrepancy_dates = ratio_df.index[discrepancies]
        print(f"First 10 discrepancy dates:")
        print(f"{'Date':<12} {'MTPI PGO':<12} {'Cross PGO':<12} {'MTPI':<6} {'Cross':<6}")
        print("-" * 70)
        for i, date in enumerate(discrepancy_dates[:10]):
            idx = ratio_df.index.get_loc(date)
            pgo_mtpi = pgo_values_mtpi.iloc[idx]
            pgo_cross = pgo_values_crossover.iloc[idx]
            mtpi_sig = binary_mtpi.iloc[idx]
            cross_sig = binary_crossover.iloc[idx]
            print(f"{date.strftime('%Y-%m-%d'):<12} {pgo_mtpi:+8.4f}    {pgo_cross:+8.4f}     {mtpi_sig:<6} {cross_sig:<6}")
    else:
        print("No discrepancies found!")
    
    # Analyze around threshold boundaries
    print(f"\n🎯 THRESHOLD BOUNDARY ANALYSIS:")
    near_upper = (pgo_values_mtpi > (upper_threshold - 0.2)) & (pgo_values_mtpi < (upper_threshold + 0.2))
    near_lower = (pgo_values_mtpi > (lower_threshold - 0.2)) & (pgo_values_mtpi < (lower_threshold + 0.2))
    
    upper_boundary_discrepancies = discrepancies & near_upper
    lower_boundary_discrepancies = discrepancies & near_lower
    
    print(f"Discrepancies near upper threshold ({upper_threshold}): {upper_boundary_discrepancies.sum()}")
    print(f"Discrepancies near lower threshold ({lower_threshold}): {lower_boundary_discrepancies.sum()}")
    
    return {
        'ratio_name': ratio_name,
        'total_signals': total_signals,
        'discrepancies': num_discrepancies,
        'discrepancy_rate': discrepancy_rate,
        'mtpi_long_count': mtpi_long_count,
        'crossover_long_count': crossover_long_count,
        'mtpi_long_pct': mtpi_long_pct,
        'crossover_long_pct': crossover_long_pct,
        'pgo_values_mtpi': pgo_values_mtpi,
        'pgo_values_crossover': pgo_values_crossover,
        'pgo_max_diff': max_pgo_diff,
        'pgo_mean_diff': mean_pgo_diff,
        'signal_mtpi': binary_mtpi,
        'signal_crossover': binary_crossover,
        'discrepancy_mask': discrepancies
    }

def main():
    """Main function to run the PGO signal comparison."""
    parser = argparse.ArgumentParser(description='Compare PGO signal generation methods')
    parser.add_argument('--asset-a', default='TRX', help='First asset symbol (default: TRX)')
    parser.add_argument('--asset-b', default='BTC', help='Second asset symbol (default: BTC)')
    parser.add_argument('--quote', default='USDT', help='Quote currency (default: USDT)')
    parser.add_argument('--start-date', default='2025-02-10', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--pgo-length', type=int, default=35, help='PGO length (default: 35)')
    parser.add_argument('--upper-threshold', type=float, default=1.1, help='Upper threshold (default: 1.1)')
    parser.add_argument('--lower-threshold', type=float, default=-0.58, help='Lower threshold (default: -0.58)')
    
    args = parser.parse_args()
    
    # Configuration
    asset_a = f"{args.asset_a}/{args.quote}"
    asset_b = f"{args.asset_b}/{args.quote}"
    ratio_name = f"{args.asset_a}/{args.asset_b}"
    
    print(f"🔍 PGO SIGNAL GENERATION COMPARISON")
    print(f"="*80)
    print(f"Ratio: {ratio_name}")
    print(f"PGO Length: {args.pgo_length}")
    print(f"Upper Threshold: {args.upper_threshold}")
    print(f"Lower Threshold: {args.lower_threshold}")
    print(f"Start Date: {args.start_date}")
    
    # Fetch data
    print(f"\n📊 Fetching data...")
    try:
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=[asset_a, asset_b],
            timeframe='1d',
            limit=500  # Fetch enough data for analysis
        )
        
        if not data_dict or asset_a not in data_dict or asset_b not in data_dict:
            print(f"ERROR: Failed to fetch data for {asset_a} and {asset_b}")
            return
            
        print(f"✅ Successfully fetched data for {asset_a} and {asset_b}")
        
        # Create ratio OHLCV
        ratio_df = create_ratio_ohlcv(data_dict[asset_a], data_dict[asset_b])
        print(f"✅ Created ratio data: {len(ratio_df)} candles")
        
        # Compare signal generation methods
        comparison_results = compare_pgo_signals(
            ratio_df=ratio_df,
            ratio_name=ratio_name,
            pgo_length=args.pgo_length,
            upper_threshold=args.upper_threshold,
            lower_threshold=args.lower_threshold
        )
        
        # Summary
        print(f"\n" + "="*100)
        print("🎯 COMPARISON SUMMARY")
        print("="*100)
        print(f"Ratio analyzed: {ratio_name}")
        print(f"Total signals compared: {comparison_results['total_signals']}")
        print(f"\n📊 PGO VALUES COMPARISON:")
        print(f"Max PGO difference: {comparison_results['pgo_max_diff']:.10f}")
        print(f"Mean PGO difference: {comparison_results['pgo_mean_diff']:.10f}")
        if comparison_results['pgo_max_diff'] < 1e-10:
            print("✅ PGO calculation methods are identical")
        else:
            print("⚠️ PGO calculation methods differ!")

        print(f"\n📈 SIGNAL GENERATION COMPARISON:")
        print(f"Signal discrepancies: {comparison_results['discrepancies']} ({comparison_results['discrepancy_rate']:.1f}%)")
        print(f"MTPI method long signals: {comparison_results['mtpi_long_count']} ({comparison_results['mtpi_long_pct']:.1f}%)")
        print(f"Crossover method long signals: {comparison_results['crossover_long_count']} ({comparison_results['crossover_long_pct']:.1f}%)")

        if comparison_results['discrepancy_rate'] > 0:
            print(f"\n⚠️  CONCLUSION: Signal generation methods show {comparison_results['discrepancy_rate']:.1f}% disagreement!")
            print("This confirms there are fundamental differences in signal generation logic.")
        else:
            print(f"\n✅ CONCLUSION: Signal generation methods are identical!")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
