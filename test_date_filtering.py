#!/usr/bin/env python3
"""
Test script to verify date filtering is working correctly in the DWMA indicator.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data
import pandas as pd
from datetime import datetime

def test_date_filtering():
    """Test that date filtering works correctly."""
    print("=" * 60)
    print("Date Filtering Test")
    print("=" * 60)
    
    # Test different start dates
    test_dates = [
        '2023-10-19',  # Your requested date
        '2024-01-01',  # New Year
        '2024-06-01',  # Mid-year
    ]
    
    for start_date in test_dates:
        print(f"\n--- Testing start date: {start_date} ---")
        
        try:
            # Fetch data with date filtering
            data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=['BTC/USDT'],
                timeframe='1d',
                since=start_date  # Pass as string for proper filtering
            )
            
            if 'BTC/USDT' not in data_dict:
                print(f"❌ Failed to fetch BTC data for {start_date}")
                continue
            
            btc_df = data_dict['BTC/USDT']
            
            # Check the actual date range
            actual_start = btc_df.index[0].date()
            actual_end = btc_df.index[-1].date()
            expected_start = pd.to_datetime(start_date).date()
            
            print(f"Expected start: {expected_start}")
            print(f"Actual start:   {actual_start}")
            print(f"Actual end:     {actual_end}")
            print(f"Total candles:  {len(btc_df)}")
            
            # Verify the filtering worked
            if actual_start >= expected_start:
                print(f"✅ Date filtering working correctly")
            else:
                print(f"❌ Date filtering failed - data starts before {start_date}")
                
        except Exception as e:
            print(f"❌ Error testing {start_date}: {e}")
    
    print("\n" + "=" * 60)
    print("Date Filtering Test Completed")
    print("=" * 60)

if __name__ == "__main__":
    test_date_filtering()
