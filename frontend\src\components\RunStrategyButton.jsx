import React, { useState } from 'react';
import axios from 'axios';

const RunStrategyButton = ({ onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const runStrategy = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Running strategy with MTPI signal...');

      const response = await axios.post('/api/run-strategy', {
        use_mtpi_signal: true,
        mtpi_timeframe: '1d',
        timeframe: '1d',
        analysis_start_date: '2023-10-20',
        transaction_fee_rate: 0.001
      });

      console.log('Strategy run successful:', response.data);

      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }

      setLoading(false);
    } catch (err) {
      console.error('Error running strategy:', err);
      setError(err.response?.data?.error || err.message || 'An error occurred');
      setLoading(false);
    }
  };

  return (
    <div style={{ marginBottom: '20px', textAlign: 'center' }}>
      <button
        onClick={runStrategy}
        disabled={loading}
        style={{
          padding: '12px 24px',
          backgroundColor: loading ? '#cccccc' : '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          fontWeight: 'bold',
          fontSize: '16px',
          width: '300px',
          maxWidth: '100%'
        }}
      >
        {loading ? 'Running Strategy...' : 'Run Strategy with MTPI Signal'}
      </button>

      {error && (
        <div style={{ color: 'red', marginTop: '10px' }}>
          Error: {error}
        </div>
      )}
    </div>
  );
};

export default RunStrategyButton;
