# Cryptocurrency RSI Visualizer

This set of Python scripts allows you to visualize cryptocurrency data with RSI (Relative Strength Index) indicators for debugging and analysis purposes. The scripts are designed to help you compare different cryptocurrencies, particularly BTCUSDT and SOLUSDT.

## Scripts Overview

1. **crypto_rsi_visualizer.py**: Basic script that fetches cryptocurrency data from Binance and displays price charts with RSI indicators.

2. **crypto_rsi_comparison_advanced.py**: Advanced visualization that includes normalized price comparison and relative strength analysis between two cryptocurrencies.

3. **crypto_rsi_from_csv.py**: Loads data from CSV files instead of fetching from an exchange, useful for offline analysis or when working with specific historical data.

## Requirements

- Python 3.6+
- Required packages:
  - pandas
  - numpy
  - matplotlib
  - ccxt (for exchange data fetching)

Install the required packages using:

```bash
pip install pandas numpy matplotlib ccxt
```

## Usage

### Basic RSI Visualization

```bash
python crypto_rsi_visualizer.py
```

This will fetch the last 100 daily candles for BTCUSDT and SOLUSDT from Binance, calculate RSI with a 14-period length, and save the visualization to `crypto_rsi_comparison.png`.

### Advanced RSI Comparison

```bash
python crypto_rsi_comparison_advanced.py
```

This will create an advanced visualization that includes:
- Individual price charts for both assets
- RSI indicators for both assets
- Normalized price comparison (base 100)
- RSI comparison
- Relative strength analysis (ratio of prices)

The visualization will be saved to `crypto_rsi_advanced_comparison.png`.

### Visualization from CSV Files

```bash
python crypto_rsi_from_csv.py --file1 path/to/btc_data.csv --file2 path/to/sol_data.csv --symbol1 BTC/USDT --symbol2 SOL/USDT --output output.png
```

Command line arguments:
- `--file1`: Path to the first CSV file
- `--file2`: Path to the second CSV file
- `--symbol1`: Symbol for the first asset (default: BTC/USDT)
- `--symbol2`: Symbol for the second asset (default: SOL/USDT)
- `--date-col`: Name of the date column in the CSV (default: timestamp)
- `--date-format`: Format of the date column (e.g., %Y-%m-%d)
- `--rsi-length`: RSI calculation period (default: 14)
- `--output`: Output file path for the plot

## CSV File Format

The CSV files should contain at least the following columns:
- `timestamp` (or the column specified by `--date-col`): Date/time of the candle
- `open`: Opening price
- `high`: Highest price
- `low`: Lowest price
- `close`: Closing price
- `volume` (optional): Trading volume

## Customization

You can modify the scripts to:
- Change the exchange (default is Binance)
- Adjust the timeframe (default is daily '1d')
- Change the RSI period length (default is 14)
- Add additional technical indicators
- Modify the visualization layout and style

## Example Output

The visualization includes:
1. Price charts for each cryptocurrency
2. RSI indicators with overbought (70) and oversold (30) levels
3. Normalized price comparison to see relative performance
4. Relative strength analysis to determine which asset is stronger

## Debugging with debug_matrices

If you have debug_matrices CSV files, you can use the `crypto_rsi_from_csv.py` script to analyze them. These files typically contain comparison matrices between different assets.

## License

This code is provided for educational and debugging purposes.
