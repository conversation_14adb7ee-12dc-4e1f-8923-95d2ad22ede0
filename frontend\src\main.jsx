import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { ChakraProvider, ColorModeScript } from '@chakra-ui/react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from './App'
import theme from './theme'
import { DashboardProvider } from './context/DashboardContext'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnMount: true,        // Refetch when component mounts
      refetchOnReconnect: true,    // Refetch when reconnecting
      retry: 1,                    // Retry failed requests once
      staleTime: 60000,            // Consider data stale after 1 minute
      cacheTime: 300000,           // Cache data for 5 minutes
      onError: (error) => {
        console.error('Global query error:', error);
      },
    },
    mutations: {
      onError: (error) => {
        console.error('Global mutation error:', error);
      },
    },
  },
})

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    {/* ColorModeScript initializes the color mode before the app renders */}
    <ColorModeScript initialColorMode={theme.config.initialColorMode} />
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <DashboardProvider>
            <App />
          </DashboardProvider>
        </ChakraProvider>
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
