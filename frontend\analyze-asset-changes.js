// Script to analyze asset changes in detail
import fs from 'fs/promises';

// Format date for better readability
function formatDate(timestamp) {
  return new Date(timestamp * 1000).toISOString().split('T')[0];
}

async function analyzeAssetChanges() {
  try {
    console.log('Loading backend data from backend-data.json...');
    const backendDataRaw = await fs.readFile('backend-data.json', 'utf8');
    const backendData = JSON.parse(backendDataRaw);
    
    console.log('\n=== ASSET CHANGES ANALYSIS ===');
    
    if (!backendData.assetChanges || backendData.assetChanges.length === 0) {
      console.log('No asset changes found in backend data');
      return;
    }
    
    // Sort asset changes by date
    const sortedAssetChanges = [...backendData.assetChanges].sort((a, b) => a.date - b.date);
    
    console.log(`Found ${sortedAssetChanges.length} asset changes in backend data`);
    
    // Find the strategy start date
    let strategyStartDate = null;
    if (backendData.curves && backendData.curves.strategy && backendData.curves.strategy.length > 0) {
      strategyStartDate = backendData.curves.strategy[0].time;
      console.log(`Strategy start date: ${formatDate(strategyStartDate)}`);
    }
    
    // Filter asset changes to only include those after the strategy start date
    let relevantAssetChanges = sortedAssetChanges;
    if (strategyStartDate) {
      relevantAssetChanges = sortedAssetChanges.filter(change => change.date >= strategyStartDate);
      console.log(`Filtered to ${relevantAssetChanges.length} asset changes after strategy start date`);
    }
    
    // Print all relevant asset changes
    console.log('\nRelevant asset changes:');
    relevantAssetChanges.forEach((change, i) => {
      console.log(`  ${i+1}. ${formatDate(change.date)}: ${change.asset || 'out-of-market'}`);
    });
    
    // Check for MTPI signals
    if (backendData.metadata && backendData.metadata.rawMtpiSignals) {
      const mtpiSignals = backendData.metadata.rawMtpiSignals;
      console.log(`\nFound ${mtpiSignals.length} MTPI signals in backend data`);
      
      // Sort MTPI signals by time
      const sortedMtpiSignals = [...mtpiSignals].sort((a, b) => a.time - b.time);
      
      // Filter MTPI signals to only include those after the strategy start date
      let relevantMtpiSignals = sortedMtpiSignals;
      if (strategyStartDate) {
        relevantMtpiSignals = sortedMtpiSignals.filter(signal => signal.time >= strategyStartDate);
        console.log(`Filtered to ${relevantMtpiSignals.length} MTPI signals after strategy start date`);
      }
      
      // Count signal distribution
      const bullish = relevantMtpiSignals.filter(s => s.value === 1).length;
      const neutral = relevantMtpiSignals.filter(s => s.value === 0).length;
      const bearish = relevantMtpiSignals.filter(s => s.value === -1).length;
      
      console.log(`Signal distribution: Bullish=${bullish} (${(bullish/relevantMtpiSignals.length*100).toFixed(1)}%), Neutral=${neutral} (${(neutral/relevantMtpiSignals.length*100).toFixed(1)}%), Bearish=${bearish} (${(bearish/relevantMtpiSignals.length*100).toFixed(1)}%)`);
      
      // Check for signal transitions
      let transitions = 0;
      for (let i = 1; i < relevantMtpiSignals.length; i++) {
        if (relevantMtpiSignals[i].value !== relevantMtpiSignals[i-1].value) {
          transitions++;
        }
      }
      console.log(`Signal transitions: ${transitions}`);
      
      // Analyze how MTPI signals affect asset changes
      console.log('\nAnalyzing how MTPI signals affect asset changes:');
      
      // Create a map of time to MTPI signal
      const mtpiMap = {};
      relevantMtpiSignals.forEach(signal => {
        mtpiMap[signal.time] = signal.value;
      });
      
      // Check each asset change against the MTPI signal at that time
      let outOfMarketChanges = 0;
      relevantAssetChanges.forEach((change, i) => {
        // Find the most recent MTPI signal before or at this asset change
        let mtpiValue = null;
        let mtpiTime = null;
        
        for (let j = relevantMtpiSignals.length - 1; j >= 0; j--) {
          if (relevantMtpiSignals[j].time <= change.date) {
            mtpiValue = relevantMtpiSignals[j].value;
            mtpiTime = relevantMtpiSignals[j].time;
            break;
          }
        }
        
        // Check if this asset change should be filtered by MTPI
        if (mtpiValue !== null && mtpiValue !== 1) {
          console.log(`  Asset change at ${formatDate(change.date)} should be filtered by MTPI (signal=${mtpiValue} from ${formatDate(mtpiTime)})`);
          outOfMarketChanges++;
        }
      });
      
      console.log(`\nFound ${outOfMarketChanges} asset changes that should be filtered by MTPI`);
      console.log(`Expected number of segments: ${relevantAssetChanges.length - outOfMarketChanges}`);
      
      // Simulate the frontend rendering process
      console.log('\nSimulating frontend rendering process:');
      
      // Create a list of rendered segments
      let currentAsset = null;
      let segmentStart = null;
      let segments = [];
      
      // Get the strategy data points
      const strategyData = backendData.curves.strategy;
      
      // Process each strategy data point
      for (let i = 0; i < strategyData.length; i++) {
        const point = strategyData[i];
        const pointTime = point.time;
        
        // Find the most recent asset change before this point
        let assetAtPoint = null;
        
        for (let j = relevantAssetChanges.length - 1; j >= 0; j--) {
          if (pointTime >= relevantAssetChanges[j].date) {
            assetAtPoint = relevantAssetChanges[j].asset;
            break;
          }
        }
        
        // Find the most recent MTPI signal before this point
        let mtpiAtPoint = null;
        
        for (let j = relevantMtpiSignals.length - 1; j >= 0; j--) {
          if (pointTime >= relevantMtpiSignals[j].time) {
            mtpiAtPoint = relevantMtpiSignals[j].value;
            break;
          }
        }
        
        // If MTPI is not bullish, we should be out of market
        if (mtpiAtPoint !== null && mtpiAtPoint !== 1) {
          assetAtPoint = 'out-of-market';
        }
        
        // Check if we need to start a new segment
        if (assetAtPoint !== currentAsset) {
          // End the previous segment if it exists
          if (currentAsset !== null && segmentStart !== null) {
            segments.push({
              asset: currentAsset,
              start: segmentStart,
              end: pointTime
            });
          }
          
          // Start a new segment
          currentAsset = assetAtPoint;
          segmentStart = pointTime;
        }
        
        // If this is the last point, end the current segment
        if (i === strategyData.length - 1 && currentAsset !== null && segmentStart !== null) {
          segments.push({
            asset: currentAsset,
            start: segmentStart,
            end: pointTime
          });
        }
      }
      
      console.log(`Simulated ${segments.length} segments`);
      
      // Print the simulated segments
      console.log('\nSimulated segments:');
      segments.forEach((segment, i) => {
        console.log(`  ${i+1}. ${formatDate(segment.start)} to ${formatDate(segment.end)}: ${segment.asset}`);
      });
      
      // Save the simulated segments to a file
      await fs.writeFile('simulated-segments.json', JSON.stringify(segments, null, 2));
      console.log('\nSimulated segments saved to simulated-segments.json');
    } else {
      console.log('No MTPI signals found in backend data');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the analysis
analyzeAssetChanges();
