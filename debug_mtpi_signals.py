#!/usr/bin/env python3
"""
Debug script to analyze why MTPI signals are returning 0 with recent start dates.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from MTPI_signal_handler import debug_mtpi_signals, fetch_historical_mtpi_signals, calculate_pgo, generate_pgo_signal
from data_fetcher import fetch_ohlcv_data
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def plot_mtpi_analysis(btc_df, pgo_values, signals, upper_threshold, lower_threshold, analysis_start):
    """
    Create a comprehensive visualization showing:
    1. Top panel: Buy & Hold performance curve
    2. Bottom panel: PGO values with threshold lines and crossover points
    """
    # Calculate buy & hold returns (normalized to start at 100)
    returns = (btc_df['close'] / btc_df['close'].iloc[0]) * 100

    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    fig.suptitle('MTPI Signal Analysis: Buy & Hold vs PGO Indicator', fontsize=16, fontweight='bold')

    # Top panel: Buy & Hold performance
    ax1.plot(btc_df.index, returns, linewidth=2, color='blue', label='BTC Buy & Hold')
    ax1.set_ylabel('Normalized Price (Start = 100)', fontsize=12)
    ax1.set_title('BTC Buy & Hold Performance', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # Add analysis start line
    analysis_start_ts = pd.Timestamp(analysis_start).tz_localize('UTC')
    if analysis_start_ts in btc_df.index:
        ax1.axvline(x=analysis_start_ts, color='red', linestyle='--', alpha=0.7, label=f'Analysis Start: {analysis_start}')
        ax1.legend()

    # Bottom panel: PGO values with thresholds
    # Plot PGO line
    ax2.plot(pgo_values.index, pgo_values, linewidth=2, color='purple', label='PGO Values')

    # Add threshold lines
    ax2.axhline(y=upper_threshold, color='green', linestyle='-', alpha=0.8, linewidth=2, label=f'Upper Threshold ({upper_threshold})')
    ax2.axhline(y=lower_threshold, color='red', linestyle='-', alpha=0.8, linewidth=2, label=f'Lower Threshold ({lower_threshold})')

    # Fill threshold zones
    ax2.fill_between(pgo_values.index, upper_threshold, pgo_values.max() * 1.1, alpha=0.2, color='green', label='Bullish Zone')
    ax2.fill_between(pgo_values.index, lower_threshold, pgo_values.min() * 1.1, alpha=0.2, color='red', label='Bearish Zone')
    ax2.fill_between(pgo_values.index, lower_threshold, upper_threshold, alpha=0.1, color='gray', label='Neutral Zone')

    # Mark crossover points
    for i in range(1, len(pgo_values)):
        if not pd.isna(pgo_values.iloc[i-1]) and not pd.isna(pgo_values.iloc[i]):
            prev_pgo = pgo_values.iloc[i-1]
            curr_pgo = pgo_values.iloc[i]
            date = pgo_values.index[i]

            # Bullish crossover (crosses above upper threshold)
            if prev_pgo <= upper_threshold and curr_pgo > upper_threshold:
                ax2.scatter(date, curr_pgo, color='green', s=100, marker='^', zorder=5, label='Bullish Crossover' if i == 1 else "")
                ax2.annotate(f'↑ {curr_pgo:.2f}', (date, curr_pgo), xytext=(5, 10), textcoords='offset points', fontsize=8, color='green')

            # Bearish crossover (crosses below lower threshold)
            elif prev_pgo >= lower_threshold and curr_pgo < lower_threshold:
                ax2.scatter(date, curr_pgo, color='red', s=100, marker='v', zorder=5, label='Bearish Crossover' if i == 1 else "")
                ax2.annotate(f'↓ {curr_pgo:.2f}', (date, curr_pgo), xytext=(5, -15), textcoords='offset points', fontsize=8, color='red')

    # Add analysis start line
    if analysis_start_ts in pgo_values.index:
        ax2.axvline(x=analysis_start_ts, color='red', linestyle='--', alpha=0.7)
        pgo_at_start = pgo_values.loc[analysis_start_ts]
        ax2.scatter(analysis_start_ts, pgo_at_start, color='red', s=150, marker='o', zorder=5)
        ax2.annotate(f'Start: {pgo_at_start:.2f}', (analysis_start_ts, pgo_at_start), xytext=(10, 10), textcoords='offset points', fontsize=10, color='red', fontweight='bold')

    ax2.set_ylabel('PGO Value', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.set_title('MTPI PGO Indicator with Thresholds', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # Format x-axis
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # Add statistics text box
    stats_text = f"""PGO Statistics:
Min: {pgo_values.min():.2f}
Max: {pgo_values.max():.2f}
Mean: {pgo_values.mean():.2f}
Median: {pgo_values.median():.2f}

Thresholds:
Upper: {upper_threshold}
Lower: {lower_threshold}

Above Upper: {(pgo_values > upper_threshold).sum()} ({(pgo_values > upper_threshold).sum()/len(pgo_values)*100:.1f}%)
Below Lower: {(pgo_values < lower_threshold).sum()} ({(pgo_values < lower_threshold).sum()/len(pgo_values)*100:.1f}%)"""

    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=9, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.show()

    print(f"✅ Visualization created! The chart shows:")
    print(f"   • Top panel: BTC buy & hold performance")
    print(f"   • Bottom panel: PGO values with threshold lines")
    print(f"   • Green zone: Bullish (above {upper_threshold})")
    print(f"   • Red zone: Bearish (below {lower_threshold})")
    print(f"   • Gray zone: Neutral (between thresholds)")
    print(f"   • Crossover points marked with arrows")

def analyze_mtpi_issue():
    """
    Analyze why MTPI signals are returning 0 with recent start dates.
    """
    print("=" * 80)
    print("ANALYZING MTPI SIGNAL ISSUE")
    print("=" * 80)

    # Parameters from the failing run
    timeframe = '1d'
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    analysis_start = '2025-05-28'

def analyze_flat_line_period():
    """
    Analyze the specific flat line period from 2022-10-26 to 2022-11-08
    that should have been bullish but shows flat performance.
    """
    print("=" * 80)
    print("ANALYZING FLAT LINE PERIOD: 2022-10-26 to 2022-11-08")
    print("=" * 80)

    # Parameters for the problematic period
    timeframe = '1d'
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58

    # Focus on the problematic period
    problem_start = '2022-10-26'
    problem_end = '2022-11-08'

    # Fetch data starting earlier to ensure we have enough warmup
    fetch_start = '2022-09-01'  # Start earlier for warmup

    print(f"Parameters:")
    print(f"  Timeframe: {timeframe}")
    print(f"  Length: {length}")
    print(f"  Upper threshold: {upper_threshold}")
    print(f"  Lower threshold: {lower_threshold}")
    print(f"  Problem period: {problem_start} to {problem_end}")
    print(f"  Fetch start: {fetch_start}")

    try:
        # Fetch BTC data for the period
        print(f"\nFetching BTC data from {fetch_start}...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe=timeframe,
            since=fetch_start,
            limit=2000,
            use_cache=False,
            force_refresh=True
        )

        if not data_dict or 'BTC/USDT' not in data_dict:
            print("ERROR: Failed to fetch BTC data")
            return

        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")

        # Calculate PGO values
        print(f"\nCalculating PGO values...")
        pgo_values = calculate_pgo(btc_df, length=length)

        # Generate signals with and without warmup
        print(f"Generating signals...")
        signals_with_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=True
        )

        signals_without_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False
        )

        # Filter to the problem period
        problem_start_dt = pd.to_datetime(problem_start).tz_localize('UTC')
        problem_end_dt = pd.to_datetime(problem_end).tz_localize('UTC')

        # Find the indices for the problem period
        problem_mask = (btc_df.index >= problem_start_dt) & (btc_df.index <= problem_end_dt)
        problem_btc = btc_df[problem_mask]
        problem_pgo = pgo_values[problem_mask]
        problem_signals_skip = signals_with_skip[problem_mask]
        problem_signals_no_skip = signals_without_skip[problem_mask]

        print(f"\nPROBLEM PERIOD ANALYSIS ({problem_start} to {problem_end}):")
        print(f"Found {len(problem_btc)} candles in problem period")

        if len(problem_btc) == 0:
            print("ERROR: No data found for the problem period")
            return

        # Analyze BTC price movement during this period
        start_price = problem_btc['close'].iloc[0]
        end_price = problem_btc['close'].iloc[-1]
        price_change = ((end_price - start_price) / start_price) * 100

        print(f"\nBTC Price Analysis:")
        print(f"  Start price: ${start_price:,.2f}")
        print(f"  End price: ${end_price:,.2f}")
        print(f"  Price change: {price_change:+.2f}%")
        print(f"  Min price: ${problem_btc['close'].min():,.2f}")
        print(f"  Max price: ${problem_btc['close'].max():,.2f}")

        # Analyze PGO values during this period
        print(f"\nPGO Analysis for Problem Period:")
        print(f"  Min PGO: {problem_pgo.min():.4f}")
        print(f"  Max PGO: {problem_pgo.max():.4f}")
        print(f"  Mean PGO: {problem_pgo.mean():.4f}")
        print(f"  Median PGO: {problem_pgo.median():.4f}")
        print(f"  Std Dev: {problem_pgo.std():.4f}")

        # Check if PGO values are within thresholds
        above_upper = (problem_pgo > upper_threshold).sum()
        below_lower = (problem_pgo < lower_threshold).sum()
        in_neutral = ((problem_pgo >= lower_threshold) & (problem_pgo <= upper_threshold)).sum()

        print(f"\nPGO Threshold Analysis:")
        print(f"  Above upper threshold ({upper_threshold}): {above_upper} candles")
        print(f"  Below lower threshold ({lower_threshold}): {below_lower} candles")
        print(f"  In neutral zone: {in_neutral} candles")

        # Analyze signals during this period
        print(f"\nSignal Analysis (with warmup skip):")
        signal_counts_skip = problem_signals_skip.value_counts()
        for signal, count in signal_counts_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(problem_signals_skip)*100:.1f}%)")

        print(f"\nSignal Analysis (without warmup skip):")
        signal_counts_no_skip = problem_signals_no_skip.value_counts()
        for signal, count in signal_counts_no_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(problem_signals_no_skip)*100:.1f}%)")

        # Detailed day-by-day analysis
        print(f"\nDETAILED DAY-BY-DAY ANALYSIS:")
        print(f"{'Date':<12} {'Close':<10} {'PGO':<10} {'Skip':<6} {'NoSkip':<8} {'Note'}")
        print("-" * 65)

        for i, (date, row) in enumerate(problem_btc.iterrows()):
            close_price = row['close']
            pgo_val = problem_pgo.iloc[i]
            skip_signal = problem_signals_skip.iloc[i]
            no_skip_signal = problem_signals_no_skip.iloc[i]

            # Add notes for significant events
            note = ""
            if i == 0:
                note = "START"
            elif i == len(problem_btc) - 1:
                note = "END"
            elif abs(pgo_val) > 1.0:
                note = "HIGH PGO"
            elif skip_signal != 0:
                note = "SIGNAL!"

            print(f"{date.strftime('%Y-%m-%d'):<12} ${close_price:<9.2f} {pgo_val:<10.4f} {skip_signal:<6} {no_skip_signal:<8} {note}")

        # Check for crossovers during this period
        print(f"\nCROSSOVER ANALYSIS:")
        crossovers_up = 0
        crossovers_down = 0

        for i in range(1, len(problem_pgo)):
            if not pd.isna(problem_pgo.iloc[i-1]) and not pd.isna(problem_pgo.iloc[i]):
                prev_pgo = problem_pgo.iloc[i-1]
                curr_pgo = problem_pgo.iloc[i]
                date = problem_pgo.index[i]

                # Check for crossovers
                if prev_pgo <= upper_threshold and curr_pgo > upper_threshold:
                    crossovers_up += 1
                    print(f"  BULLISH crossover on {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} -> {curr_pgo:.4f}")
                elif prev_pgo >= lower_threshold and curr_pgo < lower_threshold:
                    crossovers_down += 1
                    print(f"  BEARISH crossover on {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} -> {curr_pgo:.4f}")

        print(f"\nTotal crossovers in problem period:")
        print(f"  Bullish crossovers: {crossovers_up}")
        print(f"  Bearish crossovers: {crossovers_down}")

        if crossovers_up == 0 and crossovers_down == 0:
            print(f"\n⚠️  NO CROSSOVERS DETECTED IN PROBLEM PERIOD!")
            print(f"This explains the flat line - PGO stayed within neutral zone.")

            # Suggest what might have caused this
            print(f"\n💡 POSSIBLE CAUSES:")
            print(f"1. Market was in a consolidation phase with low volatility")
            print(f"2. PGO thresholds may be too wide for this market condition")
            print(f"3. The 35-period length may be too long for detecting shorter-term moves")

            # Check if we need different thresholds
            pgo_range = problem_pgo.max() - problem_pgo.min()
            print(f"4. PGO range during period: {pgo_range:.4f} (may need tighter thresholds)")

        # Compare with broader period to see if this was unusual
        print(f"\nCOMPARISON WITH BROADER PERIOD:")

        # Get 30 days before and after the problem period
        broader_start = problem_start_dt - pd.Timedelta(days=30)
        broader_end = problem_end_dt + pd.Timedelta(days=30)
        broader_mask = (btc_df.index >= broader_start) & (btc_df.index <= broader_end)
        broader_pgo = pgo_values[broader_mask]
        broader_signals = signals_with_skip[broader_mask]

        print(f"Broader period PGO stats (±30 days):")
        print(f"  Min: {broader_pgo.min():.4f}")
        print(f"  Max: {broader_pgo.max():.4f}")
        print(f"  Range: {broader_pgo.max() - broader_pgo.min():.4f}")

        broader_signal_counts = broader_signals.value_counts()
        print(f"Broader period signal distribution:")
        for signal, count in broader_signal_counts.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name}: {count/len(broader_signals)*100:.1f}%")

    except Exception as e:
        print(f"Error in analyze_flat_line_period: {e}")
        import traceback
        traceback.print_exc()
    
    # Calculate fetch start date - use more days for recent dates to ensure crossovers
    from datetime import datetime, timedelta
    analysis_date = datetime.strptime(analysis_start, '%Y-%m-%d')

    # For recent dates, we need more history to capture crossovers
    # Let's test with 120 days instead of 60
    warmup_days = 60
    mtpi_fetch_start_date = (analysis_date - timedelta(days=warmup_days)).strftime('%Y-%m-%d')

    print(f"Analysis start: {analysis_start}")
    print(f"MTPI fetch start: {mtpi_fetch_start_date} ({warmup_days} days before analysis)")

    # Fetch BTC data using the same approach as main program
    print(f"\nFetching BTC data...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDC'],
        timeframe=timeframe,
        limit=365,
        since=mtpi_fetch_start_date,  # Use the calculated start date
        use_cache=True
    )
    
    if not data_dict or 'BTC/USDC' not in data_dict:
        print("ERROR: Failed to fetch BTC data")
        return
    
    btc_df = data_dict['BTC/USDC']
    print(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")

    # Debug: Check data distribution by month
    print(f"\nData distribution by month:")
    for month in btc_df.index.to_period('M').unique():
        month_data = btc_df[btc_df.index.to_period('M') == month]
        print(f"  {month}: {len(month_data)} candles ({month_data.index[0].strftime('%Y-%m-%d')} to {month_data.index[-1].strftime('%Y-%m-%d')})")

    # Debug: Check if we have data before May
    may_start = pd.Timestamp('2025-05-01').tz_localize('UTC')
    before_may = btc_df[btc_df.index < may_start]
    print(f"\nData before May 1st: {len(before_may)} candles")
    if len(before_may) > 0:
        print(f"  Earliest: {before_may.index[0]}")
        print(f"  Latest: {before_may.index[-1]}")
    else:
        print(f"  ⚠️  NO DATA BEFORE MAY 1ST!")
    
    # Calculate PGO values
    print(f"\nCalculating PGO values...")
    pgo_values = calculate_pgo(btc_df, length=length)
    
    print(f"PGO statistics:")
    print(f"  Min: {pgo_values.min():.4f}")
    print(f"  Max: {pgo_values.max():.4f}")
    print(f"  Mean: {pgo_values.mean():.4f}")
    print(f"  Median: {pgo_values.median():.4f}")
    print(f"  Std Dev: {pgo_values.std():.4f}")
    
    # Check how many values are above/below thresholds
    above_upper = (pgo_values > upper_threshold).sum()
    below_lower = (pgo_values < lower_threshold).sum()
    between = ((pgo_values >= lower_threshold) & (pgo_values <= upper_threshold)).sum()
    
    print(f"\nPGO threshold analysis:")
    print(f"  Values above {upper_threshold}: {above_upper} ({above_upper/len(pgo_values)*100:.1f}%)")
    print(f"  Values below {lower_threshold}: {below_lower} ({below_lower/len(pgo_values)*100:.1f}%)")
    print(f"  Values between thresholds: {between} ({between/len(pgo_values)*100:.1f}%)")
    
    # Test using the same function as main program
    print(f"\nTesting with fetch_historical_mtpi_signals (same as main program)...")
    mtpi_signals_main = fetch_historical_mtpi_signals(
        timeframe=timeframe,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False,  # Same as main program
        since=mtpi_fetch_start_date,  # Same as main program
        use_cache=True
    )

    if mtpi_signals_main is not None:
        print(f"MTPI signals from main function: {len(mtpi_signals_main)} signals")
        signal_counts = mtpi_signals_main.value_counts()
        for signal, count in signal_counts.items():
            print(f"  Signal {signal}: {count} ({count/len(mtpi_signals_main)*100:.1f}%)")

    # Generate signals with and without warmup for comparison
    print(f"\nGenerating signals for comparison...")
    signals_with_skip = generate_pgo_signal(
        btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=True
    )

    signals_without_skip = generate_pgo_signal(
        btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False
    )
    
    # Analyze signal distribution
    print(f"\nSignal distribution (with warmup skip):")
    signal_counts = signals_with_skip.value_counts()
    for signal, count in signal_counts.items():
        print(f"  Signal {signal}: {count} ({count/len(signals_with_skip)*100:.1f}%)")
    
    print(f"\nSignal distribution (without warmup skip):")
    signal_counts = signals_without_skip.value_counts()
    for signal, count in signal_counts.items():
        print(f"  Signal {signal}: {count} ({count/len(signals_without_skip)*100:.1f}%)")
    
    # Find analysis start date in data
    analysis_start_ts = pd.Timestamp(analysis_start).tz_localize('UTC')
    if analysis_start_ts in btc_df.index:
        idx = btc_df.index.get_loc(analysis_start_ts)
        print(f"\nAnalysis at {analysis_start_ts} (index {idx}):")
        print(f"  PGO value: {pgo_values.iloc[idx]:.4f}")
        print(f"  Signal (with skip): {signals_with_skip.iloc[idx]}")
        print(f"  Signal (without skip): {signals_without_skip.iloc[idx]}")
        
        # Check warmup period
        warmup_period = length * 2
        print(f"  Warmup period: {warmup_period} candles")
        print(f"  Is in warmup period: {idx < warmup_period}")
        
        # Show context around analysis start
        start_idx = max(0, idx - 5)
        end_idx = min(len(btc_df), idx + 6)
        
        print(f"\nContext around analysis start (±5 candles):")
        print(f"{'Date':<20} {'PGO':<10} {'Skip':<6} {'NoSkip':<8} {'Note'}")
        print("-" * 55)
        
        for i in range(start_idx, end_idx):
            date_str = btc_df.index[i].strftime('%Y-%m-%d')
            pgo_val = pgo_values.iloc[i]
            skip_val = signals_with_skip.iloc[i]
            noskip_val = signals_without_skip.iloc[i]
            
            note = ""
            if i == idx:
                note = "← START"
            elif i < warmup_period:
                note = "WARMUP"
            
            print(f"{date_str:<20} {pgo_val:<10.4f} {skip_val:<6} {noskip_val:<8} {note}")
    
    # Detailed PGO analysis around thresholds
    print(f"\nDetailed PGO analysis:")
    print(f"PGO values around upper threshold ({upper_threshold}):")
    around_upper = pgo_values[(pgo_values > upper_threshold - 0.5) & (pgo_values < upper_threshold + 0.5)]
    for idx, val in around_upper.items():
        print(f"  {idx.strftime('%Y-%m-%d')}: {val:.4f}")

    print(f"\nPGO values around lower threshold ({lower_threshold}):")
    around_lower = pgo_values[(pgo_values > lower_threshold - 0.5) & (pgo_values < lower_threshold + 0.5)]
    for idx, val in around_lower.items():
        print(f"  {idx.strftime('%Y-%m-%d')}: {val:.4f}")

    # Check for crossovers with detailed logging
    print(f"\nDetailed crossover analysis:")
    crossovers_up = 0
    crossovers_down = 0

    print(f"Checking all consecutive PGO pairs for crossovers...")
    for i in range(1, len(pgo_values)):
        if not pd.isna(pgo_values.iloc[i-1]) and not pd.isna(pgo_values.iloc[i]):
            prev_pgo = pgo_values.iloc[i-1]
            curr_pgo = pgo_values.iloc[i]
            date = btc_df.index[i]

            # Check for crossovers with detailed conditions
            bullish_cross = prev_pgo <= upper_threshold and curr_pgo > upper_threshold
            bearish_cross = prev_pgo >= lower_threshold and curr_pgo < lower_threshold

            if bullish_cross:
                crossovers_up += 1
                print(f"  ✅ BULLISH crossover at {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} → {curr_pgo:.4f} (crosses above {upper_threshold})")
            elif bearish_cross:
                crossovers_down += 1
                print(f"  ❌ BEARISH crossover at {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} → {curr_pgo:.4f} (crosses below {lower_threshold})")
            elif abs(prev_pgo - upper_threshold) < 0.2 or abs(curr_pgo - upper_threshold) < 0.2:
                print(f"  🔍 Near upper threshold at {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} → {curr_pgo:.4f}")
            elif abs(prev_pgo - lower_threshold) < 0.2 or abs(curr_pgo - lower_threshold) < 0.2:
                print(f"  🔍 Near lower threshold at {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} → {curr_pgo:.4f}")

    print(f"\nTotal crossovers:")
    print(f"  Bullish (above {upper_threshold}): {crossovers_up}")
    print(f"  Bearish (below {lower_threshold}): {crossovers_down}")
    
    if crossovers_up == 0 and crossovers_down == 0:
        print(f"\n⚠️  NO CROSSOVERS DETECTED!")
        print(f"This explains why all signals are 0 (neutral).")
        print(f"The PGO values are staying within the threshold range [{lower_threshold}, {upper_threshold}]")
        
        # Suggest threshold adjustments
        pgo_min = pgo_values.min()
        pgo_max = pgo_values.max()
        
        print(f"\n💡 SUGGESTED FIXES:")
        print(f"1. Lower the upper threshold below {pgo_max:.4f} (current: {upper_threshold})")
        print(f"2. Raise the lower threshold above {pgo_min:.4f} (current: {lower_threshold})")
        print(f"3. Use a longer history period to capture more market volatility")
        print(f"4. Consider using different thresholds for recent market conditions")

        # Test different threshold combinations
        print(f"\n🧪 TESTING DIFFERENT THRESHOLDS:")

        # Calculate percentile-based thresholds
        p25 = pgo_values.quantile(0.25)
        p75 = pgo_values.quantile(0.75)
        median = pgo_values.median()

        test_thresholds = [
            (p75, p25, "75th/25th percentile"),
            (median + 0.5, median - 0.5, "Median ± 0.5"),
            (3.5, 1.5, "Manual: 3.5/1.5"),
            (3.0, 2.0, "Manual: 3.0/2.0"),
            (4.0, 1.0, "Manual: 4.0/1.0")
        ]

        for upper_test, lower_test, desc in test_thresholds:
            crossovers_up_test = 0
            crossovers_down_test = 0

            for i in range(1, len(pgo_values)):
                if not pd.isna(pgo_values.iloc[i-1]) and not pd.isna(pgo_values.iloc[i]):
                    prev_pgo = pgo_values.iloc[i-1]
                    curr_pgo = pgo_values.iloc[i]

                    if prev_pgo <= upper_test and curr_pgo > upper_test:
                        crossovers_up_test += 1
                    elif prev_pgo >= lower_test and curr_pgo < lower_test:
                        crossovers_down_test += 1

            print(f"  {desc} ({upper_test:.2f}/{lower_test:.2f}): {crossovers_up_test} bullish, {crossovers_down_test} bearish crossovers")

    # Create comprehensive visualization
    print(f"\n📊 CREATING VISUALIZATION...")
    plot_mtpi_analysis(btc_df, pgo_values, signals_without_skip, upper_threshold, lower_threshold, analysis_start)

if __name__ == "__main__":
    # Run the flat line period analysis first
    analyze_flat_line_period()

    print("\n" + "="*80)
    print("ORIGINAL ANALYSIS (for comparison)")
    print("="*80)

    # Also run the original analysis for comparison
    analyze_mtpi_issue()
