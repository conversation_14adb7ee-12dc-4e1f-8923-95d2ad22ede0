.asset-selector {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.asset-selector h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-color);
  font-size: 18px;
  font-weight: 600;
}

.asset-selector-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.asset-search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  margin-right: 16px;
  background-color: var(--card-bg);
  color: var(--text-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.asset-search-input::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

.asset-selector-buttons {
  display: flex;
  gap: 8px;
}

.asset-selector-button {
  background-color: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--text-color);
}

.asset-selector-button:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.asset-selector-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--gray-color);
}

.asset-selector-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
  background-color: var(--card-bg);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.asset-selector-item {
  padding: 8px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.asset-selector-item:hover {
  background-color: var(--highlight-row-bg);
}

.asset-selector-item:last-child {
  border-bottom: none;
}

.asset-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-color);
  width: 100%;
}

.asset-checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
  accent-color: var(--primary-color);
  width: 16px;
  height: 16px;
}

.asset-checkbox-label input[type="checkbox"]:disabled + .asset-name {
  color: var(--text-muted);
  opacity: 0.6;
}

.asset-selector-loading,
.asset-selector-error,
.asset-selector-no-results {
  padding: 16px;
  text-align: center;
  color: var(--text-muted);
}

.asset-selector-error {
  color: var(--danger-color);
}

.asset-selector-selected {
  margin-top: 16px;
}

.asset-selector-selected-count {
  margin-bottom: 8px;
  font-weight: bold;
  color: var(--text-color);
}

.asset-selector-selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.asset-selector-selected-item {
  display: flex;
  align-items: center;
  background-color: var(--highlight-row-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--text-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.asset-selector-remove-button {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 16px;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  transition: color 0.2s;
}

.asset-selector-remove-button:hover {
  color: var(--danger-color);
}

/* Custom scrollbar for asset list */
.asset-selector-list::-webkit-scrollbar {
  width: 8px;
}

.asset-selector-list::-webkit-scrollbar-track {
  background: var(--card-bg);
  border-radius: 4px;
}

.asset-selector-list::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid var(--card-bg);
}

.asset-selector-list::-webkit-scrollbar-thumb:hover {
  background-color: var(--gray-color);
}
