#!/usr/bin/env python3
"""
Debug script to check DWMA parameter types from YAML configuration.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.MTPI_signal_handler import load_mtpi_multi_indicator_config

def debug_dwma_config():
    """Debug DWMA configuration parameter types."""
    print("🔍 DEBUGGING DWMA CONFIGURATION TYPES")
    print("=" * 50)
    
    # Load configuration
    config = load_mtpi_multi_indicator_config()
    dwma_config = config.get('dwma_score', {})
    
    print("📋 DWMA Configuration:")
    for key, value in dwma_config.items():
        print(f"  {key}: {value} (type: {type(value).__name__})")
    
    print("\n🎯 Checking specific weight parameters:")
    upper_sd_weight = dwma_config.get('upper_sd_weight', 1.031)
    lower_sd_weight = dwma_config.get('lower_sd_weight', 0.996)
    
    print(f"  upper_sd_weight: {upper_sd_weight} (type: {type(upper_sd_weight).__name__})")
    print(f"  lower_sd_weight: {lower_sd_weight} (type: {type(lower_sd_weight).__name__})")
    
    # Test multiplication
    print("\n🧮 Testing multiplication:")
    try:
        test_value = 100.0
        result1 = test_value * upper_sd_weight
        result2 = test_value * lower_sd_weight
        print(f"  100.0 * upper_sd_weight = {result1}")
        print(f"  100.0 * lower_sd_weight = {result2}")
        print("  ✅ Multiplication works!")
    except Exception as e:
        print(f"  ❌ Multiplication failed: {e}")
        print(f"  Error type: {type(e).__name__}")
    
    # Check if values are strings that look like numbers
    print("\n🔍 String analysis:")
    if isinstance(upper_sd_weight, str):
        print(f"  upper_sd_weight is a string: '{upper_sd_weight}'")
        print(f"  Contains space: {' ' in upper_sd_weight}")
        print(f"  Repr: {repr(upper_sd_weight)}")
    
    if isinstance(lower_sd_weight, str):
        print(f"  lower_sd_weight is a string: '{lower_sd_weight}'")
        print(f"  Contains space: {' ' in lower_sd_weight}")
        print(f"  Repr: {repr(lower_sd_weight)}")

if __name__ == "__main__":
    debug_dwma_config()
