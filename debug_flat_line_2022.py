#!/usr/bin/env python3
"""
Debug script to analyze the flat line period from 2022-10-26 to 2022-11-08
that should have been bullish but shows flat performance.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from data_fetcher import fetch_ohlcv_data
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_flat_line_period():
    """
    Analyze the specific flat line period from 2022-10-26 to 2022-11-08
    that should have been bullish but shows flat performance.
    """
    print("=" * 80)
    print("ANALYZING FLAT LINE PERIOD: 2022-10-26 to 2022-11-08")
    print("=" * 80)
    
    # Parameters for the problematic period
    timeframe = '1d'
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    # Focus on the problematic period
    problem_start = '2022-10-26'
    problem_end = '2022-11-08'
    
    # Fetch data starting earlier to ensure we have enough warmup
    fetch_start = '2022-09-01'  # Start earlier for warmup
    
    print(f"Parameters:")
    print(f"  Timeframe: {timeframe}")
    print(f"  Length: {length}")
    print(f"  Upper threshold: {upper_threshold}")
    print(f"  Lower threshold: {lower_threshold}")
    print(f"  Problem period: {problem_start} to {problem_end}")
    print(f"  Fetch start: {fetch_start}")
    
    try:
        # Fetch BTC data for the period
        print(f"\nFetching BTC data from {fetch_start}...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe=timeframe,
            since=fetch_start,
            limit=2000,
            use_cache=False,
            force_refresh=True
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("ERROR: Failed to fetch BTC data")
            return
            
        btc_df = data_dict['BTC/USDT']
        print(f"Fetched {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Calculate PGO values
        print(f"\nCalculating PGO values...")
        pgo_values = calculate_pgo(btc_df, length=length)
        
        # Generate signals with and without warmup
        print(f"Generating signals...")
        signals_with_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=True
        )
        
        signals_without_skip = generate_pgo_signal(
            btc_df,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            skip_warmup=False
        )
        
        # Filter to the problem period
        problem_start_dt = pd.to_datetime(problem_start).tz_localize('UTC')
        problem_end_dt = pd.to_datetime(problem_end).tz_localize('UTC')
        
        # Find the indices for the problem period
        problem_mask = (btc_df.index >= problem_start_dt) & (btc_df.index <= problem_end_dt)
        problem_btc = btc_df[problem_mask]
        problem_pgo = pgo_values[problem_mask]
        problem_signals_skip = signals_with_skip[problem_mask]
        problem_signals_no_skip = signals_without_skip[problem_mask]
        
        print(f"\nPROBLEM PERIOD ANALYSIS ({problem_start} to {problem_end}):")
        print(f"Found {len(problem_btc)} candles in problem period")
        
        if len(problem_btc) == 0:
            print("ERROR: No data found for the problem period")
            return
            
        # Analyze BTC price movement during this period
        start_price = problem_btc['close'].iloc[0]
        end_price = problem_btc['close'].iloc[-1]
        price_change = ((end_price - start_price) / start_price) * 100
        
        print(f"\nBTC Price Analysis:")
        print(f"  Start price: ${start_price:,.2f}")
        print(f"  End price: ${end_price:,.2f}")
        print(f"  Price change: {price_change:+.2f}%")
        print(f"  Min price: ${problem_btc['close'].min():,.2f}")
        print(f"  Max price: ${problem_btc['close'].max():,.2f}")
        
        # Analyze PGO values during this period
        print(f"\nPGO Analysis for Problem Period:")
        print(f"  Min PGO: {problem_pgo.min():.4f}")
        print(f"  Max PGO: {problem_pgo.max():.4f}")
        print(f"  Mean PGO: {problem_pgo.mean():.4f}")
        print(f"  Median PGO: {problem_pgo.median():.4f}")
        print(f"  Std Dev: {problem_pgo.std():.4f}")
        
        # Check if PGO values are within thresholds
        above_upper = (problem_pgo > upper_threshold).sum()
        below_lower = (problem_pgo < lower_threshold).sum()
        in_neutral = ((problem_pgo >= lower_threshold) & (problem_pgo <= upper_threshold)).sum()
        
        print(f"\nPGO Threshold Analysis:")
        print(f"  Above upper threshold ({upper_threshold}): {above_upper} candles")
        print(f"  Below lower threshold ({lower_threshold}): {below_lower} candles")
        print(f"  In neutral zone: {in_neutral} candles")
        
        # Analyze signals during this period
        print(f"\nSignal Analysis (with warmup skip):")
        signal_counts_skip = problem_signals_skip.value_counts()
        for signal, count in signal_counts_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(problem_signals_skip)*100:.1f}%)")
        
        print(f"\nSignal Analysis (without warmup skip):")
        signal_counts_no_skip = problem_signals_no_skip.value_counts()
        for signal, count in signal_counts_no_skip.items():
            signal_name = "Bullish" if signal == 1 else "Bearish" if signal == -1 else "Neutral"
            print(f"  {signal_name} ({signal}): {count} candles ({count/len(problem_signals_no_skip)*100:.1f}%)")
        
        # Detailed day-by-day analysis
        print(f"\nDETAILED DAY-BY-DAY ANALYSIS:")
        print(f"{'Date':<12} {'Close':<10} {'PGO':<10} {'Skip':<6} {'NoSkip':<8} {'Note'}")
        print("-" * 65)
        
        for i, (date, row) in enumerate(problem_btc.iterrows()):
            close_price = row['close']
            pgo_val = problem_pgo.iloc[i]
            skip_signal = problem_signals_skip.iloc[i]
            no_skip_signal = problem_signals_no_skip.iloc[i]
            
            # Add notes for significant events
            note = ""
            if i == 0:
                note = "START"
            elif i == len(problem_btc) - 1:
                note = "END"
            elif abs(pgo_val) > 1.0:
                note = "HIGH PGO"
            elif skip_signal != 0:
                note = "SIGNAL!"
                
            print(f"{date.strftime('%Y-%m-%d'):<12} ${close_price:<9.2f} {pgo_val:<10.4f} {skip_signal:<6} {no_skip_signal:<8} {note}")
        
        # Check for crossovers during this period
        print(f"\nCROSSOVER ANALYSIS:")
        crossovers_up = 0
        crossovers_down = 0
        
        for i in range(1, len(problem_pgo)):
            if not pd.isna(problem_pgo.iloc[i-1]) and not pd.isna(problem_pgo.iloc[i]):
                prev_pgo = problem_pgo.iloc[i-1]
                curr_pgo = problem_pgo.iloc[i]
                date = problem_pgo.index[i]
                
                # Check for crossovers
                if prev_pgo <= upper_threshold and curr_pgo > upper_threshold:
                    crossovers_up += 1
                    print(f"  BULLISH crossover on {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} -> {curr_pgo:.4f}")
                elif prev_pgo >= lower_threshold and curr_pgo < lower_threshold:
                    crossovers_down += 1
                    print(f"  BEARISH crossover on {date.strftime('%Y-%m-%d')}: {prev_pgo:.4f} -> {curr_pgo:.4f}")
        
        print(f"\nTotal crossovers in problem period:")
        print(f"  Bullish crossovers: {crossovers_up}")
        print(f"  Bearish crossovers: {crossovers_down}")
        
        if crossovers_up == 0 and crossovers_down == 0:
            print(f"\n⚠️  NO CROSSOVERS DETECTED IN PROBLEM PERIOD!")
            print(f"This explains the flat line - PGO stayed within neutral zone.")
            
            # Suggest what might have caused this
            print(f"\n💡 POSSIBLE CAUSES:")
            print(f"1. Market was in a consolidation phase with low volatility")
            print(f"2. PGO thresholds may be too wide for this market condition")
            print(f"3. The 35-period length may be too long for detecting shorter-term moves")
            
            # Check if we need different thresholds
            pgo_range = problem_pgo.max() - problem_pgo.min()
            print(f"4. PGO range during period: {pgo_range:.4f} (may need tighter thresholds)")
            
    except Exception as e:
        print(f"Error in analyze_flat_line_period: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_flat_line_period()
