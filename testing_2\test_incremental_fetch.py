#!/usr/bin/env python
"""
Test script for incremental data fetching and appending to cache.

This script demonstrates fetching missing data from Binance and appending it to cache files,
then continuing to fetch and append new data in real-time as each minute closes.
"""

import os
import sys
import logging
import pandas as pd
import time
import shutil
from datetime import datetime, timedelta
import ccxt

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.data_cache import (
    append_to_cache,
    load_from_cache,
    get_cache_path
)
from src.utils import setup_logging

def setup_test():
    """Set up the test environment."""
    setup_logging(level=logging.INFO)

    print("\n" + "=" * 70)
    print("TESTING INCREMENTAL DATA FETCHING AND APPENDING TO CACHE")
    print("=" * 70)

    # Test parameters
    exchange_id = 'binance'
    symbols = ['SOL/USDC']  # Focus on just one symbol for clarity
    timeframe = '1m'

    # Create backup of original files
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"

        if os.path.exists(cache_path):
            print(f"Creating backup of {cache_path} to {backup_path}")
            shutil.copy2(cache_path, backup_path)

    return exchange_id, symbols, timeframe

def restore_backups(exchange_id, symbols, timeframe):
    """Restore original files from backups."""
    print("\nRestoring original files from backups...")
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"

        if os.path.exists(backup_path):
            print(f"Restoring {cache_path} from {backup_path}")
            shutil.copy2(backup_path, cache_path)
            os.remove(backup_path)

def fetch_missing_data(exchange, symbol, timeframe, last_timestamp):
    """Fetch data from last_timestamp to now."""
    print(f"\nFetching missing data for {symbol} from {last_timestamp} to now...")

    # Convert timestamp to milliseconds for CCXT
    since_ms = int(pd.to_datetime(last_timestamp).timestamp() * 1000)

    try:
        # Fetch candles
        candles = exchange.fetch_ohlcv(symbol, timeframe, since=since_ms, limit=1000)

        if not candles:
            print(f"No candles available for {symbol}")
            return None

        print(f"Fetched {len(candles)} candles for {symbol}")

        # Convert to DataFrame
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        df = pd.DataFrame(candles, columns=columns)

        # Convert timestamp to datetime with UTC timezone
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)

        # Set timestamp as index
        df.set_index('timestamp', inplace=True)

        # Convert last_timestamp to UTC timezone-aware datetime for comparison
        # First ensure it's a pandas Timestamp
        last_ts = pd.to_datetime(last_timestamp)
        # Then ensure it has UTC timezone
        if last_ts.tzinfo is None:
            last_ts = last_ts.tz_localize('UTC')

        # Now we can safely compare
        df = df[df.index > last_ts]

        if df.empty:
            print(f"No new data to append for {symbol}")
            return None

        print(f"Prepared {len(df)} new candles for {symbol} from {df.index.min()} to {df.index.max()}")

        return df

    except Exception as e:
        print(f"Error fetching data for {symbol}: {e}")
        return None

def wait_for_next_candle(exchange, symbol, timeframe, last_timestamp):
    """Wait for the next candle to close and fetch it."""
    print(f"\nWaiting for the next candle to close for {symbol}...")

    try:
        # Wait until the next minute closes
        current_time = datetime.now()
        seconds_to_next_minute = 60 - current_time.second
        print(f"Waiting {seconds_to_next_minute} seconds for the next minute to close...")
        time.sleep(seconds_to_next_minute + 2)  # Add 2 seconds to ensure the candle is closed

        # Fetch the latest candle
        candles = exchange.fetch_ohlcv(symbol, timeframe, limit=1)

        if not candles:
            print(f"No new candle available for {symbol}")
            return None

        # Convert to DataFrame
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        df = pd.DataFrame(candles, columns=columns)

        # Convert timestamp to datetime with UTC timezone
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)

        # Set timestamp as index
        df.set_index('timestamp', inplace=True)

        # Check if this is newer than our last timestamp
        # Convert last_timestamp to UTC timezone-aware datetime for comparison
        last_ts = pd.to_datetime(last_timestamp)
        if last_ts.tzinfo is None:
            last_ts = last_ts.tz_localize('UTC')

        if df.index.max() <= last_ts:
            print(f"Fetched candle is not newer than last timestamp")
            return None

        print(f"Fetched new candle for {symbol} at {df.index[0]}")
        print(df)

        return df

    except Exception as e:
        print(f"Error waiting for next candle: {e}")
        return None

def main():
    """Main function to run the test."""
    try:
        # Set up the test
        exchange_id, symbols, timeframe = setup_test()

        # Initialize the exchange
        exchange = ccxt.binance()

        # Process each symbol
        for symbol in symbols:
            try:
                # Load current data
                current_data = load_from_cache(exchange_id, symbol, timeframe)
                if current_data is None or current_data.empty:
                    print(f"No data found for {symbol}, skipping...")
                    continue

                last_timestamp = current_data.index.max()
                print(f"\nProcessing {symbol}...")
                print(f"Loaded {len(current_data)} rows from cache")
                print(f"Last timestamp in cache: {last_timestamp}")

                # 1. Fetch missing historical data
                missing_data = fetch_missing_data(exchange, symbol, timeframe, last_timestamp)

                if missing_data is not None and not missing_data.empty:
                    # Append to cache
                    print("\nAppending missing data to cache...")
                    append_success = append_to_cache(missing_data, exchange_id, symbol, timeframe)

                    if append_success:
                        print(f"✅ Successfully appended {len(missing_data)} historical candles")
                        last_timestamp = missing_data.index.max()

                        # Load the data again to verify
                        updated_data = load_from_cache(exchange_id, symbol, timeframe)
                        print(f"Cache now has {len(updated_data)} rows")
                        print(f"New last timestamp: {updated_data.index.max()}")

                        # Show the last few rows
                        print("\nLast 5 rows of the updated file:")
                        print(updated_data.tail(5))
                    else:
                        print(f"❌ Failed to append historical data")

                # 2. Wait for and fetch the next real-time candle
                print("\nNow waiting for the next real-time candle...")
                new_candle = wait_for_next_candle(exchange, symbol, timeframe, last_timestamp)

                if new_candle is not None and not new_candle.empty:
                    # Append to cache
                    print("\nAppending new real-time candle to cache...")
                    append_success = append_to_cache(new_candle, exchange_id, symbol, timeframe)

                    if append_success:
                        print(f"✅ Successfully appended real-time candle at {new_candle.index[0]}")

                        # Load the data again to verify
                        final_data = load_from_cache(exchange_id, symbol, timeframe)
                        print(f"Cache now has {len(final_data)} rows")
                        print(f"Final last timestamp: {final_data.index.max()}")

                        # Show the last few rows
                        print("\nLast 5 rows of the final file:")
                        print(final_data.tail(5))
                    else:
                        print(f"❌ Failed to append real-time candle")

            except Exception as e:
                print(f"Error processing {symbol}: {e}")

        print("\nTest completed successfully")

    except Exception as e:
        print(f"Error in test: {e}")
    finally:
        # Restore original files
        restore_backups(exchange_id, symbols, timeframe)
        print("\nTest completed and original files restored")

if __name__ == "__main__":
    main()
