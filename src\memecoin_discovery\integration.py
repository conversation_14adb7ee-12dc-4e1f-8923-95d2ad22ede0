"""
Integration module for connecting memecoin discovery with Asset Rotation Strategy.

This module provides interfaces to integrate discovered tokens with the existing
trading and analysis infrastructure.
"""

import logging
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from .database import DiscoveryDatabase, DiscoveredToken
from .discovery_engine import MemecoinDiscoveryEngine

class MemecoinStrategyIntegration:
    """Integration layer between memecoin discovery and Asset Rotation Strategy."""
    
    def __init__(self, config_path: str = "config/memecoin_discovery.yaml"):
        """Initialize integration with configuration."""
        self.config_path = config_path
        self.config = self._load_config()
        
        # Initialize discovery components
        self.database = DiscoveryDatabase()
        self.discovery_engine = MemecoinDiscoveryEngine(self.config)
        
        # Integration settings
        self.integration_settings = self.config.get('integration', {})
        self.auto_add_threshold = self.integration_settings.get('auto_add_threshold', 80)
        self.max_auto_additions = self.integration_settings.get('max_auto_additions', 3)
        self.review_required_threshold = self.integration_settings.get('review_required_threshold', 65)
        
        logging.info("Memecoin strategy integration initialized")
    
    def _load_config(self) -> Dict:
        """Load configuration from YAML file."""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    return yaml.safe_load(f)
            else:
                # Return default configuration
                return self._get_default_config()
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Get default configuration."""
        return {
            'discovery': {
                'max_tokens_per_run': 50,
                'min_mentions_threshold': 3,
                'hours_lookback': 24
            },
            'integration': {
                'auto_add_threshold': 80,
                'max_auto_additions': 3,
                'review_required_threshold': 65,
                'geckoterminal_config_path': 'config/geckoterminal_tokens.yaml'
            },
            'notifications': {
                'enabled': True,
                'discovery_alerts': True,
                'integration_alerts': True
            }
        }
    
    def run_discovery_and_integration(self) -> Dict[str, Any]:
        """Run discovery cycle and integrate results with strategy."""
        logging.info("Starting discovery and integration cycle")
        
        results = {
            'discovery_results': {},
            'integration_results': {},
            'recommendations': [],
            'errors': []
        }
        
        try:
            # Step 1: Run discovery cycle
            discovery_results = self.discovery_engine.run_discovery_cycle()
            results['discovery_results'] = discovery_results
            
            # Step 2: Process discovered tokens for integration
            integration_results = self._process_discovered_tokens()
            results['integration_results'] = integration_results
            
            # Step 3: Generate recommendations
            recommendations = self._generate_integration_recommendations()
            results['recommendations'] = recommendations
            
            logging.info("Discovery and integration cycle completed successfully")
            
        except Exception as e:
            error_msg = f"Discovery and integration cycle failed: {str(e)}"
            logging.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def _process_discovered_tokens(self) -> Dict[str, Any]:
        """Process recently discovered tokens for potential integration."""
        processing_results = {
            'tokens_reviewed': 0,
            'auto_added': 0,
            'pending_review': 0,
            'rejected': 0,
            'added_tokens': [],
            'review_tokens': []
        }
        
        try:
            # Get recently discovered tokens (last 24 hours)
            recent_tokens = self.database.get_discovered_tokens(
                status='discovered',
                days_back=1
            )
            
            processing_results['tokens_reviewed'] = len(recent_tokens)
            
            for token in recent_tokens:
                try:
                    # Evaluate token for integration
                    integration_decision = self._evaluate_token_for_integration(token)
                    
                    if integration_decision['action'] == 'auto_add':
                        if processing_results['auto_added'] < self.max_auto_additions:
                            success = self._add_token_to_strategy(token)
                            if success:
                                processing_results['auto_added'] += 1
                                processing_results['added_tokens'].append({
                                    'symbol': token.symbol,
                                    'score': token.final_score,
                                    'reason': integration_decision['reason']
                                })
                                # Update token status
                                self.database.update_token_status(token.symbol, 'approved')
                    
                    elif integration_decision['action'] == 'review_required':
                        processing_results['pending_review'] += 1
                        processing_results['review_tokens'].append({
                            'symbol': token.symbol,
                            'score': token.final_score,
                            'reason': integration_decision['reason'],
                            'risk_factors': integration_decision.get('risk_factors', [])
                        })
                        # Update token status
                        self.database.update_token_status(token.symbol, 'analyzing')
                    
                    else:  # reject
                        processing_results['rejected'] += 1
                        # Update token status
                        self.database.update_token_status(token.symbol, 'rejected')
                
                except Exception as e:
                    logging.error(f"Error processing token {token.symbol}: {e}")
                    continue
            
        except Exception as e:
            logging.error(f"Error processing discovered tokens: {e}")
        
        return processing_results
    
    def _evaluate_token_for_integration(self, token: DiscoveredToken) -> Dict[str, Any]:
        """Evaluate a discovered token for integration into the strategy."""
        try:
            score = (token.final_score or 0) * 100  # Convert to 0-100 scale
            
            # Check for auto-addition criteria
            if score >= self.auto_add_threshold:
                # Additional checks for auto-addition
                risk_score = (token.risk_score or 0) * 100
                if risk_score < 30:  # Low risk
                    return {
                        'action': 'auto_add',
                        'reason': f'High score ({score:.1f}) with low risk ({risk_score:.1f})',
                        'confidence': 'high'
                    }
            
            # Check for manual review criteria
            if score >= self.review_required_threshold:
                risk_factors = []
                
                # Analyze risk factors
                risk_score = (token.risk_score or 0) * 100
                if risk_score > 50:
                    risk_factors.append(f'High risk score: {risk_score:.1f}')
                
                # Check AI confidence
                ai_score = (token.ai_score or 0) * 100
                if ai_score < 60:
                    risk_factors.append(f'Low AI confidence: {ai_score:.1f}')
                
                # Check social sentiment
                sentiment_score = (token.sentiment_score or 0) * 100
                if sentiment_score < 40:
                    risk_factors.append(f'Poor sentiment: {sentiment_score:.1f}')
                
                return {
                    'action': 'review_required',
                    'reason': f'Moderate score ({score:.1f}) requires manual review',
                    'risk_factors': risk_factors,
                    'confidence': 'medium'
                }
            
            # Reject tokens with low scores
            return {
                'action': 'reject',
                'reason': f'Score too low ({score:.1f}) for integration',
                'confidence': 'high'
            }
            
        except Exception as e:
            logging.error(f"Error evaluating token {token.symbol}: {e}")
            return {
                'action': 'reject',
                'reason': f'Evaluation failed: {str(e)}',
                'confidence': 'low'
            }
    
    def _add_token_to_strategy(self, token: DiscoveredToken) -> bool:
        """Add a token to the GeckoTerminal configuration for strategy inclusion."""
        try:
            geckoterminal_config_path = self.integration_settings.get(
                'geckoterminal_config_path', 
                'config/geckoterminal_tokens.yaml'
            )
            
            # Load existing GeckoTerminal configuration
            if Path(geckoterminal_config_path).exists():
                with open(geckoterminal_config_path, 'r') as f:
                    gt_config = yaml.safe_load(f)
            else:
                gt_config = {'tokens': []}
            
            # Check if token already exists
            existing_symbols = [t.get('symbol', '').split('/')[0] for t in gt_config.get('tokens', [])]
            if token.symbol in existing_symbols:
                logging.info(f"Token {token.symbol} already exists in GeckoTerminal config")
                return True
            
            # Prepare new token entry
            new_token_entry = {
                'network': token.network or 'ethereum',
                'token_address': token.contract_address or '',
                'symbol': f"{token.symbol}/USDT",
                'name': token.name or token.symbol,
                'color': self._generate_color_for_token(token.symbol),
                'added_by': 'memecoin_discovery',
                'added_date': datetime.now().isoformat(),
                'discovery_score': token.final_score
            }
            
            # Add to configuration
            if 'tokens' not in gt_config:
                gt_config['tokens'] = []
            
            gt_config['tokens'].append(new_token_entry)
            
            # Save updated configuration
            with open(geckoterminal_config_path, 'w') as f:
                yaml.dump(gt_config, f, default_flow_style=False, indent=2)
            
            logging.info(f"Added token {token.symbol} to GeckoTerminal configuration")
            return True
            
        except Exception as e:
            logging.error(f"Error adding token {token.symbol} to strategy: {e}")
            return False
    
    def _generate_color_for_token(self, symbol: str) -> str:
        """Generate a color for a token based on its symbol."""
        # Simple hash-based color generation
        hash_value = hash(symbol) % 16777216  # 24-bit color space
        return f"#{hash_value:06X}"
    
    def _generate_integration_recommendations(self) -> List[Dict[str, Any]]:
        """Generate recommendations for manual review."""
        recommendations = []
        
        try:
            # Get tokens pending review
            pending_tokens = self.database.get_discovered_tokens(
                status='analyzing',
                days_back=7
            )
            
            for token in pending_tokens:
                try:
                    # Generate recommendation
                    recommendation = {
                        'symbol': token.symbol,
                        'name': token.name,
                        'discovery_date': token.discovery_date.isoformat(),
                        'final_score': (token.final_score or 0) * 100,
                        'ai_score': (token.ai_score or 0) * 100,
                        'sentiment_score': (token.sentiment_score or 0) * 100,
                        'risk_score': (token.risk_score or 0) * 100,
                        'network': token.network,
                        'contract_address': token.contract_address,
                        'source': token.source,
                        'recommendation': self._get_recommendation_text(token),
                        'action_required': self._get_required_action(token),
                        'metadata': token.metadata
                    }
                    
                    recommendations.append(recommendation)
                    
                except Exception as e:
                    logging.error(f"Error generating recommendation for {token.symbol}: {e}")
                    continue
            
            # Sort by score (descending)
            recommendations.sort(key=lambda x: x['final_score'], reverse=True)
            
        except Exception as e:
            logging.error(f"Error generating integration recommendations: {e}")
        
        return recommendations
    
    def _get_recommendation_text(self, token: DiscoveredToken) -> str:
        """Generate recommendation text for a token."""
        score = (token.final_score or 0) * 100
        risk_score = (token.risk_score or 0) * 100
        
        if score >= 80 and risk_score < 30:
            return "Strong candidate for inclusion - high score with low risk"
        elif score >= 70 and risk_score < 50:
            return "Good candidate for inclusion - monitor for risk factors"
        elif score >= 60:
            return "Moderate candidate - requires careful evaluation"
        else:
            return "Weak candidate - consider rejection unless special circumstances"
    
    def _get_required_action(self, token: DiscoveredToken) -> str:
        """Get required action for a token."""
        score = (token.final_score or 0) * 100
        risk_score = (token.risk_score or 0) * 100
        
        if score >= 75 and risk_score < 40:
            return "approve"
        elif score >= 60:
            return "review"
        else:
            return "reject"
    
    def approve_token(self, symbol: str) -> bool:
        """Manually approve a token for inclusion in strategy."""
        try:
            # Get token from database
            tokens = self.database.get_discovered_tokens()
            token = next((t for t in tokens if t.symbol == symbol), None)
            
            if not token:
                logging.error(f"Token {symbol} not found in database")
                return False
            
            # Add to strategy
            success = self._add_token_to_strategy(token)
            
            if success:
                # Update status
                self.database.update_token_status(symbol, 'approved')
                logging.info(f"Token {symbol} manually approved and added to strategy")
                return True
            else:
                logging.error(f"Failed to add token {symbol} to strategy")
                return False
                
        except Exception as e:
            logging.error(f"Error approving token {symbol}: {e}")
            return False
    
    def reject_token(self, symbol: str, reason: str = "") -> bool:
        """Manually reject a token."""
        try:
            # Update status with rejection reason
            success = self.database.update_token_status(symbol, 'rejected')
            
            if success:
                logging.info(f"Token {symbol} manually rejected. Reason: {reason}")
                return True
            else:
                logging.error(f"Failed to reject token {symbol}")
                return False
                
        except Exception as e:
            logging.error(f"Error rejecting token {symbol}: {e}")
            return False
    
    def get_integration_dashboard_data(self) -> Dict[str, Any]:
        """Get data for integration dashboard."""
        try:
            # Get summary statistics
            summary = self.discovery_engine.get_discovery_summary(days=7)
            
            # Get pending recommendations
            recommendations = self._generate_integration_recommendations()
            
            # Get recent activity
            recent_approved = self.database.get_discovered_tokens(
                status='approved',
                days_back=7,
                limit=10
            )
            
            recent_rejected = self.database.get_discovered_tokens(
                status='rejected',
                days_back=7,
                limit=10
            )
            
            return {
                'summary': summary,
                'pending_recommendations': recommendations[:20],  # Limit to top 20
                'recent_approved': [
                    {
                        'symbol': t.symbol,
                        'score': (t.final_score or 0) * 100,
                        'date': t.discovery_date.isoformat()
                    }
                    for t in recent_approved
                ],
                'recent_rejected': [
                    {
                        'symbol': t.symbol,
                        'score': (t.final_score or 0) * 100,
                        'date': t.discovery_date.isoformat()
                    }
                    for t in recent_rejected
                ],
                'integration_stats': {
                    'auto_add_threshold': self.auto_add_threshold,
                    'review_threshold': self.review_required_threshold,
                    'max_auto_additions': self.max_auto_additions
                }
            }
            
        except Exception as e:
            logging.error(f"Error getting dashboard data: {e}")
            return {'error': str(e)}
