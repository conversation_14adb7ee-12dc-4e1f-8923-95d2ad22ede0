:root {
  /* Common colors that don't change with theme */
  --primary-color: #2962FF;
  --secondary-color: #0052CC;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #FF9800;
  --info-color: #17a2b8;

  /* Light theme colors (default) */
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --gray-color: #6c757d;
  --border-color: #dee2e6;
  --bg-color: #f5f5f5;
  --card-bg: #ffffff;
  --text-color: #333333;
  --text-muted: #6c757d;
  --table-header-bg: rgba(0,0,0,0.02);
  --highlight-row-bg: rgba(41, 98, 255, 0.05);
  --chart-bg: #1a202c;
  --chart-text: #e2e8f0;
  --chart-grid: #4a5568;
  --info-box-bg: #f0f7ff;
  --info-box-border: #d0e3ff;

  /* Asset colors */
  --btc-color: #f7931a; /* Bitcoin orange */
  --eth-color: #627eea; /* Ethereum blue */
  --sol-color: #14F195; /* Solana green */
  --sui-color: #6f4ef2; /* SUI purple */
  --strategy-color: #0d6efd; /* Strategy blue */
}

/* Dark theme colors */
[data-theme="dark"] {
  --light-color: #2d3748;
  --dark-color: #1a202c;
  --gray-color: #a0aec0;
  --border-color: #4a5568;
  --bg-color: #1a202c;
  --card-bg: #2d3748;
  --text-color: #e2e8f0;
  --text-muted: #a0aec0;
  --table-header-bg: rgba(255,255,255,0.05);
  --highlight-row-bg: rgba(66, 153, 225, 0.1);
  --chart-bg: #2d3748;
  --chart-text: #e2e8f0;
  --chart-grid: #4a5568;
  --info-box-bg: #2c3e50;
  --info-box-border: #3498db;
}

/* Hide horizontal dashed lines in the chart */
.chart-container line[stroke-dasharray] {
  stroke-opacity: 0 !important;
}

/* Hide horizontal lines from TradingView charts */
.chart-container .tv-lightweight-charts line[stroke-dasharray][y1][y2] {
  display: none !important;
}

/* Additional selector to ensure all horizontal grid lines are hidden */
.trading-view-dashboard .chart path[stroke-dasharray] {
  stroke-opacity: 0.6;
}

/* Enhance time axis visibility */
.chart-container .tv-lightweight-charts text {
  font-size: 13px !important;
  font-weight: 600 !important;
  fill: #e2e8f0 !important;
  opacity: 1 !important;
}

/* Improve axis lines visibility */
.chart-container .tv-lightweight-charts line[stroke]:not([stroke-dasharray]) {
  stroke-width: 1.5px !important;
  stroke-opacity: 0.8 !important;
}

/* Specific style for time axis text to ensure it's visible */
.chart-container .tv-lightweight-charts text[y][dominant-baseline="central"] {
  font-size: 13px !important;
  font-weight: 600 !important;
  transform: translateY(-5px) !important; /* Move time axis text up slightly */
}

/* Add padding at the bottom of the chart to ensure time axis is fully visible */
.chart {
  padding-bottom: 40px !important;
  margin-bottom: 20px !important;
}

.trading-view-dashboard {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  background-color: var(--card-bg);
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-header h1 {
  font-size: 1.5rem;
  color: var(--text-color);
  margin: 0;
}

.last-run-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: var(--info-box-bg);
  border: 1px solid var(--info-box-border);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  margin-top: 10px;
}

.last-run-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.last-run-label {
  font-weight: 500;
  color: var(--text-muted);
  min-width: 110px;
}

.last-run-time {
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
}

.badge-success {
  background-color: rgba(40, 167, 69, 0.15);
  color: var(--success-color);
}

.badge-danger {
  background-color: rgba(220, 53, 69, 0.15);
  color: var(--danger-color);
}

.badge-secondary {
  background-color: rgba(108, 117, 125, 0.15);
  color: var(--gray-color);
}

.badge-warning {
  background-color: rgba(255, 152, 0, 0.15);
  color: var(--warning-color);
}

.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.header-indicators {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 0 auto;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header h2 {
  font-size: 1.25rem;
  margin: 0;
}

.card-body {
  padding: 20px;
}

.chart-container {
  width: 100%;
  position: relative;
  margin-bottom: 40px;
  padding-bottom: 20px;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.control-group {
  display: flex;
  gap: 5px;
}

.scale-toggle-btn, .zoom-btn, .replay-btn {
  background-color: var(--light-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.zoom-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  padding: 0;
}

.replay-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
  font-weight: 500;
}

.replay-btn span {
  font-size: 1rem;
}

.scale-toggle-btn.active, .replay-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.replay-btn.active {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.scale-toggle-btn:hover, .zoom-btn:hover, .replay-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.replay-btn:hover {
  background-color: var(--danger-color);
}

.full-view-btn {
  background-color: var(--light-color);
  color: var(--text-color);
  font-size: 1rem;
}

.full-view-btn:hover {
  background-color: var(--success-color);
}

.chart {
  width: 100%;
  height: 500px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.chart.replay-mode {
  border: 2px solid var(--danger-color);
}

.replay-mode-indicator {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 15;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

/* Bar Replay Marker Line */
.marker-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(255, 0, 0, 0.7);
  pointer-events: none;
  z-index: 10;
}

/* Mouse-following marker line */
.marker-line:not(.marker-line-fixed) {
  background-color: rgba(255, 0, 0, 0.5);
  box-shadow: none;
}

/* Fixed marker line (after clicking) */
.marker-line-fixed {
  width: 2px;
  background-color: rgba(255, 0, 0, 0.9);
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.marker-date {
  position: absolute;
  bottom: 5px;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: bold;
  z-index: 11;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

/* Floating date that follows mouse */
.marker-date-floating {
  background-color: rgba(0, 0, 0, 0.6);
  font-weight: normal;
  font-size: 12px;
  padding: 4px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.bar-replay-btn {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 8px 14px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  z-index: 12;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  font-size: 14px;
  letter-spacing: 0.5px;
}

.bar-replay-btn:hover {
  background-color: #c82333;
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.bar-replay-btn:active {
  transform: translateX(-50%) translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.bar-replay-btn:disabled {
  background-color: var(--gray-color);
  cursor: not-allowed;
  transform: translateX(-50%);
  box-shadow: none;
  opacity: 0.7;
}

.zoom-instructions {
  text-align: center;
  font-size: 0.95rem;
  color: var(--text-color);
  margin-top: 20px;
  margin-bottom: 30px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  font-weight: 600;
  position: relative;
  z-index: 5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

.legend, .chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  justify-content: center;
  position: relative;
  z-index: 10; /* Ensure legend appears above chart elements */
}

.legend {
  margin-top: 60px;
  margin-bottom: 20px;
  border: 2px solid var(--border-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.chart-legend {
  margin: 0;
  background-color: var(--card-bg);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 600;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin: 5px;
}

.legend-color {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Message popup styles (common for error and success) */
.message-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.message-popup-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 450px;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

.message-popup-header {
  color: white;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  position: relative;
}

.error-header {
  background-color: var(--danger-color);
}

.success-header {
  background-color: var(--success-color);
}

.message-popup-header h3 {
  margin: 0;
  font-size: 1.2rem;
  margin-left: 10px;
}

.message-icon {
  font-size: 1.4rem;
}

.close-button {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.close-button:hover {
  opacity: 1;
}

.message-popup-body {
  padding: 20px;
}

.message-popup-body p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
}

.message-popup-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
}

.message-ok-button {
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.error-ok-button {
  background-color: var(--primary-color);
}

.error-ok-button:hover {
  background-color: var(--secondary-color);
}

.success-ok-button {
  background-color: var(--success-color);
}

.success-ok-button:hover {
  background-color: #218838; /* Darker green */
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.metrics-table, .scores-table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-table, .scores-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--card-bg);
}

.metrics-table th, .metrics-table td,
.scores-table th, .scores-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

/* Ensure all numeric cells are right-aligned */
.metrics-table th:not(:first-child),
.metrics-table td:not(:first-child) {
  text-align: right;
}

.metrics-table th, .scores-table th {
  background-color: var(--table-header-bg);
  font-weight: 600;
  color: var(--text-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Sortable header styles */
.sortable-header {
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s;
  user-select: none;
}

.sortable-header:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.sortable-header::after {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 5px;
  vertical-align: middle;
  transition: transform 0.2s;
}

/* Add a subtle indicator on hover */
.sortable-header:hover::after {
  opacity: 0.5;
}

/* Active sort indicator is handled by the component's getSortIndicator function */

.metrics-table tbody tr:hover, .scores-table tbody tr:hover {
  background-color: var(--highlight-row-bg);
}

.metrics-table tr:last-child td,
.scores-table tr:last-child td {
  border-bottom: none;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-muted {
  color: var(--text-muted);
}

.highlight-row {
  background-color: var(--highlight-row-bg);
}

/* Asset-specific row styling for performance metrics table */
.metrics-table tr.asset-row {
  position: relative;
  transition: all 0.2s ease;
}

.metrics-table tr.asset-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--asset-color, var(--primary-color));
  opacity: 0.8;
}

.metrics-table tr.asset-row:hover::before {
  width: 6px;
  opacity: 1;
}

/* Add subtle background tint for asset rows */
.metrics-table tr.asset-row {
  background-color: rgba(var(--asset-color-rgb, 0, 0, 0), 0.05);
}

/* Darker background on hover */
.metrics-table tr.asset-row:hover {
  background-color: rgba(var(--asset-color-rgb, 0, 0, 0), 0.1);
}

/* Strategy row styling */
.metrics-table tr.strategy-row {
  position: relative;
  transition: all 0.2s ease;
}

.metrics-table tr.strategy-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom,
    var(--btc-color) 0%,
    var(--eth-color) 33%,
    var(--sol-color) 66%,
    var(--sui-color) 100%);
  opacity: 0.8;
}

.metrics-table tr.strategy-row:hover::before {
  width: 6px;
  opacity: 1;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.25rem;
  color: var(--text-muted);
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.25rem;
  color: var(--danger-color);
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--danger-color);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.warning {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.25rem;
  color: var(--warning-color);
  background-color: rgba(255, 152, 0, 0.1);
  border: 1px solid var(--warning-color);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.loading::after {
  content: "...";
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: "."; }
  40% { content: ".."; }
  60%, 100% { content: "..."; }
}

.footer {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Strategy controls */
.strategy-params {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.param-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.asset-selector-container {
  margin-top: 20px;
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
}

.param-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.param-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}

/* Info box for settings display */
.info-row {
  justify-content: center;
  margin: 5px 0;
}

/* Allocation mode styles */
.weights-display {
  font-weight: 500;
  color: var(--text-primary);
  background-color: rgba(0, 0, 0, 0.1);
  padding: 3px 8px;
  border-radius: 4px;
}

.info-box {
  background-color: var(--info-box-bg);
  border: 1px solid var(--info-box-border);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.info-label {
  font-weight: 500;
  color: var(--primary-color);
}

.info-value {
  color: var(--text-color);
}

.info-note {
  font-style: italic;
  color: var(--text-muted);
  font-size: 12px;
}

.param-group select,
.param-group input[type="date"],
.param-group input[type="number"],
.param-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--card-bg);
  color: var(--text-color);
  cursor: pointer;
  opacity: 1;
  min-width: 120px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Style for number inputs specifically */
.param-group input[type="number"] {
  font-weight: 500;
  text-align: right;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

/* Special styling for capital and fee inputs */
.capital-input, .fee-input {
  font-weight: 600 !important;
  text-align: right !important;
  min-width: 120px !important;
  padding: 10px 12px !important;
  border-width: 2px !important;
  border-color: var(--border-color) !important;
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.capital-input:focus, .fee-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(41, 98, 255, 0.2) !important;
  outline: none !important;
}

/* Remove spinner buttons from number inputs in some browsers */
.capital-input::-webkit-inner-spin-button,
.capital-input::-webkit-outer-spin-button,
.fee-input::-webkit-inner-spin-button,
.fee-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
.capital-input, .fee-input {
  -moz-appearance: textfield;
}

.mtpi-type-display {
  padding: 8px 12px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
  display: inline-block;
  color: var(--text-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.run-strategy-btn {
  background-color: var(--success-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.run-strategy-btn:hover {
  background-color: #218838;
}

.run-strategy-btn:disabled {
  background-color: var(--gray-color);
  cursor: not-allowed;
}

.cancel-strategy-btn {
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  margin-left: 10px;
}

.cancel-strategy-btn:hover {
  background-color: #c82333;
}

.cancel-strategy-btn:disabled {
  background-color: var(--gray-color);
  cursor: not-allowed;
}

.strategy-buttons {
  display: flex;
  align-items: center;
}

/* Strategy progress bar styles */
.strategy-progress-container {
  width: 100%;
  margin-top: 15px;
  padding: 0 10px;
}

.strategy-progress-bar {
  width: 100%;
  height: 12px;
  background-color: var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  position: relative;
}

.strategy-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color) 0%, var(--primary-color) 100%);
  border-radius: 6px;
  transition: width 0.5s ease-out; /* Smoother transition */
  box-shadow: 0 0 5px rgba(41, 98, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.strategy-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: progress-animation 1s linear infinite;
  z-index: 1;
}

@keyframes progress-animation {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

.strategy-progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.strategy-progress-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
}

.strategy-elapsed-time {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.time-icon {
  font-size: 14px;
  color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-info {
    margin-top: 10px;
  }

  .last-run-info {
    width: 100%;
  }

  .param-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .metrics-table {
    display: block;
    overflow-x: auto;
  }
}

.footer {
  text-align: center;
  margin-top: 30px;
  padding: 20px 0;
  color: var(--text-muted);
  font-size: 0.875rem;
  border-top: 1px solid var(--border-color);
}



