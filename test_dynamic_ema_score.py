#!/usr/bin/env python3
"""
Test script for Dynamic EMA Score indicator implementation.

This script tests the Dynamic EMA Score indicator with BTC/USDT data
and compares the results with the expected PineScript behavior.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.dynamic_ema_score import generate_dynamic_ema_score_signal, calculate_dynamic_ema_score
from src.config_manager import load_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_dynamic_ema_score_basic():
    """Test basic Dynamic EMA Score functionality with default parameters."""
    print("\n" + "="*60)
    print("TESTING DYNAMIC EMA SCORE INDICATOR")
    print("="*60)
    
    try:
        # Fetch BTC data
        print("📊 Fetching BTC/USDT data...")
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=100
        )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return False
        
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Loaded {len(btc_df)} candles from {btc_df.index[0]} to {btc_df.index[-1]}")
        
        # Test with default parameters (matching PineScript)
        print("\n🔧 Testing Dynamic EMA Score with default parameters:")
        print("   - Median Length: 9")
        print("   - Median Source: close")
        print("   - EMA Length: 12")
        print("   - Smoothing Style: Weighted SD")
        print("   - SD Length: 33")
        print("   - Upper SD Weight: 1.017")
        print("   - Lower SD Weight: 0.996")
        
        # Calculate Dynamic EMA Score
        signal, dwmas, upper_band, lower_band = calculate_dynamic_ema_score(
            df=btc_df,
            median_length=9,
            median_src='close',
            ema_length=12,
            smoothing_style='Weighted SD',
            sd_length=33,
            upper_sd_weight=1.017,
            lower_sd_weight=0.996
        )
        
        # Generate binary signals
        binary_signal = generate_dynamic_ema_score_signal(
            df=btc_df,
            median_length=9,
            median_src='close',
            ema_length=12,
            smoothing_style='Weighted SD',
            sd_length=33,
            upper_sd_weight=1.017,
            lower_sd_weight=0.996
        )
        
        # Display results
        print(f"\n📈 Dynamic EMA Score Results (last 10 values):")
        print("-" * 70)
        
        for i in range(max(0, len(btc_df)-10), len(btc_df)):
            date = btc_df.index[i].strftime('%Y-%m-%d')
            close_price = btc_df['close'].iloc[i]
            dwmas_val = dwmas.iloc[i] if not pd.isna(dwmas.iloc[i]) else 0
            upper_val = upper_band.iloc[i] if not pd.isna(upper_band.iloc[i]) else 0
            lower_val = lower_band.iloc[i] if not pd.isna(lower_band.iloc[i]) else 0
            signal_val = signal.iloc[i]
            
            signal_text = "🟢 LONG" if signal_val == 1 else "🔴 SHORT" if signal_val == -1 else "⚪ NEUTRAL"
            
            print(f"{date}: Close={close_price:8.2f} | DWMAS={dwmas_val:8.2f} | "
                  f"Upper={upper_val:8.2f} | Lower={lower_val:8.2f} | {signal_text}")
        
        # Summary statistics
        long_signals = (binary_signal == 1).sum()
        short_signals = (binary_signal == -1).sum()
        neutral_signals = (binary_signal == 0).sum()
        
        print(f"\n📊 Signal Summary:")
        print(f"   🟢 Long signals: {long_signals}")
        print(f"   🔴 Short signals: {short_signals}")
        print(f"   ⚪ Neutral signals: {neutral_signals}")
        print(f"   📈 Latest signal: {binary_signal.iloc[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Dynamic EMA Score: {e}")
        logging.error(f"Error in Dynamic EMA Score test: {e}")
        return False

def test_dynamic_ema_score_with_config():
    """Test Dynamic EMA Score with configuration from YAML."""
    print("\n" + "="*60)
    print("TESTING DYNAMIC EMA SCORE WITH YAML CONFIG")
    print("="*60)
    
    try:
        # Load configuration
        config = load_config()
        settings = config.get('settings', {})
        mtpi_indicators = settings.get('mtpi_indicators', {})
        dynamic_config = mtpi_indicators.get('dynamic_ema_score', {})
        
        print(f"📋 Loaded Dynamic EMA config from YAML: {dynamic_config}")
        
        # Fetch BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=50
        )
        
        btc_df = data_dict['BTC/USDT']
        
        # Test with YAML parameters
        signal = generate_dynamic_ema_score_signal(
            df=btc_df,
            median_length=dynamic_config.get('median_length', 9),
            median_src=dynamic_config.get('median_src', 'close'),
            ema_length=dynamic_config.get('ema_length', 12),
            smoothing_style=dynamic_config.get('smoothing_style', 'Weighted SD'),
            sd_length=dynamic_config.get('sd_length', 33),
            upper_sd_weight=dynamic_config.get('upper_sd_weight', 1.017),
            lower_sd_weight=dynamic_config.get('lower_sd_weight', 0.996),
            atr_period=dynamic_config.get('atr_period', 14),
            atr_multiplier=dynamic_config.get('atr_multiplier', 1.2)
        )
        
        print(f"✅ Generated Dynamic EMA signals using YAML config")
        print(f"📈 Latest signal: {signal.iloc[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Dynamic EMA Score with config: {e}")
        return False

def test_different_smoothing_styles():
    """Test Dynamic EMA Score with different smoothing styles."""
    print("\n" + "="*60)
    print("TESTING DYNAMIC EMA SCORE WITH DIFFERENT SMOOTHING STYLES")
    print("="*60)
    
    try:
        # Fetch BTC data
        data_dict = fetch_ohlcv_data(
            exchange_id='binance',
            symbols=['BTC/USDT'],
            timeframe='1d',
            limit=50
        )
        
        btc_df = data_dict['BTC/USDT']
        
        smoothing_styles = ['ATR', 'Weighted SD']
        
        for style in smoothing_styles:
            print(f"\n🔧 Testing with {style} smoothing...")
            
            signal = generate_dynamic_ema_score_signal(
                df=btc_df,
                median_length=9,
                median_src='close',
                ema_length=12,
                smoothing_style=style,
                sd_length=33,
                upper_sd_weight=1.017,
                lower_sd_weight=0.996,
                atr_period=14,
                atr_multiplier=1.2
            )
            
            latest_signal = signal.iloc[-1]
            signal_text = "🟢 LONG" if latest_signal == 1 else "🔴 SHORT" if latest_signal == -1 else "⚪ NEUTRAL"
            
            print(f"   {style}: {signal_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing different smoothing styles: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 DYNAMIC EMA SCORE INDICATOR TEST SUITE")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    test_results.append(("Basic Dynamic EMA Score Test", test_dynamic_ema_score_basic()))
    test_results.append(("YAML Config Test", test_dynamic_ema_score_with_config()))
    test_results.append(("Different Smoothing Styles Test", test_different_smoothing_styles()))
    
    # Summary
    print("\n" + "="*60)
    print("TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All tests passed! Dynamic EMA Score indicator is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
