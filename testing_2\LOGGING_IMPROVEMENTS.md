# Logging Improvements

This document summarizes the detailed logging improvements made to help diagnose issues with the asset rotation strategy, particularly with the MTPI signal handling.

## Main.py Improvements

1. Added detailed system information logging:
   - Python version
   - Platform information
   - Current working directory
   - Available modules

2. Enhanced MTPI signal logging:
   - Signal distribution statistics (bullish/bearish/neutral percentages)
   - Warning for extremely skewed distributions
   - Detailed logging of signals around the analysis start date
   - Time difference between signals and analysis start date

3. Improved equity curve calculation logging:
   - Strategy start date and timezone information
   - Data availability at strategy start date
   - MTPI signal status at strategy start date
   - Asset distribution in the strategy
   - Equity curve statistics (start/end values, total return, max drawdown)

4. Enhanced exception handling:
   - Full traceback logging
   - Specific error type detection and helpful messages
   - More detailed error information in the returned data

## MTPI_signal_handler.py Improvements

1. Enhanced PGO calculation logging:
   - Input DataFrame shape and columns
   - NaN value detection in input data
   - First and last few rows of input data
   - SMA and ATR calculation details
   - SMA and ATR value samples
   - Zero and NaN value detection in ATR
   - PGO value samples
   - Extreme PGO value detection

2. Improved historical MTPI signal fetching:
   - Detailed parameter logging
   - BTC data statistics (candle count, date range)
   - Data column and type information
   - Gap detection in the data
   - Signal distribution statistics

## Data_fetcher.py Improvements

1. Enhanced data fetching logging:
   - Exchange capabilities (available timeframes)
   - Raw candle data samples
   - DataFrame shape and column information before processing

2. Improved data cleaning and formatting logging:
   - Duplicate timestamp detection and removal
   - Timestamp range and ordering checks
   - Numeric conversion error handling
   - NaN value detection and sample logging
   - Final DataFrame statistics
   - Extreme value detection in price data

## Frontend Improvements

1. Enhanced strategy execution logging:
   - Detailed parameter logging
   - Browser information
   - API call timing
   - Response status and headers
   - Response data size
   - Detailed error information from API responses

## API Server Improvements

1. Enhanced request handling:
   - Detailed parameter logging
   - Response structure logging
   - Improved error handling with full traceback
   - Specific error type detection and helpful messages

These logging improvements will help identify issues with the strategy execution, particularly with the MTPI signal handling and data processing. The logs will provide detailed information about the data flow, calculations, and any errors that occur during execution.
