#!/usr/bin/env python3
"""
Startup script for Person 2's background service.
Loads their specific environment variables.
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

def main():
    # Load Person 2's environment variables
    env_file = Path('.env.person2')
    if env_file.exists():
        load_dotenv(env_file)
        print("✓ Loaded Person 2's environment variables")
        print(f"  - Binance API Key: {'*****' + os.getenv('BINANCE_API_KEY', '')[-4:] if os.getenv('BINANCE_API_KEY') else 'Not set'}")
        print(f"  - Telegram Bot Token: {'*****' + os.getenv('TELEGRAM_BOT_TOKEN', '')[-4:] if os.getenv('TELEGRAM_BOT_TOKEN') else 'Not set'}")
        print(f"  - Telegram Chat ID: {os.getenv('TELEGRAM_CHAT_ID', 'Not set')}")
    else:
        print("ERROR: .env.person2 file not found!")
        print("Please create .env.person2 with Person 2's credentials")
        sys.exit(1)

    # Start the background service
    print("\nStarting background service for Person 2...")
    try:
        subprocess.run([
            sys.executable,
            'background_service.py',
            '--log-level', 'INFO'
        ], check=True)
    except KeyboardInterrupt:
        print("\nService stopped by user")
    except Exception as e:
        print(f"Error running service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
