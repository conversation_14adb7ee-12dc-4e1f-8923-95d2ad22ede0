#!/usr/bin/env python3
"""
Test script to debug the rebalancing drift detection issue.
"""

import logging
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main_program import AllocationTester

def test_rebalancing_debug():
    """Test rebalancing with debug logging to identify the issue."""
    
    # Set up logging to see debug information
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    print("Testing rebalancing drift detection...")
    
    # Test with a small date range to see the issue quickly
    tester = AllocationTester(
        timeframe='1d',
        mtpi_timeframe='1d',
        analysis_start_date='2021-04-01',  # Start from the date where we see issues
        analysis_end_date='2021-05-01',    # Short period to debug
        n_assets=2,
        transaction_fee_rate=0.001,
        selected_assets=['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'LINK/USDT'],
        use_cache=True,
        use_weighted_allocation=True,
        weights=[0.8, 0.2],
        enable_rebalancing=True,
        rebalance_threshold=0.05,  # 5% threshold
        wait_for_confirmed_signals=True,
        use_mtpi=True
    )
    
    # Run the strategy
    equity_curve, assets_held, mtpi_stats = tester.run_strategy_with_detailed_logging()

    # Analyze the results
    allocation_df, return_df = tester.analyze_allocation()
    
    print("\nTest completed. Check the logs above for drift detection details.")
    
    # Print some summary information
    print(f"\nStrategy Performance:")
    print(f"Final Equity: {equity_curve.iloc[-1]:.4f}")
    print(f"Total Return: {((equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1) * 100:.2f}%")
    
    # Check allocation history for rebalancing events
    rebalancing_events = [entry for entry in tester.allocation_history if entry.get('rebalanced', False)]
    print(f"\nRebalancing Events: {len(rebalancing_events)}")
    
    for i, event in enumerate(rebalancing_events[:10]):  # Show first 10 events
        print(f"  {i+1}. {event['date'].date()}: {event['rebalancing_reason']}")
        if 'weights' in event:
            print(f"     Holdings: {event['weights']}")

if __name__ == "__main__":
    test_rebalancing_debug()
