#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify PGO signal consistency between universal visualizer and background service
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_fetcher import fetch_ohlcv_data
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from src.indicators.ratio_indicators import calculate_ratio_pgo_with_signal_from_dataframes

def create_ratio_ohlcv_visualizer_method(df_a: pd.DataFrame, df_b: pd.DataFrame) -> pd.DataFrame:
    """Create OHLCV data for the ratio between two assets (visualizer method)."""
    # Align dataframes by common index
    common_index = df_a.index.intersection(df_b.index)
    df_a_aligned = df_a.reindex(common_index)
    df_b_aligned = df_b.reindex(common_index)
    
    # Calculate ratio OHLCV
    ratio_df = pd.DataFrame(index=common_index)
    ratio_df['open'] = df_a_aligned['open'] / df_b_aligned['open']
    ratio_df['high'] = df_a_aligned['high'] / df_b_aligned['high']
    ratio_df['low'] = df_a_aligned['low'] / df_b_aligned['low']
    ratio_df['close'] = df_a_aligned['close'] / df_b_aligned['close']
    ratio_df['volume'] = df_a_aligned['volume']  # Use volume from asset A
    
    return ratio_df.dropna()

def test_pgo_consistency():
    """Test that both methods produce the same PGO signals."""
    print("Testing PGO signal consistency between visualizer and background service...")
    
    # Fetch test data for AAVE and TRX
    symbols = ['AAVE/USDT', 'TRX/USDT']
    print(f"Fetching data for {symbols}...")
    
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=symbols,
        timeframe='1d',
        limit=100,  # Get recent 100 days
        force_refresh=True
    )
    
    if not data_dict or len(data_dict) < 2:
        print("ERROR: Failed to fetch data")
        return False
    
    aave_df = data_dict['AAVE/USDT']
    trx_df = data_dict['TRX/USDT']
    
    print(f"AAVE data: {len(aave_df)} candles from {aave_df.index[0]} to {aave_df.index[-1]}")
    print(f"TRX data: {len(trx_df)} candles from {trx_df.index[0]} to {trx_df.index[-1]}")
    
    # Test parameters
    pgo_length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    
    print(f"\nTesting with PGO parameters: length={pgo_length}, upper={upper_threshold}, lower={lower_threshold}")
    
    # Method 1: Universal Visualizer approach
    print("\n1. Universal Visualizer Method:")
    ratio_df_visualizer = create_ratio_ohlcv_visualizer_method(aave_df, trx_df)
    pgo_values_visualizer = calculate_pgo(ratio_df_visualizer, length=pgo_length)
    pgo_signal_visualizer = generate_pgo_signal(
        df=ratio_df_visualizer,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )
    
    print(f"  Ratio data points: {len(ratio_df_visualizer)}")
    print(f"  PGO values: {len(pgo_values_visualizer)} (last: {pgo_values_visualizer.iloc[-1]:.6f})")
    print(f"  PGO signals: {len(pgo_signal_visualizer)} (last: {pgo_signal_visualizer.iloc[-1]})")
    
    # Method 2: Background Service approach (new)
    print("\n2. Background Service Method (Updated):")
    ratio_close_bg, pgo_values_bg, pgo_signal_bg = calculate_ratio_pgo_with_signal_from_dataframes(
        aave_df, trx_df, pgo_length, upper_threshold, lower_threshold
    )
    
    print(f"  Ratio data points: {len(ratio_close_bg)}")
    print(f"  PGO values: {len(pgo_values_bg)} (last: {pgo_values_bg.iloc[-1]:.6f})")
    print(f"  PGO signals: {len(pgo_signal_bg)} (last: {pgo_signal_bg.iloc[-1]})")
    
    # Compare results
    print("\n3. Comparison:")
    
    # Find common index
    common_idx = pgo_values_visualizer.index.intersection(pgo_values_bg.index)
    if len(common_idx) == 0:
        print("ERROR: No common dates between methods")
        return False
    
    print(f"  Common data points: {len(common_idx)}")
    
    # Compare PGO values
    pgo_diff = (pgo_values_visualizer.loc[common_idx] - pgo_values_bg.loc[common_idx]).abs()
    max_pgo_diff = pgo_diff.max()
    mean_pgo_diff = pgo_diff.mean()
    
    print(f"  PGO value differences: max={max_pgo_diff:.8f}, mean={mean_pgo_diff:.8f}")
    
    # Compare signals
    signal_diff = (pgo_signal_visualizer.loc[common_idx] - pgo_signal_bg.loc[common_idx]).abs()
    signal_mismatches = signal_diff.sum()
    
    print(f"  Signal mismatches: {signal_mismatches} out of {len(common_idx)} ({signal_mismatches/len(common_idx)*100:.1f}%)")
    
    # Show last few values for debugging
    print("\n4. Last 5 values comparison:")
    print("Date                PGO_Visualizer  PGO_Background   Signal_Vis  Signal_BG")
    print("-" * 75)
    
    last_5_idx = common_idx[-5:]
    for date in last_5_idx:
        pgo_vis = pgo_values_visualizer.loc[date]
        pgo_bg = pgo_values_bg.loc[date]
        sig_vis = pgo_signal_visualizer.loc[date]
        sig_bg = pgo_signal_bg.loc[date]
        
        print(f"{date.strftime('%Y-%m-%d')}      {pgo_vis:+10.6f}    {pgo_bg:+10.6f}       {sig_vis:2d}        {sig_bg:2d}")
    
    # Final assessment
    print(f"\n5. Final Assessment:")
    if max_pgo_diff < 1e-6 and signal_mismatches == 0:
        print("✅ SUCCESS: Methods are consistent!")
        return True
    elif signal_mismatches == 0:
        print("✅ SUCCESS: Signals match (small PGO differences are acceptable)")
        return True
    else:
        print("❌ FAILURE: Signal mismatches detected")
        return False

if __name__ == "__main__":
    success = test_pgo_consistency()
    sys.exit(0 if success else 1)
