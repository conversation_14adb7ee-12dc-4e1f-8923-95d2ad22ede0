#!/usr/bin/env python3
"""
Test script to verify paper trading works with multiple exchanges.
This tests Binance, Kraken, and Bitvavo using our existing paper trading system.
"""

import sys
import logging
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from src.trading.executor import TradingExecutor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_paper_trading_for_exchange(exchange_id: str) -> dict:
    """Test paper trading functionality for a specific exchange."""
    results = {
        'exchange': exchange_id,
        'initialization': False,
        'market_data': False,
        'paper_balance': False,
        'paper_buy_order': False,
        'paper_sell_order': False,
        'portfolio_tracking': False,
        'errors': []
    }
    
    print(f"\n{'='*60}")
    print(f"TESTING PAPER TRADING FOR {exchange_id.upper()}")
    print(f"{'='*60}")
    
    try:
        # Test 1: Initialize trading executor in paper mode
        print(f"1. Initializing trading executor...")
        executor = TradingExecutor(exchange_id=exchange_id, test_mode=True)
        
        # Force paper trading mode
        executor.set_trading_mode('paper')
        
        if executor.paper_trading:
            print(f"✅ Paper trading executor initialized")
            results['initialization'] = True
        else:
            print(f"❌ Paper trading executor not initialized")
            results['errors'].append("Paper trading executor not initialized")
            return results
        
        # Test 2: Get market data (this should work without credentials)
        print(f"2. Testing market data access...")
        try:
            # Test getting current price for exchange-appropriate pairs
            if exchange_id == 'bitvavo':
                test_symbol = 'BTC/EUR'  # Bitvavo uses EUR
            else:
                test_symbol = 'BTC/USDT'  # Most exchanges have this

            price = executor.get_current_price(test_symbol)
            print(f"✅ Market data access successful: {test_symbol} = ${price:,.2f}")
            results['market_data'] = True
        except Exception as e:
            print(f"❌ Market data access failed: {e}")
            results['errors'].append(f"Market data access failed: {e}")
        
        # Test 3: Check paper trading balance
        print(f"3. Testing paper trading balance...")
        try:
            balance = executor.paper_trading.get_balance()
            print(f"✅ Paper balance: {balance}")
            results['paper_balance'] = True
        except Exception as e:
            print(f"❌ Paper balance check failed: {e}")
            results['errors'].append(f"Paper balance check failed: {e}")
        
        # Test 4: Execute paper buy order
        print(f"4. Testing paper buy order...")
        try:
            # Use appropriate symbol and quote currency for each exchange
            if exchange_id == 'bitvavo':
                test_symbol = 'BTC/EUR'
                quote_currency = 'EUR'
                # Set up EUR balance for Bitvavo
                executor.paper_trading.balances = {'EUR': 1000.0}
            else:
                test_symbol = 'BTC/USDT'
                quote_currency = 'USDT'
                # Set up USDT balance for other exchanges
                executor.paper_trading.balances = {'USDT': 1000.0}

            test_amount = 100.0  # $100 worth
            current_price = executor.get_current_price(test_symbol)

            order = executor.paper_trading.create_market_buy_order(
                test_symbol, test_amount, current_price
            )
            
            if order and 'id' in order:
                print(f"✅ Paper buy order successful: {order['id']}")
                print(f"   Bought {order['filled']:.8f} BTC at ${order['price']:,.2f}")
                results['paper_buy_order'] = True
            else:
                print(f"❌ Paper buy order failed: empty order returned")
                results['errors'].append("Paper buy order failed: empty order returned")
                
        except Exception as e:
            print(f"❌ Paper buy order failed: {e}")
            results['errors'].append(f"Paper buy order failed: {e}")
        
        # Test 5: Check updated balance after buy
        print(f"5. Checking balance after buy...")
        try:
            balance_after_buy = executor.paper_trading.get_balance()
            print(f"✅ Balance after buy: {balance_after_buy}")
            
            # Check if we have BTC position
            positions = executor.paper_trading.get_positions()
            if positions:
                print(f"✅ Positions: {positions}")
                results['portfolio_tracking'] = True
            else:
                print(f"⚠️ No positions found after buy order")
                
        except Exception as e:
            print(f"❌ Balance check after buy failed: {e}")
            results['errors'].append(f"Balance check after buy failed: {e}")
        
        # Test 6: Execute paper sell order (if we have a position)
        print(f"6. Testing paper sell order...")
        try:
            positions = executor.paper_trading.get_positions()
            # Use the same test_symbol from the buy order
            if exchange_id == 'bitvavo':
                test_symbol = 'BTC/EUR'
            else:
                test_symbol = 'BTC/USDT'

            if test_symbol in positions:
                position = positions[test_symbol]
                sell_amount = position['amount'] * 0.5  # Sell half
                current_price = executor.get_current_price(test_symbol)

                sell_order = executor.paper_trading.create_market_sell_order(
                    test_symbol, sell_amount, current_price
                )
                
                if sell_order and 'id' in sell_order:
                    print(f"✅ Paper sell order successful: {sell_order['id']}")
                    print(f"   Sold {sell_order['filled']:.8f} BTC at ${sell_order['price']:,.2f}")
                    results['paper_sell_order'] = True
                else:
                    print(f"❌ Paper sell order failed: empty order returned")
                    results['errors'].append("Paper sell order failed: empty order returned")
            else:
                print(f"⚠️ No position to sell (this might be expected if buy failed)")
                
        except Exception as e:
            print(f"❌ Paper sell order failed: {e}")
            results['errors'].append(f"Paper sell order failed: {e}")
        
        # Test 7: Final balance and position check
        print(f"7. Final balance and position check...")
        try:
            final_balance = executor.paper_trading.get_balance()
            final_positions = executor.paper_trading.get_positions()
            
            print(f"✅ Final balance: {final_balance}")
            print(f"✅ Final positions: {final_positions}")
            
            # Calculate total portfolio value
            if exchange_id == 'bitvavo':
                total_value = final_balance.get('EUR', 0)
            else:
                total_value = final_balance.get('USDT', 0)

            for _, position in final_positions.items():
                total_value += position.get('current_value', 0)
            
            print(f"✅ Total portfolio value: ${total_value:,.2f}")
            
        except Exception as e:
            print(f"❌ Final check failed: {e}")
            results['errors'].append(f"Final check failed: {e}")
            
    except Exception as e:
        error_msg = f"Unexpected error testing {exchange_id}: {e}"
        results['errors'].append(error_msg)
        print(f"❌ {error_msg}")
    
    return results

def print_summary(all_results: dict):
    """Print a summary of all test results."""
    print(f"\n{'='*70}")
    print(f"MULTI-EXCHANGE PAPER TRADING TEST SUMMARY")
    print(f"{'='*70}")
    
    # Create summary table
    exchanges = list(all_results.keys())
    tests = ['initialization', 'market_data', 'paper_balance', 'paper_buy_order', 'paper_sell_order', 'portfolio_tracking']
    
    print(f"\n{'Test':<20} {'Binance':<10} {'Kraken':<10} {'Bitvavo':<10}")
    print(f"{'-'*20} {'-'*10} {'-'*10} {'-'*10}")
    
    for test in tests:
        row = f"{test.replace('_', ' ').title():<20}"
        for exchange in exchanges:
            result = all_results[exchange].get(test, False)
            status = "✅" if result else "❌"
            row += f" {status:<10}"
        print(row)
    
    # Count successes
    print(f"\n{'Exchange':<10} {'Success Rate':<15} {'Errors':<10}")
    print(f"{'-'*10} {'-'*15} {'-'*10}")
    
    for exchange, results in all_results.items():
        successes = sum(1 for test in tests if results.get(test, False))
        success_rate = f"{successes}/{len(tests)}"
        error_count = len(results.get('errors', []))
        
        print(f"{exchange.capitalize():<10} {success_rate:<15} {error_count:<10}")
    
    # Overall assessment
    print(f"\n{'='*70}")
    print(f"ASSESSMENT")
    print(f"{'='*70}")
    
    all_working = True
    for exchange, results in all_results.items():
        key_tests = ['initialization', 'market_data', 'paper_buy_order']
        working = all(results.get(test, False) for test in key_tests)
        
        if working:
            print(f"✅ {exchange.upper()}: Paper trading fully functional!")
        else:
            print(f"❌ {exchange.upper()}: Issues detected")
            all_working = False
            
            # Show specific issues
            for test in key_tests:
                if not results.get(test, False):
                    print(f"   - {test.replace('_', ' ').title()}: Failed")
    
    if all_working:
        print(f"\n🎉 EXCELLENT! Paper trading works on ALL exchanges!")
        print(f"📝 NEXT STEPS:")
        print(f"1. Set up API credentials for Kraken and Bitvavo")
        print(f"2. Test with small amounts in live mode")
        print(f"3. Configure multi-exchange background service")
    else:
        print(f"\n⚠️ Some issues detected. Check the errors above.")

def main():
    """Main function to test paper trading across multiple exchanges."""
    print(f"\n{'='*70}")
    print(f"MULTI-EXCHANGE PAPER TRADING TEST")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*70}")
    
    # Test all exchanges
    exchanges = ['binance', 'kraken', 'bitvavo']
    all_results = {}
    
    for exchange_id in exchanges:
        all_results[exchange_id] = test_paper_trading_for_exchange(exchange_id)
    
    # Print comprehensive summary
    print_summary(all_results)

if __name__ == "__main__":
    main()
