# Rate Limiting Solution for Binance API

## Problem Analysis

The background service was encountering **503 Service Temporarily Unavailable** errors from Binance's CloudFront CDN with the message:
```
This distribution exceeded the limit on requests per second.
```

This occurred because the system was making too many API requests in rapid succession when:
1. Fetching data for 14 different assets
2. Getting account balance information
3. Executing trades

## Binance API Rate Limits (2025)

- **Request Weight**: 1200 per minute (20 requests per second average)
- **Raw Requests**: ~20 requests per second
- **Account Balance endpoint** (`GET /api/v3/account`): Weight of 10

## Solution Implemented

### 1. Rate Limiting Infrastructure

Added rate limiting functionality to both `AccountManager` and `OrderManager` classes:

**Key Components:**
- `min_api_interval = 0.1` (100ms between calls = max 10 requests/second)
- `max_retries = 3` (retry failed requests up to 3 times)
- `retry_delay = 1.0` (exponential backoff starting at 1 second)

### 2. Rate Limiting Methods

**`_rate_limit_api_call()`:**
- Ensures minimum time interval between API calls
- Tracks last API call time
- Sleeps if necessary to maintain rate limit

**`_make_api_call_with_retry()`:**
- Wraps all API calls with retry logic
- Detects rate limiting errors (503, 429, "rate limit", etc.)
- Implements exponential backoff for retries
- Fails gracefully after max retries

### 3. Updated Methods

**AccountManager:**
- `get_balance()` - Now uses rate-limited API calls
- `get_all_balances()` - Rate limited
- `get_open_positions()` - Rate limited ticker fetches

**OrderManager:**
- `create_market_buy_order()` - Rate limited ticker fetch and order creation
- `create_market_sell_order()` - Rate limited ticker fetch and order creation
- `create_limit_buy_order()` - Rate limited order creation
- `create_limit_sell_order()` - Rate limited order creation
- `cancel_order()` - Rate limited order cancellation
- `get_order()` - Rate limited order fetching
- `get_open_orders()` - Rate limited
- `get_order_history()` - Rate limited

### 4. Error Detection

The system now detects these rate limiting errors:
- HTTP 503 (Service Temporarily Unavailable)
- HTTP 429 (Too Many Requests)
- "service temporarily unavailable"
- "rate limit"
- "too many requests"
- "exceeded the limit on requests per second"

### 5. Conservative Rate Limiting

**Settings chosen:**
- **10 requests per second** (vs Binance's ~20/sec limit)
- **100ms minimum interval** between calls
- **Exponential backoff** (1s, 2s, 4s delays)

This provides a safety margin to prevent hitting rate limits while maintaining reasonable performance.

## Benefits

1. **Prevents 503 errors** from CloudFront rate limiting
2. **Automatic retry logic** for transient failures
3. **Graceful degradation** under high load
4. **Maintains system stability** during peak usage
5. **Backwards compatible** - no changes to calling code needed

## Testing

Run the test script to verify rate limiting:
```bash
python test_rate_limiting.py
```

## Expected Behavior

- **Before**: Rapid API calls could trigger 503 errors
- **After**: API calls are automatically spaced out with 100ms minimum intervals
- **Retry Logic**: Failed requests are automatically retried with exponential backoff
- **Logging**: Rate limiting and retry attempts are logged for monitoring

## Performance Impact

- **Minimal impact** on normal operations
- **Slight delay** during rapid successive API calls (100ms between calls)
- **Better reliability** vs raw speed trade-off
- **Prevents service disruption** from rate limit bans

The solution prioritizes reliability and stability over raw speed, ensuring the trading system can operate continuously without hitting Binance's rate limits.
