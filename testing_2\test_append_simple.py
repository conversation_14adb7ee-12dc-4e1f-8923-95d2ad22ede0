#!/usr/bin/env python
"""
Simple test script for appending data to cache files.
"""

import os
import sys
import logging
import pandas as pd
import time
import shutil
from datetime import datetime, timedelta
import ccxt

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.data_cache import (
    append_to_cache,
    load_from_cache,
    get_cache_path
)
from src.utils import setup_logging

def setup_test():
    """Set up the test environment."""
    setup_logging(level=logging.INFO)
    
    print("\n" + "=" * 70)
    print("TESTING SIMPLE DATA APPENDING TO CACHE")
    print("=" * 70)
    
    # Test parameters
    exchange_id = 'binance'
    symbols = ['SOL/USDC']
    timeframe = '1m'
    
    # Create backup of original files
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"
        
        if os.path.exists(cache_path):
            print(f"Creating backup of {cache_path} to {backup_path}")
            shutil.copy2(cache_path, backup_path)
    
    return exchange_id, symbols, timeframe

def restore_backups(exchange_id, symbols, timeframe):
    """Restore original files from backups."""
    print("\nRestoring original files from backups...")
    for symbol in symbols:
        symbol_file = symbol.replace('/', '_')
        cache_path = f"data/ohlcv/{exchange_id}/{timeframe}/{symbol_file}.csv"
        backup_path = f"{cache_path}.bak"
        
        if os.path.exists(backup_path):
            print(f"Restoring {cache_path} from {backup_path}")
            shutil.copy2(backup_path, cache_path)
            os.remove(backup_path)

def fetch_missing_data(exchange, symbol, timeframe, last_timestamp_str):
    """Fetch missing data from last_timestamp to now."""
    print(f"\nFetching missing data for {symbol} from {last_timestamp_str} to now...")
    
    # Convert timestamp string to datetime object
    last_timestamp = pd.to_datetime(last_timestamp_str)
    
    # Convert to milliseconds for CCXT
    since_ms = int(last_timestamp.timestamp() * 1000)
    
    try:
        # Fetch candles
        candles = exchange.fetch_ohlcv(symbol, timeframe, since=since_ms, limit=1000)
        
        if not candles:
            print(f"No candles available for {symbol}")
            return None
            
        print(f"Fetched {len(candles)} candles for {symbol}")
        
        # Convert to DataFrame
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        df = pd.DataFrame(candles, columns=columns)
        
        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Set timestamp as index
        df.set_index('timestamp', inplace=True)
        
        # Filter out data that's already in the cache
        df = df[df.index > last_timestamp]
        
        print(f"After filtering, {len(df)} new candles to append")
        
        if not df.empty:
            print(f"New data range: {df.index.min()} to {df.index.max()}")
            print("\nSample of new data:")
            print(df.head(3))
            print("...")
            print(df.tail(3))
        
        return df
        
    except Exception as e:
        print(f"Error fetching data: {e}")
        return None

def main():
    """Main function to run the test."""
    try:
        # Set up the test
        exchange_id, symbols, timeframe = setup_test()
        
        # Initialize the exchange
        exchange = ccxt.binance()
        
        # Process each symbol
        for symbol in symbols:
            try:
                # Load current data
                current_data = load_from_cache(exchange_id, symbol, timeframe)
                if current_data is None or current_data.empty:
                    print(f"No data found for {symbol}, skipping...")
                    continue
                    
                last_timestamp = current_data.index.max()
                print(f"\nProcessing {symbol}...")
                print(f"Loaded {len(current_data)} rows from cache")
                print(f"Last timestamp: {last_timestamp}")
                
                # Fetch missing data
                new_data = fetch_missing_data(exchange, symbol, timeframe, last_timestamp)
                
                if new_data is not None and not new_data.empty:
                    # Append to cache
                    print("\nAppending new data to cache...")
                    append_success = append_to_cache(new_data, exchange_id, symbol, timeframe)
                    
                    if append_success:
                        print(f"✅ Successfully appended {len(new_data)} candles for {symbol}")
                        
                        # Load the data again to verify
                        loaded_data = load_from_cache(exchange_id, symbol, timeframe)
                        print(f"Loaded {len(loaded_data)} rows after append")
                        print(f"New last timestamp: {loaded_data.index.max()}")
                        
                        # Show the last few rows
                        print("\nLast 5 rows of the updated file:")
                        print(loaded_data.tail(5))
                    else:
                        print(f"❌ Failed to append data for {symbol}")
                else:
                    print(f"No new data to append for {symbol}")
                
            except Exception as e:
                print(f"Error processing {symbol}: {e}")
        
        print("\nTest completed successfully")
        
    except Exception as e:
        print(f"Error in test: {e}")
    finally:
        # Restore original files
        restore_backups(exchange_id, symbols, timeframe)
        print("\nTest completed and original files restored")

if __name__ == "__main__":
    main()
