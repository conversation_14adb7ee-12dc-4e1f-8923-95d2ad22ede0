#!/usr/bin/env python3
"""
Test script to verify Median Score implementation matches PineScript exactly.

PineScript parameters:
- subject1 = 12 (Supertrend len)
- mul1 = 1.45 (Multiplier)
- slen = 27 (Median len)
- src_me = high (Median smoothing source)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score, calculate_median_smoothing, calculate_median_supertrend
import matplotlib.pyplot as plt

def test_median_pinescript_match():
    """Test if our implementation matches the PineScript exactly."""
    print("=" * 80)
    print("MEDIAN SCORE PINESCRIPT MATCH VERIFICATION")
    print("=" * 80)
    
    # Fetch BTC data for testing
    print("Fetching BTC/USDT data...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Loaded {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # PineScript parameters (exact match)
    pinescript_params = {
        'atr_period': 12,      # subject1
        'multiplier': 1.45,    # mul1
        'median_length': 27,   # slen
        'src_col': 'high'      # src_me
    }
    
    print(f"\nUsing PineScript parameters:")
    for key, value in pinescript_params.items():
        print(f"  {key}: {value}")
    
    # Calculate components step by step
    print(f"\n" + "=" * 60)
    print("STEP-BY-STEP CALCULATION")
    print("=" * 60)
    
    # Step 1: Median smoothing
    print("\n1. Median Smoothing (ta.percentile_nearest_rank(high, 27, 50)):")
    smooth1 = calculate_median_smoothing(btc_df, 
                                       length=pinescript_params['median_length'], 
                                       src_col=pinescript_params['src_col'])
    
    print(f"   Last 5 smooth1 values:")
    for i in range(-5, 0):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        high_val = btc_df['high'].iloc[i]
        smooth_val = smooth1.iloc[i]
        print(f"   {date}: high={high_val:.2f}, smooth1={smooth_val:.2f}")
    
    # Step 2: Calculate Supertrend
    print(f"\n2. Median Supertrend Calculation:")
    supertrend_line, direction = calculate_median_supertrend(btc_df, **pinescript_params)
    
    print(f"   Last 5 supertrend values:")
    for i in range(-5, 0):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        close_val = btc_df['close'].iloc[i]
        st_val = supertrend_line.iloc[i]
        dir_val = direction.iloc[i]
        print(f"   {date}: close={close_val:.2f}, supertrend={st_val:.2f}, direction={dir_val}")
    
    # Step 3: Generate signals
    print(f"\n3. Signal Generation:")
    signal, _, _ = calculate_median_score(btc_df, **pinescript_params)
    
    # Find signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"   Found {len(change_indices)} signal changes")
    print(f"   Last 8 signal changes:")
    
    for change_date in change_indices[-8:]:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            
            print(f"   {change_date.strftime('%Y-%m-%d')}: {prev_signal} → {curr_signal} "
                  f"(close=${close_price:.2f}, dir: {prev_direction}→{direction_val})")
    
    # Step 4: Verify PineScript logic
    print(f"\n" + "=" * 60)
    print("PINESCRIPT LOGIC VERIFICATION")
    print("=" * 60)
    
    # Manually calculate crossovers to verify
    print("\nVerifying crossover logic:")
    print("PineScript logic:")
    print("  stl = ta.crossunder(d1, 0)  # direction crosses under 0")
    print("  sts = ta.crossover(d1, 0)   # direction crosses over 0")
    print("  L1 = stl, S1 = sts")
    print("  if L1 and not S1: medianScore := 1")
    print("  if S1: medianScore := -1")
    
    # Calculate crossovers manually
    crossunder = (direction < 0) & (direction.shift(1) >= 0)
    crossover = (direction > 0) & (direction.shift(1) <= 0)
    
    print(f"\nLast 10 days crossover analysis:")
    for i in range(-10, 0):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        dir_curr = direction.iloc[i]
        dir_prev = direction.iloc[i-1] if i > -len(direction) else 0
        cross_under = crossunder.iloc[i]
        cross_over = crossover.iloc[i]
        signal_val = signal.iloc[i]
        
        print(f"  {date}: dir={dir_prev}→{dir_curr}, "
              f"crossunder={cross_under}, crossover={cross_over}, signal={signal_val}")
    
    # Current status
    print(f"\n" + "=" * 60)
    print("CURRENT STATUS")
    print("=" * 60)
    
    current_price = btc_df['close'].iloc[-1]
    current_signal = signal.iloc[-1]
    current_supertrend = supertrend_line.iloc[-1]
    current_direction = direction.iloc[-1]
    current_smooth = smooth1.iloc[-1]
    
    print(f"Current Price: ${current_price:.2f}")
    print(f"Current Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Supertrend: ${current_supertrend:.2f}")
    print(f"Direction: {current_direction}")
    print(f"Smooth1 (median): ${current_smooth:.2f}")
    print(f"Price vs Supertrend: {((current_price - current_supertrend) / current_supertrend * 100):+.2f}%")
    
    # Plot for visual verification
    print(f"\n" + "=" * 60)
    print("GENERATING VISUAL CHART")
    print("=" * 60)
    
    # Create plot
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # Plot 1: Price and Supertrend
    recent_data = btc_df.tail(100)
    recent_smooth = smooth1.tail(100)
    recent_supertrend = supertrend_line.tail(100)
    recent_direction = direction.tail(100)
    recent_signal = signal.tail(100)
    
    ax1.plot(recent_data.index, recent_data['close'], label='BTC Close', color='black', linewidth=1)
    ax1.plot(recent_data.index, recent_data['high'], label='BTC High', color='gray', alpha=0.5)
    ax1.plot(recent_data.index, recent_smooth, label='Smooth1 (Median)', color='blue', linewidth=1)
    ax1.plot(recent_data.index, recent_supertrend, label='Supertrend', color='purple', linewidth=2)
    ax1.set_title('BTC Price vs Median Supertrend')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Direction
    ax2.plot(recent_data.index, recent_direction, label='Direction', color='orange', linewidth=2)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax2.set_title('Supertrend Direction')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Signals
    ax3.plot(recent_data.index, recent_signal, label='Median Score Signal', color='green', linewidth=2)
    ax3.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    ax3.axhline(y=1, color='green', linestyle='--', alpha=0.5, label='Long')
    ax3.axhline(y=-1, color='red', linestyle='--', alpha=0.5, label='Short')
    ax3.set_title('Median Score Signals')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('median_score_pinescript_verification.png', dpi=300, bbox_inches='tight')
    print("Chart saved as 'median_score_pinescript_verification.png'")
    plt.show()
    
    return pinescript_params, signal, supertrend_line, direction

if __name__ == "__main__":
    test_median_pinescript_match()
