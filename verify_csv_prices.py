#!/usr/bin/env python3
"""
Verify that the prices shown in manual.txt logs match the actual prices in CSV files.
This script checks both daily close prices and 12h candle close prices.
"""

import pandas as pd
import os
import sys

def load_csv_data(symbol, timeframe):
    """Load CSV data for a symbol and timeframe."""
    csv_path = f'data/ohlcv/binance/{timeframe}/{symbol.replace("/", "_")}.csv'
    
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return None
    
    try:
        df = pd.read_csv(csv_path, index_col=0, parse_dates=True)
        print(f"✅ Loaded {csv_path}: {len(df)} candles")
        return df
    except Exception as e:
        print(f"❌ Error loading {csv_path}: {e}")
        return None

def verify_specific_prices():
    """Verify specific prices from the logs against CSV data."""
    print("🔍 VERIFYING SPECIFIC PRICES FROM LOGS")
    print("=" * 60)
    
    # Test cases from the logs
    test_cases = [
        {
            'date': '2023-11-13',
            'symbol': 'AVAX/USDT',
            'log_12pm_price': 17.7375,
            'log_daily_close': 16.1200,
            'description': 'AVAX buy on 2023-11-13 (large difference +10.03%)'
        },
        {
            'date': '2023-12-23',
            'symbol': 'SOL/USDT',
            'log_12pm_price': 102.1475,
            'log_daily_close': 107.8800,
            'description': 'SOL buy on 2023-12-23 (diff -5.31%)'
        },
        {
            'date': '2024-01-12',
            'symbol': 'ETH/USDT',
            'log_12pm_price': 2578.9675,
            'log_daily_close': 2522.5400,
            'description': 'ETH buy on 2024-01-12 (diff +2.24%)'
        },
        {
            'date': '2024-02-09',
            'symbol': 'LINK/USDT',
            'log_12pm_price': 18.3193,
            'log_daily_close': 18.4700,
            'description': 'LINK buy on 2024-02-09 (diff -0.82%)'
        },
        {
            'date': '2024-02-09',
            'symbol': 'SUI/USDT',
            'log_12pm_price': 1.6537,
            'log_daily_close': 1.7612,
            'description': 'SUI buy on 2024-02-09 (diff -6.10%)'
        },
        {
            'date': '2024-12-04',
            'symbol': 'TRX/USDT',
            'log_12pm_price': 0.3839,
            'log_daily_close': 0.3306,
            'description': 'TRX buy on 2024-12-04 (large difference +16.13%)'
        }
    ]
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n📊 TEST {i}: {test['description']}")
        print("-" * 50)
        
        # Load daily data
        daily_data = load_csv_data(test['symbol'], '1d')
        if daily_data is None:
            continue
            
        # Load 12h data
        twelve_h_data = load_csv_data(test['symbol'], '12h')
        if twelve_h_data is None:
            continue
        
        # Check daily close price
        date_str = test['date']
        try:
            daily_date = pd.Timestamp(date_str, tz='UTC')
            if daily_date in daily_data.index:
                csv_daily_close = daily_data.loc[daily_date, 'close']
                log_daily_close = test['log_daily_close']
                
                daily_diff = abs(csv_daily_close - log_daily_close)
                daily_match = daily_diff < 0.01  # Allow small rounding differences
                
                print(f"Daily Close Price:")
                print(f"  CSV:  ${csv_daily_close:.4f}")
                print(f"  Log:  ${log_daily_close:.4f}")
                print(f"  Diff: ${daily_diff:.4f} {'✅ MATCH' if daily_match else '❌ MISMATCH'}")
            else:
                print(f"❌ Daily date {daily_date} not found in CSV")
                continue
        except Exception as e:
            print(f"❌ Error checking daily price: {e}")
            continue
        
        # Check 12h midnight candle close price (should be 12 PM price)
        try:
            midnight_date = pd.Timestamp(date_str + ' 00:00:00', tz='UTC')
            midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0]
            
            if midnight_date in midnight_candles.index:
                csv_12pm_close = midnight_candles.loc[midnight_date, 'close']
                log_12pm_price = test['log_12pm_price']
                
                twelve_h_diff = abs(csv_12pm_close - log_12pm_price)
                twelve_h_match = twelve_h_diff < 0.01  # Allow small rounding differences
                
                print(f"12 PM Price (from midnight candle):")
                print(f"  CSV:  ${csv_12pm_close:.4f}")
                print(f"  Log:  ${log_12pm_price:.4f}")
                print(f"  Diff: ${twelve_h_diff:.4f} {'✅ MATCH' if twelve_h_match else '❌ MISMATCH'}")
                
                # Verify the price difference calculation
                expected_diff = (log_12pm_price - log_daily_close) / log_daily_close * 100
                print(f"Price Difference Verification:")
                print(f"  12PM vs Daily: {expected_diff:.2f}%")
                
            else:
                print(f"❌ Midnight candle for {midnight_date} not found in 12h CSV")
        except Exception as e:
            print(f"❌ Error checking 12h price: {e}")

def verify_data_structure():
    """Verify the structure of CSV files."""
    print(f"\n🔍 VERIFYING CSV DATA STRUCTURE")
    print("=" * 40)
    
    # Check a few key files
    test_symbols = ['SOL/USDT', 'ETH/USDT', 'BTC/USDT']
    
    for symbol in test_symbols:
        print(f"\n📈 {symbol}:")
        
        # Check daily data
        daily_data = load_csv_data(symbol, '1d')
        if daily_data is not None:
            print(f"  Daily: {len(daily_data)} candles, {daily_data.index.min()} to {daily_data.index.max()}")
            print(f"  Columns: {list(daily_data.columns)}")
            
            # Show sample data
            sample = daily_data.tail(3)
            print(f"  Last 3 daily candles:")
            for date, row in sample.iterrows():
                print(f"    {date.strftime('%Y-%m-%d')}: Close=${row['close']:.4f}")
        
        # Check 12h data
        twelve_h_data = load_csv_data(symbol, '12h')
        if twelve_h_data is not None:
            print(f"  12h: {len(twelve_h_data)} candles, {twelve_h_data.index.min()} to {twelve_h_data.index.max()}")
            
            # Check hour distribution
            hours = twelve_h_data.index.hour
            hour_counts = pd.Series(hours).value_counts().sort_index()
            print(f"  Hour distribution: {dict(hour_counts)}")
            
            # Show sample midnight candles
            midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0].tail(3)
            print(f"  Last 3 midnight candles (00:00-12:00):")
            for date, row in midnight_candles.iterrows():
                print(f"    {date.strftime('%Y-%m-%d %H:%M')}: Close=${row['close']:.4f}")

def verify_random_samples():
    """Verify a few random samples from different time periods."""
    print(f"\n🎲 RANDOM SAMPLE VERIFICATION")
    print("=" * 35)
    
    # Pick some dates from different periods
    sample_dates = [
        '2024-03-18',  # SOL buy
        '2024-06-11',  # ETH buy  
        '2024-11-05',  # BTC buy
    ]
    
    for date_str in sample_dates:
        print(f"\n📅 Checking {date_str}:")
        
        # Load SOL data for this sample
        daily_data = load_csv_data('SOL/USDT', '1d')
        twelve_h_data = load_csv_data('SOL/USDT', '12h')
        
        if daily_data is None or twelve_h_data is None:
            continue
            
        try:
            daily_date = pd.Timestamp(date_str, tz='UTC')
            midnight_date = pd.Timestamp(date_str + ' 00:00:00', tz='UTC')
            
            if daily_date in daily_data.index:
                daily_close = daily_data.loc[daily_date, 'close']
                print(f"  Daily close: ${daily_close:.4f}")
            
            midnight_candles = twelve_h_data[twelve_h_data.index.hour == 0]
            if midnight_date in midnight_candles.index:
                twelve_pm_close = midnight_candles.loc[midnight_date, 'close']
                print(f"  12 PM close: ${twelve_pm_close:.4f}")
                
                if daily_date in daily_data.index:
                    diff_pct = (twelve_pm_close - daily_close) / daily_close * 100
                    print(f"  Difference: {diff_pct:.2f}%")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")

def main():
    """Run all verification tests."""
    print("🔍 CSV PRICE VERIFICATION")
    print("=" * 30)
    print("Verifying that prices in manual.txt logs match actual CSV data...")
    
    verify_data_structure()
    verify_specific_prices()
    verify_random_samples()
    
    print(f"\n🎯 VERIFICATION COMPLETE")
    print("Check the results above to confirm price accuracy.")

if __name__ == "__main__":
    main()
