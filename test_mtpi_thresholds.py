#!/usr/bin/env python3
"""
Test MTPI Thresholds and Signal Generation

This script tests the new threshold-based signal generation that mimics
PineScript crossover/crossunder behavior.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.MTPI_signal_handler import (
    load_mtpi_indicators_config,
    fetch_mtpi_signal_from_config,
    combine_multi_signals
)

def test_threshold_configuration():
    """Test loading threshold configuration from YAML."""
    print("🔧 Testing Threshold Configuration")
    print("=" * 50)
    
    try:
        config = load_mtpi_indicators_config()
        
        print(f"✅ Configuration loaded successfully!")
        print(f"📊 Enabled indicators: {config.get('enabled_indicators', [])}")
        print(f"🔀 Combination method: {config.get('combination_method', 'unknown')}")
        print(f"📈 Long threshold: {config.get('long_threshold', 'N/A')}")
        print(f"📉 Short threshold: {config.get('short_threshold', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_signal_combination_with_thresholds():
    """Test signal combination with configurable thresholds."""
    print("\n🧮 Testing Signal Combination with Thresholds")
    print("=" * 50)
    
    try:
        # Test different signal combinations
        test_cases = [
            # (signals_dict, expected_consensus, expected_weighted)
            ({'pgo': 1, 'bb': 1}, 1, 1),  # Both long
            ({'pgo': -1, 'bb': -1}, -1, -1),  # Both short
            ({'pgo': 1, 'bb': -1}, 0, 0),  # Mixed signals
            ({'pgo': 1, 'bb': 1, 'dwma': 1}, 1, 1),  # All long
            ({'pgo': 1, 'bb': 1, 'dwma': -1}, 1, 0),  # Majority long
        ]
        
        for i, (signals, expected_consensus, expected_weighted) in enumerate(test_cases):
            print(f"\nTest Case {i+1}: {signals}")
            
            # Test consensus method
            consensus_result = combine_multi_signals(signals, 'consensus')
            print(f"  Consensus result: {consensus_result} (expected: {expected_consensus})")
            
            # Test weighted consensus with thresholds
            weighted_result = combine_multi_signals(signals, 'weighted_consensus', 
                                                   long_threshold=0.6, short_threshold=-0.6)
            print(f"  Weighted result: {weighted_result} (expected: {expected_weighted})")
            
            # Test majority method
            majority_result = combine_multi_signals(signals, 'majority')
            print(f"  Majority result: {majority_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signal combination: {e}")
        return False

def test_mtpi_signal_with_thresholds():
    """Test MTPI signal generation with threshold configuration."""
    print("\n🚀 Testing MTPI Signal Generation with Thresholds")
    print("=" * 50)
    
    try:
        # Test the config-based function
        signal = fetch_mtpi_signal_from_config(
            timeframe='1d',
            limit=50,
            skip_warmup=False
        )
        
        if signal is not None:
            signal_name = {1: "LONG 📈", -1: "SHORT 📉", 0: "NEUTRAL ➡️"}[signal]
            print(f"✅ MTPI Signal (with thresholds): {signal_name} ({signal})")
            
            # Load config to show threshold values used
            config = load_mtpi_indicators_config()
            print(f"📊 Used thresholds:")
            print(f"   - Long threshold: {config.get('long_threshold', 'N/A')}")
            print(f"   - Short threshold: {config.get('short_threshold', 'N/A')}")
            print(f"   - Combination method: {config.get('combination_method', 'N/A')}")
            
        else:
            print("❌ Failed to generate MTPI signal")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating MTPI signal: {e}")
        return False

def demonstrate_crossover_behavior():
    """Demonstrate crossover/crossunder behavior like PineScript."""
    print("\n🎯 Demonstrating Crossover/Crossunder Behavior")
    print("=" * 50)
    
    print("PineScript Logic:")
    print("  entryConditionTPI = ta.crossover(finaltpi, longt)")
    print("  exitConditionTPI = ta.crossunder(finaltpi, shortt)")
    print("  Signal persists until opposite crossover occurs")
    
    print("\nOur Implementation:")
    print("  - Signal = 1 when consensus crosses above long_threshold")
    print("  - Signal = -1 when consensus crosses below short_threshold")
    print("  - Signal persists until opposite threshold is crossed")
    print("  - No neutral zone - maintains previous signal")
    
    # Show example threshold values
    config = load_mtpi_indicators_config()
    long_thresh = config.get('long_threshold', 0.6)
    short_thresh = config.get('short_threshold', -0.6)
    
    print(f"\nCurrent Thresholds:")
    print(f"  📈 Long threshold: {long_thresh} (60% consensus for long)")
    print(f"  📉 Short threshold: {short_thresh} (-60% consensus for short)")
    print(f"  🔄 Persistence: Signal maintained between thresholds")

def main():
    """Main test function."""
    print("🧪 MTPI THRESHOLDS AND CROSSOVER TEST")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Threshold Configuration", test_threshold_configuration),
        ("Signal Combination", test_signal_combination_with_thresholds),
        ("MTPI Signal Generation", test_mtpi_signal_with_thresholds),
        ("Crossover Behavior Demo", demonstrate_crossover_behavior)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        if test_name == "Crossover Behavior Demo":
            print(f"ℹ️  INFO: {test_name}")  # Demo doesn't have pass/fail
        else:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}")
            if result:
                passed += 1
    
    actual_tests = len([r for r in results if r[0] != "Crossover Behavior Demo"])
    print(f"\n🎯 Results: {passed}/{actual_tests} tests passed")
    
    if passed == actual_tests:
        print("🎉 All tests passed! Threshold-based MTPI system is working correctly.")
        print("📊 Ready for visualization with crossover/crossunder behavior!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
