#!/usr/bin/env python3
"""
Test script to verify that the ratio_calculation parameter is properly configured
and passed through the background services.

This script tests:
1. YAML configuration loading for ratio_calculation parameter
2. Parameter passing to run_strategy_for_web function
3. Both manual_inversion and independent calculation methods

Usage:
    python test_ratio_calculation_config.py
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_yaml_config_loading():
    """Test that YAML configuration files properly load the ratio_calculation parameter."""
    print("\n" + "=" * 60)
    print("TESTING YAML CONFIGURATION LOADING")
    print("=" * 60)
    
    try:
        from src.config_manager import load_config
        
        # Test main settings.yaml
        print("\n1. Testing config/settings.yaml...")
        config = load_config('config/settings.yaml')
        settings = config.get('settings', {})
        ratio_calculation = settings.get('ratio_calculation', 'NOT_FOUND')
        
        print(f"   ratio_calculation parameter: {ratio_calculation}")
        if ratio_calculation in ['manual_inversion', 'independent']:
            print("   ✓ Valid ratio_calculation parameter found")
        else:
            print("   ✗ Invalid or missing ratio_calculation parameter")
            return False
        
        # Test memecoin settings
        print("\n2. Testing config/settings_memecoins.yaml...")
        memecoin_config = load_config('config/settings_memecoins.yaml')
        memecoin_settings = memecoin_config.get('settings', {})
        memecoin_ratio_calculation = memecoin_settings.get('ratio_calculation', 'NOT_FOUND')
        
        print(f"   ratio_calculation parameter: {memecoin_ratio_calculation}")
        if memecoin_ratio_calculation in ['manual_inversion', 'independent']:
            print("   ✓ Valid ratio_calculation parameter found")
        else:
            print("   ✗ Invalid or missing ratio_calculation parameter")
            return False
        
        print("\n✓ YAML configuration loading test PASSED")
        return True
        
    except Exception as e:
        print(f"\n✗ YAML configuration loading test FAILED: {e}")
        return False

def test_background_service_parameter_passing():
    """Test that background_service.py properly passes the ratio_calculation parameter."""
    print("\n" + "=" * 60)
    print("TESTING BACKGROUND SERVICE PARAMETER PASSING")
    print("=" * 60)
    
    try:
        from src.config_manager import load_config
        
        # Load the main config
        config = load_config('config/settings.yaml')
        settings = config.get('settings', {})
        
        # Check if the parameter would be passed correctly
        ratio_calculation = settings.get('ratio_calculation', 'manual_inversion')
        print(f"\nParameter that would be passed: {ratio_calculation}")
        
        # Verify it's a valid value
        if ratio_calculation in ['manual_inversion', 'independent']:
            print("✓ Valid parameter value for background service")
        else:
            print("✗ Invalid parameter value for background service")
            return False
        
        print("✓ Background service parameter passing test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Background service parameter passing test FAILED: {e}")
        return False

def test_memecoin_service_parameter_passing():
    """Test that background_service_memecoins.py properly passes the ratio_calculation parameter."""
    print("\n" + "=" * 60)
    print("TESTING MEMECOIN SERVICE PARAMETER PASSING")
    print("=" * 60)
    
    try:
        from src.config_manager import load_config
        
        # Load the memecoin config
        config = load_config('config/settings_memecoins.yaml')
        settings = config.get('settings', {})
        
        # Check if the parameter would be passed correctly
        ratio_calculation = settings.get('ratio_calculation', 'manual_inversion')
        print(f"\nParameter that would be passed: {ratio_calculation}")
        
        # Verify it's a valid value
        if ratio_calculation in ['manual_inversion', 'independent']:
            print("✓ Valid parameter value for memecoin service")
        else:
            print("✗ Invalid parameter value for memecoin service")
            return False
        
        print("✓ Memecoin service parameter passing test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Memecoin service parameter passing test FAILED: {e}")
        return False

def test_run_strategy_for_web_parameter():
    """Test that run_strategy_for_web function accepts the ratio_calculation parameter."""
    print("\n" + "=" * 60)
    print("TESTING run_strategy_for_web PARAMETER ACCEPTANCE")
    print("=" * 60)
    
    try:
        from main_program import run_strategy_for_web
        import inspect
        
        # Get the function signature
        sig = inspect.signature(run_strategy_for_web)
        parameters = list(sig.parameters.keys())
        
        print(f"\nChecking if 'ratio_calculation' parameter exists...")
        if 'ratio_calculation' in parameters:
            print("✓ ratio_calculation parameter found in function signature")
            
            # Get the default value
            default_value = sig.parameters['ratio_calculation'].default
            print(f"   Default value: {default_value}")
            
            if default_value == 'manual_inversion':
                print("✓ Correct default value")
            else:
                print(f"✗ Unexpected default value: {default_value}")
                return False
        else:
            print("✗ ratio_calculation parameter NOT found in function signature")
            print(f"   Available parameters: {parameters}")
            return False
        
        print("✓ run_strategy_for_web parameter test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ run_strategy_for_web parameter test FAILED: {e}")
        return False

def test_both_calculation_methods():
    """Test that both calculation methods are properly supported."""
    print("\n" + "=" * 60)
    print("TESTING BOTH CALCULATION METHODS")
    print("=" * 60)
    
    try:
        from src.strategy import calculate_daily_scores
        import inspect
        
        # Check if calculate_daily_scores supports ratio_calculation parameter
        sig = inspect.signature(calculate_daily_scores)
        parameters = list(sig.parameters.keys())
        
        print(f"\nChecking calculate_daily_scores function...")
        if 'ratio_calculation' in parameters:
            print("✓ ratio_calculation parameter found in calculate_daily_scores")
            
            # Get the default value
            default_value = sig.parameters['ratio_calculation'].default
            print(f"   Default value: {default_value}")
            
            if default_value == 'manual_inversion':
                print("✓ Correct default value")
            else:
                print(f"✗ Unexpected default value: {default_value}")
                return False
        else:
            print("✗ ratio_calculation parameter NOT found in calculate_daily_scores")
            return False
        
        # Check the PGO scoring function
        from src.indicators.ratio_indicators import calculate_daily_pgo_scores
        sig = inspect.signature(calculate_daily_pgo_scores)
        parameters = list(sig.parameters.keys())
        
        print(f"\nChecking calculate_daily_pgo_scores function...")
        if 'ratio_calculation' in parameters:
            print("✓ ratio_calculation parameter found in calculate_daily_pgo_scores")
            
            # Get the default value
            default_value = sig.parameters['ratio_calculation'].default
            print(f"   Default value: {default_value}")
            
            if default_value == 'manual_inversion':
                print("✓ Correct default value")
            else:
                print(f"✗ Unexpected default value: {default_value}")
                return False
        else:
            print("✗ ratio_calculation parameter NOT found in calculate_daily_pgo_scores")
            return False
        
        print("✓ Both calculation methods test PASSED")
        return True
        
    except Exception as e:
        print(f"✗ Both calculation methods test FAILED: {e}")
        return False

def main():
    """Run all tests."""
    print("RATIO CALCULATION CONFIGURATION TEST")
    print("=" * 60)
    print("This test verifies that the ratio_calculation parameter is properly")
    print("configured and passed through all components of the system.")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("YAML Configuration Loading", test_yaml_config_loading),
        ("Background Service Parameter Passing", test_background_service_parameter_passing),
        ("Memecoin Service Parameter Passing", test_memecoin_service_parameter_passing),
        ("run_strategy_for_web Parameter", test_run_strategy_for_web_parameter),
        ("Both Calculation Methods", test_both_calculation_methods),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name} FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        icon = "✓" if result else "✗"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✓ ratio_calculation parameter is properly configured")
        print("✓ Both manual_inversion and independent methods are supported")
        print("✓ Background services will correctly pass the parameter")
        print("✓ The system is ready to use flexible ratio calculation methods")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("Some issues need to be resolved before the feature is fully functional")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
