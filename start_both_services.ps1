# PowerShell script to start both background services
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting Multiple Background Services" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Starting YOUR service (using .env)..." -ForegroundColor Green
Start-Process -FilePath "python" -ArgumentList "start_service_main.py" -WindowStyle Normal

Write-Host ""
Write-Host "Waiting 5 seconds before starting Person 2's service..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Starting PERSON 2's service (using .env.person2)..." -ForegroundColor Green
Start-Process -FilePath "python" -ArgumentList "start_service_person2.py" -WindowStyle Normal

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Both services started in separate windows" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Your service uses: .env" -ForegroundColor Cyan
Write-Host "Person 2's service uses: .env.person2" -ForegroundColor Cyan
Write-Host ""
Write-Host "To stop the services:" -ForegroundColor Yellow
Write-Host "1. Close the respective command windows, or" -ForegroundColor Yellow
Write-Host "2. Press Ctrl+C in each window" -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to continue"
