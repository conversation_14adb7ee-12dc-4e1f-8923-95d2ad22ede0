#!/usr/bin/env python3
"""
Visual Test Script for DEMA Supertrend Implementation

This script creates comprehensive visualizations for the DEMA Supertrend indicator
to verify it matches TradingView behavior and test different parameter combinations.

Features:
- Price chart with DEMA line
- Supertrend line with color coding
- Buy/Sell signal markers
- Direction change indicators
- Parameter comparison plots
- Configurable date ranges

Usage Examples:
    # Test with default parameters (last 200 days)
    python test_dema_supertrend_visual.py
    
    # Test with specific date range
    python test_dema_supertrend_visual.py --start-date 2024-01-01
    
    # Test with custom parameters
    python test_dema_supertrend_visual.py --start-date 2023-06-01 --atr-period 14 --multiplier 2.0
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from indicators.dema_supertrend import (
    calculate_dema_supertrend_score,
    generate_dema_supertrend_signal,
    calculate_dema,
    calculate_atr
)
import pandas as pd
import numpy as np

def plot_dema_supertrend_analysis(df, atr_period=19, multiplier=2.8, dema_length=17,
                                 src_col='close', days_to_show=100, save_path=None):
    """
    Create comprehensive DEMA Supertrend visualization.
    
    Args:
        df: DataFrame with OHLCV data
        atr_period: ATR calculation period
        multiplier: Supertrend multiplier
        dema_length: DEMA calculation length
        src_col: Source column for DEMA
        days_to_show: Number of recent days to display (0 = all)
        save_path: Optional path to save the plot
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates
        from matplotlib.patches import Rectangle
        
        # Calculate DEMA Supertrend components
        signal, supertrend_line, direction = calculate_dema_supertrend_score(
            df, atr_period=atr_period, multiplier=multiplier, 
            dema_length=dema_length, src_col=src_col
        )
        
        # Calculate individual components for plotting
        dema = calculate_dema(df[src_col], dema_length)
        atr = calculate_atr(df, atr_period)
        
        # Get recent data for plotting
        if days_to_show > 0:
            df_plot = df.tail(days_to_show).copy()
            signal_plot = signal.tail(days_to_show)
            supertrend_plot = supertrend_line.tail(days_to_show)
            direction_plot = direction.tail(days_to_show)
            dema_plot = dema.tail(days_to_show)
            atr_plot = atr.tail(days_to_show)
        else:
            df_plot = df.copy()
            signal_plot = signal
            supertrend_plot = supertrend_line
            direction_plot = direction
            dema_plot = dema
            atr_plot = atr
        
        # Create figure with subplots
        fig, axes = plt.subplots(4, 1, figsize=(16, 14))
        fig.suptitle(f'DEMA Supertrend Analysis\n'
                    f'Parameters: ATR={atr_period}, Multiplier={multiplier}, '
                    f'DEMA={dema_length}, Source={src_col}',
                    fontsize=14, fontweight='bold')
        
        # Subplot 1: Price, DEMA, and Supertrend
        ax1 = axes[0]
        
        # Plot price candlesticks (simplified as line)
        ax1.plot(df_plot.index, df_plot['close'], label='Close Price', color='black', linewidth=1)
        ax1.plot(df_plot.index, df_plot[src_col], label=f'{src_col.title()} Price', 
                color='gray', alpha=0.7, linewidth=1)
        ax1.plot(df_plot.index, dema_plot, label=f'DEMA({dema_length})', color='blue', linewidth=2)
        
        # Plot Supertrend with color coding
        # Green when direction > 0 (uptrend), Red when direction < 0 (downtrend)
        for i in range(len(supertrend_plot)):
            if i == 0:
                continue
            color = 'green' if direction_plot.iloc[i] > 0 else 'red'
            ax1.plot([df_plot.index[i-1], df_plot.index[i]], 
                    [supertrend_plot.iloc[i-1], supertrend_plot.iloc[i]], 
                    color=color, linewidth=3, alpha=0.8)
        
        # Add signal markers
        long_signals = signal_plot == 1
        short_signals = signal_plot == -1
        
        if long_signals.any():
            ax1.scatter(df_plot.index[long_signals], df_plot['close'][long_signals], 
                       color='green', marker='^', s=100, label='LONG Signal', zorder=5)
        
        if short_signals.any():
            ax1.scatter(df_plot.index[short_signals], df_plot['close'][short_signals], 
                       color='red', marker='v', s=100, label='SHORT Signal', zorder=5)
        
        ax1.set_title('Price, DEMA, and Supertrend')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Subplot 2: ATR
        ax2 = axes[1]
        ax2.plot(df_plot.index, atr_plot, label=f'ATR({atr_period})', color='orange', linewidth=2)
        ax2.set_title('Average True Range (ATR)')
        ax2.set_ylabel('ATR Value')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Subplot 3: Direction and Signals
        ax3 = axes[2]
        
        # Plot direction as area
        ax3.fill_between(df_plot.index, 0, direction_plot, 
                        where=(direction_plot > 0), color='green', alpha=0.3, label='Uptrend')
        ax3.fill_between(df_plot.index, 0, direction_plot, 
                        where=(direction_plot < 0), color='red', alpha=0.3, label='Downtrend')
        
        # Plot signal line
        ax3.plot(df_plot.index, signal_plot, color='black', linewidth=2, label='Signal')
        
        ax3.set_title('Direction and Trading Signals')
        ax3.set_ylabel('Direction / Signal')
        ax3.set_ylim(-1.2, 1.2)
        ax3.set_yticks([-1, 0, 1])
        ax3.set_yticklabels(['SHORT', 'NEUTRAL', 'LONG'])
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Subplot 4: Signal Statistics
        ax4 = axes[3]
        
        # Calculate signal statistics
        signal_counts = signal_plot.value_counts().sort_index()
        signal_labels = []
        signal_values = []
        colors = []
        
        for sig_val in [-1, 0, 1]:
            count = signal_counts.get(sig_val, 0)
            if sig_val == -1:
                signal_labels.append(f'SHORT\n({count})')
                colors.append('red')
            elif sig_val == 0:
                signal_labels.append(f'NEUTRAL\n({count})')
                colors.append('gray')
            else:
                signal_labels.append(f'LONG\n({count})')
                colors.append('green')
            signal_values.append(count)
        
        bars = ax4.bar(signal_labels, signal_values, color=colors, alpha=0.7)
        ax4.set_title('Signal Distribution')
        ax4.set_ylabel('Count')
        
        # Add value labels on bars
        for bar, value in zip(bars, signal_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(signal_values)*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        ax4.grid(True, alpha=0.3, axis='y')
        
        # Format x-axis for time series
        for ax in axes[:3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        # Add current values text box
        latest_signal = signal_plot.iloc[-1]
        latest_supertrend = supertrend_plot.iloc[-1]
        latest_direction = direction_plot.iloc[-1]
        latest_price = df_plot['close'].iloc[-1]
        latest_dema = dema_plot.iloc[-1]
        latest_date = df_plot.index[-1].strftime('%Y-%m-%d')
        
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        direction_name = "UPTREND" if latest_direction > 0 else "DOWNTREND"
        
        info_text = f"""Current Status ({latest_date}):
• Signal: {signal_name} ({latest_signal})
• Direction: {direction_name} ({latest_direction:.1f})
• Supertrend: ${latest_supertrend:.2f}
• DEMA: ${latest_dema:.2f}
• Price: ${latest_price:.2f}
• Total Signals: {len(signal_plot)}"""
        
        fig.text(0.02, 0.02, info_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, bottom=0.15)
        
        # Save plot if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📁 Plot saved to: {save_path}")
        
        plt.show()
        
        # Print summary statistics
        print(f"\n📊 DEMA Supertrend Analysis Summary:")
        print(f"Period: {df_plot.index[0].strftime('%Y-%m-%d')} to {df_plot.index[-1].strftime('%Y-%m-%d')}")
        print(f"Total Days: {len(df_plot)}")
        print(f"Signal Distribution: {dict(signal_counts)}")
        print(f"Current Signal: {signal_name} ({latest_signal})")
        print(f"Current Direction: {direction_name} ({latest_direction:.1f})")
        print(f"Current Supertrend: ${latest_supertrend:.2f}")
        print(f"Current DEMA: ${latest_dema:.2f}")
        
    except ImportError:
        print("❌ matplotlib is required for plotting. Install with: pip install matplotlib")
    except Exception as e:
        print(f"❌ Error creating DEMA Supertrend plot: {e}")
        import traceback
        traceback.print_exc()

def test_dema_supertrend_visual(start_date=None, days_back=200, atr_period=19,
                               multiplier=2.8, dema_length=17, src_col='close'):
    """
    Test DEMA Supertrend with visual output.
    
    Args:
        start_date: Starting date in 'YYYY-MM-DD' format (optional)
        days_back: Number of days to fetch if start_date not specified
        atr_period: ATR calculation period
        multiplier: Supertrend multiplier
        dema_length: DEMA calculation length
        src_col: Source column for DEMA calculation
    """
    
    print("🔍 Testing DEMA Supertrend with Visual Analysis")
    print("=" * 60)
    
    try:
        # Fetch BTC data
        if start_date:
            print(f"📊 Fetching BTC/USDT data from {start_date}...")
            
            data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=['BTC/USDT'],
                timeframe='1d',
                since=start_date,
                limit=1000,
                ensure_data_since=True,
                force_refresh=True,
                use_cache=False
            )
        else:
            print(f"📊 Fetching BTC/USDT data (last {days_back} days)...")
            data_dict = fetch_ohlcv_data(
                exchange_id='binance',
                symbols=['BTC/USDT'],
                timeframe='1d',
                limit=days_back
            )
        
        if not data_dict or 'BTC/USDT' not in data_dict:
            print("❌ Failed to fetch BTC data")
            return
            
        btc_df = data_dict['BTC/USDT']
        print(f"✅ Fetched {len(btc_df)} candles")
        print(f"📅 Date range: {btc_df.index[0].strftime('%Y-%m-%d')} to {btc_df.index[-1].strftime('%Y-%m-%d')}")
        
        # Verify the start date if specified
        if start_date:
            actual_start = btc_df.index[0].strftime('%Y-%m-%d')
            if actual_start != start_date:
                print(f"⚠️  Note: Requested start date {start_date}, but actual data starts from {actual_start}")
            else:
                print(f"✅ Data starts from requested date: {start_date}")
        
        # Test with specified parameters
        print(f"\n🎯 Testing with parameters:")
        print(f"  ATR Period: {atr_period}")
        print(f"  Multiplier: {multiplier}")
        print(f"  DEMA Length: {dema_length}")
        print(f"  Source: {src_col}")
        
        # Calculate DEMA Supertrend
        print(f"\n🔧 Calculating DEMA Supertrend...")
        signal, supertrend_line, direction = calculate_dema_supertrend_score(
            btc_df, atr_period=atr_period, multiplier=multiplier, 
            dema_length=dema_length, src_col=src_col
        )
        
        # Show recent values
        print(f"\n📈 Recent Values (last 10 days):")
        recent_data = btc_df.tail(10)
        recent_signal = signal.tail(10)
        recent_supertrend = supertrend_line.tail(10)
        recent_direction = direction.tail(10)
        
        print("Date       | Close    | Signal | Direction | Supertrend")
        print("-" * 60)
        for i in range(len(recent_data)):
            date = recent_data.index[i].strftime('%Y-%m-%d')
            close = recent_data['close'].iloc[i]
            sig = recent_signal.iloc[i]
            dir_val = recent_direction.iloc[i]
            st_val = recent_supertrend.iloc[i]
            sig_name = {1: "LONG", -1: "SHORT", 0: "NEUT"}[sig]
            
            print(f"{date} | {close:8.2f} | {sig_name:6} | {dir_val:9.1f} | {st_val:10.2f}")
        
        # Signal statistics
        signal_counts = signal.value_counts().sort_index()
        print(f"\n📊 Signal Distribution:")
        for sig_val, count in signal_counts.items():
            sig_name = {-1: "SHORT", 0: "NEUTRAL", 1: "LONG"}[sig_val]
            percentage = (count / len(signal)) * 100
            print(f"  {sig_name}: {count} ({percentage:.1f}%)")
        
        # Current status
        latest_signal = signal.iloc[-1]
        latest_supertrend = supertrend_line.iloc[-1]
        latest_direction = direction.iloc[-1]
        latest_price = btc_df['close'].iloc[-1]
        latest_date = btc_df.index[-1].strftime('%Y-%m-%d')
        
        signal_name = {1: "LONG", -1: "SHORT", 0: "NEUTRAL"}[latest_signal]
        direction_name = "UPTREND" if latest_direction > 0 else "DOWNTREND"
        
        print(f"\n🎯 Current Status ({latest_date}):")
        print(f"  Signal: {signal_name} ({latest_signal})")
        print(f"  Direction: {direction_name} ({latest_direction:.1f})")
        print(f"  Supertrend: ${latest_supertrend:.2f}")
        print(f"  BTC Price: ${latest_price:.2f}")
        
        # Create visualization
        print(f"\n📊 Creating DEMA Supertrend visualization...")
        
        # Determine how many days to show in the plot
        if start_date:
            plot_days = 0  # Show all data
            print(f"📊 Showing all data from {start_date} ({len(btc_df)} days)")
        else:
            plot_days = min(200, len(btc_df))  # Show last 200 days or all if less
            print(f"📊 Showing last {plot_days} days")
        
        plot_dema_supertrend_analysis(
            df=btc_df,
            atr_period=atr_period,
            multiplier=multiplier,
            dema_length=dema_length,
            src_col=src_col,
            days_to_show=plot_days,
            save_path="dema_supertrend_analysis.png"
        )
        
        print(f"\n✅ DEMA Supertrend visual test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in DEMA Supertrend test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Visual test for DEMA Supertrend implementation')
    parser.add_argument('--start-date', type=str, help='Start date in YYYY-MM-DD format')
    parser.add_argument('--days-back', type=int, default=200, help='Number of days to fetch (default: 200)')
    parser.add_argument('--atr-period', type=int, default=19, help='ATR period (default: 19)')
    parser.add_argument('--multiplier', type=float, default=2.8, help='Supertrend multiplier (default: 2.8)')
    parser.add_argument('--dema-length', type=int, default=17, help='DEMA length (default: 17)')
    parser.add_argument('--source', type=str, default='close', choices=['high', 'low', 'close', 'open'],
                       help='Source for DEMA calculation (default: close)')
    
    args = parser.parse_args()
    
    if args.start_date:
        print(f"📅 Using start date: {args.start_date}")
        test_dema_supertrend_visual(
            start_date=args.start_date,
            atr_period=args.atr_period,
            multiplier=args.multiplier,
            dema_length=args.dema_length,
            src_col=args.source
        )
    else:
        print(f"📅 Using last {args.days_back} days")
        test_dema_supertrend_visual(
            days_back=args.days_back,
            atr_period=args.atr_period,
            multiplier=args.multiplier,
            dema_length=args.dema_length,
            src_col=args.source
        )
