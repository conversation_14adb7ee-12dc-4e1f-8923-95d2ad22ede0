[Unit]
Description=Debug Environment Variables Test
After=network.target

[Service]
Type=oneshot
User=admin
WorkingDirectory=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy
EnvironmentFile=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/.env.Liepa
ExecStart=/home/<USER>/asset_rotation_screener/venv/bin/python /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/debug_env.py
StandardOutput=append:/var/log/debug-env.log
StandardError=append:/var/log/debug-env-error.log

[Install]
WantedBy=multi-user.target
