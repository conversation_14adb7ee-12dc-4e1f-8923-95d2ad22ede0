#!/usr/bin/env python3
"""
Debug script to reproduce the exact NoneType comparison error.
"""

import sys
import os
import logging
import traceback

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading.order import OrderManager

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_exact_order_creation():
    """Test the exact order creation that's failing."""
    
    print("=== REPRODUCING NONETYPE ERROR ===")
    
    try:
        # Initialize order manager exactly as in the logs
        order_manager = OrderManager(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        # Test the exact parameters from the failing logs
        test_cases = [
            {
                'symbol': 'BTC/EUR',
                'amount': 39.70106670,  # Exact amount from logs
                'description': 'BTC/EUR order that failed'
            },
            {
                'symbol': 'TRX/EUR',
                'amount': 9.92526667,   # Exact amount from logs  
                'description': 'TRX/EUR order that failed'
            }
        ]
        
        for test_case in test_cases:
            print(f"\n=== Testing {test_case['description']} ===")
            print(f"Symbol: {test_case['symbol']}")
            print(f"Amount: {test_case['amount']} EUR")
            
            try:
                # This should reproduce the exact error
                print("Calling create_market_buy_order...")
                order = order_manager.create_market_buy_order(
                    symbol=test_case['symbol'],
                    amount=test_case['amount']
                )
                
                print(f"✅ Order created successfully: {order}")
                
            except Exception as e:
                print(f"❌ Error occurred: {e}")
                print(f"Error type: {type(e)}")
                
                # Print the full traceback to see exactly where the error occurs
                print("\nFull traceback:")
                traceback.print_exc()
                
                # Check if it's the NoneType comparison error
                if "'>' not supported between instances of 'NoneType' and" in str(e):
                    print("\n🔍 This is the NoneType comparison error!")
                    print("Let's trace where it's happening...")
                    
                    # Get the traceback details
                    tb = traceback.format_exc()
                    lines = tb.split('\n')
                    
                    for i, line in enumerate(lines):
                        if 'File "' in line and ('order.py' in line or 'precision.py' in line):
                            print(f"Error location: {line}")
                            if i + 1 < len(lines):
                                print(f"Code: {lines[i + 1]}")
                
                print("\n" + "="*50)

    except Exception as e:
        print(f"Test setup failed: {e}")
        traceback.print_exc()

def test_step_by_step():
    """Test each step of order creation to isolate the error."""
    
    print("\n=== STEP BY STEP DEBUGGING ===")
    
    try:
        from src.trading.order import OrderManager
        from src.utils.precision import adjust_amount_for_precision
        
        # Initialize order manager
        order_manager = OrderManager(
            exchange_id='kraken',
            test_mode=False,
            config_path='config/settings_kraken_eur.yaml'
        )
        
        symbol = 'BTC/EUR'
        amount = 39.70106670
        
        print(f"1. Testing ticker fetch for {symbol}...")
        try:
            ticker = order_manager.exchange.fetch_ticker(symbol)
            price = ticker['last']
            print(f"   ✅ Price: {price}")
        except Exception as e:
            print(f"   ❌ Ticker fetch failed: {e}")
            return
        
        print(f"2. Testing base amount calculation...")
        try:
            base_amount = amount / price
            print(f"   ✅ Base amount: {base_amount:.8f}")
        except Exception as e:
            print(f"   ❌ Base amount calculation failed: {e}")
            return
        
        print(f"3. Testing precision adjustment...")
        try:
            adjusted_base_amount = adjust_amount_for_precision(
                base_amount,
                symbol,
                price=price,
                is_buy=True,
                trading_config=order_manager.trading_config
            )
            print(f"   ✅ Adjusted amount: {adjusted_base_amount:.8f}")
        except Exception as e:
            print(f"   ❌ Precision adjustment failed: {e}")
            traceback.print_exc()
            return
        
        print(f"4. Testing market limits validation...")
        try:
            markets = order_manager.exchange.markets
            if symbol in markets:
                market = markets[symbol]
                limits = market.get('limits', {})
                print(f"   ✅ Market limits: {limits}")
                
                # Check for None values
                amount_limits = limits.get('amount', {})
                cost_limits = limits.get('cost', {})
                
                print(f"   Amount limits: {amount_limits}")
                print(f"   Cost limits: {cost_limits}")
                
                # Test the exact comparisons that might fail
                if amount_limits:
                    min_amount = amount_limits.get('min')
                    max_amount = amount_limits.get('max')
                    
                    print(f"   Min amount: {min_amount} (type: {type(min_amount)})")
                    print(f"   Max amount: {max_amount} (type: {type(max_amount)})")
                    
                    if min_amount is not None:
                        result = adjusted_base_amount < min_amount
                        print(f"   ✅ Min amount comparison: {adjusted_base_amount} < {min_amount} = {result}")
                    
                    if max_amount is not None:
                        result = adjusted_base_amount > max_amount
                        print(f"   ✅ Max amount comparison: {adjusted_base_amount} > {max_amount} = {result}")
                    elif max_amount is None:
                        print(f"   ⚠️ Max amount is None - this could cause issues")
                        
        except Exception as e:
            print(f"   ❌ Market limits validation failed: {e}")
            traceback.print_exc()
            return
        
        print(f"5. Testing CCXT order creation...")
        try:
            # This is where the error likely occurs
            order = order_manager.exchange.create_market_buy_order(symbol, adjusted_base_amount)
            print(f"   ✅ CCXT order created: {order}")
        except Exception as e:
            print(f"   ❌ CCXT order creation failed: {e}")
            traceback.print_exc()
            
            # This is likely where our NoneType error is happening
            if "'>' not supported between instances of 'NoneType' and" in str(e):
                print(f"   🔍 Found the NoneType error in CCXT!")
        
    except Exception as e:
        print(f"Step-by-step test failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_exact_order_creation()
    test_step_by_step()
    print("\nDebugging completed!")
