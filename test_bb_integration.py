#!/usr/bin/env python3
"""
Test script to verify MTPI indicators integration (BB, DWMA, etc.).
This script tests the new configuration-based MTPI signal generation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.MTPI_signal_handler import (
    load_mtpi_indicators_config, 
    fetch_mtpi_signal_from_config,
    fetch_combined_mtpi_signal
)

def test_config_loading():
    """Test loading MTPI indicators configuration."""
    print("🔧 Testing MTPI Configuration Loading")
    print("=" * 50)
    
    try:
        config = load_mtpi_indicators_config()
        
        print(f"✅ Configuration loaded successfully!")
        print(f"📊 Enabled indicators: {config.get('enabled_indicators', [])}")
        print(f"🔀 Combination method: {config.get('combination_method', 'unknown')}")
        
        # Check BB parameters
        if 'bollinger_bands' in config:
            bb_config = config['bollinger_bands']
            print(f"\n🎯 Bollinger Bands Parameters:")
            print(f"   - Length: {bb_config.get('length', 'N/A')}")
            print(f"   - Multiplier: {bb_config.get('multiplier', 'N/A')}")
            print(f"   - Long Threshold: {bb_config.get('long_threshold', 'N/A')}")
            print(f"   - Short Threshold: {bb_config.get('short_threshold', 'N/A')}")
            print(f"   - Use Heikin-Ashi: {bb_config.get('use_heikin_ashi', 'N/A')}")
            print(f"   - Heikin Source: {bb_config.get('heikin_src', 'N/A')}")

        # Check DWMA parameters
        if 'dwma_score' in config:
            dwma_config = config['dwma_score']
            print(f"\n📊 DWMA Score Parameters (TradingView matched):")
            print(f"   - Smoothing Style: {dwma_config.get('smoothing_style', 'N/A')}")
            print(f"   - Source Column: {dwma_config.get('src_col', 'N/A')}")
            print(f"   - DWA Length: {dwma_config.get('length', 'N/A')}")
            print(f"   - MA Type: {dwma_config.get('ma_type', 'N/A')}")
            print(f"   - MA Smooth: {dwma_config.get('ma_smooth_length', 'N/A')}")
            print(f"   - SD Length: {dwma_config.get('sd_length', 'N/A')}")
            print(f"   - Upper SD Weight: {dwma_config.get('upper_sd_weight', 'N/A')}")
            print(f"   - Lower SD Weight: {dwma_config.get('lower_sd_weight', 'N/A')}")
            print(f"   - ATR Period: {dwma_config.get('atr_period', 'N/A')}")
            print(f"   - ATR Multiplier: {dwma_config.get('atr_multiplier', 'N/A')}")
            print(f"   - Loop Start: {dwma_config.get('loop_start', 'N/A')}")
            print(f"   - Loop End: {dwma_config.get('loop_end', 'N/A')}")
            print(f"   - Long Threshold: {dwma_config.get('long_threshold', 'N/A')}")
            print(f"   - Short Threshold: {dwma_config.get('short_threshold', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_mtpi_signal_generation():
    """Test MTPI signal generation using configuration."""
    print("\n🚀 Testing MTPI Signal Generation from Config")
    print("=" * 50)
    
    try:
        # Test the new config-based function
        signal = fetch_mtpi_signal_from_config(
            timeframe='1d',
            limit=50,
            skip_warmup=False
        )
        
        if signal is not None:
            signal_name = {1: "LONG 📈", -1: "SHORT 📉", 0: "NEUTRAL ➡️"}[signal]
            print(f"✅ MTPI Signal (from config): {signal_name} ({signal})")
        else:
            print("❌ Failed to generate MTPI signal from config")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating MTPI signal: {e}")
        return False

def test_bb_specific_signal():
    """Test BB-specific signal generation using old method for comparison."""
    print("\n🔍 Testing BB Signal (Legacy Method for Comparison)")
    print("=" * 50)
    
    try:
        # Test the old combined function with BB parameters
        signal = fetch_combined_mtpi_signal(
            timeframe='1d',
            pgo_length=35,
            pgo_upper_threshold=1.35,
            pgo_lower_threshold=-1.0,
            bb_length=33,
            bb_multiplier=2.0,
            bb_long_threshold=76.0,
            bb_short_threshold=31.0,
            bb_use_heikin_ashi=True,
            bb_heikin_src='close',
            limit=50,
            combination_method='consensus'
        )
        
        if signal is not None:
            signal_name = {1: "LONG 📈", -1: "SHORT 📉", 0: "NEUTRAL ➡️"}[signal]
            print(f"✅ Combined Signal (legacy): {signal_name} ({signal})")
        else:
            print("❌ Failed to generate combined signal")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating combined signal: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 MTPI INDICATORS INTEGRATION TEST")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Configuration Loading", test_config_loading),
        ("MTPI Signal Generation", test_mtpi_signal_generation),
        ("BB Legacy Comparison", test_bb_specific_signal)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! MTPI indicators integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
