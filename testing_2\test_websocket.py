#!/usr/bin/env python
"""
Simple script to test the WebSocket connection to Binance.
This script uses the free WebSocket implementation to verify that it works.
"""

import asyncio
import logging
import sys
import os
import time
import platform
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the WebSocket service
from src.trading.binance_free_websocket import binance_free_ws_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def callback(symbol, data):
    """Callback function for WebSocket updates."""
    timestamp = datetime.fromtimestamp(data['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {symbol}: Open={data['open']:.2f}, High={data['high']:.2f}, "
          f"Low={data['low']:.2f}, Close={data['close']:.2f}, Volume={data['volume']:.2f}")

async def main():
    """Main function to test the WebSocket connection."""
    # Set the event loop policy for Windows
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        print("Set Windows-compatible event loop policy")

    # Symbols to test
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
    timeframe = '1m'

    print(f"Testing WebSocket connection for {symbols} with timeframe {timeframe}")

    # Start streaming
    success = binance_free_ws_service.start_streaming(symbols, timeframe, callback)
    if success:
        print("WebSocket streaming started successfully")
    else:
        print("Failed to start WebSocket streaming")
        return

    # Keep the script running for 30 seconds
    try:
        print("Waiting for WebSocket updates (press Ctrl+C to stop)...")
        for i in range(30):
            await asyncio.sleep(1)
            print(f"Waiting... {i+1}/30 seconds", end='\r')
    except KeyboardInterrupt:
        print("\nStopping WebSocket streaming...")
    finally:
        # Stop streaming
        success = binance_free_ws_service.stop_streaming()
        if success:
            print("WebSocket streaming stopped successfully")
        else:
            print("Failed to stop WebSocket streaming")

if __name__ == '__main__':
    asyncio.run(main())
