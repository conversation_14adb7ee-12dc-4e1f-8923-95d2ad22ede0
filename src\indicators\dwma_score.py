# src/indicators/dwma_score.py
"""
DWMA (Distance Weighted Moving Average) Score Indicator

This module implements the Enhanced DWMA indicator based on the provided PineScript code.
The DWMA uses distance-weighted calculations to create a more responsive moving average,
and generates signals based on multiple smoothing methods (ATR, Weighted SD, For loop).

Original PineScript implementation includes:
- T3, EHMA, THMA, TEMA, DEMA, JMA moving averages
- Distance-weighted moving average calculation
- Multiple signal generation methods (ATR, Weighted SD, For loop)
- Crossover detection for long/short signals
"""

import pandas as pd
import numpy as np
import math
from typing import Optional, Tuple
import logging

from .base_indicators import calculate_ema, calculate_sma, calculate_atr


def calculate_t3(src: pd.Series, length: int, b: float = 0.7) -> pd.Series:
    """
    Calculate T3 (Tillson T3) moving average.
    
    Args:
        src: Source price series
        length: Period length
        b: Volume factor (default 0.7)
    
    Returns:
        T3 moving average series
    """
    # Calculate 6 levels of EMA
    x1 = src.ewm(span=length).mean()
    x2 = x1.ewm(span=length).mean()
    x3 = x2.ewm(span=length).mean()
    x4 = x3.ewm(span=length).mean()
    x5 = x4.ewm(span=length).mean()
    x6 = x5.ewm(span=length).mean()
    
    # Calculate coefficients
    c1 = -math.pow(b, 3)
    c2 = 3 * math.pow(b, 2) + 3 * math.pow(b, 3)
    c3 = -6 * math.pow(b, 2) - 3 * b - 3 * math.pow(b, 3)
    c4 = 1 + 3 * b + math.pow(b, 3) + 3 * math.pow(b, 2)
    
    return c1 * x6 + c2 * x5 + c3 * x4 + c4 * x3


def calculate_ehma(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate EHMA (Exponential Hull Moving Average).
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        EHMA series
    """
    ema_half = src.ewm(span=length // 2).mean()
    ema_full = src.ewm(span=length).mean()
    sqrt_length = int(round(math.sqrt(length)))
    
    return (2 * ema_half - ema_full).ewm(span=sqrt_length).mean()


def calculate_thma(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate THMA (Triple Hull Moving Average).
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        THMA series
    """
    wma_third = src.rolling(window=length // 3).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
    wma_half = src.rolling(window=length // 2).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
    wma_full = src.rolling(window=length).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
    
    combined = wma_third * 3 - wma_half - wma_full
    return combined.rolling(window=length).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))


def calculate_tema(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate TEMA (Triple Exponential Moving Average).
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        TEMA series
    """
    x = src.ewm(span=length).mean()
    y = x.ewm(span=length).mean()
    z = y.ewm(span=length).mean()
    
    return 3 * x - 3 * y + z


def calculate_dema(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate DEMA (Double Exponential Moving Average).
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        DEMA series
    """
    x = src.ewm(span=length).mean()
    y = x.ewm(span=length).mean()
    
    return 2 * x - y


def calculate_jma(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate JMA (Jurik Moving Average) - simplified version.
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        JMA series
    """
    beta = 0.45 * (length - 1) / (0.45 * (length - 1) + 2)
    alpha = beta
    
    # Initialize series
    tmp0 = pd.Series(index=src.index, dtype=float)
    tmp1 = pd.Series(index=src.index, dtype=float)
    tmp2 = pd.Series(index=src.index, dtype=float)
    tmp3 = pd.Series(index=src.index, dtype=float)
    tmp4 = pd.Series(index=src.index, dtype=float)
    
    # Calculate JMA iteratively
    for i in range(len(src)):
        if i == 0:
            tmp0.iloc[i] = src.iloc[i]
            tmp1.iloc[i] = 0
            tmp2.iloc[i] = src.iloc[i]
            tmp3.iloc[i] = 0
            tmp4.iloc[i] = src.iloc[i]
        else:
            tmp0.iloc[i] = (1 - alpha) * src.iloc[i] + alpha * tmp0.iloc[i-1]
            tmp1.iloc[i] = (src.iloc[i] - tmp0.iloc[i]) * (1 - beta) + beta * tmp1.iloc[i-1]
            tmp2.iloc[i] = tmp0.iloc[i] + tmp1.iloc[i]
            tmp3.iloc[i] = (tmp2.iloc[i] - tmp4.iloc[i-1]) * (1 - alpha) * (1 - alpha) + alpha * alpha * tmp3.iloc[i-1]
            tmp4.iloc[i] = tmp4.iloc[i-1] + tmp3.iloc[i]
    
    return tmp4


def calculate_ma(src: pd.Series, length: int, ma_type: str) -> pd.Series:
    """
    Calculate moving average based on type.
    
    Args:
        src: Source price series
        length: Period length
        ma_type: Type of moving average
    
    Returns:
        Moving average series
    """
    if ma_type == "SMA":
        return src.rolling(window=length).mean()
    elif ma_type == "EMA":
        return src.ewm(span=length).mean()
    elif ma_type == "HMA":
        # Hull Moving Average
        wma_half = src.rolling(window=length // 2).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
        wma_full = src.rolling(window=length).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
        sqrt_length = int(round(math.sqrt(length)))
        return (2 * wma_half - wma_full).rolling(window=sqrt_length).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
    elif ma_type == "RMA":
        return src.ewm(alpha=1/length).mean()
    elif ma_type == "WMA":
        return src.rolling(window=length).apply(lambda x: np.average(x, weights=range(1, len(x) + 1)))
    elif ma_type == "DEMA":
        return calculate_dema(src, length)
    elif ma_type == "TEMA":
        return calculate_tema(src, length)
    elif ma_type == "EHMA":
        return calculate_ehma(src, length)
    elif ma_type == "THMA":
        return calculate_thma(src, length)
    elif ma_type == "T3":
        return calculate_t3(src, length)
    elif ma_type == "LSMA":
        # Linear regression (simplified)
        return src.rolling(window=length).apply(lambda x: np.polyfit(range(len(x)), x, 1)[1] + np.polyfit(range(len(x)), x, 1)[0] * (len(x) - 1))
    elif ma_type == "JMA":
        return calculate_jma(src, length)
    else:
        return src.ewm(span=length).mean()  # Default to EMA


def calculate_dwma(src: pd.Series, length: int) -> pd.Series:
    """
    Calculate Distance Weighted Moving Average.
    
    Args:
        src: Source price series
        length: Period length
    
    Returns:
        DWMA series
    """
    dwma_values = pd.Series(index=src.index, dtype=float)
    
    for i in range(length - 1, len(src)):
        sum_weighted = 0.0
        weight_sum = 0.0
        
        # Calculate weights based on distance
        for j in range(length):
            idx = i - j
            if idx >= 0:
                # Calculate distance sum for this point
                distance_sum = 0.0
                for k in range(length):
                    k_idx = i - k
                    if k_idx >= 0:
                        distance_sum += abs(src.iloc[idx] - src.iloc[k_idx])
                
                # Weight is inverse of distance sum (avoid division by zero)
                weight = 1.0 / max(distance_sum, 1e-10)
                sum_weighted += src.iloc[idx] * weight
                weight_sum += weight
        
        dwma_values.iloc[i] = sum_weighted / weight_sum if weight_sum > 0 else src.iloc[i]
    
    return dwma_values


def calculate_atr_smoothing(df: pd.DataFrame, atr_period: int = 12, multiplier: float = 1.0) -> pd.Series:
    """
    Calculate ATR-based smoothing factor.
    
    Args:
        df: DataFrame with OHLCV data
        atr_period: ATR calculation period
        multiplier: ATR multiplier
    
    Returns:
        ATR smoothing factor series
    """
    atr = calculate_atr(df, length=atr_period)
    return multiplier * atr


def calculate_for_loop_signal(dwmas: pd.Series, start: int = 1, end: int = 60) -> pd.Series:
    """
    Calculate for loop signal based on DWMA comparison.
    
    Args:
        dwmas: Smoothed DWMA series
        start: Start index for loop
        end: End index for loop
    
    Returns:
        For loop signal series
    """
    loop_values = pd.Series(index=dwmas.index, dtype=float)
    
    for i in range(end, len(dwmas)):
        sum_signal = 0.0
        for j in range(start, end + 1):
            if i - j >= 0:
                if dwmas.iloc[i] > dwmas.iloc[i - j]:
                    sum_signal += 1
                else:
                    sum_signal -= 1
        loop_values.iloc[i] = sum_signal
    
    return loop_values


def calculate_dwma_score(
    df: pd.DataFrame,
    smoothing_style: str = "ATR",  # "ATR", "Weighted SD", "For loop"
    src_col: str = 'close',
    length: int = 2,
    ma_type: str = "EMA",
    ma_smooth_length: int = 12,
    # ATR parameters
    atr_period: int = 12,
    atr_multiplier: float = 1.0,
    # Weighted SD parameters
    sd_length: int = 30,
    upper_sd_weight: float = 1.035,
    lower_sd_weight: float = 1.02,
    # For loop parameters
    loop_start: int = 1,
    loop_end: int = 60,
    long_threshold: int = 30,
    short_threshold: int = 0
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate DWMA Score based on the provided PineScript implementation.

    Args:
        df: DataFrame with OHLCV data
        smoothing_style: Smoothing method ("ATR", "Weighted SD", "For loop")
        src_col: Source column name
        length: DWMA calculation length
        ma_type: Moving average type for smoothing
        ma_smooth_length: MA smoothing length
        atr_period: ATR period for ATR method
        atr_multiplier: ATR multiplier
        sd_length: Standard deviation length for Weighted SD method
        upper_sd_weight: Upper SD weight
        lower_sd_weight: Lower SD weight
        loop_start: Start value for loop method
        loop_end: End value for loop method
        long_threshold: Long signal threshold for loop method
        short_threshold: Short signal threshold for loop method

    Returns:
        Tuple of (dwma_score_signal, dwma_values, smoothed_dwma)
    """
    if src_col not in df.columns:
        raise ValueError(f"Column '{src_col}' not found in DataFrame")

    src = df[src_col]

    # Calculate DWMA
    dwma = calculate_dwma(src, length)

    # Smooth DWMA with selected MA type
    dwmas = calculate_ma(dwma, ma_smooth_length, ma_type)

    # Initialize signal series
    signal = pd.Series(0, index=df.index, dtype=int)

    if smoothing_style == "ATR":
        # ATR-based signals
        atr_smoothing = calculate_atr_smoothing(df, atr_period, atr_multiplier)

        # Calculate ATR bands
        atr_upper = dwmas + atr_smoothing
        atr_lower = dwmas - atr_smoothing

        # Generate crossover signals
        long_signals = (src > atr_upper) & (src.shift(1) <= atr_upper.shift(1))
        short_signals = (src < atr_lower) & (src.shift(1) >= atr_lower.shift(1))

        # Apply signal logic with state persistence
        current_signal = 0
        for i in range(len(df)):
            if long_signals.iloc[i] and not short_signals.iloc[i]:
                current_signal = 1
            elif short_signals.iloc[i]:
                current_signal = -1
            signal.iloc[i] = current_signal

    elif smoothing_style == "Weighted SD":
        # Weighted Standard Deviation signals
        sd = dwmas.rolling(window=sd_length).std()

        # Calculate SD bands
        sd_upper = (dwmas + sd) * upper_sd_weight
        sd_lower = (dwmas - sd) * lower_sd_weight

        # Generate crossover signals
        long_signals = (src > sd_upper) & (src.shift(1) <= sd_upper.shift(1))
        short_signals = (src < sd_lower) & (src.shift(1) >= sd_lower.shift(1))

        # Apply signal logic with state persistence
        current_signal = 0
        for i in range(len(df)):
            if long_signals.iloc[i] and not short_signals.iloc[i]:
                current_signal = 1
            elif short_signals.iloc[i]:
                current_signal = -1
            signal.iloc[i] = current_signal

    elif smoothing_style == "For loop":
        # For loop signals
        loop_values = calculate_for_loop_signal(dwmas, loop_start, loop_end)

        # Generate signals based on thresholds
        long_signals = loop_values > long_threshold
        short_signals = loop_values < short_threshold

        # Apply signal logic with state persistence
        current_signal = 0
        for i in range(len(df)):
            if long_signals.iloc[i] and not short_signals.iloc[i]:
                current_signal = 1
            elif short_signals.iloc[i]:
                current_signal = -1
            signal.iloc[i] = current_signal

    return signal, dwma, dwmas


def generate_dwma_signal(
    df: pd.DataFrame,
    smoothing_style: str = "ATR",
    src_col: str = 'close',
    length: int = 2,
    ma_type: str = "EMA",
    ma_smooth_length: int = 12,
    **kwargs
) -> pd.Series:
    """
    Generate DWMA binary signal for MTPI integration.

    Args:
        df: DataFrame with OHLCV data
        smoothing_style: Smoothing method ("ATR", "Weighted SD", "For loop")
        src_col: Source column name
        length: DWMA calculation length
        ma_type: Moving average type for smoothing
        ma_smooth_length: MA smoothing length
        **kwargs: Additional parameters for specific smoothing methods

    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        signal, _, _ = calculate_dwma_score(
            df=df,
            smoothing_style=smoothing_style,
            src_col=src_col,
            length=length,
            ma_type=ma_type,
            ma_smooth_length=ma_smooth_length,
            **kwargs
        )

        logging.info(f"Generated DWMA signals using {smoothing_style} method")
        return signal

    except Exception as e:
        logging.error(f"Error generating DWMA signals: {e}")
        return pd.Series(0, index=df.index, dtype=int)
