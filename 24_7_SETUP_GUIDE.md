# 24/7 Operation Setup Guide

This guide provides step-by-step instructions for setting up the Asset Rotation Strategy to run continuously 24/7 on a cloud server.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Initial Server Setup](#initial-server-setup)
3. [Installing the Service](#installing-the-service)
4. [Configuration](#configuration)
5. [Starting the Service](#starting-the-service)
6. [Monitoring](#monitoring)
7. [Troubleshooting](#troubleshooting)
8. [Maintenance](#maintenance)

## Prerequisites

- A Linux server (Ubuntu 20.04 or later recommended)
- Root or sudo access
- Python 3.8 or later
- Git

## Initial Server Setup

1. **Update the system**:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **Install required packages**:
   ```bash
   sudo apt install -y python3 python3-pip python3-venv git logrotate
   ```

3. **Clone the repository** (if not already done):
   ```bash
   git clone https://your-repository-url.git
   cd asset_rotation_screener
   ```

## Installing the Service

### Automatic Installation

We've created a setup script that automates the installation process:

1. **Make the setup script executable**:
   ```bash
   chmod +x Asset_Rotation_Strategy/setup_24_7_service.sh
   ```

2. **Run the setup script**:
   ```bash
   sudo Asset_Rotation_Strategy/setup_24_7_service.sh
   ```

3. **Follow the prompts** to complete the installation.

### Manual Installation

If you prefer to install manually, follow these steps:

1. **Create a Python virtual environment** (optional but recommended):
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r Asset_Rotation_Strategy/requirements.txt
   ```

3. **Configure the systemd service**:
   ```bash
   sudo cp Asset_Rotation_Strategy/asset_rotation_service.service /etc/systemd/system/
   sudo systemctl daemon-reload
   ```

4. **Set up log rotation**:
   ```bash
   sudo cp Asset_Rotation_Strategy/asset_rotation_logs /etc/logrotate.d/asset_rotation
   ```

5. **Set up the monitoring script**:
   ```bash
   chmod +x Asset_Rotation_Strategy/monitor_service.sh
   ```

6. **Add the monitoring script to crontab**:
   ```bash
   (crontab -l 2>/dev/null; echo "*/10 * * * * /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/monitor_service.sh") | crontab -
   ```

## Configuration

### API Keys and Secrets

1. **Create the .env file** if it doesn't exist:
   ```bash
   cp Asset_Rotation_Strategy/.env.example Asset_Rotation_Strategy/.env
   ```

2. **Edit the .env file** with your API keys and secrets:
   ```bash
   nano Asset_Rotation_Strategy/.env
   ```

   Add your Binance API keys and Telegram bot credentials:
   ```
   BINANCE_API_KEY=your_api_key_here
   BINANCE_API_SECRET=your_api_secret_here
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   TELEGRAM_CHAT_ID=your_chat_id_here
   ```

### Strategy Settings

Edit the strategy settings in `Asset_Rotation_Strategy/config/settings.yaml`:

```bash
nano Asset_Rotation_Strategy/config/settings.yaml
```

Key settings to configure:
- `timeframe`: Trading timeframe (e.g., '1d', '4h', '1h')
- `assets`: List of assets to trade
- `n_assets`: Number of top assets to select
- `use_weighted_allocation`: Whether to use weighted allocation
- `trading.enabled`: Set to `true` to enable trading
- `trading.mode`: Set to `paper` for paper trading or `live` for live trading

## Starting the Service

### Using Systemd (Recommended)

1. **Enable the service** to start on boot:
   ```bash
   sudo systemctl enable asset_rotation_service
   ```

2. **Start the service**:
   ```bash
   sudo systemctl start asset_rotation_service
   ```

3. **Check the service status**:
   ```bash
   sudo systemctl status asset_rotation_service
   ```

### Using the Shell Script

Alternatively, you can use the provided shell script:

```bash
cd Asset_Rotation_Strategy
./start_resilient_service.sh
```

## Monitoring

### Service Status

Check if the service is running:

```bash
sudo systemctl status asset_rotation_service
```

### Logs

View the service logs:

```bash
sudo journalctl -u asset_rotation_service -f
```

Or check the log files directly:

```bash
tail -f Asset_Rotation_Strategy/logs/resilient_service_*.log
```

### Automated Monitoring

The monitoring script (`monitor_service.sh`) automatically checks if the service is running every 10 minutes and restarts it if necessary. It also sends Telegram notifications if configured.

## Troubleshooting

### Service Won't Start

1. **Check the logs**:
   ```bash
   sudo journalctl -u asset_rotation_service -e
   ```

2. **Verify the configuration**:
   - Check that the `.env` file exists and contains valid API keys
   - Ensure the paths in the systemd service file are correct

3. **Test the service manually**:
   ```bash
   cd /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy
   python3 background_service.py
   ```

### Network Connectivity Issues

The service includes built-in network monitoring and recovery mechanisms. If you're experiencing persistent network issues:

1. **Check your server's internet connection**:
   ```bash
   ping -c 4 *******
   ping -c 4 api.binance.com
   ```

2. **Restart the service**:
   ```bash
   sudo systemctl restart asset_rotation_service
   ```

## Maintenance

### Updating the Service

1. **Pull the latest code**:
   ```bash
   cd /home/<USER>/asset_rotation_screener
   git pull
   ```

2. **Restart the service**:
   ```bash
   sudo systemctl restart asset_rotation_service
   ```

### Backup

Regularly backup your configuration and data:

```bash
# Backup configuration
cp -r /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config /backup/config_$(date +%Y%m%d)

# Backup data
cp -r /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data /backup/data_$(date +%Y%m%d)
```

### Log Management

The log rotation configuration automatically manages log files, but you can manually clean up old logs if needed:

```bash
find /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/logs -name "*.log" -mtime +30 -delete
```
