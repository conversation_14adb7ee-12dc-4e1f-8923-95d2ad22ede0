#!/usr/bin/env python3
"""
Memecoin Discovery CLI Tool

Command-line interface for running and managing the memecoin discovery system.
"""

import os
import sys
import argparse
import logging
import json
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import discovery system components
try:
    from src.memecoin_discovery.discovery_engine import MemecoinDiscoveryEngine
    from src.memecoin_discovery.integration import MemecoinStrategyIntegration
    from src.memecoin_discovery.database import DiscoveryDatabase
    DISCOVERY_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Memecoin discovery system not available: {e}")
    DISCOVERY_AVAILABLE = False

def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/memecoin_discovery_cli.log'),
            logging.StreamHandler()
        ]
    )

def run_discovery(args):
    """Run a discovery cycle."""
    if not DISCOVERY_AVAILABLE:
        print("Error: Discovery system not available. Please install required dependencies.")
        return 1
    
    print("Starting memecoin discovery cycle...")
    
    try:
        # Initialize discovery engine
        engine = MemecoinDiscoveryEngine()
        
        # Run discovery cycle
        results = engine.run_discovery_cycle()
        
        # Display results
        print("\n" + "="*60)
        print("DISCOVERY CYCLE RESULTS")
        print("="*60)
        print(f"Duration: {results.get('duration_seconds', 0):.1f} seconds")
        print(f"Tokens discovered: {results.get('tokens_discovered', 0)}")
        print(f"Tokens analyzed: {results.get('tokens_analyzed', 0)}")
        print(f"Tokens approved: {results.get('tokens_approved', 0)}")
        
        # Show top tokens
        top_tokens = results.get('top_tokens', [])
        if top_tokens:
            print("\nTop Discovered Tokens:")
            print("-" * 40)
            for i, token in enumerate(top_tokens[:10], 1):
                print(f"{i:2d}. {token['symbol']:10s} | Score: {token['score']:5.1f} | {token['recommendation']:12s} | Risk: {token['risk_level']}")
        
        # Show errors if any
        errors = results.get('errors', [])
        if errors:
            print("\nErrors encountered:")
            for error in errors:
                print(f"  - {error}")
        
        # Save results to file if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\nResults saved to: {args.output}")
        
        print("\nDiscovery cycle completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Error running discovery cycle: {e}")
        logging.error(f"Discovery cycle failed: {e}")
        return 1

def run_integration(args):
    """Run discovery and integration cycle."""
    if not DISCOVERY_AVAILABLE:
        print("Error: Discovery system not available. Please install required dependencies.")
        return 1
    
    print("Starting discovery and integration cycle...")
    
    try:
        # Initialize integration system
        integration = MemecoinStrategyIntegration()
        
        # Run discovery and integration
        results = integration.run_discovery_and_integration()
        
        # Display results
        print("\n" + "="*60)
        print("DISCOVERY AND INTEGRATION RESULTS")
        print("="*60)
        
        # Discovery results
        discovery_results = results.get('discovery_results', {})
        print(f"Tokens discovered: {discovery_results.get('tokens_discovered', 0)}")
        print(f"Tokens analyzed: {discovery_results.get('tokens_analyzed', 0)}")
        
        # Integration results
        integration_results = results.get('integration_results', {})
        print(f"Tokens reviewed: {integration_results.get('tokens_reviewed', 0)}")
        print(f"Auto-added: {integration_results.get('auto_added', 0)}")
        print(f"Pending review: {integration_results.get('pending_review', 0)}")
        print(f"Rejected: {integration_results.get('rejected', 0)}")
        
        # Show auto-added tokens
        added_tokens = integration_results.get('added_tokens', [])
        if added_tokens:
            print("\nAuto-added tokens:")
            for token in added_tokens:
                print(f"  - {token['symbol']} (Score: {token['score']:.1f}) - {token['reason']}")
        
        # Show tokens requiring review
        review_tokens = integration_results.get('review_tokens', [])
        if review_tokens:
            print("\nTokens requiring manual review:")
            for token in review_tokens:
                print(f"  - {token['symbol']} (Score: {token['score']:.1f}) - {token['reason']}")
        
        # Save results if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\nResults saved to: {args.output}")
        
        print("\nDiscovery and integration cycle completed successfully!")
        return 0
        
    except Exception as e:
        print(f"Error running integration cycle: {e}")
        logging.error(f"Integration cycle failed: {e}")
        return 1

def show_dashboard(args):
    """Show discovery dashboard."""
    if not DISCOVERY_AVAILABLE:
        print("Error: Discovery system not available. Please install required dependencies.")
        return 1
    
    try:
        # Initialize integration system
        integration = MemecoinStrategyIntegration()
        
        # Get dashboard data
        dashboard_data = integration.get_integration_dashboard_data()
        
        # Display dashboard
        print("\n" + "="*60)
        print("MEMECOIN DISCOVERY DASHBOARD")
        print("="*60)
        
        # Summary
        summary = dashboard_data.get('summary', {})
        print(f"Period: Last {summary.get('period_days', 7)} days")
        print(f"Total discovered: {summary.get('total_discovered', 0)}")
        print(f"Approved tokens: {summary.get('approved_tokens', 0)}")
        print(f"Average score: {summary.get('average_score', 0):.2f}")
        
        # Top discoveries
        top_discoveries = summary.get('top_discoveries', [])
        if top_discoveries:
            print("\nTop Recent Discoveries:")
            print("-" * 50)
            for i, token in enumerate(top_discoveries[:10], 1):
                date = datetime.fromisoformat(token['discovery_date']).strftime('%Y-%m-%d')
                print(f"{i:2d}. {token['symbol']:10s} | Score: {token['score']:5.1f} | {date} | {token['status']}")
        
        # Pending recommendations
        recommendations = dashboard_data.get('pending_recommendations', [])
        if recommendations:
            print(f"\nPending Recommendations ({len(recommendations)}):")
            print("-" * 60)
            for i, rec in enumerate(recommendations[:10], 1):
                print(f"{i:2d}. {rec['symbol']:10s} | Score: {rec['final_score']:5.1f} | Action: {rec['action_required']}")
                print(f"    {rec['recommendation']}")
        
        # Recent activity
        recent_approved = dashboard_data.get('recent_approved', [])
        if recent_approved:
            print(f"\nRecently Approved ({len(recent_approved)}):")
            for token in recent_approved[:5]:
                date = datetime.fromisoformat(token['date']).strftime('%Y-%m-%d')
                print(f"  - {token['symbol']} (Score: {token['score']:.1f}) - {date}")
        
        return 0
        
    except Exception as e:
        print(f"Error displaying dashboard: {e}")
        logging.error(f"Dashboard display failed: {e}")
        return 1

def approve_token(args):
    """Approve a token for inclusion in strategy."""
    if not DISCOVERY_AVAILABLE:
        print("Error: Discovery system not available. Please install required dependencies.")
        return 1
    
    try:
        integration = MemecoinStrategyIntegration()
        
        success = integration.approve_token(args.symbol)
        
        if success:
            print(f"Token {args.symbol} approved and added to strategy!")
        else:
            print(f"Failed to approve token {args.symbol}")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"Error approving token: {e}")
        return 1

def reject_token(args):
    """Reject a token."""
    if not DISCOVERY_AVAILABLE:
        print("Error: Discovery system not available. Please install required dependencies.")
        return 1
    
    try:
        integration = MemecoinStrategyIntegration()
        
        success = integration.reject_token(args.symbol, args.reason or "Manual rejection")
        
        if success:
            print(f"Token {args.symbol} rejected!")
        else:
            print(f"Failed to reject token {args.symbol}")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"Error rejecting token: {e}")
        return 1

def check_status(args):
    """Check system status and configuration."""
    print("\n" + "="*60)
    print("MEMECOIN DISCOVERY SYSTEM STATUS")
    print("="*60)
    
    # Check dependencies
    print("Dependencies:")
    dependencies = [
        ('openai', 'OpenAI API'),
        ('snscrape', 'Twitter Scraping'),
        ('textblob', 'Text Analysis'),
        ('vaderSentiment', 'Sentiment Analysis'),
        ('beautifulsoup4', 'Web Scraping')
    ]
    
    for module, description in dependencies:
        try:
            __import__(module)
            status = "✓ Available"
        except ImportError:
            status = "✗ Missing"
        print(f"  {description:20s}: {status}")
    
    # Check API keys
    print("\nAPI Keys:")
    api_keys = [
        ('OPENAI_API_KEY', 'OpenAI API (required)')
    ]
    
    for env_var, description in api_keys:
        value = os.getenv(env_var)
        status = "✓ Set" if value else "✗ Missing"
        print(f"  {description:20s}: {status}")
    
    # Check configuration files
    print("\nConfiguration Files:")
    config_files = [
        'config/memecoin_discovery.yaml',
        'config/geckoterminal_tokens.yaml',
        'config/settings_memecoins.yaml'
    ]
    
    for config_file in config_files:
        exists = Path(config_file).exists()
        status = "✓ Exists" if exists else "✗ Missing"
        print(f"  {config_file:30s}: {status}")
    
    # Check database
    print("\nDatabase:")
    try:
        db = DiscoveryDatabase()
        print("  Database connection: ✓ OK")
        
        # Get some stats
        recent_tokens = db.get_discovered_tokens(days_back=7)
        print(f"  Recent discoveries (7 days): {len(recent_tokens)}")
        
    except Exception as e:
        print(f"  Database connection: ✗ Error - {e}")
    
    print("\nSystem Status: " + ("✓ Ready" if DISCOVERY_AVAILABLE else "✗ Not Ready"))
    
    return 0

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Memecoin Discovery System CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s discover                    # Run discovery cycle
  %(prog)s integrate                   # Run discovery and integration
  %(prog)s dashboard                   # Show dashboard
  %(prog)s approve SYMBOL              # Approve a token
  %(prog)s reject SYMBOL --reason "..."# Reject a token
  %(prog)s status                      # Check system status
        """
    )
    
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose logging')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Discovery command
    discover_parser = subparsers.add_parser('discover', help='Run discovery cycle')
    discover_parser.add_argument('-o', '--output', help='Save results to JSON file')
    discover_parser.set_defaults(func=run_discovery)
    
    # Integration command
    integrate_parser = subparsers.add_parser('integrate', help='Run discovery and integration')
    integrate_parser.add_argument('-o', '--output', help='Save results to JSON file')
    integrate_parser.set_defaults(func=run_integration)
    
    # Dashboard command
    dashboard_parser = subparsers.add_parser('dashboard', help='Show discovery dashboard')
    dashboard_parser.set_defaults(func=show_dashboard)
    
    # Approve command
    approve_parser = subparsers.add_parser('approve', help='Approve a token')
    approve_parser.add_argument('symbol', help='Token symbol to approve')
    approve_parser.set_defaults(func=approve_token)
    
    # Reject command
    reject_parser = subparsers.add_parser('reject', help='Reject a token')
    reject_parser.add_argument('symbol', help='Token symbol to reject')
    reject_parser.add_argument('--reason', help='Rejection reason')
    reject_parser.set_defaults(func=reject_token)
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Check system status')
    status_parser.set_defaults(func=check_status)
    
    # Parse arguments
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.verbose)
    
    # Run command
    if hasattr(args, 'func'):
        return args.func(args)
    else:
        parser.print_help()
        return 1

if __name__ == '__main__':
    sys.exit(main())
