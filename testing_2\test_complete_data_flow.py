#!/usr/bin/env python
"""
Comprehensive test script to trace the complete data flow from backend to frontend.
This script:
1. Generates data in the backend format (test_allocation.py)
2. Formats it for API response (api_server.py)
3. Simulates frontend processing (TradingViewDashboard.jsx)
4. Analyzes the data at each step to identify issues
5. Visualizes the data to help identify discrepancies

Usage:
    python test_complete_data_flow.py [--run-strategy] [--output-dir OUTPUT_DIR] [--visualize]
"""

import argparse
import json
import logging
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('complete_data_flow_test')

class CompleteDataFlowTester:
    """Test the complete data flow from backend to frontend."""
    
    def __init__(self, output_dir='complete_data_flow_test_output', visualize=False):
        """Initialize the tester."""
        self.output_dir = output_dir
        self.visualize = visualize
        
        # Create output directory if it doesn't exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_backend_data(self, run_strategy=False):
        """
        Get data from the backend (either by running test_allocation.py or generating sample data).
        
        Args:
            run_strategy: Whether to run test_allocation.py to generate real data
            
        Returns:
            Dict containing the backend data
        """
        if run_strategy:
            logger.info("Running test_allocation.py to generate real data...")
            try:
                # Import test_allocation.py
                sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                from test_allocation import run_strategy_for_web
                
                # Run the strategy with default parameters
                strategy_results = run_strategy_for_web(
                    use_mtpi_signal=True,
                    mtpi_indicator_type='PGO',
                    mtpi_timeframe='1d',
                    timeframe='1d',
                    analysis_start_date='2023-10-20',
                    selected_assets=['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
                    n_assets=1,
                    use_cache=True
                )
                
                logger.info("Successfully ran test_allocation.py")
                return strategy_results
                
            except Exception as e:
                logger.error(f"Error running test_allocation.py: {e}")
                logger.info("Falling back to sample data...")
        
        # Generate sample data
        logger.info("Generating sample backend data...")
        
        # Create sample date range
        start_date = datetime(2023, 10, 20)
        end_date = datetime.now()
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate strategy equity curve
        np.random.seed(42)  # For reproducibility
        initial_value = 10000
        daily_returns = np.random.normal(0.001, 0.02, len(date_range))
        cumulative_returns = np.cumprod(1 + daily_returns)
        strategy_equity = pd.Series(initial_value * cumulative_returns, index=date_range)
        
        # Generate buy and hold curves for sample assets
        assets = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        buy_hold_curves = {}
        
        for i, asset in enumerate(assets):
            np.random.seed(42 + i)
            asset_returns = np.random.normal(0.0008, 0.025, len(date_range))
            asset_cumulative = np.cumprod(1 + asset_returns)
            buy_hold_curves[asset] = pd.Series(initial_value * asset_cumulative, index=date_range)
        
        # Generate best asset series
        best_asset_series = pd.Series(index=date_range, dtype=object)
        for i, date in enumerate(date_range):
            if i % 30 < 10:
                best_asset_series[date] = assets[0]
            elif i % 30 < 20:
                best_asset_series[date] = assets[1]
            else:
                best_asset_series[date] = assets[2]
        
        # Generate MTPI signals
        mtpi_signals = pd.Series(index=date_range, dtype=float)
        for i, date in enumerate(date_range):
            if i % 45 < 35:
                mtpi_signals[date] = 1.0
            else:
                mtpi_signals[date] = -1.0
        
        # Create performance metrics
        performance_metrics = {
            'Total Return': {
                'Strategy': 100 * (strategy_equity.iloc[-1] / strategy_equity.iloc[0] - 1),
                'BTC/USDT': 100 * (buy_hold_curves['BTC/USDT'].iloc[-1] / buy_hold_curves['BTC/USDT'].iloc[0] - 1),
                'ETH/USDT': 100 * (buy_hold_curves['ETH/USDT'].iloc[-1] / buy_hold_curves['ETH/USDT'].iloc[0] - 1),
                'SOL/USDT': 100 * (buy_hold_curves['SOL/USDT'].iloc[-1] / buy_hold_curves['SOL/USDT'].iloc[0] - 1),
            },
            'Sharpe Ratio': {
                'Strategy': 1.5,
                'BTC/USDT': 0.8,
                'ETH/USDT': 1.1,
                'SOL/USDT': 1.3,
            }
        }
        
        # Combine all data
        backend_data = {
            'strategy_equity': strategy_equity,
            'buy_hold_curves': buy_hold_curves,
            'best_asset_series': best_asset_series,
            'mtpi_signals': mtpi_signals,
            'performance_metrics': performance_metrics
        }
        
        return backend_data
    
    def format_for_api(self, backend_data):
        """
        Format the backend data for API response.
        
        Args:
            backend_data: The backend data
            
        Returns:
            Dict formatted as an API response
        """
        logger.info("Formatting backend data for API response...")
        
        # Check if we have pandas Series or DataFrames
        if isinstance(backend_data.get('strategy_equity'), pd.Series):
            # Convert pandas Series to lists of dicts for JSON serialization
            strategy_equity = backend_data['strategy_equity']
            strategy_data = []
            
            for date, value in strategy_equity.items():
                strategy_data.append({
                    'time': int(date.timestamp()),
                    'value': float(value)
                })
            
            # Format buy and hold curves
            buy_hold_data = {}
            for asset, curve in backend_data['buy_hold_curves'].items():
                asset_data = []
                for date, value in curve.items():
                    asset_data.append({
                        'time': int(date.timestamp()),
                        'value': float(value)
                    })
                buy_hold_data[asset] = asset_data
            
            # Format best asset series
            best_asset_data = []
            for date, asset in backend_data['best_asset_series'].items():
                best_asset_data.append({
                    'time': int(date.timestamp()),
                    'asset': asset
                })
            
            # Format MTPI signals
            mtpi_data = []
            for date, signal in backend_data['mtpi_signals'].items():
                mtpi_data.append({
                    'time': int(date.timestamp()),
                    'signal': float(signal)
                })
            
            # Create API response structure
            api_response = {
                'curves': {
                    'strategy': strategy_data,
                },
                'buyHoldCurves': buy_hold_data,
                'assetChanges': best_asset_data,
                'metadata': {
                    'startDate': backend_data['strategy_equity'].index.min().strftime('%Y-%m-%d'),
                    'endDate': backend_data['strategy_equity'].index.max().strftime('%Y-%m-%d'),
                    'rawMtpiSignals': mtpi_data
                },
                'performanceMetrics': backend_data['performance_metrics']
            }
        else:
            # Assume the data is already in the format returned by run_strategy_for_web
            api_response = {
                'curves': {
                    'strategy': []
                },
                'buyHoldCurves': {},
                'assetChanges': [],
                'metadata': {
                    'startDate': '',
                    'endDate': '',
                    'rawMtpiSignals': []
                },
                'performanceMetrics': {}
            }
            
            # Extract strategy equity
            if 'strategy_equity' in backend_data:
                strategy_equity = backend_data['strategy_equity']
                if isinstance(strategy_equity, dict):
                    # Convert dict to list of dicts
                    for date_str, value in strategy_equity.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['curves']['strategy'].append({
                            'time': int(date.timestamp()),
                            'value': float(value)
                        })
                else:
                    logger.warning(f"Unexpected strategy_equity type: {type(strategy_equity)}")
            
            # Extract buy and hold curves
            if 'buy_hold_curves' in backend_data:
                for asset, curve in backend_data['buy_hold_curves'].items():
                    api_response['buyHoldCurves'][asset] = []
                    if isinstance(curve, dict):
                        for date_str, value in curve.items():
                            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            api_response['buyHoldCurves'][asset].append({
                                'time': int(date.timestamp()),
                                'value': float(value)
                            })
            
            # Extract best asset series
            if 'best_asset_series' in backend_data:
                best_asset_series = backend_data['best_asset_series']
                if isinstance(best_asset_series, dict):
                    for date_str, asset in best_asset_series.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['assetChanges'].append({
                            'time': int(date.timestamp()),
                            'asset': asset
                        })
            
            # Extract MTPI signals
            if 'mtpi_signals' in backend_data:
                mtpi_signals = backend_data['mtpi_signals']
                if isinstance(mtpi_signals, dict):
                    for date_str, signal in mtpi_signals.items():
                        date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        api_response['metadata']['rawMtpiSignals'].append({
                            'time': int(date.timestamp()),
                            'signal': float(signal)
                        })
            
            # Extract performance metrics
            if 'performance_metrics' in backend_data:
                api_response['performanceMetrics'] = backend_data['performance_metrics']
            
            # Extract dates
            if 'strategy_equity' in backend_data and isinstance(backend_data['strategy_equity'], dict):
                dates = list(backend_data['strategy_equity'].keys())
                if dates:
                    api_response['metadata']['startDate'] = min(dates).split('T')[0]
                    api_response['metadata']['endDate'] = max(dates).split('T')[0]
        
        # Save API response to JSON file
        api_file = os.path.join(self.output_dir, 'api_response.json')
        with open(api_file, 'w') as f:
            json.dump(api_response, f, indent=2)
        logger.info(f"Saved API response to {api_file}")
        
        return api_response
    
    def simulate_frontend_processing(self, api_response):
        """
        Simulate how the frontend would process the API response.
        
        Args:
            api_response: The API response data
            
        Returns:
            Dict containing the processed data as it would be used in the frontend
        """
        logger.info("Simulating frontend processing...")
        
        # Create the frontend data structure
        frontend_data = {
            'curves': {},
            'metadata': api_response.get('metadata', {})
        }
        
        # Process strategy data
        if 'curves' in api_response and 'strategy' in api_response['curves']:
            strategy_data = api_response['curves']['strategy']
            logger.info(f"Processing strategy data with {len(strategy_data)} points")
            
            # In the frontend, we might need to ensure the data is sorted by time
            strategy_data.sort(key=lambda x: x.get('time', 0))
            
            # Add the strategy data to the frontend data
            frontend_data['curves']['strategy'] = strategy_data
        
        # Process buy and hold curves
        if 'buyHoldCurves' in api_response:
            for asset, data in api_response['buyHoldCurves'].items():
                logger.info(f"Processing {asset} data with {len(data)} points")
                
                # Sort the data by time
                data.sort(key=lambda x: x.get('time', 0))
                
                # Add the asset data to the frontend data
                frontend_data['curves'][asset] = data
        
        # Process asset changes
        if 'assetChanges' in api_response:
            asset_changes = api_response['assetChanges']
            logger.info(f"Processing asset changes with {len(asset_changes)} points")
            
            # Sort the asset changes by time
            asset_changes.sort(key=lambda x: x.get('time', 0))
            
            # Add the asset changes to the frontend data
            frontend_data['assetChanges'] = asset_changes
        
        # Process performance metrics
        if 'performanceMetrics' in api_response:
            frontend_data['performanceMetrics'] = api_response['performanceMetrics']
        
        # Save frontend data to JSON file
        frontend_file = os.path.join(self.output_dir, 'frontend_processed.json')
        with open(frontend_file, 'w') as f:
            json.dump(frontend_data, f, indent=2)
        logger.info(f"Saved frontend processed data to {frontend_file}")
        
        return frontend_data
    
    def visualize_data(self, backend_data, api_response, frontend_data):
        """
        Visualize the data at each step to help identify discrepancies.
        
        Args:
            backend_data: The backend data
            api_response: The API response data
            frontend_data: The frontend processed data
        """
        if not self.visualize:
            return
        
        logger.info("Visualizing data at each step...")
        
        # Create a figure with subplots
        fig, axes = plt.subplots(3, 1, figsize=(12, 18), sharex=True)
        
        # Plot backend data
        if isinstance(backend_data.get('strategy_equity'), pd.Series):
            backend_data['strategy_equity'].plot(ax=axes[0], label='Strategy')
            
            for asset, curve in backend_data['buy_hold_curves'].items():
                curve.plot(ax=axes[0], label=asset)
            
            axes[0].set_title('Backend Data')
            axes[0].set_ylabel('Value')
            axes[0].legend()
            axes[0].grid(True)
        
        # Plot API response data
        if 'curves' in api_response and 'strategy' in api_response['curves']:
            strategy_data = api_response['curves']['strategy']
            times = [datetime.fromtimestamp(point['time']) for point in strategy_data]
            values = [point['value'] for point in strategy_data]
            
            axes[1].plot(times, values, label='Strategy')
            
            for asset, data in api_response.get('buyHoldCurves', {}).items():
                asset_times = [datetime.fromtimestamp(point['time']) for point in data]
                asset_values = [point['value'] for point in data]
                axes[1].plot(asset_times, asset_values, label=asset)
            
            axes[1].set_title('API Response Data')
            axes[1].set_ylabel('Value')
            axes[1].legend()
            axes[1].grid(True)
        
        # Plot frontend processed data
        if 'curves' in frontend_data and 'strategy' in frontend_data['curves']:
            strategy_data = frontend_data['curves']['strategy']
            times = [datetime.fromtimestamp(point['time']) for point in strategy_data]
            values = [point['value'] for point in strategy_data]
            
            axes[2].plot(times, values, label='Strategy')
            
            for asset, data in frontend_data.get('curves', {}).items():
                if asset != 'strategy':
                    asset_times = [datetime.fromtimestamp(point['time']) for point in data]
                    asset_values = [point['value'] for point in data]
                    axes[2].plot(asset_times, asset_values, label=asset)
            
            axes[2].set_title('Frontend Processed Data')
            axes[2].set_xlabel('Date')
            axes[2].set_ylabel('Value')
            axes[2].legend()
            axes[2].grid(True)
        
        # Save the figure
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'data_visualization.png'))
        logger.info(f"Saved data visualization to {os.path.join(self.output_dir, 'data_visualization.png')}")
        plt.close()
    
    def run_test(self, run_strategy=False):
        """
        Run the complete data flow test.
        
        Args:
            run_strategy: Whether to run test_allocation.py to generate real data
            
        Returns:
            Tuple of (backend_data, api_response, frontend_data)
        """
        # Step 1: Get backend data
        backend_data = self.get_backend_data(run_strategy)
        
        # Step 2: Format for API response
        api_response = self.format_for_api(backend_data)
        
        # Step 3: Simulate frontend processing
        frontend_data = self.simulate_frontend_processing(api_response)
        
        # Step 4: Visualize data
        self.visualize_data(backend_data, api_response, frontend_data)
        
        # Step 5: Generate summary report
        self.generate_summary_report(backend_data, api_response, frontend_data)
        
        return backend_data, api_response, frontend_data
    
    def generate_summary_report(self, backend_data, api_response, frontend_data):
        """
        Generate a summary report of the data flow test.
        
        Args:
            backend_data: The backend data
            api_response: The API response data
            frontend_data: The frontend processed data
        """
        logger.info("\n=== DATA FLOW SUMMARY REPORT ===")
        
        # Check data point counts
        backend_count = 0
        if isinstance(backend_data.get('strategy_equity'), pd.Series):
            backend_count = len(backend_data['strategy_equity'])
        
        api_count = 0
        if 'curves' in api_response and 'strategy' in api_response['curves']:
            api_count = len(api_response['curves']['strategy'])
        
        frontend_count = 0
        if 'curves' in frontend_data and 'strategy' in frontend_data['curves']:
            frontend_count = len(frontend_data['curves']['strategy'])
        
        logger.info(f"Data point counts - Backend: {backend_count}, API: {api_count}, Frontend: {frontend_count}")
        
        if backend_count != api_count:
            logger.warning(f"Data point count mismatch between backend and API: {backend_count} vs {api_count}")
        
        if api_count != frontend_count:
            logger.warning(f"Data point count mismatch between API and frontend: {api_count} vs {frontend_count}")
        
        # Check for timestamp format consistency
        if 'curves' in frontend_data and 'strategy' in frontend_data['curves'] and frontend_data['curves']['strategy']:
            first_point = frontend_data['curves']['strategy'][0]
            if 'time' in first_point:
                logger.info(f"Frontend timestamp format: {type(first_point['time']).__name__}")
                
                # Check if timestamp is in seconds (Unix timestamp)
                if isinstance(first_point['time'], int) and first_point['time'] > 1000000000:
                    logger.info("Timestamps appear to be Unix timestamps (seconds since epoch)")
                else:
                    logger.warning(f"Unexpected timestamp format: {first_point['time']}")
        
        # Check for value format consistency
        if 'curves' in frontend_data and 'strategy' in frontend_data['curves'] and frontend_data['curves']['strategy']:
            first_point = frontend_data['curves']['strategy'][0]
            if 'value' in first_point:
                logger.info(f"Frontend value format: {type(first_point['value']).__name__}")
        
        # Generate summary report file
        summary_file = os.path.join(self.output_dir, 'summary_report.txt')
        with open(summary_file, 'w') as f:
            f.write("=== DATA FLOW SUMMARY REPORT ===\n\n")
            f.write(f"Data point counts - Backend: {backend_count}, API: {api_count}, Frontend: {frontend_count}\n")
            
            if backend_count != api_count:
                f.write(f"WARNING: Data point count mismatch between backend and API: {backend_count} vs {api_count}\n")
            
            if api_count != frontend_count:
                f.write(f"WARNING: Data point count mismatch between API and frontend: {api_count} vs {frontend_count}\n")
            
            f.write("\nOutput files:\n")
            f.write(f"- API response: {os.path.join(self.output_dir, 'api_response.json')}\n")
            f.write(f"- Frontend processed data: {os.path.join(self.output_dir, 'frontend_processed.json')}\n")
            
            if self.visualize:
                f.write(f"- Data visualization: {os.path.join(self.output_dir, 'data_visualization.png')}\n")
        
        logger.info(f"Saved summary report to {summary_file}")
        logger.info("================================\n")

def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(description='Test complete data flow from backend to frontend')
    parser.add_argument('--run-strategy', action='store_true', 
                        help='Run test_allocation.py to generate real data')
    parser.add_argument('--output-dir', type=str, default='complete_data_flow_test_output',
                        help='Directory to save output files')
    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualizations of the data')
    args = parser.parse_args()
    
    tester = CompleteDataFlowTester(output_dir=args.output_dir, visualize=args.visualize)
    backend_data, api_response, frontend_data = tester.run_test(run_strategy=args.run_strategy)
    
    logger.info("Complete data flow test completed successfully!")
    
    # Return data for potential further analysis
    return backend_data, api_response, frontend_data

if __name__ == '__main__':
    main()
