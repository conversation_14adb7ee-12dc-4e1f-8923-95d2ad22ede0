2025-06-29 14:43:37,956 - root - INFO - Loaded environment variables from .env file
2025-06-29 14:43:39,835 - root - INFO - Loaded 7 trade records from logs/trades\trade_log_20250629.json
2025-06-29 14:43:39,836 - root - INFO - Loaded 4 asset selection records from logs/trades\asset_selection_20250629.json
2025-06-29 14:43:39,836 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-29 14:43:41,580 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:41,590 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:41,591 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:41,599 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:41,599 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:41,607 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:41,607 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-29 14:43:41,608 - root - INFO - Notification configuration loaded successfully.
2025-06-29 14:43:42,481 - root - INFO - Telegram command handlers registered
2025-06-29 14:43:42,489 - root - INFO - Telegram bot polling started
2025-06-29 14:43:42,490 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-29 14:43:42,490 - root - INFO - Telegram notification channel initialized
2025-06-29 14:43:42,491 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-29 14:43:42,492 - root - INFO - Loaded 24 templates from file
2025-06-29 14:43:42,492 - root - INFO - Notification manager initialized with 1 channels
2025-06-29 14:43:42,493 - root - INFO - Notification manager initialized
2025-06-29 14:43:42,493 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-29 14:43:42,493 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-29 14:43:42,493 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-29 14:43:42,493 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-29 14:43:42,496 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-29 14:43:42,499 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250627_214259.json.json
2025-06-29 14:43:42,500 - root - INFO - Recovery manager initialized
2025-06-29 14:43:42,500 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-29 14:43:42,500 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 1, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.0025}
2025-06-29 14:43:42,500 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:42,506 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:42,507 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 14:43:42,507 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 14:43:42,507 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 14:43:42,507 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 14:43:42,507 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 14:43:42,507 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 14:43:42,507 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 14:43:42,508 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 14:43:42,508 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:42,515 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:42,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-29 14:43:42,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-29 14:43:42,569 - telegram.ext.Application - INFO - Application started
2025-06-29 14:43:42,788 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-29 14:43:42,993 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 14:43:42,995 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 14:43:42,996 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 14:43:42,996 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 14:43:42,997 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 14:43:42,997 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:43,009 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:43,456 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 14:43:43,457 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:43,463 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:43,465 - root - INFO - Trading executor initialized for bitvavo
2025-06-29 14:43:43,465 - root - INFO - Trading mode: live
2025-06-29 14:43:43,465 - root - INFO - Trading enabled: True
2025-06-29 14:43:43,465 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 14:43:43,466 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 14:43:43,466 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 14:43:43,466 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 14:43:43,466 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:43,473 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:43,675 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-29 14:43:43,941 - root - INFO - Successfully connected to bitvavo exchange.
2025-06-29 14:43:43,942 - root - INFO - Trading enabled in live mode
2025-06-29 14:43:44,247 - root - INFO - Connected to bitvavo, balance: 9089.63 EUR
2025-06-29 14:43:44,247 - root - INFO - Generated run ID: 20250629_144344
2025-06-29 14:43:44,247 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-29 14:43:44,247 - root - INFO - Background service initialized
2025-06-29 14:43:44,248 - root - INFO - Network watchdog started
2025-06-29 14:43:44,248 - root - INFO - Network watchdog started
2025-06-29 14:43:44,249 - root - INFO - Schedule set up for 1d timeframe
2025-06-29 14:43:44,249 - root - INFO - Background service started
2025-06-29 14:43:44,250 - root - INFO - Executing strategy (run #1)...
2025-06-29 14:43:44,250 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-29 14:43:44,250 - root - INFO - No trades recorded today (Max: 5)
2025-06-29 14:43:44,251 - root - INFO - Initialized daily trades counter for 2025-06-29
2025-06-29 14:43:44,251 - root - INFO - Creating snapshot for candle timestamp: 20250629
2025-06-29 14:43:44,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 14:43:50,261 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-29 14:43:50,340 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 14:43:50,341 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-29 14:43:50,341 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-29 14:43:50,342 - root - INFO - Using recent date for performance tracking: 2025-06-22
2025-06-29 14:43:50,343 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-29 14:43:50,394 - root - INFO - Loaded 2140 rows of ETH/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,394 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,395 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,395 - root - INFO - Data is up to date for ETH/USDT
2025-06-29 14:43:50,397 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,413 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,414 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,414 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,414 - root - INFO - Data is up to date for BTC/USDT
2025-06-29 14:43:50,415 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,437 - root - INFO - Loaded 1783 rows of SOL/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,437 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,438 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,438 - root - INFO - Data is up to date for SOL/USDT
2025-06-29 14:43:50,438 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,449 - root - INFO - Loaded 788 rows of SUI/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,450 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,451 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,451 - root - INFO - Data is up to date for SUI/USDT
2025-06-29 14:43:50,452 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,472 - root - INFO - Loaded 2140 rows of XRP/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,473 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,473 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,474 - root - INFO - Data is up to date for XRP/USDT
2025-06-29 14:43:50,475 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,488 - root - INFO - Loaded 1718 rows of AAVE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,488 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,489 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,489 - root - INFO - Data is up to date for AAVE/USDT
2025-06-29 14:43:50,490 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,505 - root - INFO - Loaded 1741 rows of AVAX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,505 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,506 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,506 - root - INFO - Data is up to date for AVAX/USDT
2025-06-29 14:43:50,507 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,523 - root - INFO - Loaded 2140 rows of ADA/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,524 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,524 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,524 - root - INFO - Data is up to date for ADA/USDT
2025-06-29 14:43:50,525 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,541 - root - INFO - Loaded 2140 rows of LINK/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,541 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,542 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,542 - root - INFO - Data is up to date for LINK/USDT
2025-06-29 14:43:50,543 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,556 - root - INFO - Loaded 2140 rows of TRX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,558 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,558 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,559 - root - INFO - Data is up to date for TRX/USDT
2025-06-29 14:43:50,559 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,565 - root - INFO - Loaded 786 rows of PEPE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,566 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,566 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,566 - root - INFO - Data is up to date for PEPE/USDT
2025-06-29 14:43:50,567 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,590 - root - INFO - Loaded 2140 rows of DOGE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,590 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,590 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,590 - root - INFO - Data is up to date for DOGE/USDT
2025-06-29 14:43:50,591 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,607 - root - INFO - Loaded 2140 rows of BNB/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,607 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,608 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,608 - root - INFO - Data is up to date for BNB/USDT
2025-06-29 14:43:50,609 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,623 - root - INFO - Loaded 1776 rows of DOT/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,623 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,624 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-28 00:00:00+00:00
2025-06-29 14:43:50,624 - root - INFO - Data is up to date for DOT/USDT
2025-06-29 14:43:50,624 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,626 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-29 14:43:50,627 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-29 14:43:50,627 - root - INFO -   - Number of indicators: 8
2025-06-29 14:43:50,627 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 14:43:50,627 - root - INFO -   - Combination method: consensus
2025-06-29 14:43:50,627 - root - INFO -   - Long threshold: 0.1
2025-06-29 14:43:50,627 - root - INFO -   - Short threshold: -0.1
2025-06-29 14:43:50,627 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 14:43:50,628 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:43:50,628 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-29 14:43:50,628 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-29 14:43:50,628 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-29 14:43:50,628 - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-06-29 14:43:50,628 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-29 14:43:50,628 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-29 14:43:50,628 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-29 14:43:50,628 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-29 14:43:50,628 - root - INFO - Using provided trend method: PGO For Loop
2025-06-29 14:43:50,628 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:50,637 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:50,637 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:50,642 - root - INFO - Configuration saved successfully.
2025-06-29 14:43:50,642 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 14:43:50,642 - root - INFO - Number of trend detection assets: 14
2025-06-29 14:43:50,642 - root - INFO - Selected assets type: <class 'list'>
2025-06-29 14:43:50,642 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:43:50,643 - root - INFO - Number of trading assets: 14
2025-06-29 14:43:50,643 - root - INFO - Trading assets type: <class 'list'>
2025-06-29 14:43:50,924 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:43:50,932 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:50,940 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 14:43:50,948 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:50,948 - root - INFO - Execution context: backtesting
2025-06-29 14:43:50,949 - root - INFO - Execution timing: candle_close
2025-06-29 14:43:50,949 - root - INFO - Ratio calculation method: independent
2025-06-29 14:43:50,949 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-29 14:43:50,949 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-29 14:43:50,949 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 14:43:50,949 - root - INFO - MTPI combination method override: consensus
2025-06-29 14:43:50,949 - root - INFO - MTPI long threshold override: 0.1
2025-06-29 14:43:50,950 - root - INFO - MTPI short threshold override: -0.1
2025-06-29 14:43:50,950 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-29 14:43:50,950 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-29 14:43:50,954 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,954 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,955 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,955 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,956 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,956 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,956 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,957 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,957 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,957 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,957 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,958 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,958 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,958 - root - INFO - Loaded metadata for 48 assets
2025-06-29 14:43:50,959 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-29 14:43:50,975 - root - INFO - Loaded 199 rows of ETH/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,976 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:50,977 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,977 - root - INFO - Loaded 199 rows of ETH/USDT data from cache (after filtering).
2025-06-29 14:43:50,992 - root - INFO - Loaded 199 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:50,993 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:50,993 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:50,994 - root - INFO - Loaded 199 rows of BTC/USDT data from cache (after filtering).
2025-06-29 14:43:51,007 - root - INFO - Loaded 199 rows of SOL/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,009 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,009 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,009 - root - INFO - Loaded 199 rows of SOL/USDT data from cache (after filtering).
2025-06-29 14:43:51,018 - root - INFO - Loaded 199 rows of SUI/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,022 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,023 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,023 - root - INFO - Loaded 199 rows of SUI/USDT data from cache (after filtering).
2025-06-29 14:43:51,042 - root - INFO - Loaded 199 rows of XRP/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,043 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,043 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,043 - root - INFO - Loaded 199 rows of XRP/USDT data from cache (after filtering).
2025-06-29 14:43:51,063 - root - INFO - Loaded 199 rows of AAVE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,065 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,066 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,066 - root - INFO - Loaded 199 rows of AAVE/USDT data from cache (after filtering).
2025-06-29 14:43:51,089 - root - INFO - Loaded 199 rows of AVAX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,090 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,090 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,092 - root - INFO - Loaded 199 rows of AVAX/USDT data from cache (after filtering).
2025-06-29 14:43:51,108 - root - INFO - Loaded 199 rows of ADA/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,110 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,110 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,110 - root - INFO - Loaded 199 rows of ADA/USDT data from cache (after filtering).
2025-06-29 14:43:51,128 - root - INFO - Loaded 199 rows of LINK/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,130 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,131 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,131 - root - INFO - Loaded 199 rows of LINK/USDT data from cache (after filtering).
2025-06-29 14:43:51,148 - root - INFO - Loaded 199 rows of TRX/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,149 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,149 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,149 - root - INFO - Loaded 199 rows of TRX/USDT data from cache (after filtering).
2025-06-29 14:43:51,160 - root - INFO - Loaded 199 rows of PEPE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,162 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,162 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,162 - root - INFO - Loaded 199 rows of PEPE/USDT data from cache (after filtering).
2025-06-29 14:43:51,184 - root - INFO - Loaded 199 rows of DOGE/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,188 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,189 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,189 - root - INFO - Loaded 199 rows of DOGE/USDT data from cache (after filtering).
2025-06-29 14:43:51,206 - root - INFO - Loaded 199 rows of BNB/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,208 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,209 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,209 - root - INFO - Loaded 199 rows of BNB/USDT data from cache (after filtering).
2025-06-29 14:43:51,224 - root - INFO - Loaded 199 rows of DOT/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,226 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-29 14:43:51,227 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,227 - root - INFO - Loaded 199 rows of DOT/USDT data from cache (after filtering).
2025-06-29 14:43:51,227 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 14:43:51,227 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,228 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,228 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,228 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,228 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,229 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,230 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-29 14:43:51,248 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-29 14:43:51,248 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-29 14:43:51,248 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-29 14:43:51,248 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-29 14:43:51,249 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:43:51,258 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:51,258 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-29 14:43:51,259 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-29 14:43:51,259 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 14:43:51,259 - root - INFO - Override: combination_method = consensus
2025-06-29 14:43:51,259 - root - INFO - Override: long_threshold = 0.1
2025-06-29 14:43:51,259 - root - INFO - Override: short_threshold = -0.1
2025-06-29 14:43:51,259 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-29 14:43:51,259 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-29 14:43:51,260 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-29 14:43:51,260 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-29 14:43:51,279 - root - INFO - Loaded 259 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:43:51,280 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:43:51,280 - root - INFO - Loaded 259 rows of BTC/USDT data from cache (after filtering).
2025-06-29 14:43:51,281 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 14:43:51,281 - root - INFO - Fetched BTC data: 259 candles from 2024-10-13 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:43:51,281 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-29 14:43:51,317 - root - INFO - Generated PGO Score signals: {-1: 107, 0: 34, 1: 118}
2025-06-29 14:43:51,318 - root - INFO - Generated pgo signals: 259 values
2025-06-29 14:43:51,318 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-29 14:43:51,318 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-29 14:43:51,334 - root - INFO - Generated BB Score signals: {-1: 109, 0: 32, 1: 118}
2025-06-29 14:43:51,334 - root - INFO - Generated Bollinger Band signals: 259 values
2025-06-29 14:43:51,334 - root - INFO - Generated bollinger_bands signals: 259 values
2025-06-29 14:43:51,885 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-29 14:43:51,885 - root - INFO - Generated dwma_score signals: 259 values
2025-06-29 14:43:51,933 - root - INFO - Generated DEMA Supertrend signals
2025-06-29 14:43:51,933 - root - INFO - Signal distribution: {-1: 145, 0: 1, 1: 113}
2025-06-29 14:43:51,933 - root - INFO - Generated DEMA Super Score signals
2025-06-29 14:43:51,933 - root - INFO - Generated dema_super_score signals: 259 values
2025-06-29 14:43:52,043 - root - INFO - Generated DPSD signals
2025-06-29 14:43:52,043 - root - INFO - Signal distribution: {-1: 101, 0: 87, 1: 71}
2025-06-29 14:43:52,043 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-29 14:43:52,044 - root - INFO - Generated dpsd_score signals: 259 values
2025-06-29 14:43:52,055 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-29 14:43:52,056 - root - INFO - Generated AAD Score signals using SMA method
2025-06-29 14:43:52,056 - root - INFO - Generated aad_score signals: 259 values
2025-06-29 14:43:52,131 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-29 14:43:52,132 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-29 14:43:52,132 - root - INFO - Generated dynamic_ema_score signals: 259 values
2025-06-29 14:43:52,265 - root - INFO - Generated quantile_dema_score signals: 259 values
2025-06-29 14:43:52,272 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-29 14:43:52,273 - root - INFO - Signal distribution: {1: 145, -1: 113, 0: 1}
2025-06-29 14:43:52,273 - root - INFO - Generated combined MTPI signals: 259 values using consensus method
2025-06-29 14:43:52,274 - root - INFO - Signal distribution: {1: 145, -1: 113, 0: 1}
2025-06-29 14:43:52,274 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-29 14:43:52,277 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-29 14:43:52,284 - root - INFO - Configuration saved successfully.
2025-06-29 14:43:52,284 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-29 14:43:52,285 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:43:52,293 - root - INFO - Configuration loaded successfully.
2025-06-29 14:43:52,293 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-29 14:43:52,294 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-29 14:43:52,294 - root - INFO - Using ratio calculation method: independent
2025-06-29 14:43:52,314 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,343 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:52,367 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:52,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,386 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:52,392 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:52,409 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 14:43:52,409 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,422 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 14:43:52,429 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:52,445 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:52,446 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,461 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:52,467 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:52,492 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:52,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,511 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:52,518 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:52,539 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-29 14:43:52,539 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,558 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-29 14:43:52,565 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:52,587 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:52,588 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,604 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:52,611 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:52,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:43:52,630 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:52,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,651 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:52,659 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:52,678 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:52,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,696 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:52,701 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:52,722 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:52,723 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,740 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:52,745 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:52,770 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:52,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,787 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:52,792 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:52,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,831 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:52,847 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 14:43:52,848 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,864 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 14:43:52,870 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:52,888 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:52,888 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,906 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:52,914 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:52,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,958 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:52,977 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:52,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:52,994 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:52,999 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:53,020 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:53,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,040 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:53,047 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:53,075 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:53,075 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,092 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:53,098 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:53,120 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 14:43:53,120 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,137 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 14:43:53,143 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:53,160 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:53,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,176 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:53,181 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:53,202 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:53,204 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,222 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:53,228 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:53,248 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:53,248 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,267 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:53,274 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:53,292 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:53,293 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,309 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:53,315 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:53,337 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,358 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:53,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,395 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:53,413 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 14:43:53,413 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,428 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-29 14:43:53,433 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:53,455 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-29 14:43:53,456 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,473 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-29 14:43:53,479 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:53,497 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,520 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:53,537 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:53,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,554 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:53,559 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:53,578 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:53,578 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,593 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:53,599 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:53,618 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-29 14:43:53,619 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,637 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-29 14:43:53,645 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:53,666 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:53,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,690 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:53,698 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:53,720 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:53,721 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,737 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:53,743 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:53,759 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:53,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,774 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:53,780 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:53,795 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 14:43:53,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,809 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-29 14:43:53,813 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:53,832 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:53,832 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,849 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-29 14:43:53,856 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:53,873 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:53,873 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,889 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:53,894 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:53,909 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:53,909 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,925 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:53,930 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:53,947 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:53,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:53,962 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:53,967 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:53,987 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:53,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,004 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:54,008 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:54,024 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,043 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:54,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,082 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:54,103 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,126 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:54,142 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 14:43:54,142 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,159 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 14:43:54,164 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:54,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,208 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:54,227 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,246 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:54,266 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,267 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,283 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,291 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:54,307 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,325 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:54,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,363 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:54,381 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:54,382 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,398 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:54,404 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:54,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,440 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:54,456 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,475 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:54,491 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 14:43:54,491 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,505 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 14:43:54,512 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:54,528 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,548 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:54,566 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,591 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:54,607 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:54,607 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,622 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:54,627 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:54,642 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,643 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,658 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,665 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:54,679 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 14:43:54,680 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,695 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 14:43:54,699 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:54,718 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:54,719 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,736 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:54,743 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:54,759 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,775 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,781 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:54,799 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:54,799 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,816 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:54,824 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:54,841 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,841 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,856 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-29 14:43:54,860 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:54,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,894 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:54,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,929 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:54,947 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:54,947 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:54,962 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:54,967 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:54,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,009 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:55,025 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,046 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:55,071 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,092 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:55,108 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,128 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:55,143 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,163 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:55,181 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:55,182 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,199 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:55,204 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:55,221 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:55,222 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,236 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:55,242 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:55,259 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:55,259 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,274 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:55,279 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:55,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,325 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:55,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,359 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:55,374 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:55,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,391 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:55,397 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:55,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,440 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:55,461 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:55,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,477 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:55,483 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:55,503 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,526 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:55,546 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,571 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:55,593 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,613 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:55,633 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,656 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:55,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,697 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:55,715 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 14:43:55,715 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,735 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 14:43:55,742 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:55,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,781 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:55,803 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-29 14:43:55,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,821 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-29 14:43:55,826 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:55,844 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,866 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:55,886 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,909 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:55,925 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,944 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:55,962 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:55,981 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:56,000 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,022 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:56,041 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:56,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,057 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:56,062 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:56,088 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,111 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:56,132 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,156 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:56,173 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:56,174 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,191 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:56,197 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:56,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,245 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:56,263 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:56,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,282 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:56,290 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:56,307 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:56,308 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,327 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:56,338 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:56,359 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 14:43:56,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,374 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-29 14:43:56,381 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:56,404 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,428 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:56,447 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:56,447 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,463 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:56,471 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:56,491 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:56,491 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,509 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:56,515 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:56,535 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,560 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:56,586 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:56,587 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,605 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:56,611 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:56,627 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,648 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:56,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,689 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:56,706 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,724 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:56,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,759 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:56,774 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,793 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:56,815 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 14:43:56,815 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,831 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 14:43:56,837 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:56,854 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,878 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:56,907 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:56,945 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:56,974 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,007 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:57,024 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,024 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,038 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,044 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:57,062 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,085 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:57,106 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,128 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:57,144 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,164 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:57,183 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,205 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:57,223 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,244 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:57,260 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,274 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,281 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:57,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,316 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:57,336 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:57,337 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,352 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:57,359 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:57,375 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:57,376 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,393 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:57,398 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:57,421 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:57,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,436 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:57,442 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:57,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,477 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:57,493 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:57,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,507 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:57,513 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:57,530 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:57,530 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,546 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:57,551 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:57,572 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,594 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:57,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,638 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:57,657 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,678 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:57,696 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,716 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:57,734 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,756 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:57,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,791 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:57,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,825 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:57,841 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,860 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:57,880 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-29 14:43:57,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,897 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-29 14:43:57,904 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:57,921 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:57,922 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,937 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-29 14:43:57,943 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:57,958 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,959 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:57,972 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-29 14:43:57,977 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:57,994 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-29 14:43:57,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,006 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-29 14:43:58,012 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:58,027 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,045 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:58,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,079 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:58,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,117 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:58,135 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,160 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:58,178 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,198 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:58,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,244 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:58,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,286 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:58,303 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:58,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,321 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:58,326 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:58,344 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:58,344 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,358 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-29 14:43:58,365 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:58,387 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,387 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,404 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,410 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:58,429 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 14:43:58,429 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,446 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-29 14:43:58,451 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:58,471 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 14:43:58,471 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,488 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-29 14:43:58,494 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:58,511 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:58,512 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,527 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:58,533 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:58,554 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:58,554 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,567 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-29 14:43:58,578 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:58,599 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 14:43:58,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,620 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-29 14:43:58,625 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:58,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,665 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:43:58,687 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,688 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,704 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,709 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:58,726 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,747 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:58,765 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,788 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:58,803 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:58,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,821 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:58,827 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:58,845 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,846 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,862 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:58,869 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:58,888 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:58,889 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,904 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-29 14:43:58,910 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:58,931 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:58,932 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,951 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-29 14:43:58,959 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:58,977 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 14:43:58,978 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:58,993 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-29 14:43:58,999 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:59,017 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,018 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,037 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,043 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:59,063 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-29 14:43:59,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,081 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-29 14:43:59,086 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:59,104 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:59,104 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,122 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-29 14:43:59,129 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:59,147 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,147 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,164 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,169 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:59,187 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,208 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:59,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,248 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-29 14:43:59,271 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,293 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-29 14:43:59,313 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,335 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-29 14:43:59,354 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,375 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-29 14:43:59,393 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:59,393 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,409 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-29 14:43:59,415 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-29 14:43:59,433 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:59,433 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,456 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-29 14:43:59,461 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-29 14:43:59,479 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:59,479 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,494 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-29 14:43:59,500 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-29 14:43:59,520 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 14:43:59,521 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,538 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-29 14:43:59,543 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-29 14:43:59,560 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-29 14:43:59,560 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,576 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-29 14:43:59,582 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-29 14:43:59,603 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:59,604 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,621 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-29 14:43:59,627 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-29 14:43:59,644 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,665 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-29 14:43:59,683 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,705 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-29 14:43:59,710 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-29 14:43:59,738 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,765 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-29 14:43:59,787 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-29 14:43:59,808 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-29 14:44:01,755 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-29 14:44:01,755 - root - INFO - Latest MTPI signal is -1
2025-06-29 14:44:01,755 - root - INFO - Latest MTPI signal is -1, will stay out of market during equity curve calculation
2025-06-29 14:44:01,755 - root - INFO - Finished calculating daily scores. DataFrame shape: (199, 14)
2025-06-29 14:44:01,755 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-06-29 14:44:01,759 - root - INFO - Date ranges for each asset:
2025-06-29 14:44:01,761 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,761 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,761 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,761 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,762 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,763 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,763 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,763 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,763 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,763 - root - INFO - Common dates range: 2024-12-12 to 2025-06-28 (199 candles)
2025-06-29 14:44:01,764 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-28 (139 candles)
2025-06-29 14:44:01,769 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-29 14:44:01,770 - root - INFO -    Execution Method: candle_close
2025-06-29 14:44:01,770 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-29 14:44:01,770 - root - INFO -    Signal generated and executed immediately
2025-06-29 14:44:01,793 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,793 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,794 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,794 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,794 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,794 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,795 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,795 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,795 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,795 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,795 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,796 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,796 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,796 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,802 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,809 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,809 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,809 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,811 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,811 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,811 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,811 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,812 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,813 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,819 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,827 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,827 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,828 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,828 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,829 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,829 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,829 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,829 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,830 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,839 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,845 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,846 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,847 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,856 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,859 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,860 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,860 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,860 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,861 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,863 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,863 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,863 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-29 14:44:01,871 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,887 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,899 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,911 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,919 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,929 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,939 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,949 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,963 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,973 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,985 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:01,998 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,023 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,046 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,061 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,077 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,089 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,098 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,107 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,125 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,138 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,149 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,163 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,173 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,180 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,193 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,201 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,212 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,222 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,229 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,240 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,248 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,258 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,263 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,271 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,278 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,288 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,295 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,300 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,309 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,315 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,322 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,327 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,336 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,345 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,351 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,359 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,365 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,373 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,380 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,387 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,395 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,402 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,411 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,422 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,429 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,438 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,445 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,451 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,460 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,471 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,479 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,486 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,502 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,515 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,528 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,615 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,648 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:02,648 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-29 14:44:02,648 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-29 14:44:02,648 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-29 14:44:02,648 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:02,649 - root - INFO -    Buying: ['SOL/USDT']
2025-06-29 14:44:02,649 - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-06-29 14:44:02,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:44:02,680 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,680 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-29 14:44:02,680 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-29 14:44:02,680 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-29 14:44:02,681 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:02,681 - root - INFO -    Selling: ['SOL/USDT']
2025-06-29 14:44:02,681 - root - INFO -    Buying: ['SUI/USDT']
2025-06-29 14:44:02,681 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-29 14:44:02,746 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,842 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,854 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,868 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,879 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,894 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,908 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,923 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,932 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,962 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,979 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:02,998 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,013 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,030 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,045 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,059 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,060 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-29 14:44:03,060 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-29 14:44:03,060 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-29 14:44:03,060 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:03,060 - root - INFO -    Selling: ['SUI/USDT']
2025-06-29 14:44:03,060 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-29 14:44:03,061 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-29 14:44:03,080 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,093 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,104 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,113 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,128 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,141 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,149 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,164 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,180 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,194 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,208 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,210 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-29 14:44:03,210 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-29 14:44:03,210 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-29 14:44:03,210 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:03,210 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-29 14:44:03,210 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-29 14:44:03,210 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-29 14:44:03,223 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,230 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,230 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-29 14:44:03,230 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-29 14:44:03,230 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-29 14:44:03,230 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:03,231 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-29 14:44:03,231 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-29 14:44:03,231 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-29 14:44:03,243 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,255 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,255 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-29 14:44:03,255 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-29 14:44:03,255 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-29 14:44:03,255 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:03,256 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-29 14:44:03,256 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-29 14:44:03,256 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-29 14:44:03,262 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,273 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,282 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,295 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,309 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,324 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,332 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,346 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,362 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,379 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,398 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,412 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,477 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,512 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,526 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,548 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,562 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,578 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,590 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,598 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,611 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,617 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,628 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,633 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,644 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,649 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,660 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-29 14:44:03,660 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-29 14:44:03,660 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-29 14:44:03,660 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-29 14:44:03,660 - root - INFO -    Execution Delay: 0 hours
2025-06-29 14:44:03,660 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-29 14:44:03,666 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,678 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,689 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,729 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,795 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,809 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,819 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-29 14:44:03,874 - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-06-29 14:44:03,874 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-29 14:44:03,874 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-29 14:44:03,874 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-29 14:44:03,874 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-29 14:44:03,875 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-29 14:44:03,875 - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-06-29 14:44:03,876 - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-06-29 14:44:03,877 - root - INFO - Strategy execution completed in 2s
2025-06-29 14:44:03,877 - root - INFO - DEBUG: self.elapsed_time = 2.1217682361602783 seconds
2025-06-29 14:44:03,893 - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-29 14:44:03,894 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-29 14:44:03,894 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-29 14:44:03,894 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-29 14:44:03,894 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-29 14:44:03,894 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-29 14:44:03,894 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-29 14:44:03,895 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-29 14:44:03,896 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-29 14:44:03,897 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,898 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,900 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,905 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,906 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,907 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,908 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,909 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,910 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,911 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,912 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,913 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,914 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,915 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-29 14:44:03,918 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 199 points
2025-06-29 14:44:03,920 - root - INFO - ETH/USDT B&H total return: -8.48%
2025-06-29 14:44:03,922 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 199 points
2025-06-29 14:44:03,922 - root - INFO - BTC/USDT B&H total return: 10.13%
2025-06-29 14:44:03,924 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 199 points
2025-06-29 14:44:03,924 - root - INFO - SOL/USDT B&H total return: -24.82%
2025-06-29 14:44:03,926 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 199 points
2025-06-29 14:44:03,926 - root - INFO - SUI/USDT B&H total return: -12.88%
2025-06-29 14:44:03,927 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 199 points
2025-06-29 14:44:03,928 - root - INFO - XRP/USDT B&H total return: -9.83%
2025-06-29 14:44:03,929 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 199 points
2025-06-29 14:44:03,929 - root - INFO - AAVE/USDT B&H total return: 2.89%
2025-06-29 14:44:03,930 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 199 points
2025-06-29 14:44:03,931 - root - INFO - AVAX/USDT B&H total return: -30.00%
2025-06-29 14:44:03,933 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 199 points
2025-06-29 14:44:03,933 - root - INFO - ADA/USDT B&H total return: -20.60%
2025-06-29 14:44:03,937 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 199 points
2025-06-29 14:44:03,937 - root - INFO - LINK/USDT B&H total return: -28.81%
2025-06-29 14:44:03,939 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 199 points
2025-06-29 14:44:03,939 - root - INFO - TRX/USDT B&H total return: 11.58%
2025-06-29 14:44:03,940 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 199 points
2025-06-29 14:44:03,941 - root - INFO - PEPE/USDT B&H total return: -0.42%
2025-06-29 14:44:03,942 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 199 points
2025-06-29 14:44:03,942 - root - INFO - DOGE/USDT B&H total return: -35.94%
2025-06-29 14:44:03,944 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 199 points
2025-06-29 14:44:03,944 - root - INFO - BNB/USDT B&H total return: 4.84%
2025-06-29 14:44:03,945 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 199 points
2025-06-29 14:44:03,945 - root - INFO - DOT/USDT B&H total return: -29.68%
2025-06-29 14:44:03,946 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:44:03,954 - root - INFO - Configuration loaded successfully.
2025-06-29 14:44:03,965 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-29 14:44:04,175 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:44:04,184 - root - INFO - Configuration loaded successfully.
2025-06-29 14:44:05,837 - root - INFO - Added ETH/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added BTC/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added SOL/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added SUI/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added XRP/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added AAVE/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added AVAX/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added ADA/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added LINK/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,838 - root - INFO - Added TRX/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,839 - root - INFO - Added PEPE/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,839 - root - INFO - Added DOGE/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,839 - root - INFO - Added BNB/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,839 - root - INFO - Added DOT/USDT buy-and-hold curve with 199 points
2025-06-29 14:44:05,839 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-29 14:44:05,839 - root - INFO -   - ETH/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,839 - root - INFO -   - BTC/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,839 - root - INFO -   - SOL/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - SUI/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - XRP/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - AAVE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - AVAX/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - ADA/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - LINK/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,840 - root - INFO -   - TRX/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,841 - root - INFO -   - PEPE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,841 - root - INFO -   - DOGE/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,841 - root - INFO -   - BNB/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,841 - root - INFO -   - DOT/USDT: 199 points from 2024-12-12 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,853 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-29 14:44:05,854 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-29 14:44:05,855 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-29 14:44:05,855 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-29 14:44:05,862 - root - INFO - Configuration loaded successfully.
2025-06-29 14:44:05,863 - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 14:44:05,863 - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-29 14:44:05,863 - root - INFO - Combination method: consensus
2025-06-29 14:44:05,863 - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-29 14:44:05,863 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-29 14:44:05,882 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (last updated: 2025-06-29)
2025-06-29 14:44:05,884 - root - INFO - No incomplete daily candles to filter for current date 2025-06-29
2025-06-29 14:44:05,885 - root - INFO - Loaded 2140 rows of BTC/USDT data from cache (after filtering).
2025-06-29 14:44:05,885 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-29 14:44:05,885 - root - INFO - Fetched BTC data: 2140 candles from 2019-08-20 00:00:00+00:00 to 2025-06-28 00:00:00+00:00
2025-06-29 14:44:05,885 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-29 14:44:06,135 - root - INFO - Generated PGO Score signals: {-1: 992, 0: 34, 1: 1114}
2025-06-29 14:44:06,135 - root - INFO - PGO signal: -1
2025-06-29 14:44:06,135 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-29 14:44:06,226 - root - INFO - Generated BB Score signals: {-1: 993, 0: 33, 1: 1114}
2025-06-29 14:44:06,227 - root - INFO - Bollinger Bands signal: -1
2025-06-29 14:44:10,076 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-29 14:44:10,077 - root - INFO - DWMA Score signal: -1
2025-06-29 14:44:10,447 - root - INFO - Generated DEMA Supertrend signals
2025-06-29 14:44:10,448 - root - INFO - Signal distribution: {-1: 1257, 0: 1, 1: 882}
2025-06-29 14:44:10,448 - root - INFO - Generated DEMA Super Score signals
2025-06-29 14:44:10,448 - root - INFO - DEMA Super Score signal: -1
2025-06-29 14:44:11,462 - root - INFO - Generated DPSD signals
2025-06-29 14:44:11,462 - root - INFO - Signal distribution: {-1: 1083, 0: 87, 1: 970}
2025-06-29 14:44:11,463 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-29 14:44:11,463 - root - INFO - DPSD Score signal: -1
2025-06-29 14:44:11,525 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-29 14:44:11,525 - root - INFO - Generated AAD Score signals using SMA method
2025-06-29 14:44:11,526 - root - INFO - AAD Score signal: -1
2025-06-29 14:44:11,938 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-29 14:44:11,939 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-29 14:44:11,939 - root - INFO - Dynamic EMA Score signal: -1
2025-06-29 14:44:12,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:44:12,834 - root - INFO - Quantile DEMA Score signal: -1
2025-06-29 14:44:12,834 - root - INFO - Individual signals: {'pgo': -1, 'bollinger_bands': -1, 'dwma_score': -1, 'dema_super_score': -1, 'dpsd_score': -1, 'aad_score': -1, 'dynamic_ema_score': -1, 'quantile_dema_score': -1}
2025-06-29 14:44:12,834 - root - INFO - Combined MTPI signal (consensus): -1
2025-06-29 14:44:12,834 - root - INFO - MTPI Score: -1.000000
2025-06-29 14:44:12,835 - root - INFO - Added current MTPI score to results: -1.000000 (using 1d timeframe)
2025-06-29 14:44:12,837 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-29 14:44:12,838 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-29 14:44:12,838 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-29 14:44:12,838 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-29 14:44:12,838 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-29 14:44:12,838 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 6.0)
2025-06-29 14:44:12,838 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-29 14:44:12,839 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 7.0)
2025-06-29 14:44:12,839 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 3.0)
2025-06-29 14:44:12,839 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-29 14:44:12,839 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-29 14:44:12,840 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 4.0)
2025-06-29 14:44:12,840 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 2.0)
2025-06-29 14:44:12,840 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-29 14:44:12,841 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-29 14:44:12,841 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-29 14:44:12,841 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-29 14:44:12,841 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-29 14:44:12,841 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-29 14:44:12,842 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 14:44:12,842 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:44:12,842 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 14:44:12,846 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250622_run_20250629_144344.csv
2025-06-29 14:44:12,846 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250622_run_20250629_144344.csv
2025-06-29 14:44:12,846 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-29 14:44:12,847 - root - INFO - Results type: <class 'dict'>
2025-06-29 14:44:12,847 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-29 14:44:12,847 - root - INFO - Success flag set to: True
2025-06-29 14:44:12,847 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-29 14:44:12,847 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 199 entries
2025-06-29 14:44:12,847 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-29 14:44:12,848 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 199 entries
2025-06-29 14:44:12,848 - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 259 entries
2025-06-29 14:44:12,849 - root - INFO -   - mtpi_score: <class 'float'>
2025-06-29 14:44:12,849 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 199 entries
2025-06-29 14:44:12,849 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-29 14:44:12,849 - root - INFO -   - metrics_file: <class 'str'>
2025-06-29 14:44:12,849 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-29 14:44:12,849 - root - INFO -   - success: <class 'bool'>
2025-06-29 14:44:12,849 - root - INFO -   - message: <class 'str'>
2025-06-29 14:44:12,849 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-29 14:44:12,850 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-06-29 14:44:12,852 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-24 00:00:00+00:00    
2025-06-25 00:00:00+00:00    
2025-06-26 00:00:00+00:00    
2025-06-27 00:00:00+00:00    
2025-06-28 00:00:00+00:00    
dtype: object
2025-06-29 14:44:12,852 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 14:44:12,852 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:44:12,852 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-29 14:44:12,852 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-29 14:44:12,852 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 14:44:12,852 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:44:12,853 - root - INFO - MTPI signal is -1, staying out of the market
2025-06-29 14:44:12,853 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset:  (MTPI signal: -1)
2025-06-29 14:44:12,853 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-29 14:44:12,853 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-29 14:44:12,853 - root - ERROR - [DEBUG] NO TIE - CONFIRMED SELECTION: 
2025-06-29 14:44:12,853 - root - ERROR - [DEBUG] NO TIE - Single winner: 
2025-06-29 14:44:12,875 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-29 14:44:12,876 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-29 14:44:12,876 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-29 14:44:12,876 - root - INFO - [DEBUG]   - Best asset selected: 
2025-06-29 14:44:12,877 - root - INFO - [DEBUG]   - Assets held: {}
2025-06-29 14:44:12,877 - root - INFO - [DEBUG]   - MTPI signal: -1
2025-06-29 14:44:12,877 - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-06-29 14:44:12,882 - root - INFO - MTPI signal is bearish (-1). Exiting all positions.
2025-06-29 14:44:12,946 - root - INFO - No open positions to exit.
2025-06-29 14:44:12,946 - root - INFO - Exit all positions result logged to trade log file
2025-06-29 14:44:12,947 - root - INFO - All positions exited successfully
2025-06-29 14:44:12,950 - root - INFO - Asset selection logged: 0 assets selected with single_asset allocation
2025-06-29 14:44:12,951 - root - INFO - Asset scores (sorted by score):
2025-06-29 14:44:12,951 - root - INFO -   BTC/EUR: score=13.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,951 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,951 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,951 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   SOL/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   ETH/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   AVAX/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,952 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,953 - root - INFO -   SUI/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,953 - root - INFO -   ADA/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,953 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,953 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-29 14:44:12,953 - root - INFO - Rejected assets:
2025-06-29 14:44:12,953 - root - INFO -   BTC/EUR: reason=Failed to trade, score=13.0, rank=1
2025-06-29 14:44:12,954 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 13.0) - Failed to trade
2025-06-29 14:44:12,954 - root - INFO - Asset selection logged with 14 assets scored and 0 assets selected
2025-06-29 14:44:12,954 - root - INFO - Extracted asset scores: {'ETH/EUR': 6.0, 'BTC/EUR': 13.0, 'SOL/EUR': 7.0, 'SUI/EUR': 3.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-29 14:44:13,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-29 14:44:13,016 - root - INFO - Strategy execution completed successfully in 28.77 seconds
2025-06-29 14:44:13,019 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-29 14:44:22,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:44:32,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:44:33,165 - root - INFO - Received signal 2, shutting down...
2025-06-29 14:44:42,856 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-29 14:44:43,169 - root - INFO - Network watchdog stopped
2025-06-29 14:44:43,169 - root - INFO - Network watchdog stopped
2025-06-29 14:44:43,169 - root - INFO - Background service stopped
2025-06-29 14:44:43,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
