#!/usr/bin/env python3
"""
Median Score Indicator - MTPI Component #3

Based on TradingView PineScript implementation:
- Uses median smoothing (7-period percentile rank at 50%)
- Applies Supertrend to the median-smoothed source
- Parameters: Supertrend Length=10, Multiplier=1.55, Median Length=7

Signal Logic:
- Long (1): Supertrend crossover above 0
- Short (-1): Supertrend crossunder below 0  
- Neutral (0): No signal change
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple
import pandas_ta as ta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_median_smoothing(df: pd.DataFrame, length: int = 27, src_col: str = 'high') -> pd.Series:
    """
    Calculate median smoothing using percentile rank at 50% (equivalent to median).
    Matches PineScript: ta.percentile_nearest_rank(src_me, slen, 50)

    Args:
        df: DataFrame with OHLCV data
        length: Period for median calculation (slen in PineScript)
        src_col: Source column name (src_me in PineScript, default 'high')

    Returns:
        Series with median-smoothed values
    """
    try:
        src = df[src_col]

        # Initialize result series
        result = pd.Series(index=df.index, dtype=float)

        # Calculate nearest-rank median for each position
        # This exactly matches TradingView's ta.percentile_nearest_rank(src_me, slen, 50)
        for i in range(len(df)):
            if i < length - 1:
                # For initial values, use available data
                window_data = src.iloc[:i+1].values
            else:
                # Use full window
                window_data = src.iloc[i-length+1:i+1].values

            # Sort the window data
            sorted_data = np.sort(window_data)
            n = len(sorted_data)

            # Calculate nearest-rank for 50th percentile (median)
            # Formula: rank = ceil(P/100 * N) where P=50 for median
            # For median: rank = ceil(0.5 * n)
            rank = int(np.ceil(0.5 * n))

            # Convert to 0-indexed and get value
            # TradingView uses 1-indexed, so rank-1 for 0-indexed array
            median_value = sorted_data[rank - 1]
            result.iloc[i] = median_value

        return result

    except Exception as e:
        logging.error(f"Error calculating median smoothing with nearest-rank method: {e}")
        return pd.Series(index=df.index, dtype=float)

def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """Calculate Average True Range."""
    try:
        high = df['high']
        low = df['low'] 
        close = df['close']
        
        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # ATR is the moving average of True Range
        atr = true_range.rolling(window=period, min_periods=1).mean()
        
        return atr
        
    except Exception as e:
        logging.error(f"Error calculating ATR: {e}")
        return pd.Series(index=df.index, dtype=float)

def calculate_median_supertrend(df: pd.DataFrame,
                               atr_period: int = 12,
                               multiplier: float = 1.45,
                               median_length: int = 27,
                               src_col: str = 'high') -> Tuple[pd.Series, pd.Series]:
    """
    Calculate Median Supertrend indicator.
    
    Args:
        df: DataFrame with OHLCV data
        atr_period: Period for ATR calculation
        multiplier: ATR multiplier
        median_length: Period for median smoothing
        src_col: Source column for median calculation
        
    Returns:
        Tuple of (supertrend_line, direction)
    """
    try:
        # Step 1: Calculate median smoothing
        smooth_src = calculate_median_smoothing(df, median_length, src_col)
        
        # Step 2: Calculate ATR
        atr = calculate_atr(df, atr_period)
        
        # Step 3: Calculate basic upper and lower bands
        upper_band = smooth_src + (multiplier * atr)
        lower_band = smooth_src - (multiplier * atr)
        
        # Step 4: Calculate final supertrend bands with trend logic
        close = df['close']
        
        # Initialize arrays
        final_upper = pd.Series(index=df.index, dtype=float)
        final_lower = pd.Series(index=df.index, dtype=float)
        supertrend = pd.Series(index=df.index, dtype=float)
        direction = pd.Series(index=df.index, dtype=int)
        
        # First value initialization
        final_upper.iloc[0] = upper_band.iloc[0]
        final_lower.iloc[0] = lower_band.iloc[0]
        direction.iloc[0] = 1  # Start with uptrend
        supertrend.iloc[0] = final_lower.iloc[0]
        
        # Calculate supertrend values
        for i in range(1, len(df)):
            # Upper band logic
            if upper_band.iloc[i] < final_upper.iloc[i-1] or close.iloc[i-1] > final_upper.iloc[i-1]:
                final_upper.iloc[i] = upper_band.iloc[i]
            else:
                final_upper.iloc[i] = final_upper.iloc[i-1]

            # Lower band logic
            if lower_band.iloc[i] > final_lower.iloc[i-1] or close.iloc[i-1] < final_lower.iloc[i-1]:
                final_lower.iloc[i] = lower_band.iloc[i]
            else:
                final_lower.iloc[i] = final_lower.iloc[i-1]

            # Direction logic (standard Supertrend algorithm)
            if direction.iloc[i-1] == 1 and close.iloc[i] <= final_lower.iloc[i]:
                direction.iloc[i] = -1
            elif direction.iloc[i-1] == -1 and close.iloc[i] >= final_upper.iloc[i]:
                direction.iloc[i] = 1
            else:
                direction.iloc[i] = direction.iloc[i-1]

            # Supertrend line
            supertrend.iloc[i] = final_lower.iloc[i] if direction.iloc[i] == -1 else final_upper.iloc[i]
        
        return supertrend, direction
        
    except Exception as e:
        logging.error(f"Error calculating median supertrend: {e}")
        return pd.Series(index=df.index, dtype=float), pd.Series(index=df.index, dtype=int)

def generate_median_score_signal(df: pd.DataFrame,
                                atr_period: int = 12,
                                multiplier: float = 1.45,
                                median_length: int = 27,
                                src_col: str = 'high') -> pd.Series:
    """
    Generate Median Score signals based on Supertrend crossovers.
    
    Args:
        df: DataFrame with OHLCV data
        atr_period: Supertrend ATR period
        multiplier: Supertrend multiplier
        median_length: Median smoothing period
        src_col: Source column for median calculation
        
    Returns:
        Series with signals: 1 (Long), -1 (Short), 0 (Neutral)
    """
    try:
        # Calculate median supertrend
        supertrend_line, direction = calculate_median_supertrend(
            df, atr_period, multiplier, median_length, src_col
        )
        
        # Detect crossovers - CORRECTED to match PineScript logic
        # In PineScript: L1 = crossunder(d1, 0), S1 = crossover(d1, 0)
        # L1 (crossunder) triggers LONG signal, S1 (crossover) triggers SHORT signal

        direction_prev = direction.shift(1)

        # Crossover detection - matching PineScript variable assignment
        crossunder_signal = (direction == -1) & (direction_prev == 1)  # crossunder (d1, 0)
        crossover_signal = (direction == 1) & (direction_prev == -1)   # crossover (d1, 0)

        # Apply PineScript logic: L1 = crossunder triggers LONG, S1 = crossover triggers SHORT
        # This seems counterintuitive but matches the PineScript exactly:
        # - crossunder (1→-1) triggers LONG signal
        # - crossover (-1→1) triggers SHORT signal
        long_signal = crossunder_signal   # L1 = stl = crossunder (direction: 1→-1)
        short_signal = crossover_signal   # S1 = sts = crossover (direction: -1→1)
        
        # Initialize signal series
        signal = pd.Series(0, index=df.index, dtype=int)
        
        # Apply signal logic with state persistence
        current_signal = 0
        for i in range(len(df)):
            if long_signal.iloc[i]:
                current_signal = 1
            elif short_signal.iloc[i]:
                current_signal = -1
            # Keep previous signal if no crossover
            
            signal.iloc[i] = current_signal
        
        # Log signal statistics
        signal_counts = signal.value_counts().sort_index()
        logging.info(f"Generated Median Score signals using Median Supertrend method")
        logging.info(f"Signal distribution: {dict(signal_counts)}")
        
        return signal
        
    except Exception as e:
        logging.error(f"Error generating median score signal: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def calculate_median_score(df: pd.DataFrame, **kwargs) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate complete Median Score indicator with components.
    
    Args:
        df: DataFrame with OHLCV data
        **kwargs: Additional parameters
        
    Returns:
        Tuple of (signal, supertrend_line, direction)
    """
    try:
        # Extract parameters (matching PineScript defaults)
        atr_period = kwargs.get('atr_period', 12)      # subject1 in PineScript
        multiplier = kwargs.get('multiplier', 1.45)    # mul1 in PineScript
        median_length = kwargs.get('median_length', 27) # slen in PineScript
        src_col = kwargs.get('src_col', 'high')        # src_me in PineScript
        
        # Calculate components
        supertrend_line, direction = calculate_median_supertrend(
            df, atr_period, multiplier, median_length, src_col
        )
        
        # Generate signals
        signal = generate_median_score_signal(
            df, atr_period, multiplier, median_length, src_col
        )
        
        return signal, supertrend_line, direction
        
    except Exception as e:
        logging.error(f"Error calculating median score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=int))

# Default configuration matching TradingView parameters
DEFAULT_MEDIAN_SCORE_CONFIG = {
    'atr_period': 12,        # subject1 - Supertrend length
    'multiplier': 1.45,      # mul1 - Supertrend multiplier
    'median_length': 27,     # slen - Median smoothing length
    'src_col': 'high'        # src_me - Source for median calculation
}
