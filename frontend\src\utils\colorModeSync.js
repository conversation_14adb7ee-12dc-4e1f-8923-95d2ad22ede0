import { useEffect } from 'react';
import { useColorMode } from '@chakra-ui/react';

/**
 * A utility hook that syncs Chakra UI's color mode with CSS custom properties
 * This allows us to use CSS variables in our custom CSS that respond to theme changes
 */
export const useColorModeSync = () => {
  const { colorMode } = useColorMode();

  useEffect(() => {
    // Set the data-theme attribute on the document element
    document.documentElement.setAttribute('data-theme', colorMode);
  }, [colorMode]);

  return null;
};
