#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Asset Scoring Debugger

This script recreates the asset scoring table that background_service.py generates,
using the EXACT same ratio-based PGO approach as the universal_asset_ratio_pgo_visualizer.py.
It calculates PGO signals on asset pair ratios (like TRX/BTC, TRX/ETH) using the same
MTPI_signal_handler functions and shows which assets each asset "beat" to achieve its score.
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
import yaml
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_ratio_ohlcv(df_a: pd.DataFrame, df_b: pd.DataFrame) -> pd.DataFrame:
    """Create OHLCV data for the ratio between two assets (same as visualizer)."""
    # Align dataframes by common index
    common_index = df_a.index.intersection(df_b.index)
    df_a_aligned = df_a.reindex(common_index)
    df_b_aligned = df_b.reindex(common_index)

    # Calculate ratio OHLCV
    ratio_df = pd.DataFrame(index=common_index)
    ratio_df['open'] = df_a_aligned['open'] / df_b_aligned['open']
    ratio_df['high'] = df_a_aligned['high'] / df_b_aligned['high']
    ratio_df['low'] = df_a_aligned['low'] / df_b_aligned['low']
    ratio_df['close'] = df_a_aligned['close'] / df_b_aligned['close']
    ratio_df['volume'] = df_a_aligned['volume']  # Use volume from asset A

    return ratio_df.dropna()

def load_assets_from_config(config_path: str = "config/settings.yaml") -> List[str]:
    """Load trend assets from configuration file (same as background_service.py)."""
    try:
        config = load_config(config_path)
        settings = config.get('settings', {})
        
        # Use trend_assets if available, otherwise fall back to assets
        trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
        
        logging.info(f"Loaded {len(trend_assets)} trend assets from config")
        return trend_assets
        
    except Exception as e:
        logging.error(f"Error loading assets from config: {e}")
        # Fallback to default assets
        return ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']

def calculate_ratio_based_scores_with_details(
    data_dict: Dict[str, pd.DataFrame],
    pgo_length: int = 35,
    upper_threshold: float = 1.1,
    lower_threshold: float = -0.58,
    use_manual_inversion: bool = False
) -> Tuple[Dict[str, Tuple[int, List[str]]], pd.DataFrame]:
    """
    Calculate asset scores using ratio-based PGO approach with choice of calculation method.

    Args:
        data_dict: Dictionary of asset DataFrames
        pgo_length: PGO calculation length
        upper_threshold: PGO upper threshold
        lower_threshold: PGO lower threshold
        use_manual_inversion: If True, use manual inversion method (like visualizer).
                             If False, use independent calculation method.

    Returns:
        Tuple of (scores_with_details, comparison_matrix)
    """
    assets = list(data_dict.keys())
    method_name = "manual inversion" if use_manual_inversion else "independent calculation"
    logging.info(f"Calculating ratio-based PGO scores for {len(assets)} assets using {method_name} method")

    # Step 1: Calculate PGO signals for all asset pairs
    signals_dict = {}

    if use_manual_inversion:
        # Manual inversion: calculate once and invert for opposite direction (like visualizer)
        for i in range(len(assets)):
            for j in range(i+1, len(assets)):
                asset_a = assets[i]
                asset_b = assets[j]

                try:
                    # Create ratio OHLCV data for A/B
                    ratio_df_a_vs_b = create_ratio_ohlcv(data_dict[asset_a], data_dict[asset_b])

                    if ratio_df_a_vs_b.empty:
                        logging.warning(f"No ratio data available for {asset_a}/{asset_b}")
                        common_idx = data_dict[asset_a].index.intersection(data_dict[asset_b].index)
                        signals_dict[(asset_a, asset_b)] = pd.Series(0, index=common_idx)
                        signals_dict[(asset_b, asset_a)] = pd.Series(0, index=common_idx)
                        continue

                    # Calculate PGO signal for A/B
                    signal_a_vs_b = generate_pgo_signal(
                        df=ratio_df_a_vs_b,
                        length=pgo_length,
                        upper_threshold=upper_threshold,
                        lower_threshold=lower_threshold
                    )

                    # Convert signal to binary (1 for long, 0 for not long)
                    binary_signal_a_vs_b = (signal_a_vs_b == 1).astype(int)

                    # Store signals using manual inversion
                    signals_dict[(asset_a, asset_b)] = binary_signal_a_vs_b
                    signals_dict[(asset_b, asset_a)] = 1 - binary_signal_a_vs_b  # Manual inversion

                    logging.info(f"Calculated ratio PGO signal for {asset_a}/{asset_b} using manual inversion")

                except Exception as e:
                    logging.error(f"Error calculating ratio PGO for {asset_a}/{asset_b}: {e}")
                    # Create empty Series with 0 signals
                    common_idx = data_dict[asset_a].index.intersection(data_dict[asset_b].index)
                    signals_dict[(asset_a, asset_b)] = pd.Series(0, index=common_idx)
                    signals_dict[(asset_b, asset_a)] = pd.Series(0, index=common_idx)
    else:
        # Independent calculation: calculate both directions separately
        for i in range(len(assets)):
            for j in range(len(assets)):
                if i == j:  # Skip self-comparison
                    continue

                asset_a = assets[i]
                asset_b = assets[j]

                try:
                    # Create ratio OHLCV data for A/B
                    ratio_df_a_vs_b = create_ratio_ohlcv(data_dict[asset_a], data_dict[asset_b])

                    if ratio_df_a_vs_b.empty:
                        logging.warning(f"No ratio data available for {asset_a}/{asset_b}")
                        common_idx = data_dict[asset_a].index.intersection(data_dict[asset_b].index)
                        signals_dict[(asset_a, asset_b)] = pd.Series(0, index=common_idx)
                        continue

                    # Calculate PGO signal for A/B independently
                    signal_a_vs_b = generate_pgo_signal(
                        df=ratio_df_a_vs_b,
                        length=pgo_length,
                        upper_threshold=upper_threshold,
                        lower_threshold=lower_threshold
                    )

                    # Convert signal to binary (1 for long, 0 for not long)
                    binary_signal_a_vs_b = (signal_a_vs_b == 1).astype(int)

                    # Store signal for this specific direction only
                    signals_dict[(asset_a, asset_b)] = binary_signal_a_vs_b

                    logging.info(f"Calculated independent ratio PGO signal for {asset_a}/{asset_b}")

                except Exception as e:
                    logging.error(f"Error calculating ratio PGO for {asset_a}/{asset_b}: {e}")
                    # Create empty Series with 0 signals
                    common_idx = data_dict[asset_a].index.intersection(data_dict[asset_b].index)
                    signals_dict[(asset_a, asset_b)] = pd.Series(0, index=common_idx)

    # Step 2: Get the latest day's signals and create comparison matrix
    if signals_dict:
        # Get the latest common date
        first_key = list(signals_dict.keys())[0]
        latest_date = signals_dict[first_key].index[-1]

        # Create comparison matrix for the latest day
        comparison_matrix = pd.DataFrame(0.0, index=assets, columns=assets)
        np.fill_diagonal(comparison_matrix.values, np.nan)  # NaN on diagonal

        # Fill the matrix with latest signals
        for (asset_a, asset_b), signal_series in signals_dict.items():
            if latest_date in signal_series.index:
                comparison_matrix.loc[asset_a, asset_b] = signal_series.loc[latest_date]

        # Step 3: Calculate scores and beaten assets for each asset
        scores_with_details = {}
        for asset in assets:
            score = 0
            beaten_assets = []

            # Check each comparison for this asset
            for other_asset in assets:
                if asset != other_asset:
                    signal_value = comparison_matrix.loc[asset, other_asset]



                    if signal_value == 1.0:  # Asset beats other_asset
                        score += 1
                        beaten_assets.append(other_asset)

            scores_with_details[asset] = (score, beaten_assets)
            logging.info(f"{asset}: Score {score}, Beat: {beaten_assets}")

        return scores_with_details, comparison_matrix

    else:
        # No signals calculated
        empty_scores = {asset: (0, []) for asset in assets}
        empty_matrix = pd.DataFrame(0.0, index=assets, columns=assets)
        return empty_scores, empty_matrix

def print_scoring_table(scores_with_details: Dict[str, Tuple[int, List[str]]], comparison_matrix: pd.DataFrame, method_name: str = "Unknown"):
    """Print the asset scoring table with debugging information."""

    # Sort assets by score (descending)
    sorted_assets = sorted(scores_with_details.items(), key=lambda x: x[1][0], reverse=True)

    print(f"\n" + "="*120)
    print(f"RATIO-BASED PGO ASSET SCORING DEBUG TABLE ({method_name} Method)")
    print("="*120)
    print(f"{'Rank':<4} {'Asset':<12} {'Score':<5} {'Assets Beaten'}")
    print("-"*120)

    for rank, (asset, (score, beaten_assets)) in enumerate(sorted_assets, 1):
        # Format beaten assets list
        if beaten_assets:
            beaten_text = f"({', '.join(beaten_assets)})"
        else:
            beaten_text = "(none)"

        # Add checkmark for assets with scores > 0
        indicator = "✅" if score > 0 else "  "

        print(f"{indicator}{rank:<3} {asset:<12} {score:<5} {beaten_text}")

    print("-"*120)

    # Summary statistics
    total_assets = len(scores_with_details)
    assets_with_scores = sum(1 for score, _ in scores_with_details.values() if score > 0)
    max_score = max((score for score, _ in scores_with_details.values()), default=0)

    print(f"SUMMARY:")
    print(f"Total assets: {total_assets}")
    print(f"Assets with scores > 0: {assets_with_scores}")
    print(f"Maximum score achieved: {max_score}")
    print(f"Maximum possible score: {total_assets - 1}")
    print("="*120)

    # Print comparison matrix
    print(f"\nCOMPARISON MATRIX (1 = Row asset beats Column asset via ratio PGO):")
    print("="*120)
    # Handle NaN values in the matrix before converting to int
    matrix_display = comparison_matrix.fillna(-1).round(0).astype(int)
    matrix_display = matrix_display.replace(-1, '-')  # Replace NaN placeholders with dashes
    print(matrix_display)
    print("="*120)

def main():
    """Main function to run the asset scoring debugger."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Asset Scoring Debugger - Compare ratio calculation methods')
    parser.add_argument('--start-date', type=str, default='2025-02-10',
                        help='Start date for data fetching (YYYY-MM-DD format, default: 2025-02-10)')

    # Method selection arguments (mutually exclusive)
    method_group = parser.add_mutually_exclusive_group()
    method_group.add_argument('--manual-inversion', action='store_true',
                             help='Use manual inversion method (like universal visualizer)')
    method_group.add_argument('--independent', action='store_true',
                             help='Use independent calculation method (calculate both directions separately)')

    args = parser.parse_args()

    # Determine method (default to independent if neither specified)
    use_manual_inversion = args.manual_inversion
    if not args.manual_inversion and not args.independent:
        # Default to independent (current behavior)
        use_manual_inversion = False

    method_name = "Manual Inversion" if use_manual_inversion else "Independent Calculation"

    print("Asset Scoring Debugger")
    print("=" * 50)
    print(f"Start date: {args.start_date}")
    print(f"Calculation method: {method_name}")
    
    # Load configuration and assets
    config = load_config()
    settings = config.get('settings', {})
    
    # Get PGO parameters from config
    pgo_length = settings.get('pgo_length', 35)
    upper_threshold = settings.get('pgo_upper_threshold', 1.1)
    lower_threshold = settings.get('pgo_lower_threshold', -0.58)
    
    print(f"PGO Parameters: length={pgo_length}, upper={upper_threshold}, lower={lower_threshold}")
    
    # Load assets (same as background_service.py)
    assets = load_assets_from_config()
    print(f"Analyzing {len(assets)} assets: {', '.join(assets)}")
    
    # Fetch data for all assets
    print("\nFetching latest data...")
    exchange_id = config.get('exchange', 'binance')
    timeframe = settings.get('timeframe', '1d')
    
    # Calculate warmup period (same as background service)
    analysis_start_date = args.start_date
    warmup_days = 120  # Standard warmup period for stable indicator operation

    # Calculate the actual data fetching start date (120 days before analysis date)
    analysis_dt = datetime.strptime(analysis_start_date, '%Y-%m-%d')
    data_start_dt = analysis_dt - timedelta(days=warmup_days)
    data_start_date = data_start_dt.strftime('%Y-%m-%d')

    print(f"Analysis date: {analysis_start_date}")
    print(f"Data fetching start date: {data_start_date} (120-day warmup period)")
    print(f"Total data period: {warmup_days} days for indicator stability")

    data_dict = fetch_ohlcv_data(
        exchange_id=exchange_id,
        symbols=assets,
        timeframe=timeframe,
        since=data_start_date,  # Fetch from warmup start date
        use_cache=True
    )
    
    if not data_dict:
        print("ERROR: Failed to fetch data")
        return
    
    print(f"Successfully fetched data for {len(data_dict)} assets")

    # Calculate ratio-based PGO scores using selected method
    print(f"\nCalculating ratio-based PGO scores using {method_name} method...")
    scores_with_details, comparison_matrix = calculate_ratio_based_scores_with_details(
        data_dict=data_dict,
        pgo_length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        use_manual_inversion=use_manual_inversion
    )

    # Print the debugging table
    print_scoring_table(scores_with_details, comparison_matrix, method_name)
    
    print(f"\nAnalysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
