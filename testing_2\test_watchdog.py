#!/usr/bin/env python
"""
Test script for the network watchdog.
This script demonstrates how the network watchdog detects and responds to network interruptions.
"""

import os
import sys
import time
import logging
import threading
from datetime import datetime, timedelta

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"watchdog_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

# Import the network watchdog
from src.network_watchdog import NetworkWatchdog

# Mock state to save
test_state = {
    'last_execution_time': datetime.now().isoformat(),
    'test_value': 42,
    'test_string': 'Hello Watchdog'
}

# Directory for saving state
state_dir = 'data/state'
os.makedirs(state_dir, exist_ok=True)

# Mock functions for the watchdog callbacks
def network_recovery_callback(downtime_seconds):
    """Called when network connectivity is restored."""
    logging.info(f"RECOVERY CALLBACK: Network recovered after {downtime_seconds:.1f} seconds")
    print(f"\n[RECOVERY] Network connection restored after {downtime_seconds:.1f} seconds downtime")
    print("[RECOVERY] Checking for missed operations...")
    
    # Simulate checking for missed operations
    time.sleep(1)
    print("[RECOVERY] No missed operations found")
    
    return True

def save_state_callback():
    """Called when network issues are detected to save state."""
    logging.info("SAVE STATE CALLBACK: Saving state due to network issues")
    print("\n[SAVE STATE] Network issues detected, saving current state...")
    
    # Simulate saving state
    state_file = os.path.join(state_dir, "watchdog_test_state.json")
    
    # Update timestamp
    test_state['timestamp'] = datetime.now().isoformat()
    
    # Save to file (simple version for testing)
    import json
    with open(state_file, 'w') as f:
        json.dump(test_state, f, indent=2)
    
    print(f"[SAVE STATE] State saved to {state_file}")
    return True

# Simulate a scheduled task
def scheduled_task():
    """Simulate a scheduled task like strategy execution."""
    current_time = datetime.now()
    print(f"\n[TASK] Executing scheduled task at {current_time}")
    
    # Check if we can access the internet
    import requests
    try:
        response = requests.get("https://www.google.com", timeout=5)
        print(f"[TASK] Network check: Connected (status code: {response.status_code})")
    except Exception as e:
        print(f"[TASK] Network check: Disconnected (error: {e})")
        return
    
    # Simulate task execution
    print("[TASK] Processing data...")
    time.sleep(2)
    print("[TASK] Calculating signals...")
    time.sleep(1)
    print("[TASK] Task completed successfully")

# Function to run scheduled tasks
def run_scheduler(watchdog):
    """Run scheduled tasks if network is connected."""
    while True:
        # Only run if network is connected
        if watchdog.is_connected:
            scheduled_task()
        else:
            print("\n[SCHEDULER] Network is down, skipping scheduled task")
        
        # Wait for next execution
        time.sleep(30)  # Run every 30 seconds for testing

def main():
    """Main function to test the network watchdog."""
    print("\n" + "=" * 80)
    print(f"NETWORK WATCHDOG TEST - {datetime.now()}")
    print("=" * 80)
    print(f"Log file: {log_file}")
    print("=" * 80 + "\n")
    
    # Create the network watchdog with a short check interval for testing
    watchdog = NetworkWatchdog(
        check_interval=5,  # Check every 5 seconds for testing
        recovery_callback=network_recovery_callback,
        state_save_callback=save_state_callback,
        max_failures=2  # Consider network down after 2 consecutive failures for faster testing
    )
    
    # Add a critical time window for the next 10 minutes
    now = datetime.now()
    watchdog.add_critical_time(
        hour=now.hour,
        minute=now.minute,
        duration_minutes=10,
        description="Test critical window"
    )
    
    print("Network watchdog initialized with 5-second check interval")
    print("Added critical time window for the next 10 minutes")
    
    # Start the watchdog
    watchdog.start()
    print("Network watchdog started")
    
    # Start the scheduler in a separate thread
    scheduler_thread = threading.Thread(target=run_scheduler, args=(watchdog,))
    scheduler_thread.daemon = True
    scheduler_thread.start()
    
    try:
        print("\nTest is running. You can now simulate network interruptions.")
        print("Instructions:")
        print("1. Disconnect your WiFi or unplug your network cable")
        print("2. Wait for the watchdog to detect the disconnection (about 10-15 seconds)")
        print("3. Observe the state saving action")
        print("4. Reconnect your network")
        print("5. Observe the recovery action")
        print("\nPress Ctrl+C to stop the test\n")
        
        # Keep the main thread alive
        while True:
            # Print current status every 10 seconds
            status = watchdog.get_connection_status()
            print(f"\nCurrent status: {'Connected' if status['is_connected'] else 'Disconnected'}")
            print(f"In critical window: {status['in_critical_window']}")
            print(f"Consecutive failures: {status['consecutive_failures']}")
            
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    finally:
        # Stop the watchdog
        watchdog.stop()
        print("Network watchdog stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
