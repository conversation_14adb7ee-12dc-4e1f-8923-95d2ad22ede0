# src/utils.py
# Utility functions (logging, date handling, etc.)

import logging
import sys
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Tuple, Any
import os
from datetime import datetime

def setup_logging(level=logging.INFO):
    """
    Configures basic logging for the application.

    Args:
        level: The logging level to use (e.g., logging.INFO, logging.WARNING)
    """
    # Configure root logger
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)] # Log to console
    )

    # Set specific loggers to higher levels to reduce noise
    logging.getLogger('src.scoring').setLevel(logging.WARNING)
    logging.getLogger('src.strategy').setLevel(logging.WARNING)

    # Create a filter to suppress specific repetitive messages
    class MessageFilter(logging.Filter):
        def filter(self, record):
            # Filter out repetitive messages about finding best asset and MTPI checks
            if "Finding best asset for scores" in record.getMessage():
                return False
            if "Maximum score found" in record.getMessage():
                return False
            if "Assets with the maximum score" in record.getMessage():
                return False
            if "Selected best asset" in record.getMessage():
                return False
            if "MTPI CHECK DAY" in record.getMessage():
                return False
            return True

    # Add the filter to the root logger
    for handler in logging.getLogger().handlers:
        handler.addFilter(MessageFilter())

    logging.info("Logging configured with custom filters to reduce output.")

def plot_debug_asset_scores(
    daily_scores: pd.DataFrame,
    best_asset_series: pd.Series,
    output_path: str = None
) -> None:
    """
    Creates a visualization of daily scores for each asset and highlights which asset was selected.
    This is useful for debugging discrepancies between PineScript and Python implementations.

    Args:
        daily_scores: DataFrame containing daily scores with datetime index and assets as columns
        best_asset_series: Series containing the best asset selected for each day
        output_path: Path to save the plot, if None the plot is displayed
    """
    if daily_scores.empty or best_asset_series.empty:
        logging.warning("No data to plot for debugging scores")
        return

    # Ensure the indices match
    common_idx = daily_scores.index.intersection(best_asset_series.index)
    if len(common_idx) == 0:
        logging.warning("No common timestamps between scores and best asset series")
        return

    daily_scores = daily_scores.loc[common_idx]
    best_asset_series = best_asset_series.loc[common_idx]

    # Set up plot
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})

    # Plot 1: Asset Scores over time
    assets = daily_scores.columns
    for asset in assets:
        ax1.plot(daily_scores.index, daily_scores[asset], label=asset, alpha=0.7)

    ax1.set_title('Daily Asset Scores')
    ax1.set_ylabel('Score')
    ax1.grid(True, linestyle='--', alpha=0.7)
    ax1.legend(loc='upper left')

    # Plot 2: Selected Asset (color-coded)
    asset_colors = {
        'BTC/USDT': 'aqua',
        'ETH/USDT': 'red',
        'SOL/USDT': 'green',
        'LINK/USDT': 'purple',
        'SUI/USDT': 'orange',
        'DOGE/USDT': 'yellow',
        'MANA/USDT': 'lime',
        'USD': 'white'
    }

    # Create a colormap for assets
    unique_assets = best_asset_series.unique()
    colors = []

    for date in best_asset_series.index:
        asset = best_asset_series.loc[date]
        colors.append(asset_colors.get(asset, 'gray'))

    # Plot selected asset as scatter points
    scatter = ax2.scatter(
        best_asset_series.index,
        np.ones(len(best_asset_series)),
        c=colors,
        s=100,
        marker='s'
    )

    # Create a custom legend for the selected assets
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], marker='s', color='w', markerfacecolor=asset_colors.get(asset, 'gray'),
               markersize=10, label=asset) for asset in unique_assets
    ]
    ax2.legend(handles=legend_elements, loc='upper left', title='Selected Asset')

    # Format the second plot
    ax2.set_title('Selected Best Asset')
    ax2.set_yticks([])  # Hide y-axis
    ax2.grid(True, linestyle='--', alpha=0.7, axis='x')

    # Add recent data table at the bottom
    last_n_days = min(10, len(daily_scores))
    recent_data = daily_scores.iloc[-last_n_days:].copy()
    recent_data['Selected'] = best_asset_series.iloc[-last_n_days:]

    # Format the table
    cell_text = []
    for i in range(last_n_days):
        row = [recent_data.index[i].strftime('%Y-%m-%d')]
        row.extend([str(recent_data.iloc[i, j]) for j in range(len(assets))])
        row.append(recent_data['Selected'].iloc[i])
        cell_text.append(row)

    col_labels = ['Date'] + list(assets) + ['Selected']

    # Adjust the figure to make room for the table
    plt.subplots_adjust(bottom=0.2)
    table = plt.table(
        cellText=cell_text,
        colLabels=col_labels,
        loc='bottom',
        bbox=[0, -0.5, 1, 0.3]  # [left, bottom, width, height]
    )
    table.auto_set_font_size(False)
    table.set_fontsize(8)
    table.scale(1, 1.5)

    # Adjust layout
    plt.tight_layout(rect=[0, 0.2, 1, 0.95])  # Adjust the rect to leave room for the table

    # Save or display
    if output_path:
        plt.savefig(output_path)
        logging.info(f"Saved debug plot to {output_path}")
    else:
        plt.show()

    plt.close(fig)

def save_consolidated_matrices_html(
    matrices_dict: Dict[str, Dict[str, Any]],
    output_dir: str = "debug_matrices",
    timeframe: str = "1h",
    n_assets: int = 3
) -> str:
    """
    Saves all asset comparison matrices to a single consolidated HTML file.
    Does not generate individual CSV or HTML files for each day.
    Highlights the top N assets in each matrix.

    Args:
        matrices_dict: Dictionary with format {timestamp: matrix_data}
                     where matrix_data includes 'signals', 'scores', 'matrix', etc.
        output_dir: Directory where the HTML file will be saved
        timeframe: The timeframe used for data analysis (e.g., '1h', '4h', '1d')
        n_assets: Number of top assets to highlight (default: 3)

    Returns:
        Path to the output directory
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logging.info(f"Created debug matrices directory: {output_dir}")

    if not matrices_dict:
        logging.warning("No matrices data provided. Nothing to save.")
        return output_dir

    logging.info(f"Preparing to save {len(matrices_dict)} matrices to HTML")
    # Log the keys to help with debugging
    matrix_dates = sorted([str(k) for k in matrices_dict.keys()])
    logging.info(f"Matrix dates: {matrix_dates[:5]}... (showing first 5 only)")

    # Check if any matrices have the required data
    valid_matrices = 0
    for timestamp, data in matrices_dict.items():
        if 'matrix' in data and 'assets' in data and 'scores' in data:
            valid_matrices += 1
    logging.info(f"Found {valid_matrices} valid matrices with 'matrix', 'assets', and 'scores' data")

    # Create a combined HTML file with all matrices
    html_consolidated_filepath = os.path.join(output_dir, "all_matrices_consolidated.html")
    html_content = f"""
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            table {{
                border-collapse: collapse;
                font-family: Arial, sans-serif;
                width: 100%;
                margin-bottom: 30px;
            }}
            th, td {{
                border: 1px solid #dddddd;
                text-align: center;
                padding: 8px 10px;
            }}
            th {{
                background-color: #f2f2f2;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .score-col {{
                background-color: #e6f7ff;
                font-weight: bold;
            }}
            .one-value {{
                color: green;
                font-weight: bold;
            }}
            .zero-value {{
                color: red;
            }}
            .top-asset-row {{
                background-color: #ffffcc; /* Light yellow background */
                font-weight: bold;
            }}
            .top-asset-score {{
                background-color: #ffcc00; /* Darker yellow for score */
                font-weight: bold;
            }}
            .date-header {{
                font-size: 18px;
                margin: 20px 0 5px 0;
                text-align: center;
                font-weight: bold;
                background-color: #f0f0f0;
                padding: 8px;
                border-radius: 3px;
            }}
            .timeframe-info {{
                text-align: center;
                font-style: italic;
                margin-bottom: 20px;
                color: #666;
            }}
        </style>
    </head>
    <body>
        <h1 style="font-size: 24px;">All Comparison Matrices</h1>
        <div class="timeframe-info">Data Timeframe: {timeframe}</div>
    """

    # Add each matrix
    for timestamp, matrix_data in sorted(matrices_dict.items()):
        if isinstance(timestamp, pd.Timestamp):
            date_str = timestamp.strftime('%Y-%m-%d')
        else:
            date_str = str(timestamp).replace(':', '-').replace(' ', '_')

        if 'assets' in matrix_data and 'matrix' in matrix_data and 'scores' in matrix_data:
            assets = matrix_data['assets']
            matrix = matrix_data['matrix']
            scores = matrix_data.get('scores', {})

            # Filter out USD if present
            assets = [asset for asset in assets if asset != 'USD']

            # Skip if no assets left after filtering
            if not assets:
                logging.warning(f"No assets left after filtering for date {date_str}. Skipping.")
                continue

            # Check if matrix is valid
            if matrix.empty:
                logging.warning(f"Empty matrix for date {date_str}. Skipping.")
                continue

            # Make sure all assets are in the matrix
            # For newer assets, it's expected that they won't be in earlier matrices
            # So we'll just use the assets that are actually in the matrix
            matrix_assets = [asset for asset in assets if asset in matrix.index and asset in matrix.columns]
            if not matrix_assets:
                logging.warning(f"No valid assets in matrix for date {date_str}. Skipping.")
                continue

            # Use the filtered list of assets that exist in this matrix
            assets = matrix_assets

            # Add date header
            html_content += f'<div class="date-header">Matrix for {date_str}</div>\n'
            html_content += '<table>\n<tr><th>Asset</th>\n'

            # Add column headers (assets)
            for asset in assets:
                html_content += f'<th>{asset}</th>\n'

            html_content += '<th class="score-col">Score</th>\n</tr>\n'

            # Find the top N assets based on scores
            sorted_assets = sorted([(asset, scores.get(asset, 0)) for asset in assets],
                                  key=lambda x: x[1], reverse=True)
            top_n_assets = [asset for asset, _ in sorted_assets[:n_assets]]

            # Add rows
            for asset_a in assets:
                # Check if this asset is in the top N
                is_top_asset = asset_a in top_n_assets

                # Apply special styling for top N assets
                if is_top_asset:
                    html_content += f'<tr class="top-asset-row"><td><strong>{asset_a}</strong></td>\n'
                else:
                    html_content += f'<tr><td><strong>{asset_a}</strong></td>\n'

                for asset_b in assets:
                    if asset_a == asset_b:
                        html_content += f'<td>-</td>\n'
                    else:
                        # Get the value directly from the matrix
                        value = matrix.loc[asset_a, asset_b]
                        # Convert to int for display
                        value = int(value) if not pd.isna(value) else 0
                        if value == 1:
                            html_content += f'<td class="one-value">1</td>\n'
                        else:
                            html_content += f'<td class="zero-value">0</td>\n'

                # Add score column with special styling for top N assets
                score = scores.get(asset_a, 0)
                if is_top_asset:
                    html_content += f'<td class="top-asset-score">{score}</td>\n</tr>\n'
                else:
                    html_content += f'<td class="score-col">{score}</td>\n</tr>\n'

            html_content += '</table>\n'

    html_content += """
    </body>
    </html>
    """

    # Check if we have any content to write
    if '<table>' not in html_content:
        logging.error("No tables were generated in the HTML content. Check if matrices_dict contains valid data.")
        # Write a minimal HTML file with an error message
        html_content = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .error {{ color: red; font-weight: bold; }}
            </style>
        </head>
        <body>
            <h1>Error: No Valid Matrices Found</h1>
            <p class="error">No valid matrices were found for timeframe: {timeframe}</p>
            <p>Please check the logs for more information.</p>
        </body>
        </html>
        """

    try:
        with open(html_consolidated_filepath, 'w') as f:
            f.write(html_content)

        # Get file size to verify it was written correctly
        file_size = os.path.getsize(html_consolidated_filepath)
        logging.info(f"Saved consolidated HTML matrices to {html_consolidated_filepath} (size: {file_size} bytes)")
    except Exception as e:
        logging.error(f"Error writing HTML file: {e}")
        return output_dir

    return output_dir

def save_backtest_chart(
    fig,
    filename: str = None,
    use_mtpi: bool = False,
    mtpi_timeframe: str = None,
    chart_timeframe: str = None,
    output_dir: str = "Backtest_results",
    n_assets: int = 1,
    transaction_fee_rate: float = 0.001
) -> str:
    """
    Saves a matplotlib figure to the Backtest_results directory with a descriptive filename.

    Args:
        fig: The matplotlib figure to save
        filename: Base filename (if None, will generate one)
        use_mtpi: Whether MTPI was applied in the strategy
        mtpi_timeframe: Timeframe of MTPI signals if applied (e.g., '1d', '4h')
        chart_timeframe: Timeframe of the chart data (e.g., '1h', '4h', '1d')
        output_dir: Directory where to save the output
        n_assets: Number of assets used in the strategy (default: 1)
        transaction_fee_rate: Transaction fee rate used in the strategy (default: 0.001)

    Returns:
        Path to the saved file
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logging.info(f"Created directory for backtest results: {output_dir}")

    # Generate filename if not provided
    if not filename:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")

        # MTPI information
        mtpi_str = f"mtpi_{mtpi_timeframe}_" if use_mtpi and mtpi_timeframe else "no_mtpi_"

        # Timeframe information
        chart_tf_str = f"chart_{chart_timeframe}_" if chart_timeframe else ""

        # Number of assets and strategy type (top_1 or top_n)
        assets_str = f"top_{n_assets}_" if n_assets > 0 else ""

        # Transaction fee information
        fee_str = f"fee_{int(transaction_fee_rate * 10000)}_" if transaction_fee_rate > 0 else "no_fee_"

        # Combine all components into the filename
        filename = f"backtest_{mtpi_str}{chart_tf_str}{assets_str}{fee_str}{current_time}.png"

    # Ensure .png extension
    if not filename.lower().endswith('.png'):
        filename += '.png'

    output_path = os.path.join(output_dir, filename)

    # Save the figure
    try:
        fig.savefig(output_path, dpi=300, bbox_inches='tight')
        logging.info(f"Saved backtest chart to {output_path}")
        return output_path
    except Exception as e:
        logging.error(f"Error saving backtest chart: {e}")
        return None

if __name__ == '__main__':
    setup_logging(logging.DEBUG)
    logging.debug("This is a debug message.")
    logging.info("This is an info message.")
    logging.warning("This is a warning message.")
    logging.error("This is an error message.")
    # print(f"Formatted symbol: {format_symbol_for_filename('BTC/USDT')}")
    pass