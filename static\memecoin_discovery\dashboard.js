// Memecoin Discovery Dashboard JavaScript

let currentTokens = [];
let currentTokenSymbol = null;

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadSystemStatus();
    loadDiscoverySummary();
    loadTokens();
    
    // Auto-refresh every 5 minutes
    setInterval(refreshData, 5 * 60 * 1000);
});

// Load system status
async function loadSystemStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        const statusContainer = document.getElementById('systemStatus');
        
        if (data.status === 'ok') {
            statusContainer.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <h6>Discovery System</h6>
                        <span class="status-indicator ${data.discovery_available ? 'status-ok' : 'status-error'}"></span>
                        ${data.discovery_available ? 'Available' : 'Unavailable'}
                    </div>
                    <div class="col-md-3">
                        <h6>OpenAI API</h6>
                        <span class="status-indicator ${data.api_keys.openai ? 'status-ok' : 'status-error'}"></span>
                        ${data.api_keys.openai ? 'Connected' : 'Not configured'}
                    </div>
                    <div class="col-md-3">
                        <h6>Twitter API</h6>
                        <span class="status-indicator ${data.api_keys.twitter ? 'status-ok' : 'status-error'}"></span>
                        ${data.api_keys.twitter ? 'Connected' : 'Not configured'}
                    </div>
                    <div class="col-md-3">
                        <h6>Database</h6>
                        <span class="status-indicator ${data.database ? 'status-ok' : 'status-error'}"></span>
                        ${data.database ? 'Connected' : 'Error'}
                    </div>
                </div>
            `;
        } else {
            statusContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    System Error: ${data.error || 'Unknown error'}
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading system status:', error);
        document.getElementById('systemStatus').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Failed to load system status
            </div>
        `;
    }
}

// Load discovery summary
async function loadDiscoverySummary() {
    try {
        const response = await fetch('/api/summary');
        const data = await response.json();
        
        document.getElementById('totalDiscovered').textContent = data.total_discovered || 0;
        document.getElementById('approvedTokens').textContent = data.approved_tokens || 0;
        document.getElementById('averageScore').textContent = data.average_score ? data.average_score.toFixed(1) : '0.0';
        document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        
    } catch (error) {
        console.error('Error loading discovery summary:', error);
    }
}

// Load tokens
async function loadTokens() {
    const statusFilter = document.getElementById('statusFilter').value;
    const daysFilter = document.getElementById('daysFilter').value;
    
    // Show loading spinner
    document.getElementById('tokensLoading').style.display = 'block';
    document.getElementById('tokensContainer').style.display = 'none';
    
    try {
        const params = new URLSearchParams({
            days_back: daysFilter,
            limit: 100
        });
        
        if (statusFilter) {
            params.append('status', statusFilter);
        }
        
        const response = await fetch(`/api/tokens?${params}`);
        const data = await response.json();
        
        currentTokens = data.tokens || [];
        displayTokens(currentTokens);
        
    } catch (error) {
        console.error('Error loading tokens:', error);
        document.getElementById('tokensTable').innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading tokens
                </td>
            </tr>
        `;
    } finally {
        // Hide loading spinner
        document.getElementById('tokensLoading').style.display = 'none';
        document.getElementById('tokensContainer').style.display = 'block';
    }
}

// Display tokens in table
function displayTokens(tokens) {
    const tableBody = document.getElementById('tokensTable');
    
    if (tokens.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">
                    <i class="fas fa-search me-2"></i>
                    No tokens found
                </td>
            </tr>
        `;
        return;
    }
    
    tableBody.innerHTML = tokens.map(token => {
        const discoveryDate = token.discovery_date ? 
            new Date(token.discovery_date).toLocaleDateString() : 'Unknown';
        
        const scoreClass = getScoreClass(token.final_score);
        const statusBadge = getStatusBadge(token.status);
        
        return `
            <tr class="token-row" onclick="showTokenDetail('${token.symbol}')">
                <td>
                    <strong>${token.symbol}</strong>
                    ${token.name && token.name !== token.symbol ? `<br><small class="text-muted">${token.name}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-secondary">${token.network || 'Unknown'}</span>
                </td>
                <td>
                    <span class="score-badge ${scoreClass}">${token.final_score}</span>
                </td>
                <td>${token.ai_score}</td>
                <td>${token.sentiment_score}</td>
                <td>${token.risk_score}</td>
                <td>${token.twitter_mentions || 0}</td>
                <td>${discoveryDate}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); showTokenDetail('${token.symbol}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${token.status === 'discovered' || token.status === 'analyzing' ? `
                            <button class="btn btn-outline-success btn-sm" onclick="event.stopPropagation(); quickUpdateStatus('${token.symbol}', 'approved')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); quickUpdateStatus('${token.symbol}', 'rejected')">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get score class for styling
function getScoreClass(score) {
    if (score >= 70) return 'score-high';
    if (score >= 50) return 'score-medium';
    return 'score-low';
}

// Get status badge
function getStatusBadge(status) {
    const badges = {
        'discovered': '<span class="badge bg-info">Discovered</span>',
        'analyzing': '<span class="badge bg-warning">Analyzing</span>',
        'approved': '<span class="badge bg-success">Approved</span>',
        'rejected': '<span class="badge bg-danger">Rejected</span>',
        'monitoring': '<span class="badge bg-primary">Monitoring</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

// Show token detail modal
async function showTokenDetail(symbol) {
    currentTokenSymbol = symbol;
    
    try {
        const response = await fetch(`/api/tokens/${symbol}`);
        const token = await response.json();
        
        document.getElementById('tokenModalTitle').textContent = `${token.symbol} - Token Details`;
        
        const modalBody = document.getElementById('tokenModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Basic Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Symbol:</strong></td><td>${token.symbol}</td></tr>
                        <tr><td><strong>Name:</strong></td><td>${token.name || 'Unknown'}</td></tr>
                        <tr><td><strong>Network:</strong></td><td>${token.network || 'Unknown'}</td></tr>
                        <tr><td><strong>Contract:</strong></td><td>
                            ${token.contract_address ? 
                                `<code class="small">${token.contract_address}</code>` : 
                                'Unknown'}
                        </td></tr>
                        <tr><td><strong>Discovery Date:</strong></td><td>
                            ${token.discovery_date ? 
                                new Date(token.discovery_date).toLocaleString() : 
                                'Unknown'}
                        </td></tr>
                        <tr><td><strong>Source:</strong></td><td>${token.source || 'Unknown'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Scores & Metrics</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Final Score:</strong></td><td>
                            <span class="score-badge ${getScoreClass(token.final_score)}">${token.final_score}</span>
                        </td></tr>
                        <tr><td><strong>AI Score:</strong></td><td>${token.ai_score}</td></tr>
                        <tr><td><strong>Sentiment Score:</strong></td><td>${token.sentiment_score}</td></tr>
                        <tr><td><strong>Risk Score:</strong></td><td>${token.risk_score}</td></tr>
                        <tr><td><strong>Twitter Mentions:</strong></td><td>${token.twitter_mentions || 0}</td></tr>
                        <tr><td><strong>Status:</strong></td><td>${getStatusBadge(token.status)}</td></tr>
                    </table>
                </div>
            </div>
            
            ${token.metadata ? `
                <div class="mt-3">
                    <h6>Additional Information</h6>
                    <pre class="bg-light p-2 small">${JSON.stringify(token.metadata, null, 2)}</pre>
                </div>
            ` : ''}
        `;
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('tokenModal'));
        modal.show();
        
    } catch (error) {
        console.error('Error loading token detail:', error);
        alert('Error loading token details');
    }
}

// Update token status
async function updateTokenStatus(status) {
    if (!currentTokenSymbol) return;
    
    try {
        const response = await fetch(`/api/tokens/${currentTokenSymbol}/update_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: status })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('tokenModal'));
            modal.hide();
            
            // Refresh tokens
            loadTokens();
            
            // Show success message
            showAlert('success', `Token ${currentTokenSymbol} ${status} successfully!`);
        } else {
            showAlert('danger', `Error updating token status: ${result.error}`);
        }
        
    } catch (error) {
        console.error('Error updating token status:', error);
        showAlert('danger', 'Error updating token status');
    }
}

// Quick update status (from table buttons)
async function quickUpdateStatus(symbol, status) {
    currentTokenSymbol = symbol;
    await updateTokenStatus(status);
}

// Run discovery
async function runDiscovery() {
    // Show progress modal
    const modal = new bootstrap.Modal(document.getElementById('discoveryModal'));
    modal.show();
    
    try {
        const response = await fetch('/api/discover', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        // Hide progress modal
        modal.hide();
        
        if (result.error) {
            showAlert('danger', `Discovery failed: ${result.error}`);
        } else {
            showAlert('success', `Discovery completed! Found ${result.tokens_discovered || 0} tokens, analyzed ${result.tokens_analyzed || 0}`);
            
            // Refresh data
            refreshData();
        }
        
    } catch (error) {
        console.error('Error running discovery:', error);
        modal.hide();
        showAlert('danger', 'Error running discovery');
    }
}

// Refresh all data
function refreshData() {
    const refreshIcon = document.getElementById('refreshIcon');
    refreshIcon.classList.add('spinning');
    
    Promise.all([
        loadSystemStatus(),
        loadDiscoverySummary(),
        loadTokens()
    ]).finally(() => {
        setTimeout(() => {
            refreshIcon.classList.remove('spinning');
        }, 300);
    });
}

// Sort tokens
function sortTokens() {
    const sortBy = document.getElementById('sortBy').value;
    
    const sortedTokens = [...currentTokens].sort((a, b) => {
        switch (sortBy) {
            case 'score':
                return (b.final_score || 0) - (a.final_score || 0);
            case 'date':
                return new Date(b.discovery_date || 0) - new Date(a.discovery_date || 0);
            case 'mentions':
                return (b.twitter_mentions || 0) - (a.twitter_mentions || 0);
            case 'sentiment':
                return (b.sentiment_score || 0) - (a.sentiment_score || 0);
            default:
                return 0;
        }
    });
    
    displayTokens(sortedTokens);
}

// Filter tokens by search
function filterTokens() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    
    if (!searchTerm) {
        displayTokens(currentTokens);
        return;
    }
    
    const filteredTokens = currentTokens.filter(token => 
        token.symbol.toLowerCase().includes(searchTerm) ||
        (token.name && token.name.toLowerCase().includes(searchTerm)) ||
        (token.network && token.network.toLowerCase().includes(searchTerm))
    );
    
    displayTokens(filteredTokens);
}

// Export data
async function exportData() {
    try {
        const statusFilter = document.getElementById('statusFilter').value;
        const daysFilter = document.getElementById('daysFilter').value;
        
        const params = new URLSearchParams({
            days_back: daysFilter
        });
        
        if (statusFilter) {
            params.append('status', statusFilter);
        }
        
        const response = await fetch(`/api/export?${params}`);
        const data = await response.json();
        
        // Create and download file
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `memecoin_discovery_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showAlert('success', 'Data exported successfully!');
        
    } catch (error) {
        console.error('Error exporting data:', error);
        showAlert('danger', 'Error exporting data');
    }
}

// Show alert message
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
