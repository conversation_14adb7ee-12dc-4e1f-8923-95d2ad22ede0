#!/usr/bin/env python3
"""
Complete median score signal changes for TradingView comparison.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_score
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config

def complete_median_signals():
    """Print complete median score signal changes."""
    print("=" * 80)
    print("COMPLETE MEDIAN SCORE SIGNAL CHANGES")
    print("Parameters: subject1=12, mul1=1.45, slen=27, src_me=high")
    print("=" * 80)
    
    # Load config and data
    config = load_mtpi_multi_indicator_config()
    median_config = config.get('median_score', {})
    
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Data: {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Calculate signals
    signal, supertrend_line, direction = calculate_median_score(
        btc_df,
        atr_period=median_config.get('atr_period', 12),
        multiplier=median_config.get('multiplier', 1.45),
        median_length=median_config.get('median_length', 27),
        src_col=median_config.get('src_col', 'high')
    )
    
    # Find signal changes
    signal_changes = signal != signal.shift(1)
    change_indices = signal_changes[signal_changes].index
    
    print(f"\nFOUND {len(change_indices)} SIGNAL CHANGES:")
    print("=" * 80)
    
    # Print all signal changes with complete data
    for i, change_date in enumerate(change_indices):
        idx = btc_df.index.get_loc(change_date)
        
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            
            # Price data
            close_price = btc_df['close'].iloc[idx]
            high_price = btc_df['high'].iloc[idx]
            low_price = btc_df['low'].iloc[idx]
            
            # Technical data
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            supertrend_val = supertrend_line.iloc[idx]
            
            # Calculate price vs supertrend
            price_vs_st = ((close_price - supertrend_val) / supertrend_val) * 100
            
            signal_type = "LONG" if curr_signal == 1 else "SHORT" if curr_signal == -1 else "NEUTRAL"
            
            print(f"\n{i+1:2d}. {change_date.strftime('%Y-%m-%d')} ({change_date.strftime('%A')})")
            print(f"    Signal: {prev_signal} → {curr_signal} ({signal_type})")
            print(f"    Direction: {prev_direction} → {direction_val}")
            print(f"    Close: ${close_price:,.2f}")
            print(f"    High:  ${high_price:,.2f}")
            print(f"    Low:   ${low_price:,.2f}")
            print(f"    Supertrend: ${supertrend_val:,.2f}")
            print(f"    Price vs ST: {price_vs_st:+.2f}%")
            
            # Logic verification
            if curr_signal == 1 and direction_val == -1:
                print(f"    Logic: ✅ LONG (crossunder: direction {prev_direction}→{direction_val})")
            elif curr_signal == -1 and direction_val == 1:
                print(f"    Logic: ✅ SHORT (crossover: direction {prev_direction}→{direction_val})")
            else:
                print(f"    Logic: ❌ ERROR - Signal {curr_signal} with direction {direction_val}")
    
    print(f"\n" + "=" * 80)
    print("SUMMARY FOR TRADINGVIEW COMPARISON")
    print("=" * 80)
    print("Date       | Signal | Close Price | Direction | Logic")
    print("-" * 60)
    
    for change_date in change_indices:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            
            signal_change = f"{prev_signal}→{curr_signal}"
            direction_change = f"{prev_direction}→{direction_val}"
            
            if curr_signal == 1:
                logic = "L1 (crossunder)"
            elif curr_signal == -1:
                logic = "S1 (crossover)"
            else:
                logic = "Neutral"
            
            print(f"{change_date.strftime('%Y-%m-%d')} | {signal_change:>6} | ${close_price:>9,.2f} | {direction_change:>8} | {logic}")
    
    # Current status
    current_signal = signal.iloc[-1]
    current_price = btc_df['close'].iloc[-1]
    current_direction = direction.iloc[-1]
    
    print(f"\n" + "=" * 80)
    print("CURRENT STATUS")
    print("=" * 80)
    print(f"Date: {btc_df.index[-1].strftime('%Y-%m-%d')}")
    print(f"Signal: {current_signal} ({'BULLISH' if current_signal == 1 else 'BEARISH' if current_signal == -1 else 'NEUTRAL'})")
    print(f"Price: ${current_price:,.2f}")
    print(f"Direction: {current_direction}")
    
    # Export format for easy comparison
    print(f"\n" + "=" * 80)
    print("EXPORT FORMAT (CSV-like for easy comparison)")
    print("=" * 80)
    print("Date,Signal_Change,Close_Price,High_Price,Direction_Change,Supertrend,Price_vs_ST%")
    
    for change_date in change_indices:
        idx = btc_df.index.get_loc(change_date)
        if idx > 0:
            prev_signal = signal.iloc[idx-1]
            curr_signal = signal.iloc[idx]
            close_price = btc_df['close'].iloc[idx]
            high_price = btc_df['high'].iloc[idx]
            direction_val = direction.iloc[idx]
            prev_direction = direction.iloc[idx-1]
            supertrend_val = supertrend_line.iloc[idx]
            price_vs_st = ((close_price - supertrend_val) / supertrend_val) * 100
            
            print(f"{change_date.strftime('%Y-%m-%d')},{prev_signal}→{curr_signal},${close_price:.2f},${high_price:.2f},{prev_direction}→{direction_val},${supertrend_val:.2f},{price_vs_st:+.2f}%")

if __name__ == "__main__":
    complete_median_signals()
