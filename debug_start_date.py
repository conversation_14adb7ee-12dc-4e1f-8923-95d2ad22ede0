#!/usr/bin/env python3
"""
Debug start date filtering issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from src.data_fetcher import fetch_ohlcv_data

def test_start_date_filtering():
    """Test start date filtering logic."""
    print("🔍 DEBUGGING START DATE FILTERING")
    print("=" * 50)
    
    # Fetch BTC data
    print("📊 Fetching BTC data...")
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        limit=500  # Get last 500 candles
    )
    
    if not data_dict or 'BTC/USDT' not in data_dict:
        print("❌ Failed to fetch data")
        return
    
    btc_df = data_dict['BTC/USDT']
    
    print(f"📈 Original data: {len(btc_df)} candles")
    print(f"📅 Date range: {btc_df.index[0]} to {btc_df.index[-1]}")
    print(f"🕐 Index timezone: {btc_df.index.tz}")
    
    # Test different start dates
    test_dates = ['2024-01-01', '2024-06-01', '2025-01-01']
    
    for start_date in test_dates:
        print(f"\n🎯 Testing start_date: {start_date}")
        
        try:
            # Method 1: UTC timezone
            start_dt_utc = pd.to_datetime(start_date, utc=True)
            filtered_utc = btc_df[btc_df.index >= start_dt_utc]
            print(f"   UTC method: {len(filtered_utc)} candles")
            if len(filtered_utc) > 0:
                print(f"   Range: {filtered_utc.index[0]} to {filtered_utc.index[-1]}")
            
            # Method 2: Timezone-aware
            start_dt_tz = pd.to_datetime(start_date).tz_localize(btc_df.index.tz)
            filtered_tz = btc_df[btc_df.index >= start_dt_tz]
            print(f"   TZ-aware method: {len(filtered_tz)} candles")
            if len(filtered_tz) > 0:
                print(f"   Range: {filtered_tz.index[0]} to {filtered_tz.index[-1]}")
            
            # Method 3: Convert to same timezone
            if btc_df.index.tz is not None:
                start_dt_converted = pd.to_datetime(start_date).tz_localize('UTC').tz_convert(btc_df.index.tz)
                filtered_converted = btc_df[btc_df.index >= start_dt_converted]
                print(f"   Converted method: {len(filtered_converted)} candles")
                if len(filtered_converted) > 0:
                    print(f"   Range: {filtered_converted.index[0]} to {filtered_converted.index[-1]}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_data_fetching_with_start_date():
    """Test if we need to change the data fetching approach."""
    print("\n🔄 TESTING DATA FETCHING APPROACH")
    print("=" * 50)
    
    # The issue: limit=500 gets the LATEST 500 candles
    # If we want data from 2024-01-01, we need a different approach
    
    print("💡 Current approach:")
    print("   1. Fetch latest 500 candles")
    print("   2. Filter by start_date")
    print("   3. Result: May have no data if start_date is too old")
    
    print("\n💡 Better approach:")
    print("   1. Calculate how many candles we need from start_date to now")
    print("   2. Fetch that many candles")
    print("   3. Filter by start_date")
    
    # Calculate days from start_date to now
    start_date = '2024-01-01'
    start_dt = pd.to_datetime(start_date)
    now = pd.Timestamp.now()
    days_diff = (now - start_dt).days
    
    print(f"\n📊 From {start_date} to now: {days_diff} days")
    print(f"📊 Need approximately {days_diff + 100} candles (with buffer)")

if __name__ == "__main__":
    test_start_date_filtering()
    test_data_fetching_with_start_date()
