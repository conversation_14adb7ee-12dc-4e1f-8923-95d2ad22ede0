#!/usr/bin/env python3
"""
Test script to investigate GeckoTerminal pool caching optimization.

Based on research: GeckoTerminal has ~30 requests per minute limit.
Current approach: 2 requests per token (pool search + OHLCV data)
Optimized approach: Cache pool addresses to reduce to 1 request per token

This script tests whether we can cache pool addresses to halve API usage.
"""

import os
import sys
import time
import logging
import requests
import pandas as pd
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Import GeckoTerminal functions
try:
    from geckoterminal_fetcher import search_pools, get_ohlcv_data, GECKOTERMINAL_API_BASE
    GECKOTERMINAL_AVAILABLE = True
except ImportError:
    GECKOTERMINAL_AVAILABLE = False
    logging.error("GeckoTerminal fetcher not available")
    sys.exit(1)

# Rate limiting constants
REQUESTS_PER_MINUTE = 30
SECONDS_PER_REQUEST = 2.5  # Conservative delay for 60 tokens
POOL_CACHE_FILE = "pool_cache.json"

# All memecoin assets from the configuration (29 assets)
ALL_MEMECOIN_ASSETS = [
    ("solana", "A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump", "FWOG"),
    ("solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "BONK"),
    ("eth", "******************************************", "PEPE"),
    ("solana", "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "WIF"),
    ("solana", "63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9", "GIGA"),
    ("solana", "BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump", "AUTISM"),
    ("base", "******************************************", "TOSHI"),
    ("bsc", "******************************************", "ALTURA"),
    ("solana", "5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUibpump", "SIGMA"),
    ("solana", "8Ki8DpuWNxu9VsS3kQbarsCWMcFGWkzzA8pUPto9zBd5", "LOCKIN"),
    ("solana", "J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr", "SPX"),
    ("base", "******************************************", "SKI"),
    ("solana", "Df6yfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump", "CHILLGUY"),
    ("eth", "******************************************", "APU"),
    ("solana", "9WPTUkh8fKuCnepRWoPYLH3aK9gSjPHFDenBq2X1Czdp", "SELFIE"),
    ("base", "******************************************", "ALIENBASE"),
    ("eth", "******************************************", "DOGEETH"),
    ("solana", "5mbK36SZ7J19An8jFochhQS4of8g6BwUjbeCSxBSoWdp", "MICHI"),
    ("solana", "2JcXacFwt9mVAwBQ5nZkYwCyXQkRcdsYrDXn6hj22SbP", "MINI"),
    ("solana", "GtDZKAqvMZMnti46ZewMiXCa4oXF4bZxwQPoKzXPFxZn", "NUB"),
    ("solana", "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "POPCAT"),
    ("solana", "********************************************", "GIKO"),
    ("solana", "8WnQQRbuEZ3CCDbH5MCVioBbw6o75NKANq9WdPhBDsWo", "COBY"),
    ("solana", "DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2", "AURA"),
    ("base", "******************************************", "COCORO"),
    ("solana", "DPaQfq5sFnoqw2Sh9WMmmASFL9LNu6RdtDqwE1tab2tB", "SKIBIDI"),
    ("solana", "4YK1njyeCkBuXG6phNtidJWKCbBhB659iwGkUJx98P5Z", "DOLAN"),
    ("solana", "69kdRLyP5DTRkpHraaSZAQbWmAwzF9guKjZfzMXzcbAs", "USA"),
    ("eth", "******************************************", "EPIK"),
]

# Test with first 10 assets for quick testing
TEST_ASSETS = ALL_MEMECOIN_ASSETS[:10]

def load_pool_cache() -> Dict:
    """Load cached pool addresses from file."""
    if os.path.exists(POOL_CACHE_FILE):
        try:
            with open(POOL_CACHE_FILE, 'r') as f:
                cache = json.load(f)
                logging.info(f"Loaded pool cache with {len(cache)} entries")
                return cache
        except Exception as e:
            logging.error(f"Error loading pool cache: {e}")
    return {}

def save_pool_cache(cache: Dict):
    """Save pool addresses to cache file."""
    try:
        with open(POOL_CACHE_FILE, 'w') as f:
            json.dump(cache, f, indent=2)
        logging.info(f"Saved pool cache with {len(cache)} entries")
    except Exception as e:
        logging.error(f"Error saving pool cache: {e}")

def get_cached_pool_address(network: str, token_address: str, cache: Dict) -> Optional[str]:
    """Get cached pool address for a token."""
    cache_key = f"{network}:{token_address}"
    return cache.get(cache_key)

def cache_pool_address(network: str, token_address: str, pool_address: str, cache: Dict):
    """Cache a pool address for a token."""
    cache_key = f"{network}:{token_address}"
    cache[cache_key] = pool_address

def find_best_pool_with_caching(network: str, token_address: str, name: str, cache: Dict) -> Optional[str]:
    """
    Find the best pool for a token, using cache if available.

    Returns:
        Pool address if found, None otherwise
    """
    # Check cache first
    cached_pool = get_cached_pool_address(network, token_address, cache)
    if cached_pool:
        logging.info(f"✅ Using cached pool for {name}: {cached_pool}")
        return cached_pool

    # Not in cache, search for pools (this uses 1 API request)
    logging.info(f"🔍 Searching pools for {name} (not in cache)...")
    pools = search_pools(network, token_address)

    if not pools:
        logging.error(f"❌ No pools found for {name}")
        return None

    # Find the most liquid pool
    try:
        sorted_pools = sorted(
            pools,
            key=lambda x: float(x.get('attributes', {}).get('reserve_in_usd', '0') or '0'),
            reverse=True
        )
        top_pool = sorted_pools[0]
        pool_address = top_pool.get('attributes', {}).get('address')

        if pool_address:
            # Cache the result
            cache_pool_address(network, token_address, pool_address, cache)
            liquidity = top_pool.get('attributes', {}).get('reserve_in_usd', '0')
            logging.info(f"✅ Found and cached pool for {name}: {pool_address} (${liquidity} liquidity)")
            return pool_address
        else:
            logging.error(f"❌ No pool address found for {name}")
            return None

    except Exception as e:
        logging.error(f"❌ Error processing pools for {name}: {e}")
        return None

def fetch_ohlcv_with_cached_pool(network: str, token_address: str, name: str, cache: Dict) -> Tuple[bool, str, int]:
    """
    Fetch OHLCV data using cached pool address when possible.

    Returns:
        (success, error_message, num_candles)
    """
    try:
        # Get pool address (from cache or API)
        pool_address = find_best_pool_with_caching(network, token_address, name, cache)

        if not pool_address:
            return False, f"No pool found for {name}", 0

        # Fetch OHLCV data (this uses 1 API request)
        logging.info(f"📊 Fetching OHLCV data for {name}...")
        df = get_ohlcv_data(
            network=network,
            pool_address=pool_address,
            timeframe="1h",
            limit=48  # 2 days of hourly data
        )

        if df.empty:
            return False, f"No OHLCV data returned for {name}", 0

        num_candles = len(df)
        logging.info(f"✅ Successfully fetched {num_candles} candles for {name}")
        return True, "", num_candles

    except requests.exceptions.HTTPError as e:
        if "429" in str(e):
            return False, f"Rate limit hit for {name}: {e}", 0
        else:
            return False, f"HTTP error for {name}: {e}", 0
    except Exception as e:
        return False, f"Error fetching {name}: {e}", 0

def test_pool_caching_optimization():
    """Test the pool caching optimization."""
    print("🧪 GeckoTerminal Pool Caching Optimization Test")
    print("=" * 60)

    # Load existing cache
    cache = load_pool_cache()

    results = {
        'successful': [],
        'failed': [],
        'rate_limited': [],
        'cache_hits': 0,
        'cache_misses': 0,
        'total_api_requests': 0,
        'test_duration': 0
    }

    start_time = time.time()

    print(f"Testing {len(TEST_ASSETS)} assets with pool caching...")
    print(f"Rate limit: {REQUESTS_PER_MINUTE} requests/minute ({SECONDS_PER_REQUEST:.1f}s between requests)")
    print("-" * 60)

    for i, (network, token_address, name) in enumerate(TEST_ASSETS, 1):
        print(f"\n[{i}/{len(TEST_ASSETS)}] Testing {name}...")

        # Check if pool is cached
        cached_pool = get_cached_pool_address(network, token_address, cache)
        if cached_pool:
            results['cache_hits'] += 1
            expected_requests = 1  # Only OHLCV request needed
        else:
            results['cache_misses'] += 1
            expected_requests = 2  # Pool search + OHLCV request needed

        print(f"  Expected API requests: {expected_requests} ({'cached pool' if cached_pool else 'need pool search'})")

        # Fetch data
        success, error_msg, num_candles = fetch_ohlcv_with_cached_pool(
            network, token_address, name, cache
        )

        results['total_api_requests'] += expected_requests

        if success:
            results['successful'].append({
                'name': name,
                'network': network,
                'token_address': token_address,
                'candles': num_candles,
                'used_cache': cached_pool is not None
            })
        else:
            failed_entry = {
                'name': name,
                'network': network,
                'token_address': token_address,
                'error': error_msg
            }

            if "rate limit" in error_msg.lower() or "429" in error_msg:
                results['rate_limited'].append(failed_entry)
                print(f"❌ Rate limit hit at asset {i}: {name}")
                break
            else:
                results['failed'].append(failed_entry)
                print(f"❌ Failed to fetch {name}: {error_msg}")

        # Rate limiting delay
        if i < len(TEST_ASSETS):  # Don't delay after the last request
            print(f"  Waiting {SECONDS_PER_REQUEST:.1f}s for rate limiting...")
            time.sleep(SECONDS_PER_REQUEST)

    results['test_duration'] = time.time() - start_time

    # Save updated cache
    save_pool_cache(cache)

    # Print results
    print_test_results(results)

def print_test_results(results: Dict):
    """Print detailed test results."""
    print("\n" + "=" * 60)
    print("POOL CACHING OPTIMIZATION TEST RESULTS")
    print("=" * 60)

    print(f"Test Duration: {results['test_duration']:.1f} seconds")
    print(f"Total Assets Tested: {len(results['successful']) + len(results['failed']) + len(results['rate_limited'])}")
    print(f"Successful Fetches: {len(results['successful'])}")
    print(f"Failed Fetches: {len(results['failed'])}")
    print(f"Rate Limited: {len(results['rate_limited'])}")

    print(f"\n📊 CACHING EFFICIENCY:")
    print(f"Cache Hits: {results['cache_hits']}")
    print(f"Cache Misses: {results['cache_misses']}")
    print(f"Total API Requests Made: {results['total_api_requests']}")

    # Calculate savings
    total_assets = results['cache_hits'] + results['cache_misses']
    if total_assets > 0:
        without_cache_requests = total_assets * 2  # 2 requests per asset without caching
        savings = without_cache_requests - results['total_api_requests']
        savings_percent = (savings / without_cache_requests) * 100
        print(f"API Requests Saved: {savings} ({savings_percent:.1f}%)")
        print(f"Without caching: {without_cache_requests} requests")
        print(f"With caching: {results['total_api_requests']} requests")

    if results['successful']:
        print(f"\n✅ SUCCESSFUL ASSETS ({len(results['successful'])}):")
        for asset in results['successful']:
            cache_status = "🟢 cached" if asset['used_cache'] else "🔍 searched"
            print(f"  - {asset['name']} ({asset['network']}): {asset['candles']} candles ({cache_status})")

    if results['failed']:
        print(f"\n❌ FAILED ASSETS ({len(results['failed'])}):")
        for asset in results['failed']:
            print(f"  - {asset['name']} ({asset['network']}): {asset['error']}")

    if results['rate_limited']:
        print(f"\n🚫 RATE LIMITED ASSETS ({len(results['rate_limited'])}):")
        for asset in results['rate_limited']:
            print(f"  - {asset['name']} ({asset['network']}): {asset['error']}")

def main():
    """Main function."""
    if not GECKOTERMINAL_AVAILABLE:
        logging.error("GeckoTerminal fetcher not available. Exiting.")
        return

    test_pool_caching_optimization()

if __name__ == "__main__":
    main()
