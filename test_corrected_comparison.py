#!/usr/bin/env python3
"""
Corrected PGO and Scoring Comparison Test
=========================================

This script correctly handles the parameter differences between AllocationTester and AllocationReporter
to identify the exact source of the scoring discrepancy.
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add the src directory to the path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import both implementations
from test_allocation import AllocationTester
from allocation_report import AllocationReporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('corrected_comparison_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CorrectedComparisonTester:
    """Test with corrected parameter handling"""

    def __init__(self):
        # Common parameters that both classes accept
        self.common_params = {
            'timeframe': '1d',
            'mtpi_timeframe': '1d',
            'analysis_start_date': '2023-08-20',  # Start before divergence point
            'n_assets': 1,
            'transaction_fee_rate': 0.001,
            'selected_assets': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'DOT/USDT', 'BNB/USDT'],
            'use_cache': True,
            'initial_capital': 1.0,
            'wait_for_confirmed_signals': True,
            'use_weighted_allocation': False,
            'weights': None,
            'trend_method': 'PGO For Loop'  # Force same trend method
        }

        # Parameters specific to AllocationTester
        self.tester_params = self.common_params.copy()
        self.tester_params['use_mtpi'] = False  # Disable MTPI to focus on scoring

        # Parameters specific to AllocationReporter
        self.reporter_params = self.common_params.copy()
        self.reporter_params['use_mtpi_signal'] = False  # Different parameter name!
        self.reporter_params['pgo_length'] = 35
        self.reporter_params['pgo_upper_threshold'] = 1.1
        self.reporter_params['pgo_lower_threshold'] = -0.58

    def setup_implementations(self):
        """Setup both implementations with correct parameters"""
        logger.info("=== SETTING UP IMPLEMENTATIONS WITH CORRECTED PARAMETERS ===")

        # Create AllocationTester
        logger.info("Creating AllocationTester...")
        self.tester = AllocationTester(**self.tester_params)
        logger.info(f"Tester trend method: {self.tester.trend_method}")

        # Create AllocationReporter
        logger.info("Creating AllocationReporter...")
        self.reporter = AllocationReporter(**self.reporter_params)
        logger.info(f"Reporter trend method: {self.reporter.trend_method}")

        logger.info("Both implementations created successfully!")

    def fetch_and_compare_data(self):
        """Fetch data from both and compare"""
        logger.info("=== FETCHING AND COMPARING DATA ===")

        # Fetch from tester
        logger.info("Fetching data from AllocationTester...")
        self.tester.fetch_data()
        tester_data = self.tester.data_dict

        # Fetch from reporter
        logger.info("Fetching data from AllocationReporter...")
        self.reporter.fetch_data()
        reporter_data = self.reporter.data_dict

        # Compare data
        self.compare_raw_data(tester_data, reporter_data)

        return tester_data, reporter_data

    def compare_raw_data(self, tester_data, reporter_data):
        """Compare raw OHLCV data"""
        logger.info("Comparing raw OHLCV data...")

        data_identical = True

        for asset in self.common_params['selected_assets']:
            if asset in tester_data and asset in reporter_data:
                tester_df = tester_data[asset]
                reporter_df = reporter_data[asset]

                logger.info(f"\n{asset}:")
                logger.info(f"  Tester: {len(tester_df)} rows")
                logger.info(f"  Reporter: {len(reporter_df)} rows")

                if tester_df.shape != reporter_df.shape:
                    logger.warning(f"  Shape mismatch!")
                    data_identical = False
                    continue

                # Compare close prices
                close_diff = np.abs(tester_df['close'] - reporter_df['close']).max()
                logger.info(f"  Max close price difference: {close_diff}")

                if close_diff > 1e-10:
                    logger.warning(f"  Close prices differ!")
                    data_identical = False

        if data_identical:
            logger.info("✓ Raw data is identical")
        else:
            logger.error("✗ Raw data differs")

        return data_identical

    def calculate_and_compare_scores(self, tester_data, reporter_data):
        """Calculate scores from both implementations and compare"""
        logger.info("=== CALCULATING AND COMPARING SCORES ===")

        # Calculate scores from tester
        logger.info("Calculating scores from AllocationTester...")
        try:
            # Set the data in tester
            self.tester.data_dict = tester_data
            self.tester.calculate_scores()
            tester_scores = self.tester.daily_scores_df
            logger.info(f"Tester scores shape: {tester_scores.shape}")
        except Exception as e:
            logger.error(f"Failed to calculate tester scores: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return

        # Calculate scores from reporter
        logger.info("Calculating scores from AllocationReporter...")
        try:
            # Set the data in reporter
            self.reporter.data_dict = reporter_data
            self.reporter.calculate_scores()
            reporter_scores = self.reporter.daily_scores_df
            logger.info(f"Reporter scores shape: {reporter_scores.shape}")
        except Exception as e:
            logger.error(f"Failed to calculate reporter scores: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return

        # Compare scores
        self.detailed_score_comparison(tester_scores, reporter_scores)

    def detailed_score_comparison(self, tester_scores, reporter_scores):
        """Detailed comparison of scores"""
        logger.info("=== DETAILED SCORE COMPARISON ===")

        # Save scores to files
        tester_scores.to_csv('corrected_tester_scores.csv')
        reporter_scores.to_csv('corrected_reporter_scores.csv')
        logger.info("Scores saved to CSV files")

        # Find common dates and assets
        common_dates = tester_scores.index.intersection(reporter_scores.index)
        common_assets = tester_scores.columns.intersection(reporter_scores.columns)

        logger.info(f"Comparing {len(common_dates)} dates and {len(common_assets)} assets")

        # Check critical dates around August 26, 2023
        critical_dates = [
            '2023-08-25',
            '2023-08-26',
            '2023-08-27',
            '2023-08-28'
        ]

        for date_str in critical_dates:
            try:
                date = pd.Timestamp(date_str)

                if date in common_dates:
                    logger.info(f"\nScores on {date_str}:")

                    tester_row = tester_scores.loc[date, common_assets]
                    reporter_row = reporter_scores.loc[date, common_assets]

                    # Check for differences
                    max_diff = abs(tester_row - reporter_row).max()
                    logger.info(f"  Maximum difference: {max_diff:.6f}")

                    if max_diff > 0.01:
                        logger.warning(f"  SIGNIFICANT DIFFERENCES FOUND!")
                        for asset in common_assets:
                            diff = abs(tester_row[asset] - reporter_row[asset])
                            if diff > 0.01:
                                logger.warning(f"    {asset}: Tester={tester_row[asset]:.3f}, Reporter={reporter_row[asset]:.3f}, Diff={diff:.3f}")
                    else:
                        logger.info(f"  ✓ Scores are very similar")

            except Exception as e:
                logger.error(f"Error comparing {date_str}: {e}")

        # Find first divergence
        self.find_first_divergence(tester_scores, reporter_scores, common_dates, common_assets)

    def find_first_divergence(self, tester_scores, reporter_scores, common_dates, common_assets):
        """Find the exact first divergence point"""
        logger.info("=== FINDING FIRST DIVERGENCE ===")

        first_divergence = None

        for date in common_dates:
            tester_row = tester_scores.loc[date, common_assets]
            reporter_row = reporter_scores.loc[date, common_assets]

            max_diff = abs(tester_row - reporter_row).max()

            if max_diff > 0.01:
                first_divergence = date
                logger.warning(f"First divergence on: {date}")
                logger.warning(f"Maximum difference: {max_diff:.6f}")

                # Show all differences
                for asset in common_assets:
                    diff = abs(tester_row[asset] - reporter_row[asset])
                    logger.warning(f"  {asset}: Tester={tester_row[asset]:.3f}, Reporter={reporter_row[asset]:.3f}, Diff={diff:.3f}")
                break

        if first_divergence is None:
            logger.info("✓ No significant divergence found!")
        else:
            # Show the previous day for context
            prev_dates = common_dates[common_dates < first_divergence]
            if len(prev_dates) > 0:
                prev_date = prev_dates[-1]
                logger.info(f"\nPrevious day ({prev_date}) for context:")

                tester_prev = tester_scores.loc[prev_date, common_assets]
                reporter_prev = reporter_scores.loc[prev_date, common_assets]

                for asset in common_assets:
                    logger.info(f"  {asset}: Tester={tester_prev[asset]:.3f}, Reporter={reporter_prev[asset]:.3f}")

    def investigate_calculation_methods(self):
        """Investigate the actual calculation methods"""
        logger.info("=== INVESTIGATING CALCULATION METHODS ===")

        # Check if both use the same trend method
        logger.info(f"Tester trend method: {self.tester.trend_method}")
        logger.info(f"Reporter trend method: {self.reporter.trend_method}")

        # Check if both have the same calculate_scores method
        tester_calc_method = getattr(self.tester, 'calculate_scores', None)
        reporter_calc_method = getattr(self.reporter, 'calculate_scores', None)

        logger.info(f"Tester has calculate_scores: {tester_calc_method is not None}")
        logger.info(f"Reporter has calculate_scores: {reporter_calc_method is not None}")

        # Try to get source code if possible
        if tester_calc_method and reporter_calc_method:
            try:
                import inspect

                tester_source = inspect.getsource(tester_calc_method)
                reporter_source = inspect.getsource(reporter_calc_method)

                if tester_source == reporter_source:
                    logger.info("✓ calculate_scores source code is identical")
                else:
                    logger.warning("⚠️  calculate_scores source code differs!")

                    # Save source code for comparison
                    with open('tester_calculate_scores_corrected.py', 'w') as f:
                        f.write("# AllocationTester.calculate_scores\n")
                        f.write(tester_source)

                    with open('reporter_calculate_scores_corrected.py', 'w') as f:
                        f.write("# AllocationReporter.calculate_scores\n")
                        f.write(reporter_source)

                    logger.info("Source code saved for manual comparison")

            except Exception as e:
                logger.warning(f"Could not compare source code: {e}")

def main():
    """Main execution"""
    logger.info("=== STARTING CORRECTED COMPARISON TEST ===")

    tester = CorrectedComparisonTester()

    try:
        # Setup implementations
        tester.setup_implementations()

        # Fetch and compare data
        tester_data, reporter_data = tester.fetch_and_compare_data()

        # Calculate and compare scores
        tester.calculate_and_compare_scores(tester_data, reporter_data)

        # Investigate calculation methods
        tester.investigate_calculation_methods()

        logger.info("=== CORRECTED COMPARISON TEST COMPLETED ===")

    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
