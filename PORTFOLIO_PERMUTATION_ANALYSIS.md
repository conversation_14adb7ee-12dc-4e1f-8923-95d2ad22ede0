# Portfolio Rebalancing - Comprehensive Edge Case Analysis

## Current Issues Identified

### 1. ✅ FIXED: Sell-Before-Buy Order Issue
**Status**: Fixed in recent changes
**Issue**: System tried to increase positions before reducing others
**Fix**: Separated Step 2 into 2a (sells) and 2b (buys)

### 2. 🔴 CRITICAL: Swap Proceeds Logic Flaw
**Location**: Lines 350-357
**Issue**: Swap identification logic is fundamentally broken

```python
# Find the replacement asset with the same weight
for new_asset in assets_to_buy:
    new_weight = assets_with_weights.get(new_asset, 0.0)
    if abs(new_weight - original_weight) < 0.001:  # Same weight = replacement
        asset_swaps[asset] = new_asset
        swap_proceeds[new_asset] = proceeds
        break
```

**Problems**:
- Assumes 1:1 weight mapping between sold and bought assets
- Fails when multiple assets have same weights (common in equal allocation)
- Fails when weights change during rebalancing
- Can assign same proceeds to multiple assets

**Example Failure**:
- Sell: BTC (50%) → $5000 proceeds
- Buy: ETH (25%), SOL (25%) 
- Logic assigns $5000 to ETH, nothing to SOL

### 3. 🔴 CRITICAL: Portfolio Value Calculation Inconsistency
**Location**: Lines 505-511
**Issue**: Total portfolio value calculated differently in Step 2 vs other steps

```python
# Step 2: Recalculates total portfolio value for each asset
total_portfolio_value = available_balance
for a, pos in positions.items():
    a_price = self.get_current_price(a)
    if a_price:
        total_portfolio_value += pos.get('amount', 0.0) * a_price
```

**Problems**:
- Recalculated for each asset adjustment (expensive)
- Can change between adjustments due to price movements
- Inconsistent with pre-check calculations
- No caching of portfolio value

### 4. 🟡 MEDIUM: Balance Update Timing Issues
**Location**: Lines 568-574, 618
**Issue**: Available balance updated at wrong times

**Problems**:
- Balance updated after Step 2a but not used in Step 2b calculations
- Step 2b still uses old portfolio value calculations
- Potential double-counting of freed capital

### 5. 🟡 MEDIUM: Partial Failure Recovery
**Location**: Lines 700-746
**Issue**: Portfolio state update logic doesn't handle partial failures well

**Problems**:
- If Step 2a succeeds but Step 2b fails, portfolio is in inconsistent state
- No rollback mechanism for failed rebalancing
- Weight normalization happens regardless of trade success

### 6. 🟡 MEDIUM: Price Staleness
**Location**: Multiple locations
**Issue**: Prices fetched at different times during execution

**Problems**:
- Prices in Step 1 may be stale by Step 3
- No price caching or timestamp validation
- Market volatility can cause calculation errors

### 7. 🟡 MEDIUM: Weight Threshold Inconsistency
**Location**: Line 476
**Issue**: 1% weight change threshold may be too large

```python
if abs(current_weight - new_weight) < 0.01:  # Skip if within 1%
```

**Problems**:
- 1% of a $100k portfolio = $1000 drift tolerance
- May skip important rebalancing
- No configuration option for threshold

## Potential Portfolio Permutation Failures

### Scenario 1: Multiple Equal Weight Assets
**Portfolio**: A(25%), B(25%), C(25%), D(25%) → A(50%), B(50%)
**Failure**: Swap proceeds logic assigns all proceeds to first matching asset

### Scenario 2: Complex Weight Redistribution  
**Portfolio**: A(80%), B(20%) → A(33%), B(33%), C(34%)
**Failure**: No clear 1:1 mapping for swap proceeds

### Scenario 3: Precision-Induced Failures
**Portfolio**: Small positions with minimum order sizes
**Failure**: Precision adjustments may make trades impossible

### Scenario 4: Market Volatility During Execution
**Portfolio**: Prices change significantly between steps
**Failure**: Calculations become invalid, insufficient balance errors

### Scenario 5: Partial Trade Failures
**Portfolio**: Some trades succeed, others fail
**Failure**: Portfolio left in inconsistent state

## Recommended Fixes (Priority Order)

### 1. HIGH: Fix Swap Proceeds Logic
- Remove assumption of 1:1 weight mapping
- Implement proportional proceeds distribution
- Handle multiple assets with same weights

### 2. HIGH: Implement Portfolio Value Caching
- Calculate total portfolio value once at start
- Update incrementally after each trade
- Use consistent value across all steps

### 3. MEDIUM: Improve Balance Management
- Track available balance throughout execution
- Update balance after each trade
- Validate balance before each purchase

### 4. MEDIUM: Add Rollback Mechanism
- Track all trades in transaction
- Implement rollback for partial failures
- Ensure atomic portfolio updates

### 5. LOW: Add Price Caching
- Cache prices at start of execution
- Add staleness validation
- Refresh prices if execution takes too long

## Testing Requirements

Need comprehensive tests for:
1. All weight redistribution patterns
2. Equal weight scenarios
3. Precision edge cases
4. Partial failure scenarios
5. Market volatility simulation
6. Large portfolio rebalancing
7. Minimum order size conflicts
