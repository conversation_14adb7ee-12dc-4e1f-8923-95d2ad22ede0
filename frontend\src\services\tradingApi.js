import axios from 'axios';

// Create an API instance
const api = axios.create({
  baseURL: '/api/trading',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Fetch the current trading status
 * @returns {Promise<Object>} The trading status
 */
export const fetchTradingStatus = async () => {
  try {
    const response = await api.get('/status');
    return response.data;
  } catch (error) {
    console.error('Error fetching trading status:', error);
    throw error;
  }
};

/**
 * Start the trading strategy
 * @param {Object} params Optional parameters for starting trading
 * @param {Array} params.selected_assets The assets to include in the rotation strategy
 * @param {boolean} params.auto_select_best_asset Whether to automatically select the best asset based on a quick backtest
 * @returns {Promise<Object>} The result of the operation with backtest_result if auto_select_best_asset is true
 */
export const startTrading = async (params = {}) => {
  try {
    const response = await api.post('/start', params);
    return response.data;
  } catch (error) {
    console.error('Error starting trading:', error);
    throw error;
  }
};

/**
 * Stop the trading strategy
 * @returns {Promise<Object>} The result of the operation
 */
export const stopTrading = async () => {
  try {
    const response = await api.post('/stop');
    return response.data;
  } catch (error) {
    console.error('Error stopping trading:', error);
    throw error;
  }
};

/**
 * Manually execute the trading strategy
 * @param {Object} params Optional parameters for manual execution
 * @param {string} params.best_asset The best asset to trade
 * @param {number} params.mtpi_signal The MTPI signal value
 * @param {Array} params.selected_assets The assets to include in the rotation strategy
 * @param {boolean} params.auto_select_best_asset Whether to automatically select the best asset based on a quick backtest
 * @returns {Promise<Object>} The result of the operation with backtest_result if auto_select_best_asset is true
 */
export const executeTrading = async (params = {}) => {
  try {
    const response = await api.post('/execute', params);
    return response.data;
  } catch (error) {
    console.error('Error executing trading:', error);
    throw error;
  }
};

/**
 * Fetch the trading configuration
 * @returns {Promise<Object>} The trading configuration
 */
export const fetchTradingConfig = async () => {
  try {
    const response = await api.get('/config');
    return response.data;
  } catch (error) {
    console.error('Error fetching trading configuration:', error);
    throw error;
  }
};

/**
 * Update the trading configuration
 * @param {Object} config The new trading configuration
 * @returns {Promise<Object>} The result of the operation
 */
export const updateTradingConfig = async (config) => {
  try {
    const response = await api.post('/config', config);
    return response.data;
  } catch (error) {
    console.error('Error updating trading configuration:', error);
    throw error;
  }
};

/**
 * Reset the paper trading account
 * @returns {Promise<Object>} The result of the operation
 */
export const resetPaperTrading = async () => {
  try {
    const response = await api.post('/reset');
    return response.data;
  } catch (error) {
    console.error('Error resetting paper trading:', error);
    throw error;
  }
};

/**
 * Fetch the current trading positions
 * @returns {Promise<Array>} The list of open positions
 */
export const fetchTradingPositions = async () => {
  try {
    const response = await api.get('/positions');
    return response.data;
  } catch (error) {
    console.error('Error fetching trading positions:', error);
    throw error;
  }
};

/**
 * Fetch the trading history
 * @param {Object} params Optional parameters for filtering
 * @param {number} params.limit The maximum number of trades to return
 * @param {string} params.start_date The start date for filtering
 * @param {string} params.end_date The end date for filtering
 * @returns {Promise<Array>} The list of trades
 */
export const fetchTradingHistory = async (params = {}) => {
  try {
    const response = await api.get('/history', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching trading history:', error);
    throw error;
  }
};

/**
 * Fetch the trading performance metrics
 * @param {Object} params Optional parameters for filtering
 * @param {Array} params.selected_assets The assets to include in the performance metrics
 * @returns {Promise<Object>} The performance metrics
 */
export const fetchTradingPerformance = async (params = {}) => {
  try {
    const response = await api.get('/performance', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching trading performance:', error);
    throw error;
  }
};

export default {
  fetchTradingStatus,
  startTrading,
  stopTrading,
  executeTrading,
  fetchTradingConfig,
  updateTradingConfig,
  resetPaperTrading,
  fetchTradingPositions,
  fetchTradingHistory,
  fetchTradingPerformance,
};
