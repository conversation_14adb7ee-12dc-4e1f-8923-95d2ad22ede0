# test_mtpi_sell_condition.py
# Script to test MTPI sell condition handling in the trading system

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os
import sys

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.MTPI_signal_handler import save_mtpi_signal_to_file, fetch_mtpi_signal
from src.trading.executor import TradingExecutor
from src.trading.account import AccountManager
from src.config_manager import load_config, get_trading_config
from src.notification.notification_manager import NotificationManager
from background_service import BackgroundService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mtpi_sell_test.log')
    ]
)

def test_mtpi_sell_condition():
    """
    Test the system's ability to handle MTPI sell conditions.
    This test verifies that when the MTPI signal changes from bullish to bearish,
    the system correctly exits all positions.
    """
    logging.info("=" * 80)
    logging.info("STARTING MTPI SELL CONDITION TEST")
    logging.info("=" * 80)
    
    # Initialize components
    trading_executor = TradingExecutor('binance')
    account_manager = AccountManager('binance')
    trading_config = get_trading_config()
    
    # Step 1: Ensure we're in paper trading mode
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        trading_executor.set_trading_mode('paper')
    
    # Step 2: Get current portfolio state
    if trading_config.get('mode') == 'paper':
        positions = trading_executor.paper_trading.get_positions()
        balance = trading_executor.paper_trading.get_balance()
    else:
        positions = account_manager.get_open_positions()
        balance = account_manager.get_balance('USDC')
    
    logging.info(f"Initial positions: {positions}")
    logging.info(f"Initial balance: {balance}")
    
    # Step 3: Force a bullish MTPI signal and execute a trade
    logging.info("Setting MTPI signal to bullish (1)")
    save_mtpi_signal_to_file(1, 'mtpi_signal.txt')
    
    # Execute a trade with a bullish signal
    test_asset = 'BTC/USDC'
    logging.info(f"Executing trade for {test_asset} with bullish signal")
    result = trading_executor.execute_strategy_signal(test_asset, 1)
    logging.info(f"Trade execution result: {result}")
    
    # Verify the position was entered
    if trading_config.get('mode') == 'paper':
        positions_after_buy = trading_executor.paper_trading.get_positions()
    else:
        positions_after_buy = account_manager.get_open_positions()
    
    logging.info(f"Positions after buy: {positions_after_buy}")
    
    # Step 4: Force a bearish MTPI signal and verify exit
    logging.info("Setting MTPI signal to bearish (-1)")
    save_mtpi_signal_to_file(-1, 'mtpi_signal.txt')
    
    # Execute with bearish signal
    logging.info("Executing with bearish signal")
    result = trading_executor.execute_strategy_signal(test_asset, -1)
    logging.info(f"Bearish execution result: {result}")
    
    # Verify positions were exited
    if trading_config.get('mode') == 'paper':
        positions_after_sell = trading_executor.paper_trading.get_positions()
        final_balance = trading_executor.paper_trading.get_balance()
    else:
        positions_after_sell = account_manager.get_open_positions()
        final_balance = account_manager.get_balance('USDC')
    
    logging.info(f"Positions after sell: {positions_after_sell}")
    logging.info(f"Final balance: {final_balance}")
    
    # Check if all positions were exited
    if positions_after_sell:
        logging.error("FAIL: Not all positions were exited after bearish signal")
        return False
    else:
        logging.info("SUCCESS: All positions were exited after bearish signal")
        return True

def test_background_service_mtpi_handling():
    """
    Test the background service's handling of MTPI signal changes.
    This verifies that the background service correctly responds to MTPI signal changes.
    """
    logging.info("=" * 80)
    logging.info("TESTING BACKGROUND SERVICE MTPI HANDLING")
    logging.info("=" * 80)
    
    # Initialize the background service
    service = BackgroundService()
    
    # Ensure trading is enabled in paper mode
    trading_config = get_trading_config()
    if not trading_config.get('enabled', False):
        logging.warning("Trading is disabled. Enabling for test...")
        # Note: In a real implementation, you would modify the config file
        # For this test, we'll just modify the in-memory config
        service.trading_config['enabled'] = True
    
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        service.trading_executor.set_trading_mode('paper')
    
    # Step 1: Force a bullish MTPI signal
    logging.info("Setting MTPI signal to bullish (1)")
    save_mtpi_signal_to_file(1, 'mtpi_signal.txt')
    service.last_mtpi_signal = 1
    
    # Step 2: Run the service once to establish positions
    logging.info("Running background service with bullish signal")
    service.execute_strategy()
    
    # Get current positions
    if service.trading_config.get('mode') == 'paper':
        positions_after_buy = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_buy = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after bullish execution: {positions_after_buy}")
    
    # Step 3: Force a bearish MTPI signal
    logging.info("Setting MTPI signal to bearish (-1)")
    save_mtpi_signal_to_file(-1, 'mtpi_signal.txt')
    
    # Step 4: Run the service again
    logging.info("Running background service with bearish signal")
    service.execute_strategy()
    
    # Check if positions were exited
    if service.trading_config.get('mode') == 'paper':
        positions_after_sell = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_sell = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after bearish execution: {positions_after_sell}")
    
    # Check if all positions were exited
    if positions_after_sell:
        logging.error("FAIL: Background service did not exit all positions after bearish signal")
        return False
    else:
        logging.info("SUCCESS: Background service exited all positions after bearish signal")
        return True

def test_asset_rotation():
    """
    Test the system's ability to handle asset rotation.
    This verifies that when the best asset changes, the system correctly
    exits the current position and enters a new one.
    """
    logging.info("=" * 80)
    logging.info("TESTING ASSET ROTATION")
    logging.info("=" * 80)
    
    # Initialize components
    trading_executor = TradingExecutor('binance')
    
    # Ensure we're in paper trading mode
    trading_config = get_trading_config()
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        trading_executor.set_trading_mode('paper')
    
    # Step 1: Force a bullish MTPI signal
    logging.info("Setting MTPI signal to bullish (1)")
    save_mtpi_signal_to_file(1, 'mtpi_signal.txt')
    
    # Step 2: Enter a position with the first asset
    first_asset = 'BTC/USDC'
    logging.info(f"Entering position with first asset: {first_asset}")
    result = trading_executor.execute_strategy_signal(first_asset, 1)
    logging.info(f"First asset trade result: {result}")
    
    # Verify the position was entered
    if trading_config.get('mode') == 'paper':
        positions_after_first = trading_executor.paper_trading.get_positions()
    else:
        positions_after_first = trading_executor.account_manager.get_open_positions()
    
    logging.info(f"Positions after first asset: {positions_after_first}")
    
    # Step 3: Rotate to a different asset
    second_asset = 'ETH/USDC'
    logging.info(f"Rotating to second asset: {second_asset}")
    result = trading_executor.execute_strategy_signal(second_asset, 1)
    logging.info(f"Second asset trade result: {result}")
    
    # Verify the rotation occurred
    if trading_config.get('mode') == 'paper':
        positions_after_second = trading_executor.paper_trading.get_positions()
    else:
        positions_after_second = trading_executor.account_manager.get_open_positions()
    
    logging.info(f"Positions after second asset: {positions_after_second}")
    
    # Check if rotation was successful
    rotation_success = False
    if positions_after_second:
        # Check if we're now holding the second asset and not the first
        has_second = any(second_asset in pos for pos in positions_after_second)
        has_first = any(first_asset in pos for pos in positions_after_second)
        
        if has_second and not has_first:
            logging.info("SUCCESS: Asset rotation completed successfully")
            rotation_success = True
        else:
            logging.error(f"FAIL: Asset rotation failed. Current positions: {positions_after_second}")
    else:
        logging.error("FAIL: No positions after rotation attempt")
    
    return rotation_success

if __name__ == "__main__":
    # Run all tests
    mtpi_sell_result = test_mtpi_sell_condition()
    background_mtpi_result = test_background_service_mtpi_handling()
    asset_rotation_result = test_asset_rotation()
    
    # Print summary
    logging.info("=" * 80)
    logging.info("TEST RESULTS SUMMARY")
    logging.info("=" * 80)
    logging.info(f"MTPI Sell Condition Test: {'PASSED' if mtpi_sell_result else 'FAILED'}")
    logging.info(f"Background Service MTPI Test: {'PASSED' if background_mtpi_result else 'FAILED'}")
    logging.info(f"Asset Rotation Test: {'PASSED' if asset_rotation_result else 'FAILED'}")
    
    if mtpi_sell_result and background_mtpi_result and asset_rotation_result:
        logging.info("All tests PASSED!")
    else:
        logging.info("Some tests FAILED. Check the logs for details.")
