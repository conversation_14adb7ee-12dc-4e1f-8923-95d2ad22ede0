#!/usr/bin/env python3
"""
Debug script to help troubleshoot API credential issues with Kraken and Bitvavo.
This script provides detailed information about API key configuration and common issues.
"""

import os
import sys
import ccxt
import logging
from datetime import datetime

# Add the src directory to the path
sys.path.append('src')

from config_manager import get_exchange_credentials

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_credentials(exchange_id: str):
    """Debug API credentials for a specific exchange."""
    print(f"\n{'='*60}")
    print(f"DEBUGGING {exchange_id.upper()} API CREDENTIALS")
    print(f"{'='*60}")
    
    # Check environment variables
    api_key_env = f"{exchange_id.upper()}_API_KEY"
    api_secret_env = f"{exchange_id.upper()}_API_SECRET"
    
    api_key = os.getenv(api_key_env)
    api_secret = os.getenv(api_secret_env)
    
    print(f"Environment variable {api_key_env}: {'✅ SET' if api_key else '❌ NOT SET'}")
    print(f"Environment variable {api_secret_env}: {'✅ SET' if api_secret else '❌ NOT SET'}")
    
    if api_key:
        print(f"API Key length: {len(api_key)} characters")
        print(f"API Key preview: {api_key[:8]}...{api_key[-8:] if len(api_key) > 16 else api_key}")
    
    if api_secret:
        print(f"API Secret length: {len(api_secret)} characters")
        print(f"API Secret preview: {api_secret[:8]}...{api_secret[-8:] if len(api_secret) > 16 else api_secret}")
    
    # Check config manager
    try:
        credentials = get_exchange_credentials(exchange_id)
        print(f"Config manager credentials: {'✅ LOADED' if credentials else '❌ FAILED'}")
        if credentials:
            print(f"  API Key from config: {'✅ PRESENT' if credentials.get('api_key') else '❌ MISSING'}")
            print(f"  API Secret from config: {'✅ PRESENT' if credentials.get('api_secret') else '❌ MISSING'}")
    except Exception as e:
        print(f"Config manager error: {e}")
    
    return api_key, api_secret

def test_exchange_with_debug(exchange_id: str, api_key: str, api_secret: str):
    """Test exchange connection with detailed debugging."""
    print(f"\n{'='*60}")
    print(f"TESTING {exchange_id.upper()} CONNECTION WITH DEBUG")
    print(f"{'='*60}")
    
    try:
        # Get exchange class
        exchange_class = getattr(ccxt, exchange_id, None)
        if not exchange_class:
            print(f"❌ Exchange '{exchange_id}' not found in ccxt")
            return
        
        # Create exchange instance with minimal config first
        print("Creating exchange instance...")
        exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': True,
            'sandbox': False,  # Make sure we're not in sandbox mode
        })
        
        print(f"✅ Exchange instance created")
        print(f"Exchange ID: {exchange.id}")
        print(f"Exchange name: {exchange.name}")
        print(f"Sandbox mode: {exchange.sandbox}")
        
        # Load markets first
        print("Loading markets...")
        markets = exchange.load_markets()
        print(f"✅ Markets loaded: {len(markets)} markets")
        
        # Try to get exchange status/info (non-authenticated)
        try:
            if hasattr(exchange, 'fetch_status'):
                status = exchange.fetch_status()
                print(f"Exchange status: {status}")
        except Exception as e:
            print(f"Could not fetch exchange status: {e}")
        
        # Now try authenticated calls with more specific error handling
        print("\nTesting authenticated calls...")
        
        if exchange_id == 'kraken':
            # Kraken-specific debugging
            print("Kraken-specific tests:")
            try:
                # Try a simple authenticated call first
                if hasattr(exchange, 'fetch_balance'):
                    print("Attempting to fetch balance...")
                    balance = exchange.fetch_balance()
                    print(f"✅ Balance fetched successfully")
                    
                    # Show non-zero balances
                    non_zero = {k: v for k, v in balance['free'].items() if float(v) > 0}
                    if non_zero:
                        print(f"Non-zero balances: {non_zero}")
                    else:
                        print("No non-zero balances (account might be empty)")
                        
            except Exception as e:
                print(f"❌ Balance fetch failed: {e}")
                
                # Kraken-specific error analysis
                error_str = str(e).lower()
                if 'invalid key' in error_str:
                    print("\n🔍 KRAKEN DIAGNOSIS:")
                    print("- API key appears to be invalid or not properly configured")
                    print("- Check that the API key is correctly copied from Kraken")
                    print("- Ensure the API key has 'Query Funds' permission")
                    print("- Verify the API key is not expired")
                elif 'invalid signature' in error_str:
                    print("\n🔍 KRAKEN DIAGNOSIS:")
                    print("- API secret appears to be invalid")
                    print("- Check that the API secret is correctly copied from Kraken")
                elif 'permission denied' in error_str:
                    print("\n🔍 KRAKEN DIAGNOSIS:")
                    print("- API key lacks required permissions")
                    print("- Enable 'Query Funds' permission in Kraken API settings")
        
        elif exchange_id == 'bitvavo':
            # Bitvavo-specific debugging
            print("Bitvavo-specific tests:")
            try:
                print("Attempting to fetch balance...")
                balance = exchange.fetch_balance()
                print(f"✅ Balance fetched successfully")
                
                # Show non-zero balances
                non_zero = {k: v for k, v in balance['free'].items() if float(v) > 0}
                if non_zero:
                    print(f"Non-zero balances: {non_zero}")
                else:
                    print("No non-zero balances (account might be empty)")
                    
            except Exception as e:
                print(f"❌ Balance fetch failed: {e}")
                
                # Bitvavo-specific error analysis
                error_str = str(e).lower()
                if 'no active api key' in error_str or 'errorcode":305' in error_str:
                    print("\n🔍 BITVAVO DIAGNOSIS:")
                    print("- API key is not active or not found")
                    print("- Check that the API key is correctly copied from Bitvavo")
                    print("- Ensure the API key is activated in Bitvavo settings")
                    print("- Verify you're using the correct API key (not the secret)")
                elif 'invalid signature' in error_str:
                    print("\n🔍 BITVAVO DIAGNOSIS:")
                    print("- API secret appears to be invalid")
                    print("- Check that the API secret is correctly copied from Bitvavo")
                elif 'permission' in error_str:
                    print("\n🔍 BITVAVO DIAGNOSIS:")
                    print("- API key lacks required permissions")
                    print("- Enable trading permissions in Bitvavo API settings")
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def print_troubleshooting_guide():
    """Print a comprehensive troubleshooting guide."""
    print(f"\n{'='*70}")
    print(f"TROUBLESHOOTING GUIDE")
    print(f"{'='*70}")
    
    print("\n🔧 KRAKEN API SETUP:")
    print("1. Log into your Kraken account")
    print("2. Go to Settings → API")
    print("3. Create a new API key with these permissions:")
    print("   - Query Funds ✅")
    print("   - Query Open Orders and Trades ✅")
    print("   - Query Closed Orders and Trades ✅")
    print("   - Query Ledger Entries ✅")
    print("   - Create & Modify Orders ✅ (for live trading)")
    print("4. Copy the API Key and Private Key exactly")
    print("5. Make sure the API key is not expired")
    
    print("\n🔧 BITVAVO API SETUP:")
    print("1. Log into your Bitvavo account")
    print("2. Go to Settings → API Keys")
    print("3. Create a new API key with these permissions:")
    print("   - Read ✅")
    print("   - Trade ✅ (for live trading)")
    print("4. Copy the API Key and Secret exactly")
    print("5. Make sure the API key is activated")
    
    print("\n🔧 COMMON ISSUES:")
    print("- Whitespace: Remove any leading/trailing spaces from keys")
    print("- Case sensitivity: API keys are case-sensitive")
    print("- Expiration: Check if API keys have expiration dates")
    print("- IP restrictions: Ensure your IP is whitelisted if restrictions are set")
    print("- Permissions: Verify all required permissions are enabled")
    
    print("\n🔧 ENVIRONMENT VARIABLES:")
    print("- Make sure .env file is in the project root directory")
    print("- Restart your terminal after modifying .env")
    print("- Check for typos in variable names")
    print("- Ensure no quotes around values in .env file")

def main():
    """Main debugging function."""
    print(f"\n{'='*70}")
    print(f"API CREDENTIALS DEBUG TOOL")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*70}")
    
    # Debug both exchanges
    exchanges = ['kraken', 'bitvavo']
    
    for exchange_id in exchanges:
        api_key, api_secret = debug_credentials(exchange_id)
        
        if api_key and api_secret:
            test_exchange_with_debug(exchange_id, api_key, api_secret)
        else:
            print(f"❌ Cannot test {exchange_id} - missing credentials")
    
    # Print troubleshooting guide
    print_troubleshooting_guide()

if __name__ == "__main__":
    main()
