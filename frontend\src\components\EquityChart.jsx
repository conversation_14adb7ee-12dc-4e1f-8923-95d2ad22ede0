import React, { useRef, useEffect } from 'react'
import { Box, useColorModeValue } from '@chakra-ui/react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <Box
        bg="white"
        p={2}
        border="1px"
        borderColor="gray.200"
        borderRadius="md"
        boxShadow="sm"
      >
        <p>{`Date: ${new Date(label).toLocaleDateString()}`}</p>
        {payload.map((entry, index) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {`${entry.name}: $${entry.value.toFixed(2)}`}
          </p>
        ))}
      </Box>
    )
  }

  return null
}

const EquityChart = ({ strategyData, buyHoldData, height = 400, buyHoldCurves = {} }) => {
  // Check if we have multiple buy-and-hold curves
  const hasMultipleBuyHoldCurves = buyHoldCurves && Object.keys(buyHoldCurves).length > 0;

  // Create chart data with strategy and buy-hold curves
  const chartData = strategyData.map((item) => {
    // Start with strategy data
    const dataPoint = {
      date: new Date(item.time || item.date).getTime(),
      strategy: item.value,
    };

    // Add legacy buy-hold data if available
    if (buyHoldData && buyHoldData.length > 0) {
      // Find matching buy-hold data point by date
      const matchingBuyHold = buyHoldData.find(
        bhItem => new Date(bhItem.time || bhItem.date).getTime() === dataPoint.date
      );
      if (matchingBuyHold) {
        dataPoint.buyHold = matchingBuyHold.value;
      }
    }

    // Add data from multiple buy-hold curves if available
    if (hasMultipleBuyHoldCurves) {
      Object.entries(buyHoldCurves).forEach(([symbol, curveData]) => {
        if (curveData && curveData.length > 0) {
          // Find matching data point by date
          const matchingPoint = curveData.find(
            point => new Date(point.time || point.date).getTime() === dataPoint.date
          );
          if (matchingPoint) {
            // Use symbol as the key (e.g., "BTC/USDT")
            dataPoint[symbol] = matchingPoint.value;
          }
        }
      });
    }

    return dataPoint;
  });

  return (
    <Box
      bg="white"
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      h={`${height}px`}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" horizontal={false} />
          <XAxis
            dataKey="date"
            tickFormatter={(timestamp) => new Date(timestamp).toLocaleDateString()}
            minTickGap={50}
          />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {/* Strategy line */}
          <Line
            type="monotone"
            dataKey="strategy"
            name="Rotation Strategy"
            stroke="#0080ff"
            activeDot={{ r: 8 }}
            strokeWidth={2}
          />

          {/* Legacy buy-hold line */}
          {buyHoldData && buyHoldData.length > 0 && (
            <Line
              type="monotone"
              dataKey="buyHold"
              name="Buy & Hold"
              stroke="#8884d8"
              strokeWidth={2}
            />
          )}

          {/* Multiple buy-hold curves */}
          {hasMultipleBuyHoldCurves &&
            Object.keys(buyHoldCurves).map((symbol, index) => {
              // Define colors for different assets
              const colors = [
                '#8884d8', // Purple
                '#82ca9d', // Green
                '#ffc658', // Yellow
                '#ff8042', // Orange
                '#0088fe', // Blue
                '#ff6b6b', // Red
                '#8dd1e1', // Light Blue
                '#a4de6c', // Light Green
                '#d0ed57', // Lime
                '#ffc0cb', // Pink
              ];

              const color = colors[index % colors.length];

              return (
                <Line
                  key={symbol}
                  type="monotone"
                  dataKey={symbol}
                  name={`${symbol} Buy & Hold`}
                  stroke={color}
                  strokeWidth={1.5}
                  dot={false}
                />
              );
            })
          }
        </LineChart>
      </ResponsiveContainer>
    </Box>
  )
}

export default EquityChart
