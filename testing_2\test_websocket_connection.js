// test_websocket_connection.js
// Simple script to test the WebSocket connection to the backend

const io = require('socket.io-client');

// Create a socket instance
const socket = io('http://localhost:5001', {
  path: '/socket.io',
  transports: ['websocket', 'polling'],
  reconnection: true,
  timeout: 10000
});

// Set up event listeners
socket.on('connect', () => {
  console.log('Connected to WebSocket server');
  
  // Subscribe to market data updates
  console.log('Subscribing to market data...');
  socket.emit('subscribe_market_data', {
    symbols: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
    timeframe: '1m'
  });
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});

socket.on('subscription_status', (status) => {
  console.log('Subscription status:', status);
});

socket.on('market_data_update', (data) => {
  console.log('Market data update received:', data);
});

// Keep the script running
console.log('Waiting for WebSocket events. Press Ctrl+C to exit.');
