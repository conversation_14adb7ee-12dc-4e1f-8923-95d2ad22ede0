# src/indicators/pgo_score.py
"""
PGO (Price Gap Oscillator) Score Indicator for MTPI System

This module implements the PGO indicator specifically for the MTPI 8-indicator system.
The PGO measures the difference between the source price and its SMA, normalized by ATR,
and generates binary signals based on crossover detection.

This implementation matches the TradingView PineScript implementation with exact logic.
"""

import pandas as pd
import numpy as np
import logging
from typing import Optional
from .base_indicators import calculate_sma, calculate_atr

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Default configuration for PGO Score
DEFAULT_PGO_SCORE_CONFIG = {
    'length': 35,
    'upper_threshold': 1.35,
    'lower_threshold': -1.0,
    'src_col': 'close'
}

def calculate_pgo(df: pd.DataFrame, 
                 length: int = 35, 
                 src_col: str = 'close') -> pd.Series:
    """
    Calculate the raw PGO (Price Gap Oscillator) values.
    
    Args:
        df: DataFrame with OHLCV data
        length: Period length for SMA and ATR calculation
        src_col: Source column name
        
    Returns:
        Series with raw PGO values
    """
    try:
        # Calculate SMA and ATR
        sma = calculate_sma(df, length=length, close_col=src_col)
        atr = calculate_atr(df, length=length)
        
        # Handle potential division by zero in ATR
        atr_safe = atr.replace(0, np.nan)
        
        # Calculate Raw PGO value: (price - sma) / atr
        pgo_raw = (df[src_col] - sma) / atr_safe
        
        return pgo_raw
        
    except Exception as e:
        logging.error(f"Error calculating PGO: {e}")
        return pd.Series(index=df.index, dtype=float)

def generate_pgo_score_signal(df: pd.DataFrame,
                             length: int = 35,
                             upper_threshold: float = 1.35,
                             lower_threshold: float = -0.58,
                             src_col: str = 'close',
                             skip_warmup: bool = False) -> pd.Series:
    """
    Generate PGO Score signals for MTPI integration.
    
    This implementation matches the TradingView PineScript logic with crossover detection.
    
    Args:
        df: DataFrame with OHLCV data
        length: Period length for PGO calculation
        upper_threshold: Threshold for long signals (crossover above)
        lower_threshold: Threshold for short signals (crossover below)
        src_col: Source column name
        skip_warmup: Whether to skip generating signals during warm-up period
        
    Returns:
        Binary signal series (1 for long, -1 for short, 0 for neutral)
    """
    try:
        logging.info(f"Generating PGO Score signals with length={length}, "
                    f"upper_threshold={upper_threshold}, lower_threshold={lower_threshold}")
        
        # Calculate raw PGO values
        pgo_raw = calculate_pgo(df, length=length, src_col=src_col)
        
        # Initialize signal series
        signal = pd.Series(0, index=df.index, dtype=int)
        
        # Initialize state variables to match PineScript's behavior
        is_long = False
        is_short = False
        
        # Define warm-up period
        warmup_period = length * 2
        
        # Process signals using direct comparison (matching PineScript exactly)
        for i in range(len(df)):
            pgo_value = pgo_raw.iloc[i]

            # Skip NaN values and warm-up period if requested
            if pd.isna(pgo_value) or (skip_warmup and i < warmup_period):
                continue

            # Direct comparison logic (exactly matching PineScript)
            if pgo_value > upper_threshold:
                is_long = True
                is_short = False
            elif pgo_value < lower_threshold:
                is_long = False
                is_short = True
            # Note: No else clause - state persists between thresholds

            # Set signal based on current state (convert PineScript 1/0 to MTPI 1/-1/0 format)
            if is_long:
                signal.iloc[i] = 1   # Long signal
            elif is_short:
                signal.iloc[i] = -1  # Short signal (convert PineScript 0 to MTPI -1)
            else:
                signal.iloc[i] = 0   # Neutral (no position)
        
        # Log signal statistics
        signal_counts = signal.value_counts().sort_index()
        logging.info(f"Generated PGO Score signals: {dict(signal_counts)}")
        
        return signal
        
    except Exception as e:
        logging.error(f"Error generating PGO Score signals: {e}")
        return pd.Series(0, index=df.index, dtype=int)

def calculate_pgo_score(df: pd.DataFrame, **kwargs) -> tuple:
    """
    Calculate PGO Score with all components for analysis.
    
    Args:
        df: DataFrame with OHLCV data
        **kwargs: PGO parameters
        
    Returns:
        Tuple of (signal, pgo_raw, sma, atr)
    """
    try:
        # Extract parameters
        length = kwargs.get('length', 35)
        upper_threshold = kwargs.get('upper_threshold', 1.35)
        lower_threshold = kwargs.get('lower_threshold', -0.58)
        src_col = kwargs.get('src_col', 'close')
        skip_warmup = kwargs.get('skip_warmup', False)
        
        # Calculate components
        pgo_raw = calculate_pgo(df, length, src_col)
        sma = calculate_sma(df, length=length, close_col=src_col)
        atr = calculate_atr(df, length=length)
        
        # Generate signals
        signal = generate_pgo_score_signal(
            df, length, upper_threshold, lower_threshold, src_col, skip_warmup
        )
        
        return signal, pgo_raw, sma, atr
        
    except Exception as e:
        logging.error(f"Error calculating PGO Score: {e}")
        return (pd.Series(0, index=df.index, dtype=int),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float),
                pd.Series(index=df.index, dtype=float))

# Alias for backward compatibility
generate_pgo_signal = generate_pgo_score_signal
