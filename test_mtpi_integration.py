#!/usr/bin/env python3
"""
Test script to verify MTPI multi-indicator integration with background services.
This script tests that the YAML configuration is properly loaded and used.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from src.config_manager import load_config
from src.MTPI_signal_handler import load_mtpi_multi_indicator_config, fetch_multi_indicator_mtpi_signal

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_yaml_config_loading():
    """Test that YAML configuration is properly loaded."""
    print("=" * 80)
    print("TESTING MTPI MULTI-INDICATOR YAML CONFIGURATION LOADING")
    print("=" * 80)
    
    # 1. Test raw YAML loading
    print("\n1. RAW YAML CONFIG:")
    print("-" * 40)
    config = load_config()
    settings = config.get('settings', {})
    mtpi_indicators = settings.get('mtpi_indicators', {})
    
    print(f"Raw YAML mtpi_indicators section found: {bool(mtpi_indicators)}")
    if mtpi_indicators:
        enabled_indicators = mtpi_indicators.get('enabled_indicators', [])
        combination_method = mtpi_indicators.get('combination_method', 'consensus')
        long_threshold = mtpi_indicators.get('long_threshold', 0.1)
        short_threshold = mtpi_indicators.get('short_threshold', -0.1)
        
        print(f"  - Enabled indicators: {enabled_indicators}")
        print(f"  - Combination method: {combination_method}")
        print(f"  - Long threshold: {long_threshold}")
        print(f"  - Short threshold: {short_threshold}")
        print(f"  - Total indicators configured: {len([k for k in mtpi_indicators.keys() if k not in ['enabled_indicators', 'combination_method', 'long_threshold', 'short_threshold']])}")
    
    # 2. Test MTPI config loader
    print("\n2. MTPI CONFIG LOADER:")
    print("-" * 40)
    mtpi_config = load_mtpi_multi_indicator_config()
    
    print(f"MTPI config loaded successfully: {bool(mtpi_config)}")
    if mtpi_config:
        enabled_indicators = mtpi_config.get('enabled_indicators', [])
        combination_method = mtpi_config.get('combination_method', 'consensus')
        long_threshold = mtpi_config.get('long_threshold', 0.1)
        short_threshold = mtpi_config.get('short_threshold', -0.1)
        
        print(f"  - Enabled indicators: {enabled_indicators}")
        print(f"  - Combination method: {combination_method}")
        print(f"  - Long threshold: {long_threshold}")
        print(f"  - Short threshold: {short_threshold}")
        
        # Check individual indicator configs
        print(f"\n  Individual indicator configurations:")
        for indicator in enabled_indicators:
            if indicator in mtpi_config:
                print(f"    - {indicator}: {mtpi_config[indicator]}")
            else:
                print(f"    - {indicator}: NOT CONFIGURED")
    
    return mtpi_config

def test_background_service_integration():
    """Test how background services would load the configuration."""
    print("\n3. BACKGROUND SERVICE INTEGRATION TEST:")
    print("-" * 40)
    
    # Simulate what background_service.py does
    config = load_config()
    settings = config.get('settings', {})
    
    # Load MTPI multi-indicator configuration from YAML
    mtpi_config = settings.get('mtpi_indicators', {})
    enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo', 'bollinger_bands'])
    combination_method = mtpi_config.get('combination_method', 'consensus')
    long_threshold = mtpi_config.get('long_threshold', 0.1)
    short_threshold = mtpi_config.get('short_threshold', -0.1)
    
    print(f"Background service would load:")
    print(f"  - Enabled indicators: {enabled_indicators}")
    print(f"  - Combination method: {combination_method}")
    print(f"  - Long threshold: {long_threshold}")
    print(f"  - Short threshold: {short_threshold}")
    
    # Test that this matches what the MTPI handler loads
    mtpi_handler_config = load_mtpi_multi_indicator_config()
    handler_indicators = mtpi_handler_config.get('enabled_indicators', [])
    handler_method = mtpi_handler_config.get('combination_method', 'consensus')
    handler_long = mtpi_handler_config.get('long_threshold', 0.1)
    handler_short = mtpi_handler_config.get('short_threshold', -0.1)
    
    print(f"\nMTPI handler loads:")
    print(f"  - Enabled indicators: {handler_indicators}")
    print(f"  - Combination method: {handler_method}")
    print(f"  - Long threshold: {handler_long}")
    print(f"  - Short threshold: {handler_short}")
    
    # Check if they match
    configs_match = (
        enabled_indicators == handler_indicators and
        combination_method == handler_method and
        long_threshold == handler_long and
        short_threshold == handler_short
    )
    
    print(f"\nConfigurations match: {configs_match}")
    if not configs_match:
        print("WARNING: Background service and MTPI handler configurations don't match!")
    
    return configs_match

def test_mtpi_signal_fetch():
    """Test fetching a multi-indicator MTPI signal."""
    print("\n4. MTPI SIGNAL FETCH TEST:")
    print("-" * 40)
    
    try:
        # Test fetching the latest signal
        latest_signal = fetch_multi_indicator_mtpi_signal(
            timeframe='1d'
        )
        
        print(f"Latest MTPI signal: {latest_signal}")
        print(f"Signal type: {type(latest_signal)}")
        
        if latest_signal is not None:
            if latest_signal == 1:
                print("Signal interpretation: BULLISH")
            elif latest_signal == -1:
                print("Signal interpretation: BEARISH")
            else:
                print("Signal interpretation: NEUTRAL")
        else:
            print("WARNING: Could not fetch MTPI signal")
        
        return latest_signal is not None
        
    except Exception as e:
        print(f"ERROR fetching MTPI signal: {e}")
        return False

def main():
    """Run all tests."""
    print("MTPI MULTI-INDICATOR INTEGRATION TEST")
    print("=" * 80)
    
    # Test 1: YAML configuration loading
    mtpi_config = test_yaml_config_loading()
    config_loaded = bool(mtpi_config)
    
    # Test 2: Background service integration
    integration_ok = test_background_service_integration()
    
    # Test 3: MTPI signal fetching
    signal_fetch_ok = test_mtpi_signal_fetch()
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    print(f"✓ YAML Configuration Loading: {'PASS' if config_loaded else 'FAIL'}")
    print(f"✓ Background Service Integration: {'PASS' if integration_ok else 'FAIL'}")
    print(f"✓ MTPI Signal Fetching: {'PASS' if signal_fetch_ok else 'FAIL'}")
    
    all_tests_pass = config_loaded and integration_ok and signal_fetch_ok
    print(f"\nOverall Result: {'ALL TESTS PASS' if all_tests_pass else 'SOME TESTS FAILED'}")
    
    if all_tests_pass:
        print("\n🎉 MTPI multi-indicator integration is working correctly!")
        print("The background services should now use the multi-indicator system.")
    else:
        print("\n⚠️  Some issues detected. Please check the configuration.")
    
    return all_tests_pass

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
