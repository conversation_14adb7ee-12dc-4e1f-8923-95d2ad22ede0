#!/usr/bin/env python3
"""
Debug the core median supertrend calculations to find discrepancy with TradingView.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.median_score import calculate_median_smoothing, calculate_atr, calculate_median_supertrend

def debug_median_calculations():
    """Debug each step of median supertrend calculation."""
    print("=" * 80)
    print("DEBUGGING MEDIAN SUPERTREND CALCULATIONS")
    print("=" * 80)
    
    # Fetch data
    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe='1d',
        since='2024-01-01'
    )
    
    btc_df = data_dict['BTC/USDT']
    print(f"Data: {len(btc_df)} candles from {btc_df.index[0].date()} to {btc_df.index[-1].date()}")
    
    # Parameters
    atr_period = 12
    multiplier = 1.45
    median_length = 27
    src_col = 'high'
    
    print(f"\nParameters:")
    print(f"  ATR Period: {atr_period}")
    print(f"  Multiplier: {multiplier}")
    print(f"  Median Length: {median_length}")
    print(f"  Source: {src_col}")
    
    # Step 1: Calculate median smoothing
    print(f"\n1. MEDIAN SMOOTHING (ta.percentile_nearest_rank equivalent)")
    smooth1 = calculate_median_smoothing(btc_df, median_length, src_col)
    
    print(f"   First 10 values:")
    for i in range(10):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        high_val = btc_df[src_col].iloc[i]
        smooth_val = smooth1.iloc[i]
        print(f"   {date}: high={high_val:.2f}, smooth1={smooth_val:.2f}")
    
    # Step 2: Calculate ATR
    print(f"\n2. ATR CALCULATION")
    atr = calculate_atr(btc_df, atr_period)
    
    print(f"   First 10 ATR values:")
    for i in range(10):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        atr_val = atr.iloc[i]
        print(f"   {date}: ATR={atr_val:.2f}")
    
    # Step 3: Calculate basic bands
    print(f"\n3. BASIC SUPERTREND BANDS")
    upper_band = smooth1 + (multiplier * atr)
    lower_band = smooth1 - (multiplier * atr)
    
    print(f"   First 10 band values:")
    for i in range(10):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        smooth_val = smooth1.iloc[i]
        atr_val = atr.iloc[i]
        upper_val = upper_band.iloc[i]
        lower_val = lower_band.iloc[i]
        print(f"   {date}: smooth={smooth_val:.2f}, atr={atr_val:.2f}, upper={upper_val:.2f}, lower={lower_val:.2f}")
    
    # Step 4: Manual supertrend calculation to debug
    print(f"\n4. MANUAL SUPERTREND CALCULATION")
    
    close = btc_df['close']
    
    # Initialize arrays
    final_upper = pd.Series(index=btc_df.index, dtype=float)
    final_lower = pd.Series(index=btc_df.index, dtype=float)
    supertrend = pd.Series(index=btc_df.index, dtype=float)
    direction = pd.Series(index=btc_df.index, dtype=int)
    
    # First value initialization
    final_upper.iloc[0] = upper_band.iloc[0]
    final_lower.iloc[0] = lower_band.iloc[0]
    direction.iloc[0] = 1  # Start with uptrend
    supertrend.iloc[0] = final_lower.iloc[0]
    
    print(f"   Initial values:")
    print(f"   {btc_df.index[0].strftime('%Y-%m-%d')}: upper={final_upper.iloc[0]:.2f}, lower={final_lower.iloc[0]:.2f}, direction={direction.iloc[0]}")
    
    # Calculate step by step
    for i in range(1, min(20, len(btc_df))):  # First 20 for debugging
        # Upper band logic
        if upper_band.iloc[i] < final_upper.iloc[i-1] or close.iloc[i-1] > final_upper.iloc[i-1]:
            final_upper.iloc[i] = upper_band.iloc[i]
        else:
            final_upper.iloc[i] = final_upper.iloc[i-1]
        
        # Lower band logic
        if lower_band.iloc[i] > final_lower.iloc[i-1] or close.iloc[i-1] < final_lower.iloc[i-1]:
            final_lower.iloc[i] = lower_band.iloc[i]
        else:
            final_lower.iloc[i] = final_lower.iloc[i-1]
        
        # Direction logic
        if direction.iloc[i-1] == 1 and close.iloc[i] <= final_lower.iloc[i]:
            direction.iloc[i] = -1
        elif direction.iloc[i-1] == -1 and close.iloc[i] >= final_upper.iloc[i]:
            direction.iloc[i] = 1
        else:
            direction.iloc[i] = direction.iloc[i-1]
        
        # Supertrend line
        supertrend.iloc[i] = final_lower.iloc[i] if direction.iloc[i] == -1 else final_upper.iloc[i]
        
        date = btc_df.index[i].strftime('%Y-%m-%d')
        close_val = close.iloc[i]
        print(f"   {date}: close={close_val:.2f}, upper={final_upper.iloc[i]:.2f}, lower={final_lower.iloc[i]:.2f}, direction={direction.iloc[i]}, st={supertrend.iloc[i]:.2f}")
    
    # Step 5: Compare with our function
    print(f"\n5. COMPARE WITH OUR FUNCTION")
    st_line, st_direction = calculate_median_supertrend(btc_df, atr_period, multiplier, median_length, src_col)
    
    print(f"   First 10 comparisons:")
    for i in range(10):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        manual_st = supertrend.iloc[i] if i < len(supertrend) else np.nan
        manual_dir = direction.iloc[i] if i < len(direction) else np.nan
        func_st = st_line.iloc[i]
        func_dir = st_direction.iloc[i]
        
        st_match = "✅" if abs(manual_st - func_st) < 0.01 else "❌"
        dir_match = "✅" if manual_dir == func_dir else "❌"
        
        print(f"   {date}: manual_st={manual_st:.2f}, func_st={func_st:.2f} {st_match}, manual_dir={manual_dir}, func_dir={func_dir} {dir_match}")
    
    # Step 6: Check for potential issues
    print(f"\n6. POTENTIAL ISSUES CHECK")
    
    # Check for NaN values
    nan_smooth = smooth1.isna().sum()
    nan_atr = atr.isna().sum()
    print(f"   NaN values: smooth1={nan_smooth}, atr={nan_atr}")
    
    # Check median calculation method
    print(f"\n   Testing median calculation methods:")
    test_data = btc_df[src_col].iloc[26:27+median_length]  # 27 values for median
    pandas_median = test_data.median()
    numpy_median = np.median(test_data.values)
    percentile_50 = np.percentile(test_data.values, 50)
    
    print(f"   Pandas median: {pandas_median:.6f}")
    print(f"   Numpy median: {numpy_median:.6f}")
    print(f"   Percentile 50: {percentile_50:.6f}")
    print(f"   All equal: {abs(pandas_median - numpy_median) < 1e-10 and abs(numpy_median - percentile_50) < 1e-10}")
    
    # Check ATR calculation
    print(f"\n   ATR calculation check (manual vs function):")
    for i in range(atr_period, atr_period + 5):
        date = btc_df.index[i].strftime('%Y-%m-%d')
        
        # Manual ATR calculation
        high_vals = btc_df['high'].iloc[i-atr_period+1:i+1]
        low_vals = btc_df['low'].iloc[i-atr_period+1:i+1]
        close_vals = btc_df['close'].iloc[i-atr_period:i]  # Previous closes
        
        tr1 = high_vals - low_vals
        tr2 = abs(high_vals - close_vals.values)
        tr3 = abs(low_vals - close_vals.values)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        manual_atr = true_range.mean()
        func_atr = atr.iloc[i]
        
        atr_match = "✅" if abs(manual_atr - func_atr) < 0.01 else "❌"
        print(f"   {date}: manual_atr={manual_atr:.2f}, func_atr={func_atr:.2f} {atr_match}")

if __name__ == "__main__":
    debug_median_calculations()
