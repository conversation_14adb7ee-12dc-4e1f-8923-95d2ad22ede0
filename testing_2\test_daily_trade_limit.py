#!/usr/bin/env python
"""
Diagnostic script to test if BTC is hitting its daily trade limit.
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
from src.config_manager import get_trading_config
from src.trading.risk_manager import RiskManager
from src.trading.executor import TradingExecutor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('daily_trade_limit_test.log')
    ]
)

def test_daily_trade_limit():
    """Test if BTC is hitting its daily trade limit."""
    print("\n" + "=" * 50)
    print("TESTING DAILY TRADE LIMIT")
    print("=" * 50)
    
    # Initialize risk manager
    risk_manager = RiskManager()
    
    # Get trading config
    trading_config = get_trading_config()
    
    # Get max daily trades from config
    max_daily_trades = trading_config.get('risk_management', {}).get('max_daily_trades', 5)
    print(f"Maximum daily trades limit: {max_daily_trades}")
    
    # Initialize executor
    executor = TradingExecutor()
    
    # Log current daily trade counts
    print("\nCURRENT DAILY TRADE COUNTS:")
    risk_manager.log_daily_trade_counts()
    
    # Get current counts
    daily_counts = risk_manager.get_daily_trade_counts()
    if daily_counts:
        for symbol, count in sorted(daily_counts.items(), key=lambda x: x[1], reverse=True):
            status = "LIMIT REACHED" if count >= max_daily_trades else f"{count}/{max_daily_trades}"
            print(f"  {symbol}: {status}")
    else:
        print("  No trades recorded today")
    
    # Test specific assets
    test_assets = ["BTC/USDC", "ETH/USDC", "SOL/USDC", "SUI/USDC", "NEAR/USDC", "LTC/USDC"]
    
    print("\nTESTING SPECIFIC ASSETS:")
    for asset in test_assets:
        # Check if this asset would hit daily trade limit
        would_hit_limit = not risk_manager.check_daily_trade_limit(asset, increment=False)
        status = "WOULD HIT LIMIT" if would_hit_limit else "OK"
        
        # Get current count
        count = daily_counts.get(asset, 0)
        count_status = f"{count}/{max_daily_trades}"
        
        print(f"  {asset}: {status} (Count: {count_status})")
    
    # Test incrementing BTC counter
    btc_symbol = "BTC/USDC"
    print(f"\nTESTING INCREMENTING {btc_symbol} COUNTER:")
    
    # Get current count
    before_count = daily_counts.get(btc_symbol, 0)
    print(f"  Before: {before_count}/{max_daily_trades}")
    
    # Increment counter
    allowed = risk_manager.check_daily_trade_limit(btc_symbol, increment=True)
    
    # Get new count
    after_count = risk_manager.get_daily_trade_counts().get(btc_symbol, 0)
    print(f"  After: {after_count}/{max_daily_trades}")
    print(f"  Allowed: {allowed}")
    
    # Reset counter for testing
    today = datetime.now().date()
    if today in risk_manager.daily_trades and btc_symbol in risk_manager.daily_trades[today]:
        risk_manager.daily_trades[today][btc_symbol] = before_count
        print(f"  Reset to: {risk_manager.daily_trades[today][btc_symbol]}/{max_daily_trades}")
    
    print("\n" + "=" * 50)
    print("TEST COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    test_daily_trade_limit()
