#!/usr/bin/env python3
"""
Test script to verify that background services are properly using the multi-indicator MTPI system.
This script simulates what the background services do and verifies the integration.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from datetime import datetime, timedelta
from src.config_manager import load_config
from main_program import run_strategy_for_web

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_background_service_strategy_execution():
    """Test that background service strategy execution uses multi-indicator MTPI."""
    print("=" * 80)
    print("TESTING BACKGROUND SERVICE STRATEGY EXECUTION WITH MULTI-INDICATOR MTPI")
    print("=" * 80)
    
    # Load configuration like background_service.py does
    config = load_config()
    settings = config.get('settings', {})
    
    # Load MTPI multi-indicator configuration from YAML (like our updated background_service.py)
    mtpi_config = settings.get('mtpi_indicators', {})
    enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo', 'bollinger_bands'])
    combination_method = mtpi_config.get('combination_method', 'consensus')
    long_threshold = mtpi_config.get('long_threshold', 0.1)
    short_threshold = mtpi_config.get('short_threshold', -0.1)
    
    print(f"Configuration loaded:")
    print(f"  - Enabled indicators: {enabled_indicators}")
    print(f"  - Combination method: {combination_method}")
    print(f"  - Long threshold: {long_threshold}")
    print(f"  - Short threshold: {short_threshold}")
    
    # Set up test parameters
    analysis_start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    assets = settings.get('assets', ['BTC/USDC', 'ETH/USDC'])[:2]  # Limit to 2 assets for faster testing
    
    print(f"\nTest parameters:")
    print(f"  - Analysis start date: {analysis_start_date}")
    print(f"  - Assets: {assets}")
    print(f"  - Timeframe: {settings.get('timeframe', '1d')}")
    
    try:
        print(f"\nRunning strategy with multi-indicator MTPI...")
        
        # Run the strategy exactly like the updated background_service.py does
        results = run_strategy_for_web(
            use_mtpi_signal=settings.get('use_mtpi_signal', True),
            mtpi_timeframe=settings.get('mtpi_timeframe', '1d'),
            # Multi-indicator MTPI parameters
            mtpi_indicators=enabled_indicators,
            mtpi_combination_method=combination_method,
            mtpi_long_threshold=long_threshold,
            mtpi_short_threshold=short_threshold,
            timeframe=settings.get('timeframe', '1d'),
            analysis_start_date=analysis_start_date,
            selected_assets=assets,
            n_assets=1,  # Single asset for simplicity
            use_weighted_allocation=False,
            weights=None,
            enable_rebalancing=False,
            rebalance_threshold=0.05,
            use_cache=True,
            force_refresh_cache=False,
            context='test_integration'
        )
        
        print(f"✓ Strategy execution completed successfully!")
        
        # Verify results structure
        if results and isinstance(results, dict):
            print(f"\nResults structure:")
            print(f"  - Keys: {list(results.keys())}")
            
            # Check for MTPI signals
            if 'mtpi_signals' in results and results['mtpi_signals'] is not None:
                print(f"  - MTPI signals found: ✓")
                
                # Get the latest signal
                mtpi_signals = results['mtpi_signals']
                if hasattr(mtpi_signals, 'iloc') and len(mtpi_signals) > 0:
                    latest_signal = mtpi_signals.iloc[-1]
                    print(f"  - Latest MTPI signal: {latest_signal}")
                    
                    if latest_signal == 1:
                        print(f"  - Signal interpretation: BULLISH")
                    elif latest_signal == -1:
                        print(f"  - Signal interpretation: BEARISH")
                    else:
                        print(f"  - Signal interpretation: NEUTRAL")
                else:
                    print(f"  - MTPI signals format: {type(mtpi_signals)}")
            else:
                print(f"  - MTPI signals: NOT FOUND")
            
            # Check for best asset
            if 'best_asset_series' in results and results['best_asset_series'] is not None:
                print(f"  - Best asset series found: ✓")
                best_asset_series = results['best_asset_series']
                if hasattr(best_asset_series, 'iloc') and len(best_asset_series) > 0:
                    latest_best_asset = best_asset_series.iloc[-1]
                    print(f"  - Latest best asset: {latest_best_asset}")
            
            # Check for performance metrics
            if 'performance_metrics' in results and results['performance_metrics'] is not None:
                print(f"  - Performance metrics found: ✓")
                metrics = results['performance_metrics']
                if isinstance(metrics, dict):
                    print(f"  - Metrics keys: {list(metrics.keys())}")
            
            return True
            
        else:
            print(f"✗ Strategy execution failed or returned invalid results")
            print(f"Results type: {type(results)}")
            if results:
                print(f"Results content: {results}")
            return False
            
    except Exception as e:
        print(f"✗ Strategy execution failed with error: {e}")
        logging.error(f"Strategy execution error: {e}", exc_info=True)
        return False

def test_memecoin_service_strategy_execution():
    """Test that memecoin service strategy execution uses multi-indicator MTPI."""
    print("\n" + "=" * 80)
    print("TESTING MEMECOIN SERVICE STRATEGY EXECUTION WITH MULTI-INDICATOR MTPI")
    print("=" * 80)
    
    # Load configuration like background_service_memecoins.py does
    config = load_config()
    settings = config.get('settings', {})
    
    # Load MTPI multi-indicator configuration from YAML (like our updated background_service_memecoins.py)
    mtpi_config = settings.get('mtpi_indicators', {})
    enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo', 'bollinger_bands'])
    combination_method = mtpi_config.get('combination_method', 'consensus')
    long_threshold = mtpi_config.get('long_threshold', 0.1)
    short_threshold = mtpi_config.get('short_threshold', -0.1)
    
    print(f"Memecoin service configuration:")
    print(f"  - Enabled indicators: {enabled_indicators}")
    print(f"  - Combination method: {combination_method}")
    print(f"  - Long threshold: {long_threshold}")
    print(f"  - Short threshold: {short_threshold}")
    
    # Prepare arguments for strategy execution with multi-indicator MTPI (like our updated memecoin service)
    strategy_args = {
        'timeframe': settings.get('timeframe', '1d'),
        'mtpi_timeframe': settings.get('mtpi_timeframe', '1d'),
        'analysis_start_date': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
        'n_assets': 1,  # Single asset for testing
        'transaction_fee_rate': settings.get('transaction_fee_rate', 0.001),
        'selected_assets': ['BTC/USDC'],  # Use a simple asset for testing
        'use_cache': True,
        'initial_capital': settings.get('initial_capital', 10000),
        'use_mtpi_signal': settings.get('use_mtpi_signal', True),
        'use_weighted_allocation': False,
        'weights': [],
        'trend_method': settings.get('trend_method', 'PGO For Loop'),
        # Multi-indicator MTPI parameters
        'mtpi_indicators': enabled_indicators,
        'mtpi_combination_method': combination_method,
        'mtpi_long_threshold': long_threshold,
        'mtpi_short_threshold': short_threshold,
        # Asset trend PGO parameters (for asset scoring, not MTPI)
        'pgo_length': settings.get('pgo_length', 35),
        'pgo_upper_threshold': settings.get('pgo_upper_threshold', 1.1),
        'pgo_lower_threshold': settings.get('pgo_lower_threshold', -0.58),
        'geckoterminal_tokens': [],  # Empty for testing
        'force_refresh_cache': False,
        'context': 'test_memecoin_integration'
    }
    
    print(f"\nMemecoin strategy arguments prepared:")
    print(f"  - MTPI indicators: {strategy_args['mtpi_indicators']}")
    print(f"  - MTPI combination method: {strategy_args['mtpi_combination_method']}")
    print(f"  - MTPI thresholds: {strategy_args['mtpi_long_threshold']}/{strategy_args['mtpi_short_threshold']}")
    
    try:
        print(f"\nRunning memecoin strategy with multi-indicator MTPI...")
        
        # Run the strategy like the updated background_service_memecoins.py does
        results = run_strategy_for_web(**strategy_args)
        
        print(f"✓ Memecoin strategy execution completed successfully!")
        
        # Verify results
        if results and isinstance(results, dict):
            print(f"  - Results structure verified: ✓")
            return True
        else:
            print(f"✗ Memecoin strategy execution failed")
            return False
            
    except Exception as e:
        print(f"✗ Memecoin strategy execution failed with error: {e}")
        logging.error(f"Memecoin strategy execution error: {e}", exc_info=True)
        return False

def main():
    """Run all background service integration tests."""
    print("BACKGROUND SERVICE MTPI MULTI-INDICATOR INTEGRATION TEST")
    print("=" * 80)
    
    # Test 1: Background service strategy execution
    background_service_ok = test_background_service_strategy_execution()
    
    # Test 2: Memecoin service strategy execution
    memecoin_service_ok = test_memecoin_service_strategy_execution()
    
    # Summary
    print("\n" + "=" * 80)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 80)
    print(f"✓ Background Service Integration: {'PASS' if background_service_ok else 'FAIL'}")
    print(f"✓ Memecoin Service Integration: {'PASS' if memecoin_service_ok else 'FAIL'}")
    
    all_tests_pass = background_service_ok and memecoin_service_ok
    print(f"\nOverall Result: {'ALL TESTS PASS' if all_tests_pass else 'SOME TESTS FAILED'}")
    
    if all_tests_pass:
        print("\n🎉 Background services are successfully using the multi-indicator MTPI system!")
        print("✓ YAML configuration is properly loaded")
        print("✓ Multi-indicator parameters are correctly passed to run_strategy_for_web")
        print("✓ Strategy execution works with the new integration")
    else:
        print("\n⚠️  Some integration issues detected.")
    
    return all_tests_pass

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
